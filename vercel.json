{"buildCommand": "bun run build", "devCommand": "bun dev", "installCommand": "bun install", "framework": "nextjs", "regions": ["iad1"], "functions": {"app/api/auth/[...all]/route.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}], "rewrites": [{"source": "/api/auth/(.*)", "destination": "/api/auth/[...all]"}]}