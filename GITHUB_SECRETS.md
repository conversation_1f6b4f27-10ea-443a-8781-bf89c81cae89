# GitHub Secrets Configuration

This document explains how to configure GitHub repository secrets for automated deployment.

## Required Secrets

Navigate to your GitHub repository → Settings → Secrets and variables → Actions, then add the following secrets:

### Convex Configuration

| Secret Name | Description | Example Value |
|-------------|-------------|---------------|
| `NEXT_PUBLIC_CONVEX_URL` | Production Convex URL | `https://your-prod.convex.cloud` |
| `CONVEX_DEPLOYMENT` | Production deployment name | `prod:your-deployment-name` |

### Netlify Configuration

| Secret Name | Description | How to Get |
|-------------|-------------|------------|
| `NETLIFY_AUTH_TOKEN` | Netlify personal access token | Go to Netlify → User settings → Personal access tokens |
| `NETLIFY_SITE_ID` | Your site ID | Go to Site settings → General → Site details |

### Optional Secrets

| Secret Name | Description | Example Value |
|-------------|-------------|---------------|
| `PRODUCTION_URL` | Your production domain | `https://your-domain.com` |

## How to Add Secrets

1. **Go to Repository Settings**
   - Navigate to your GitHub repository
   - Click on "Settings" tab
   - Go to "Secrets and variables" → "Actions"

2. **Add New Repository Secret**
   - Click "New repository secret"
   - Enter the secret name (exactly as shown above)
   - Enter the secret value
   - Click "Add secret"

3. **Repeat for All Required Secrets**
   - Add each secret from the table above
   - Ensure names match exactly (case-sensitive)

## Getting Netlify Credentials

### Netlify Auth Token

1. Log in to [Netlify](https://app.netlify.com)
2. Go to User settings (click your avatar → User settings)
3. Navigate to "Personal access tokens"
4. Click "New access token"
5. Give it a descriptive name (e.g., "GitHub Actions Deploy")
6. Copy the generated token and add it as `NETLIFY_AUTH_TOKEN`

### Netlify Site ID

1. Go to your site dashboard on Netlify
2. Navigate to Site settings → General
3. Under "Site details", copy the "Site ID"
4. Add it as `NETLIFY_SITE_ID`

## Getting Convex Credentials

### Convex URL and Deployment

1. Run `npx convex deploy --prod` locally
2. Copy the production URL from the output
3. The deployment name is usually in format `prod:your-deployment-name`

Alternatively, check your Convex dashboard:
1. Go to [Convex Dashboard](https://dashboard.convex.dev)
2. Select your project
3. Go to "Settings" → "Environment Variables"
4. Copy the production URL and deployment name

## Verifying Secrets

After adding all secrets, you can verify they're correctly set:

1. Go to your repository's Actions tab
2. Trigger a workflow (push to main branch or create a PR)
3. Check the workflow logs to ensure secrets are being used correctly

## Security Best Practices

1. **Never commit secrets to code**
   - Always use GitHub secrets for sensitive data
   - Add `.env*` files to `.gitignore`

2. **Use least privilege principle**
   - Netlify tokens should only have necessary permissions
   - Regularly rotate access tokens

3. **Monitor secret usage**
   - Review which workflows have access to secrets
   - Remove unused secrets periodically

4. **Environment-specific secrets**
   - Use different secrets for staging and production
   - Never use production secrets in development

## Troubleshooting

### Common Issues

1. **Secret not found error**
   - Check secret name spelling (case-sensitive)
   - Ensure secret is added to the correct repository
   - Verify workflow has access to secrets

2. **Invalid Netlify token**
   - Regenerate the personal access token
   - Ensure token has correct permissions
   - Check token hasn't expired

3. **Convex deployment fails**
   - Verify `CONVEX_DEPLOYMENT` format is correct
   - Check Convex authentication in local environment
   - Ensure production deployment exists

### Testing Secrets

You can test if secrets are working by adding a debug step to your workflow:

```yaml
- name: Debug secrets (remove after testing)
  run: |
    echo "Convex URL length: ${#NEXT_PUBLIC_CONVEX_URL}"
    echo "Netlify token length: ${#NETLIFY_AUTH_TOKEN}"
  env:
    NEXT_PUBLIC_CONVEX_URL: ${{ secrets.NEXT_PUBLIC_CONVEX_URL }}
    NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
```

**Important**: Remove debug steps before production use to avoid exposing secret information.

## Alternative: Vercel Deployment

If using Vercel instead of Netlify, replace Netlify secrets with:

| Secret Name | Description | How to Get |
|-------------|-------------|------------|
| `VERCEL_TOKEN` | Vercel access token | Vercel → Settings → Tokens |
| `VERCEL_ORG_ID` | Organization ID | Vercel project settings |
| `VERCEL_PROJECT_ID` | Project ID | Vercel project settings |

Update the workflow file accordingly to use Vercel CLI instead of Netlify CLI.
