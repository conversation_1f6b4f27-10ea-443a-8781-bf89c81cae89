# Database Schema Migration Guide

This document outlines the enhanced database schema optimizations and migration steps for production deployment.

## Schema Enhancements Overview

### 🔧 **Key Improvements Made**

1. **Enhanced Data Validation**
   - Added strict type unions for enum-like fields
   - Implemented proper data constraints
   - Added validation helpers for data integrity

2. **Improved Indexing Strategy**
   - Added composite indexes for common query patterns
   - Optimized single-field indexes for performance
   - Added indexes for foreign key relationships

3. **Better Data Relationships**
   - Added user references across tables
   - Implemented proper foreign key patterns
   - Enhanced metadata structures

4. **Security Enhancements**
   - Added session tracking fields (IP, User Agent)
   - Implemented user role system
   - Added audit trail capabilities

## Detailed Schema Changes

### 👤 **Users Table Enhancements**

**New Fields Added:**
- `role`: User role system (admin, user, viewer)
- `isActive`: Account status tracking
- `lastLoginAt`: Login tracking
- `preferences`: User preferences object

**New Indexes:**
- `by_role`: Query users by role
- `by_active`: Query active/inactive users
- `by_last_login`: Sort by last login time

### 🔐 **Sessions Table Enhancements**

**New Fields Added:**
- `ipAddress`: Security tracking
- `userAgent`: Device tracking
- `isActive`: Session management

**New Indexes:**
- `by_expires`: Cleanup expired sessions
- `by_active_user`: Query active sessions per user

### 📊 **Stats Table Enhancements**

**Improvements:**
- Strict enum validation for stat types
- Added metadata object for additional context
- Composite index for type + timestamp queries

**Supported Stat Types:**
- `activeUsers`, `ontologyEntities`, `totalProjects`
- `completedTasks`, `systemLoad`, `apiCalls`, `errorRate`

### 🌍 **Project Activities Enhancements**

**New Fields Added:**
- `userId`: Track who performed the activity
- `duration`: Activity duration tracking
- `status`: Activity completion status
- Enhanced location object with city/country

**New Indexes:**
- `by_project`: Query activities by project
- `by_user`: Query activities by user
- `by_importance`: Sort by importance level

### 📈 **Performance Metrics Enhancements**

**New Fields Added:**
- `lastUpdated`: Track when metrics were updated
- `projectId`: Unique project identifier
- `teamSize`: Team size tracking
- `completionPercentage`: Project completion tracking

**Status Validation:**
- Added "critical" status for urgent issues
- Strict enum validation for all status fields

### 🤖 **Agent Tables Enhancements**

**Agent Activities:**
- Added error tracking and metadata
- Enhanced status types with "pending" and "cancelled"
- Added user tracking for audit trails

**Agent Calls:**
- Added cost tracking for financial monitoring
- Enhanced error reporting with codes and messages
- Added model and request ID tracking

## Migration Steps

### Step 1: Backup Current Data

```bash
# Export current data (if any exists)
npx convex export --output backup-$(date +%Y%m%d).json
```

### Step 2: Deploy New Schema

```bash
# Deploy the enhanced schema
npx convex deploy

# For production deployment
npx convex deploy --prod
```

### Step 3: Validate Schema Deployment

```bash
# Check schema deployment status
npx convex dashboard
```

### Step 4: Initialize Enhanced Data

The new schema is backward compatible, but you may want to initialize new fields:

```bash
# Run data initialization (if needed)
npx convex run migrations:initializeEnhancedFields
```

### Step 5: Update Application Code

Update your application code to use the new fields and validation:

1. **Update type definitions** to match new schema
2. **Add validation** using the new validators
3. **Update queries** to use new indexes
4. **Implement role-based access** using user roles

## Validation Implementation

### Using Validators in Mutations

```typescript
import { validationHelpers, validationErrors } from "./validators";

export const createUser = mutation({
  args: {
    email: v.string(),
    name: v.optional(v.string()),
    role: userRoles,
  },
  handler: async (ctx, args) => {
    // Validate email format
    if (!validationHelpers.isValidEmail(args.email)) {
      throw new Error(validationErrors.INVALID_EMAIL);
    }

    // Insert with validation
    return await ctx.db.insert("users", {
      email: args.email,
      name: args.name,
      role: args.role || "user",
      isActive: true,
      createdAt: Date.now(),
    });
  },
});
```

### Using Enhanced Indexes

```typescript
// Query active users by role
const adminUsers = await ctx.db
  .query("users")
  .withIndex("by_role", (q) => q.eq("role", "admin"))
  .filter((q) => q.eq(q.field("isActive"), true))
  .collect();

// Query recent activities by type and time
const recentDeployments = await ctx.db
  .query("projectActivities")
  .withIndex("by_type_timestamp", (q) => 
    q.eq("type", "deployment").gte("timestamp", Date.now() - 86400000)
  )
  .collect();
```

## Performance Optimizations

### 🚀 **Query Performance**

1. **Use Composite Indexes**
   - `by_type_timestamp` for time-based queries
   - `by_status_timestamp` for status filtering
   - `by_active_user` for user session queries

2. **Optimize Common Queries**
   - User authentication: `by_email` index
   - Session management: `by_token` and `by_expires` indexes
   - Activity feeds: `by_timestamp` and `by_type_timestamp` indexes

3. **Batch Operations**
   - Use batch inserts for bulk data
   - Implement pagination for large result sets
   - Use appropriate limits in queries

### 📊 **Data Integrity**

1. **Input Validation**
   - Use validation helpers before database operations
   - Sanitize string inputs to prevent issues
   - Validate numeric ranges and constraints

2. **Error Handling**
   - Implement proper error messages
   - Log validation failures for monitoring
   - Provide user-friendly error responses

## Monitoring and Maintenance

### 📈 **Performance Monitoring**

1. **Query Performance**
   - Monitor slow queries in Convex dashboard
   - Track index usage and effectiveness
   - Optimize queries based on usage patterns

2. **Data Growth**
   - Monitor table sizes and growth rates
   - Implement data archiving for old records
   - Set up alerts for unusual data patterns

### 🔧 **Maintenance Tasks**

1. **Regular Cleanup**
   - Remove expired sessions
   - Archive old activity logs
   - Clean up orphaned records

2. **Index Optimization**
   - Review index usage statistics
   - Add new indexes based on query patterns
   - Remove unused indexes to improve write performance

## Rollback Plan

If issues arise with the new schema:

1. **Immediate Rollback**
   ```bash
   # Rollback to previous deployment
   npx convex rollback --prod
   ```

2. **Data Recovery**
   ```bash
   # Restore from backup if needed
   npx convex import backup-YYYYMMDD.json
   ```

3. **Gradual Migration**
   - Deploy schema changes incrementally
   - Test each change in staging environment
   - Monitor performance after each deployment

## Testing Checklist

Before production deployment:

- [ ] Schema validates correctly
- [ ] All existing queries work with new schema
- [ ] New indexes improve query performance
- [ ] Validation helpers work correctly
- [ ] Error handling is implemented
- [ ] Data migration scripts tested
- [ ] Backup and restore procedures tested
- [ ] Performance benchmarks meet requirements

## Next Steps

After schema migration:

1. **Implement Security Hardening** (Phase 4)
2. **Add Performance Monitoring** (Phase 6)
3. **Set up Automated Testing** (Phase 7)
4. **Configure Production Deployment** (Phase 10)
