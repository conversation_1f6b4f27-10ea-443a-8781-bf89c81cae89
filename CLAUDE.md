# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

```bash
# Development (runs both Next.js and Convex dev servers)
npm run dev                # Starts both servers in parallel
npm run dev:next          # Start only Next.js with Turbopack on 0.0.0.0
npm run dev:convex        # Start only Convex dev server

# Build & Production
npm run build             # Builds Next.js and deploys Convex
npm run start             # Start production server

# Code Quality
npm run lint              # Run Biome linter and TypeScript type checking
npm run format            # Format code with Biome

# Alternative package managers
bun dev                   # Bun is the primary package manager
bun install               # Install dependencies with Bun
```

## Architecture

### Technology Stack
- **Framework**: Next.js 15 with App Router and Turbopack
- **Database**: Convex (real-time, reactive database)
- **Authentication**: Better Auth (using in-memory SQLite for development)
- **UI Components**: shadcn/ui with Radix UI primitives
- **Styling**: Tailwind CSS with CVA for component variants
- **3D Graphics**: Three.js with React Three Fiber for globe visualization
- **Type Safety**: TypeScript with strict mode enabled

### Project Structure
```
src/
├── app/                    # Next.js App Router pages
│   ├── agents/            # Agent management pages (metrics, library, designer)
│   ├── projects/          # Project lifecycle management
│   ├── ontology/          # Ontology editor with collaboration
│   └── providers.tsx      # Global providers (Convex, Theme)
├── components/
│   ├── ui/               # shadcn/ui base components
│   ├── dashboard/        # Dashboard-specific components
│   ├── layout/           # Layout components (Header, Footer, MainLayout)
│   ├── ontology/         # Ontology visualization and editing
│   └── ConvexClientProvider.tsx  # Convex client wrapper
├── lib/
│   ├── auth.ts           # Better Auth server configuration
│   ├── auth-client.ts    # Better Auth client
│   ├── utils.ts          # cn() utility for className merging
│   └── hooks/            # Custom hooks (useStats, useAuth, useAgentMetrics)
convex/
├── schema.ts             # Database schema definition
├── http.ts               # HTTP routes for auth
├── stats.ts              # Stats queries and mutations
├── agentMetrics.ts       # Agent performance data
└── initDemo.ts           # Demo data initialization
```

### Key Architectural Patterns

1. **Real-time Data with Convex**
   - All data operations go through Convex functions
   - Use `useQuery` hooks for reactive data fetching
   - Use `useMutation` hooks for data modifications
   - Schema-defined tables provide type safety
   - No manual API routes needed for data operations

2. **Provider Hierarchy**
   ```typescript
   ConvexClientProvider
   └── ThemeProvider
       └── Application Components
   ```

3. **Component Patterns**
   - Use `forwardRef` for components needing DOM access
   - Apply `cn()` utility from `/lib/utils.ts` for className merging
   - Follow shadcn/ui patterns for new UI components
   - Keep components focused and composable

4. **Database Schema**
   Key tables in Convex:
   - `users` - User authentication data
   - `stats` - Dashboard metrics
   - `projectActivities` - Globe visualization data
   - `agentCalls` - Agent performance tracking
   - `agentMetrics` - Agent analytics data

### Code Style

- **Formatter**: Biome with double quotes, 2-space indentation
- **Linter**: Biome with relaxed a11y rules
- **TypeScript**: Strict mode enabled
- **Path Aliases**: `@/*` maps to `./src/*`

### Environment Variables

Required in `.env.local`:
```bash
# Convex
NEXT_PUBLIC_CONVEX_URL=     # Public Convex URL
CONVEX_DEPLOYMENT=          # Convex deployment identifier

# Application
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Optional OAuth providers
# GITHUB_CLIENT_ID=
# GITHUB_CLIENT_SECRET=
```

### Deployment

Configured for Netlify with:
- Bun 1.0.25 runtime
- Next.js plugin
- Convex deployment during build
- Remote image optimization for Unsplash

### BMAD Framework Integration

The project includes `.cursor/rules/` with agent personas for development workflow:
- `dev.mdc` - Full Stack Developer agent
- `architect.mdc` - System Architect agent
- `bmad-orchestrator.mdc` - BMAD workflow orchestrator

These define structured development workflows with task execution patterns and validation checkpoints.