# Deployment Guide

This guide provides step-by-step instructions for deploying the ALIAS HQ application to production.

## Quick Start

1. **Prerequisites Check**
   ```bash
   # Verify all tools are installed
   node --version  # Should be 18+
   bun --version   # Should be 1.0+
   npx convex --version
   ```

2. **Environment Setup**
   ```bash
   # Copy production environment template
   cp .env.production .env.local
   # Edit .env.local with your actual production values
   ```

3. **Deploy to Production**
   ```bash
   # Option A: Netlify (recommended)
   bun run deploy:netlify
   
   # Option B: Vercel
   bun run deploy:prod
   ```

## Detailed Deployment Steps

### Step 1: Prepare Production Environment

1. **Create Production Convex Deployment**
   ```bash
   # Login to Convex (if not already logged in)
   npx convex login
   
   # Deploy to production
   npx convex deploy --prod
   ```
   
   This will output your production Convex URL. Copy it for the next step.

2. **Configure Environment Variables**
   
   Update `.env.production` with your actual values:
   ```bash
   # Replace with your actual production Convex URL
   NEXT_PUBLIC_CONVEX_URL=https://your-actual-production.convex.cloud
   CONVEX_DEPLOYMENT=prod:your-actual-deployment-name
   
   # Generate a secure secret
   BETTER_AUTH_SECRET=$(openssl rand -base64 32)
   
   # Set your production domain
   NEXT_PUBLIC_APP_URL=https://your-domain.com
   ```

### Step 2: Configure OAuth Providers

#### GitHub OAuth Setup

1. Go to [GitHub Developer Settings](https://github.com/settings/developers)
2. Click "New OAuth App"
3. Fill in the details:
   - **Application name**: ALIAS HQ Production
   - **Homepage URL**: `https://your-domain.com`
   - **Authorization callback URL**: `https://your-domain.com/api/auth/callback/github`
4. Copy the Client ID and Client Secret to your environment variables

#### Google OAuth Setup (Optional)

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to APIs & Services > Credentials
3. Create OAuth 2.0 Client ID:
   - **Application type**: Web application
   - **Authorized redirect URIs**: `https://your-domain.com/api/auth/callback/google`
4. Copy the Client ID and Client Secret to your environment variables

### Step 3: Deploy to Hosting Platform

#### Option A: Netlify Deployment (Recommended)

1. **Install Netlify CLI** (if not already installed)
   ```bash
   npm install -g netlify-cli
   ```

2. **Login to Netlify**
   ```bash
   netlify login
   ```

3. **Initialize Netlify Site** (first time only)
   ```bash
   netlify init
   ```

4. **Deploy to Production**
   ```bash
   bun run deploy:netlify
   ```

5. **Configure Environment Variables in Netlify**
   - Go to your site dashboard on Netlify
   - Navigate to Site settings > Environment variables
   - Add all variables from `.env.production`

#### Option B: Vercel Deployment

1. **Install Vercel CLI** (if not already installed)
   ```bash
   npm install -g vercel
   ```

2. **Login to Vercel**
   ```bash
   vercel login
   ```

3. **Deploy to Production**
   ```bash
   bun run deploy:prod
   ```

4. **Configure Environment Variables in Vercel**
   - Go to your project dashboard on Vercel
   - Navigate to Settings > Environment Variables
   - Add all variables from `.env.production`

### Step 4: Initialize Production Data

After successful deployment:

```bash
# Set production environment
export CONVEX_DEPLOYMENT=prod:your-deployment-name

# Initialize demo data
bun run convex:init-prod
```

### Step 5: Verify Deployment

1. **Check Application Health**
   - Visit your production URL
   - Test user authentication (sign up/sign in)
   - Verify data loading and real-time updates
   - Test the 3D globe visualization

2. **Monitor Convex Dashboard**
   - Check function execution logs
   - Monitor database operations
   - Verify real-time subscriptions are working

3. **Test Performance**
   - Run Lighthouse audit
   - Check Core Web Vitals
   - Test on different devices and browsers

## Post-Deployment Checklist

- [ ] Application loads successfully
- [ ] Authentication flows work correctly
- [ ] Data loads from Convex backend
- [ ] Real-time updates are functional
- [ ] 3D globe renders properly
- [ ] All environment variables are secure
- [ ] SSL certificate is active
- [ ] Custom domain is configured (if applicable)
- [ ] Performance metrics are acceptable
- [ ] Error monitoring is active

## Troubleshooting

### Common Issues

1. **Build Failures**
   ```bash
   # Check TypeScript compilation
   bun run type-check
   
   # Check for linting errors
   bun run lint
   ```

2. **Convex Connection Issues**
   - Verify `NEXT_PUBLIC_CONVEX_URL` is correct
   - Check Convex deployment status in dashboard
   - Ensure environment variables are set correctly

3. **Authentication Issues**
   - Verify OAuth callback URLs match exactly
   - Check `BETTER_AUTH_SECRET` is set and secure
   - Ensure `NEXT_PUBLIC_APP_URL` matches your domain

4. **Performance Issues**
   - Check bundle size: `bun run build` and review output
   - Monitor Convex function performance in dashboard
   - Use browser dev tools to identify bottlenecks

### Getting Help

- Check the [Convex Documentation](https://docs.convex.dev)
- Review [Better Auth Documentation](https://better-auth.com)
- Create an issue in the project repository
- Check hosting platform documentation (Netlify/Vercel)

## Rollback Procedure

If you need to rollback a deployment:

### Netlify Rollback
```bash
# List recent deployments
netlify sites:list

# Rollback to previous deployment
netlify rollback
```

### Vercel Rollback
```bash
# List recent deployments
vercel ls

# Promote a previous deployment
vercel promote <deployment-url>
```

### Convex Rollback
```bash
# Rollback Convex functions (if needed)
npx convex deploy --prod --rollback
```
