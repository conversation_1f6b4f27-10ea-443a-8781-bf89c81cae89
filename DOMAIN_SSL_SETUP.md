# Domain and SSL Setup Guide

This guide walks you through setting up a custom domain and SSL certificate for your production deployment.

## Overview

- **Default URLs**: Netlify/Vercel provide default subdomains
- **Custom Domain**: Use your own domain (recommended for production)
- **SSL Certificate**: Automatically provided by hosting platforms
- **DNS Configuration**: Point your domain to the hosting platform

## Option 1: Using Netlify Default Domain

If you don't have a custom domain, you can use Netlify's default domain:

1. **Deploy to Netlify** (as per deployment guide)
2. **Get your Netlify URL**
   - Format: `https://your-site-name.netlify.app`
   - Found in your Netlify site dashboard

3. **Update Environment Variables**
   ```bash
   NEXT_PUBLIC_APP_URL=https://your-site-name.netlify.app
   ```

## Option 2: Custom Domain Setup

### Step 1: Purchase a Domain

Popular domain registrars:
- [Namecheap](https://www.namecheap.com)
- [GoDaddy](https://www.godaddy.com)
- [Google Domains](https://domains.google.com)
- [Cloudflare](https://www.cloudflare.com/products/registrar/)

### Step 2: Configure Domain in Netlify

1. **Go to Netlify Site Dashboard**
   - Navigate to your site
   - Go to "Domain settings"

2. **Add Custom Domain**
   - Click "Add custom domain"
   - Enter your domain (e.g., `your-domain.com`)
   - Click "Verify"

3. **Configure DNS**
   
   **Option A: Use Netlify DNS (Recommended)**
   - Netlify will provide nameservers
   - Update nameservers at your domain registrar
   - Netlify handles all DNS configuration
   
   **Option B: External DNS**
   - Keep your current DNS provider
   - Add DNS records manually (see below)

### Step 3: DNS Configuration (External DNS)

If using external DNS, add these records:

#### For Root Domain (example.com)

| Type | Name | Value | TTL |
|------|------|-------|-----|
| A | @ | 75.2.60.5 | 3600 |
| AAAA | @ | 2600:1f18:3e2:e500::2 | 3600 |

#### For Subdomain (www.example.com)

| Type | Name | Value | TTL |
|------|------|-------|-----|
| CNAME | www | your-site-name.netlify.app | 3600 |

#### For Both Root and WWW

Add both sets of records above, then configure redirect in Netlify.

### Step 4: SSL Certificate

Netlify automatically provides SSL certificates:

1. **Automatic SSL**
   - SSL certificate is automatically generated
   - Usually takes 1-24 hours to provision
   - No additional configuration needed

2. **Verify SSL**
   - Check that `https://your-domain.com` works
   - Look for the lock icon in browser
   - Certificate should be valid and trusted

### Step 5: Configure Redirects

Add to your `netlify.toml`:

```toml
# Redirect www to non-www
[[redirects]]
  from = "https://www.your-domain.com/*"
  to = "https://your-domain.com/:splat"
  status = 301
  force = true

# Redirect http to https
[[redirects]]
  from = "http://your-domain.com/*"
  to = "https://your-domain.com/:splat"
  status = 301
  force = true
```

## Option 3: Vercel Custom Domain

### Step 1: Add Domain in Vercel

1. **Go to Vercel Project Dashboard**
   - Navigate to your project
   - Go to "Settings" → "Domains"

2. **Add Domain**
   - Click "Add"
   - Enter your domain
   - Click "Add"

### Step 2: Configure DNS for Vercel

#### For Root Domain

| Type | Name | Value | TTL |
|------|------|-------|-----|
| A | @ | 76.76.19.61 | 3600 |
| AAAA | @ | 2600:1f18:3e2:e500::2 | 3600 |

#### For Subdomain

| Type | Name | Value | TTL |
|------|------|-------|-----|
| CNAME | www | cname.vercel-dns.com | 3600 |

### Step 3: SSL with Vercel

- SSL is automatically provided
- Certificate is managed by Vercel
- Usually provisions within minutes

## Environment Variable Updates

After domain setup, update your environment variables:

### Production Environment

```bash
# Update in .env.production
NEXT_PUBLIC_APP_URL=https://your-domain.com

# Update OAuth providers
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
```

### OAuth Provider Updates

Update callback URLs in your OAuth providers:

#### GitHub OAuth
1. Go to GitHub Settings → Developer settings → OAuth Apps
2. Edit your production app
3. Update "Authorization callback URL" to:
   ```
   https://your-domain.com/api/auth/callback/github
   ```

#### Google OAuth
1. Go to Google Cloud Console → APIs & Services → Credentials
2. Edit your OAuth 2.0 Client ID
3. Update "Authorized redirect URIs" to:
   ```
   https://your-domain.com/api/auth/callback/google
   ```

## Verification Checklist

After domain setup, verify:

- [ ] Domain resolves to your application
- [ ] SSL certificate is valid and trusted
- [ ] HTTPS redirect works (http → https)
- [ ] WWW redirect works (if configured)
- [ ] Authentication flows work with new domain
- [ ] All OAuth providers updated with new callback URLs
- [ ] Environment variables updated in hosting platform
- [ ] Application loads and functions correctly

## Troubleshooting

### Common Issues

1. **Domain not resolving**
   - Check DNS propagation (use tools like `dig` or online DNS checkers)
   - Verify DNS records are correct
   - Wait for DNS propagation (up to 48 hours)

2. **SSL certificate not working**
   - Wait for certificate provisioning (can take up to 24 hours)
   - Check domain verification in hosting platform
   - Ensure DNS records are correct

3. **Authentication not working**
   - Verify OAuth callback URLs are updated
   - Check `NEXT_PUBLIC_APP_URL` environment variable
   - Ensure `BETTER_AUTH_SECRET` is set correctly

4. **Mixed content errors**
   - Ensure all resources load over HTTPS
   - Check for hardcoded HTTP URLs in code
   - Verify external APIs support HTTPS

### DNS Propagation Check

Check if DNS changes have propagated:

```bash
# Check A record
dig your-domain.com A

# Check CNAME record
dig www.your-domain.com CNAME

# Check from different locations
nslookup your-domain.com *******
```

### SSL Certificate Check

Verify SSL certificate:

```bash
# Check certificate details
openssl s_client -connect your-domain.com:443 -servername your-domain.com

# Check certificate expiration
echo | openssl s_client -connect your-domain.com:443 2>/dev/null | openssl x509 -noout -dates
```

## Performance Optimization

### CDN Configuration

Both Netlify and Vercel provide global CDN:

- **Automatic**: Static assets are automatically cached
- **Edge locations**: Content served from nearest location
- **Cache headers**: Configure in `next.config.js`

### DNS Optimization

For better performance:

1. **Use short TTL during setup** (300 seconds)
2. **Increase TTL after stable** (3600 seconds)
3. **Consider Cloudflare** for additional DNS optimization

## Security Considerations

1. **HSTS Headers**: Automatically configured by hosting platforms
2. **Certificate Transparency**: Certificates are logged in CT logs
3. **Domain Validation**: Ensure you control the domain
4. **Subdomain Security**: Consider wildcard certificates if using subdomains

## Next Steps

After domain and SSL setup:

1. **Update documentation** with new domain
2. **Test all functionality** on production domain
3. **Set up monitoring** for domain and SSL
4. **Configure analytics** with new domain
5. **Update any external integrations** with new domain
