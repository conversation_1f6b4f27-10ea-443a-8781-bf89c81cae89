[images]
  remote_images = ["https://source.unsplash.com/.*", "https://images.unsplash.com/.*", "https://ext.same-assets.com/.*", "https://ugc.same-assets.com/.*"]

[build]
  command = "bun run build"
  publish = ".next"

[build.environment]
  NETLIFY_NEXT_PLUGIN_SKIP = "true"
  NODE_VERSION = "18"
  BUN_VERSION = "1.0.25"

# Production build context
[context.production]
  command = "bun run build"

[context.production.environment]
  NODE_ENV = "production"

# Redirects for production
[[redirects]]
  from = "/api/auth/*"
  to = "/api/auth/[...all]"
  status = 200

# Force HTTPS redirect
[[redirects]]
  from = "http://your-domain.com/*"
  to = "https://your-domain.com/:splat"
  status = 301
  force = true

# WWW to non-WWW redirect (uncomment and update domain)
# [[redirects]]
#   from = "https://www.your-domain.com/*"
#   to = "https://your-domain.com/:splat"
#   status = 301
#   force = true

# Headers for security
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=()"

[[plugins]]
  package = "@netlify/plugin-nextjs"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
