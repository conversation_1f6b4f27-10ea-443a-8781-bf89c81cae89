# OAuth Providers Setup Guide

This guide walks you through setting up OAuth providers for production authentication with Better Auth and Convex.

## Overview

OAuth providers allow users to sign in using their existing accounts from services like GitHub, Google, Discord, etc. This improves user experience and security.

## Supported Providers

Better Auth supports many OAuth providers out of the box:
- GitHub
- Google
- Discord
- Microsoft
- Apple
- Facebook
- Twitter/X
- LinkedIn
- And many more...

## GitHub OAuth Setup

### Step 1: Create GitHub OAuth App

1. **Go to GitHub Developer Settings**
   - Navigate to [GitHub Developer Settings](https://github.com/settings/developers)
   - Click "OAuth Apps" in the left sidebar
   - Click "New OAuth App"

2. **Configure OAuth App**
   ```
   Application name: ALIAS HQ
   Homepage URL: https://your-domain.com
   Application description: AI-powered project management dashboard
   Authorization callback URL: https://your-domain.com/api/auth/callback/github
   ```

3. **For Development**
   ```
   Application name: ALIAS HQ (Development)
   Homepage URL: http://localhost:3007
   Authorization callback URL: http://localhost:3007/api/auth/callback/github
   ```

4. **Get Credentials**
   - After creating the app, copy the "Client ID"
   - Generate a new "Client Secret" and copy it
   - Add these to your environment variables

### Step 2: Environment Configuration

Add to your `.env.local` (development):
```bash
GITHUB_CLIENT_ID=your_github_client_id_here
GITHUB_CLIENT_SECRET=your_github_client_secret_here
```

Add to your `.env.production` (production):
```bash
GITHUB_CLIENT_ID=your_production_github_client_id
GITHUB_CLIENT_SECRET=your_production_github_client_secret
```

### Step 3: Update Auth Configuration

The GitHub provider is already configured in `src/lib/auth.ts`. Make sure the environment variables are set correctly.

## Google OAuth Setup

### Step 1: Create Google Cloud Project

1. **Go to Google Cloud Console**
   - Navigate to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select an existing one

2. **Enable Google+ API**
   - Go to "APIs & Services" > "Library"
   - Search for "Google+ API" and enable it

3. **Configure OAuth Consent Screen**
   - Go to "APIs & Services" > "OAuth consent screen"
   - Choose "External" user type
   - Fill in required information:
     ```
     App name: ALIAS HQ
     User support email: <EMAIL>
     Developer contact information: <EMAIL>
     ```

### Step 2: Create OAuth Credentials

1. **Create Credentials**
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth 2.0 Client IDs"
   - Choose "Web application"

2. **Configure OAuth Client**
   ```
   Name: ALIAS HQ Web Client
   Authorized JavaScript origins:
     - https://your-domain.com
     - http://localhost:3007 (for development)
   
   Authorized redirect URIs:
     - https://your-domain.com/api/auth/callback/google
     - http://localhost:3007/api/auth/callback/google (for development)
   ```

3. **Get Credentials**
   - Copy the "Client ID" and "Client Secret"

### Step 3: Environment Configuration

Add to your environment files:
```bash
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
```

### Step 4: Enable Google Provider

Uncomment the Google provider in `src/lib/auth.ts`:
```typescript
socialProviders: {
  github: {
    clientId: process.env.GITHUB_CLIENT_ID!,
    clientSecret: process.env.GITHUB_CLIENT_SECRET!,
  },
  google: {
    clientId: process.env.GOOGLE_CLIENT_ID!,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
  },
},
```

## Discord OAuth Setup

### Step 1: Create Discord Application

1. **Go to Discord Developer Portal**
   - Navigate to [Discord Developer Portal](https://discord.com/developers/applications)
   - Click "New Application"
   - Enter application name: "ALIAS HQ"

2. **Configure OAuth2**
   - Go to "OAuth2" > "General"
   - Add redirect URLs:
     ```
     https://your-domain.com/api/auth/callback/discord
     http://localhost:3007/api/auth/callback/discord (for development)
     ```

3. **Get Credentials**
   - Copy "Client ID" and "Client Secret" from the General tab

### Step 2: Add Discord Provider

Add to `src/lib/auth.ts`:
```typescript
socialProviders: {
  // ... existing providers
  discord: {
    clientId: process.env.DISCORD_CLIENT_ID!,
    clientSecret: process.env.DISCORD_CLIENT_SECRET!,
  },
},
```

## Security Best Practices

### 1. Environment Variable Security

- **Never commit secrets to version control**
- Use different OAuth apps for development and production
- Rotate secrets regularly
- Use environment-specific callback URLs

### 2. OAuth App Configuration

- **Restrict callback URLs** to only your domains
- **Use HTTPS** in production callback URLs
- **Set appropriate scopes** (minimal required permissions)
- **Monitor OAuth app usage** in provider dashboards

### 3. User Data Handling

- **Respect user privacy** and data protection laws
- **Only request necessary scopes**
- **Provide clear privacy policy**
- **Allow users to revoke access**

## Testing OAuth Integration

### Development Testing

1. **Start development server**
   ```bash
   bun dev
   ```

2. **Test OAuth flow**
   - Navigate to your app
   - Click "Sign in with GitHub/Google"
   - Complete OAuth flow
   - Verify user is created in database

### Production Testing

1. **Deploy to staging environment**
2. **Test with production OAuth apps**
3. **Verify callback URLs work correctly**
4. **Test user creation and session management**

## Troubleshooting

### Common Issues

1. **"Invalid redirect URI" error**
   - Check callback URL matches exactly in OAuth app settings
   - Ensure protocol (http/https) matches
   - Verify domain spelling

2. **"Invalid client" error**
   - Check client ID and secret are correct
   - Verify environment variables are loaded
   - Ensure OAuth app is active

3. **"Access denied" error**
   - Check OAuth app permissions
   - Verify user has access to the OAuth app
   - Check if app is in development mode with restricted access

### Debug Steps

1. **Check environment variables**
   ```bash
   echo $GITHUB_CLIENT_ID
   echo $GOOGLE_CLIENT_ID
   ```

2. **Verify OAuth app configuration**
   - Double-check callback URLs
   - Ensure app is published (for Google)
   - Check app permissions

3. **Monitor network requests**
   - Use browser dev tools
   - Check for CORS errors
   - Verify redirect flows

## Adding More Providers

To add additional OAuth providers:

1. **Check Better Auth documentation** for supported providers
2. **Create OAuth app** with the provider
3. **Add environment variables**
4. **Update auth configuration**
5. **Test the integration**

Example for adding Microsoft:
```typescript
socialProviders: {
  // ... existing providers
  microsoft: {
    clientId: process.env.MICROSOFT_CLIENT_ID!,
    clientSecret: process.env.MICROSOFT_CLIENT_SECRET!,
  },
},
```

## Production Checklist

- [ ] OAuth apps created for production domains
- [ ] Environment variables set in hosting platform
- [ ] Callback URLs configured correctly
- [ ] HTTPS enabled for all callback URLs
- [ ] OAuth apps published/approved (if required)
- [ ] User consent screens configured
- [ ] Privacy policy and terms of service linked
- [ ] OAuth integration tested end-to-end
- [ ] Error handling implemented
- [ ] User data handling complies with regulations
