"use client";

import { MainLayout } from "@/components/layout/MainLayout";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, Card<PERSON>itle, CardDescription, CardFooter } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  Database, Search, Plus, FileText, Book, Code, Share2,
  Bookmark, Eye, Clock, Tag, BarChart2, MoveUp, Filter,
  RefreshCw, Download, EyeOff, Brain
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";

// Mock knowledge base articles
const knowledgeArticles = [
  {
    id: 1,
    title: "Getting Started with ALIAS MOSAIC Framework",
    description: "Learn the basics of the ALIAS MOSAIC framework and how to set up your first project.",
    category: "Documentation",
    tags: ["beginner", "setup", "overview"],
    author: "ALIAS Team",
    dateUpdated: "May 15, 2025",
    views: 1243,
    type: "guide"
  },
  {
    id: 2,
    title: "Ontology-Driven Development Principles",
    description: "Deep dive into how ontology drives the development process in MOSAIC projects.",
    category: "Best Practices",
    tags: ["ontology", "methodology", "advanced"],
    author: "Dr. Sarah Chen",
    dateUpdated: "May 12, 2025",
    views: 892,
    type: "article"
  },
  {
    id: 3,
    title: "Setting Up AI Agents for Project Requirements",
    description: "How to configure and deploy AI agents to capture and process project requirements.",
    category: "Tutorials",
    tags: ["ai", "agents", "requirements"],
    author: "Michael Rodriguez",
    dateUpdated: "May 10, 2025",
    views: 756,
    type: "tutorial"
  },
  {
    id: 4,
    title: "Semantic Layer Design Patterns",
    description: "Best practices for designing the semantic layer of your ontology for different domains.",
    category: "Best Practices",
    tags: ["semantic", "ontology", "design"],
    author: "Lisa Johnson",
    dateUpdated: "May 8, 2025",
    views: 624,
    type: "article"
  },
  {
    id: 5,
    title: "MOSAIC CI/CD Pipeline Configuration",
    description: "Set up continuous integration and deployment pipelines optimized for MOSAIC projects.",
    category: "Tutorials",
    tags: ["devops", "ci-cd", "automation"],
    author: "David Park",
    dateUpdated: "May 5, 2025",
    views: 583,
    type: "tutorial"
  },
  {
    id: 6,
    title: "Agent-Driven Code Generation Best Practices",
    description: "Guidelines for effective use of AI agents in generating and reviewing code.",
    category: "Best Practices",
    tags: ["ai", "code-generation", "quality"],
    author: "Alex Mercer",
    dateUpdated: "May 1, 2025",
    views: 921,
    type: "guide"
  },
  {
    id: 7,
    title: "ALIAS MOSAIC Architecture Overview",
    description: "Technical architecture of the MOSAIC framework and its core components.",
    category: "Documentation",
    tags: ["architecture", "technical", "advanced"],
    author: "ALIAS Team",
    dateUpdated: "April 28, 2025",
    views: 1105,
    type: "technical"
  },
  {
    id: 8,
    title: "Integrating External APIs with Ontology Models",
    description: "How to connect external APIs with your ontology-driven data models.",
    category: "Tutorials",
    tags: ["integration", "api", "data-models"],
    author: "Samantha Wu",
    dateUpdated: "April 25, 2025",
    views: 674,
    type: "tutorial"
  }
];

// Mock categories with counts
const categories = [
  { name: "Documentation", count: 15 },
  { name: "Tutorials", count: 23 },
  { name: "Best Practices", count: 18 },
  { name: "Case Studies", count: 7 },
  { name: "API Reference", count: 12 },
  { name: "Release Notes", count: 6 }
];

// Mock popular tags
const popularTags = [
  { name: "ontology", count: 42 },
  { name: "ai", count: 38 },
  { name: "agents", count: 25 },
  { name: "integration", count: 19 },
  { name: "architecture", count: 16 },
  { name: "best-practices", count: 15 },
  { name: "automation", count: 14 },
  { name: "tutorial", count: 13 }
];

export default function KnowledgeBasePage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all");

  // Filter articles based on search query and active tab
  const filteredArticles = knowledgeArticles.filter(article => {
    const matchesSearch = searchQuery === "" ||
      article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesTab = activeTab === "all" ||
      (activeTab === "docs" && article.category === "Documentation") ||
      (activeTab === "tutorials" && article.category === "Tutorials") ||
      (activeTab === "best-practices" && article.category === "Best Practices");

    return matchesSearch && matchesTab;
  });

  // Get icon for article type
  const getArticleTypeIcon = (type: string) => {
    switch (type) {
      case "guide": return <Book className="h-4 w-4" />;
      case "tutorial": return <Code className="h-4 w-4" />;
      case "article": return <FileText className="h-4 w-4" />;
      case "technical": return <Database className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  return (
    <MainLayout>
      <div className="p-4 md:p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-normal mb-1">Knowledge Base</h1>
            <p className="text-muted-foreground">
              Explore documentation, tutorials, and best practices for ALIAS MOSAIC
            </p>
          </div>

          <div className="flex items-center space-x-2">
            <div className="relative">
              <Input
                className="pl-8 bg-background border-muted w-[250px] md:w-[350px]"
                placeholder="Search knowledge base..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <Search className="w-4 h-4 absolute left-2 top-3 text-muted-foreground" />
            </div>
            <Button variant="outline" className="border-muted">
              <Filter className="h-4 w-4 mr-1" />
              Filters
            </Button>
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-6">
          {/* Sidebar */}
          <div className="w-full md:w-64 lg:w-72 space-y-6 flex-shrink-0">
            <Card className="bg-card border-border">
              <CardHeader className="pb-2">
                <CardTitle className="text-base font-normal flex items-center">
                  <FileText className="h-5 w-5 mr-2 text-primary" />
                  Categories
                </CardTitle>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="space-y-1">
                  <Button
                    variant={activeTab === "all" ? "default" : "ghost"}
                    className="w-full justify-start text-sm h-9 px-2"
                    onClick={() => setActiveTab("all")}
                  >
                    All Articles
                    <span className="ml-auto text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full">
                      {knowledgeArticles.length}
                    </span>
                  </Button>

                  {categories.map((category) => (
                    <Button
                      key={category.name}
                      variant={activeTab === category.name.toLowerCase().replace(' ', '-') ? "default" : "ghost"}
                      className="w-full justify-start text-sm h-9 px-2"
                      onClick={() => setActiveTab(category.name.toLowerCase().replace(' ', '-'))}
                    >
                      {category.name}
                      <span className="ml-auto text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full">
                        {category.count}
                      </span>
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="bg-card border-border">
              <CardHeader className="pb-2">
                <CardTitle className="text-base font-normal flex items-center">
                  <Tag className="h-5 w-5 mr-2 text-primary" />
                  Popular Tags
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {popularTags.map((tag) => (
                    <button
                      key={tag.name}
                      className="px-2 py-1 bg-secondary hover:bg-secondary/80 rounded-full text-xs flex items-center"
                      onClick={() => setSearchQuery(tag.name)}
                    >
                      {tag.name}
                      <span className="ml-1 text-muted-foreground">({tag.count})</span>
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="bg-card border-border">
              <CardHeader className="pb-2">
                <CardTitle className="text-base font-normal flex items-center">
                  <MoveUp className="h-5 w-5 mr-2 text-primary" />
                  Quick Links
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <Link href="#" className="flex items-center hover:text-primary transition-colors">
                    <Brain className="h-4 w-4 mr-2 text-muted-foreground" />
                    AI Agent Documentation
                  </Link>
                  <Link href="#" className="flex items-center hover:text-primary transition-colors">
                    <Database className="h-4 w-4 mr-2 text-muted-foreground" />
                    Ontology Design Guide
                  </Link>
                  <Link href="#" className="flex items-center hover:text-primary transition-colors">
                    <RefreshCw className="h-4 w-4 mr-2 text-muted-foreground" />
                    Latest Updates
                  </Link>
                  <Link href="#" className="flex items-center hover:text-primary transition-colors">
                    <Download className="h-4 w-4 mr-2 text-muted-foreground" />
                    Download Resources
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main content */}
          <div className="flex-1">
            <Card className="bg-card border-border">
              <CardHeader className="pb-2 flex flex-row items-center justify-between">
                <CardTitle className="text-base font-normal">
                  {activeTab === "all" ? "All Articles" :
                   activeTab === "docs" ? "Documentation" :
                   activeTab === "tutorials" ? "Tutorials" :
                   activeTab === "best-practices" ? "Best Practices" :
                   "Knowledge Articles"}
                </CardTitle>
                <div className="flex items-center gap-2">
                  <Button variant="ghost" size="sm" className="h-8 text-xs">
                    <Eye className="h-4 w-4 mr-1" />
                    Most Viewed
                  </Button>
                  <Button variant="ghost" size="sm" className="h-8 text-xs">
                    <Clock className="h-4 w-4 mr-1" />
                    Recent
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {filteredArticles.map((article) => (
                    <Card key={article.id} className="bg-background hover:border-primary/50 transition-colors">
                      <CardContent className="p-4">
                        <div className="flex items-start space-x-2">
                          <div className={`p-2 rounded-md
                            ${article.type === "guide" ? "bg-primary/10 text-primary" :
                             article.type === "tutorial" ? "bg-chart-2/10 text-chart-2" :
                             article.type === "article" ? "bg-chart-3/10 text-chart-3" :
                             "bg-chart-4/10 text-chart-4"}`}
                          >
                            {getArticleTypeIcon(article.type)}
                          </div>

                          <div className="flex-1">
                            <h3 className="font-medium mb-1">{article.title}</h3>
                            <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
                              {article.description}
                            </p>

                            <div className="flex flex-wrap gap-1 mb-2">
                              {article.tags.map((tag) => (
                                <span key={tag} className="text-xs px-1.5 py-0.5 bg-secondary rounded-sm">
                                  {tag}
                                </span>
                              ))}
                            </div>

                            <div className="flex items-center justify-between text-xs text-muted-foreground">
                              <div className="flex items-center">
                                <span>{article.author}</span>
                                <span className="mx-2">•</span>
                                <span>{article.dateUpdated}</span>
                              </div>
                              <div className="flex items-center">
                                <Eye className="h-3 w-3 mr-1" />
                                {article.views}
                              </div>
                            </div>
                          </div>

                          <Button variant="ghost" size="icon" className="h-8 w-8 flex-shrink-0">
                            <Bookmark className="h-4 w-4" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {filteredArticles.length === 0 && (
                  <div className="flex flex-col items-center justify-center py-12">
                    <EyeOff className="h-12 w-12 text-muted-foreground opacity-20 mb-4" />
                    <h3 className="text-lg font-medium mb-1">No articles found</h3>
                    <p className="text-muted-foreground text-center max-w-md">
                      No articles match your search criteria. Try adjusting your search terms or filters.
                    </p>
                    <Button className="mt-4" onClick={() => {
                      setSearchQuery("");
                      setActiveTab("all");
                    }}>
                      Reset Filters
                    </Button>
                  </div>
                )}
              </CardContent>
              <CardFooter className="flex justify-center border-t border-border py-4">
                <Button variant="outline" className="border-muted">
                  Load More Articles
                </Button>
              </CardFooter>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
