import { MainLayout } from "@/components/layout/MainLayout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";

export default function GenerateDataPage() {
  return (
    <MainLayout>
      <div className="p-6 flex items-center justify-center min-h-[calc(100vh-200px)]">
        <Card className="max-w-lg w-full bg-card border-border">
          <CardHeader className="border-b border-border">
            <CardTitle className="text-lg font-normal">Generate Data</CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <p className="text-muted-foreground mb-6">
              Generate data for the Dria decentralized AI inference network.
              This feature allows you to test the network with your own data.
            </p>

            <div className="flex items-center justify-between">
              <Link href="/">
                <Button variant="outline" className="border-muted">
                  Back to Dashboard
                </Button>
              </Link>

              <Button className="bg-primary text-black hover:bg-primary/90">
                Generate Data
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
