"use client";

import { MainLayout } from "@/components/layout/MainLayout";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { AIAssistPanel } from "@/components/dashboard/AIAssistPanel";
import {
  RocketIcon, ArrowLeft, ExternalLink, Server, Database,
  Globe, CheckCircle, XCircle, Clock, RefreshCw, AlertTriangle,
  Terminal, FileJson, Workflow, ChevronDown, ChevronUp, PanelLeft, Plus
} from "lucide-react";
import { useState, useEffect } from "react";
import Link from "next/link";

// Types for deployment
interface Deployment {
  id: string;
  projectId: string;
  projectName: string;
  environment: "development" | "staging" | "production";
  status: "queued" | "in-progress" | "success" | "failed";
  startedAt: Date;
  finishedAt?: Date;
  deployedBy: string;
  version: string;
  commitHash: string;
  url?: string;
  logs?: string[];
}

interface Project {
  id: string;
  name: string;
  environments: {
    development: boolean;
    staging: boolean;
    production: boolean;
  };
  branches: string[];
  lastDeployment?: {
    environment: "development" | "staging" | "production";
    status: "success" | "failed";
    timestamp: Date;
  };
}

// Mock data for projects
const mockProjects: Project[] = [
  {
    id: "project-1",
    name: "Enterprise CRM Portal",
    environments: {
      development: true,
      staging: true,
      production: true
    },
    branches: ["main", "develop", "feature/auth-integration", "bugfix/dashboard-layout"],
    lastDeployment: {
      environment: "staging",
      status: "success",
      timestamp: new Date(Date.now() - 3600000)
    }
  },
  {
    id: "project-2",
    name: "Healthcare Analytics Platform",
    environments: {
      development: true,
      staging: true,
      production: false
    },
    branches: ["main", "develop", "feature/patient-dashboard"],
    lastDeployment: {
      environment: "development",
      status: "success",
      timestamp: new Date(Date.now() - 7200000)
    }
  },
  {
    id: "project-3",
    name: "Financial Services Dashboard",
    environments: {
      development: true,
      staging: false,
      production: false
    },
    branches: ["main", "develop"],
    lastDeployment: {
      environment: "development",
      status: "failed",
      timestamp: new Date(Date.now() - 86400000)
    }
  }
];

// Mock data for recent deployments
const mockDeployments: Deployment[] = [
  {
    id: "dep-1",
    projectId: "project-1",
    projectName: "Enterprise CRM Portal",
    environment: "staging",
    status: "success",
    startedAt: new Date(Date.now() - 3600000),
    finishedAt: new Date(Date.now() - 3550000),
    deployedBy: "Sarah Chen",
    version: "v2.3.0",
    commitHash: "a8e5d1c",
    url: "https://staging.crm-portal.alias-mosaic.com",
    logs: [
      "1:20:30 PM - Starting deployment...",
      "1:20:35 PM - Initializing build environment",
      "1:20:40 PM - Installing dependencies",
      "1:21:15 PM - Running build script",
      "1:21:40 PM - Running database migrations",
      "1:21:50 PM - Deploying frontend assets",
      "1:21:55 PM - Deployment complete",
      "1:21:56 PM - Application is live at https://staging.crm-portal.alias-mosaic.com"
    ]
  },
  {
    id: "dep-2",
    projectId: "project-2",
    projectName: "Healthcare Analytics Platform",
    environment: "development",
    status: "success",
    startedAt: new Date(Date.now() - 7200000),
    finishedAt: new Date(Date.now() - 7150000),
    deployedBy: "Michael Rodriguez",
    version: "v1.4.2",
    commitHash: "b2d7e3f",
    url: "https://dev.health-analytics.alias-mosaic.com",
    logs: [
      "11:45:30 AM - Starting deployment...",
      "11:45:35 AM - Initializing build environment",
      "11:45:40 AM - Installing dependencies",
      "11:46:15 AM - Running build script",
      "11:46:40 AM - Running database migrations",
      "11:46:50 AM - Deploying frontend assets",
      "11:46:55 AM - Deployment complete",
      "11:46:56 AM - Application is live at https://dev.health-analytics.alias-mosaic.com"
    ]
  },
  {
    id: "dep-3",
    projectId: "project-3",
    projectName: "Financial Services Dashboard",
    environment: "development",
    status: "failed",
    startedAt: new Date(Date.now() - 86400000),
    finishedAt: new Date(Date.now() - 86380000),
    deployedBy: "Alex Mercer",
    version: "v0.9.0",
    commitHash: "c3f8a2d",
    logs: [
      "8:30:00 AM - Starting deployment...",
      "8:30:05 AM - Initializing build environment",
      "8:30:10 AM - Installing dependencies",
      "8:30:45 AM - Running build script",
      "8:30:50 AM - ERROR: Build failed - TypeScript errors",
      "8:30:51 AM - ERROR: /src/components/dashboard/ChartPanel.tsx:125:10 - Property 'data' is missing in type '{ labels: string[]; }' but required in type 'ChartProps'",
      "8:30:52 AM - Deployment aborted due to build failures"
    ]
  },
  {
    id: "dep-4",
    projectId: "project-1",
    projectName: "Enterprise CRM Portal",
    environment: "development",
    status: "in-progress",
    startedAt: new Date(Date.now() - 300000),
    deployedBy: "Lisa Johnson",
    version: "v2.3.1",
    commitHash: "d4e9b2a",
    logs: [
      "3:20:00 PM - Starting deployment...",
      "3:20:05 PM - Initializing build environment",
      "3:20:10 PM - Installing dependencies",
      "3:20:45 PM - Running build script",
      "3:21:15 PM - Running database migrations...",
    ]
  },
  {
    id: "dep-5",
    projectId: "project-1",
    projectName: "Enterprise CRM Portal",
    environment: "production",
    status: "queued",
    startedAt: new Date(Date.now() - 60000),
    deployedBy: "Sarah Chen",
    version: "v2.2.0",
    commitHash: "e5f1c2b",
    logs: [
      "3:29:00 PM - Deployment added to queue",
      "3:29:01 PM - Waiting for available deployment slot..."
    ]
  }
];

// Helper function to format dates
function formatDate(date: Date): string {
  return date.toLocaleString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

// Helper function to get status badge
function getStatusBadge(status: string) {
  switch (status) {
    case "success":
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
          <CheckCircle className="h-3 w-3 mr-1" />
          Success
        </span>
      );
    case "failed":
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400">
          <XCircle className="h-3 w-3 mr-1" />
          Failed
        </span>
      );
    case "in-progress":
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
          <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
          In Progress
        </span>
      );
    case "queued":
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400">
          <Clock className="h-3 w-3 mr-1" />
          Queued
        </span>
      );
    default:
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-400">
          {status}
        </span>
      );
  }
}

// Helper function to get environment badge
function getEnvironmentBadge(environment: string) {
  switch (environment) {
    case "development":
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400">
          <Server className="h-3 w-3 mr-1" />
          Development
        </span>
      );
    case "staging":
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
          <Database className="h-3 w-3 mr-1" />
          Staging
        </span>
      );
    case "production":
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
          <Globe className="h-3 w-3 mr-1" />
          Production
        </span>
      );
    default:
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-400">
          {environment}
        </span>
      );
  }
}

export default function DeployPage() {
  const [showAssistant, setShowAssistant] = useState(false);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [selectedEnvironment, setSelectedEnvironment] = useState<string>("development");
  const [selectedBranch, setSelectedBranch] = useState<string>("");
  const [deploying, setDeploying] = useState(false);
  const [selectedDeployment, setSelectedDeployment] = useState<Deployment | null>(null);
  const [expandedLogDeployment, setExpandedLogDeployment] = useState<string | null>(null);

  // Set default project on first load
  useEffect(() => {
    if (mockProjects.length > 0 && !selectedProject) {
      setSelectedProject(mockProjects[0]);
      setSelectedBranch(mockProjects[0].branches[0]);
    }
  }, [selectedProject]);

  // Handle project change
  const handleProjectChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const projectId = event.target.value;
    const project = mockProjects.find(p => p.id === projectId) || null;
    setSelectedProject(project);
    if (project && project.branches.length > 0) {
      setSelectedBranch(project.branches[0]);
    }
  };

  // Handle deployment
  const handleDeploy = () => {
    if (!selectedProject) return;

    setDeploying(true);

    // Simulate deployment process
    setTimeout(() => {
      setDeploying(false);
      // Show success message or redirect to deployment details
      alert(`Deployed ${selectedProject.name} to ${selectedEnvironment} environment successfully!`);
    }, 3000);
  };

  // Filter deployments by selected project
  const filteredDeployments = selectedProject
    ? mockDeployments.filter(d => d.projectId === selectedProject.id)
    : mockDeployments;

  // Toggle deployment logs
  const toggleDeploymentLogs = (deploymentId: string) => {
    if (expandedLogDeployment === deploymentId) {
      setExpandedLogDeployment(null);
    } else {
      setExpandedLogDeployment(deploymentId);
    }
  };

  return (
    <MainLayout>
      <div className="p-4 md:p-6">
        <div className="flex flex-col lg:flex-row justify-between items-start mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-normal flex items-center">
              <RocketIcon className="h-6 w-6 mr-2 text-primary" />
              Project Deployment
            </h1>
            <p className="text-muted-foreground max-w-3xl">
              Deploy your MOSAIC projects to various environments. Configure build settings,
              monitor deployments, and manage your infrastructure.
            </p>
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAssistant(!showAssistant)}
          >
            <PanelLeft className="h-4 w-4 mr-1" />
            {showAssistant ? "Hide Assistant" : "Show Assistant"}
          </Button>
        </div>

        <div className="flex flex-col lg:flex-row gap-6">
          <div className={`flex-1 ${showAssistant ? "lg:w-2/3" : "w-full"}`}>
            <Tabs defaultValue="deploy" className="space-y-6">
              <TabsList>
                <TabsTrigger value="deploy">Deploy</TabsTrigger>
                <TabsTrigger value="history">Deployment History</TabsTrigger>
                <TabsTrigger value="settings">Environment Settings</TabsTrigger>
              </TabsList>

              <TabsContent value="deploy" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Deploy Project</CardTitle>
                    <CardDescription>
                      Select a project and environment to deploy your application
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Project</label>
                        <select
                          className="w-full bg-background border border-border rounded p-2"
                          value={selectedProject?.id || ""}
                          onChange={handleProjectChange}
                        >
                          {mockProjects.map(project => (
                            <option key={project.id} value={project.id}>{project.name}</option>
                          ))}
                        </select>
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">Environment</label>
                        <select
                          className="w-full bg-background border border-border rounded p-2"
                          value={selectedEnvironment}
                          onChange={(e) => setSelectedEnvironment(e.target.value)}
                        >
                          <option value="development">Development</option>
                          <option value="staging">Staging</option>
                          <option value="production">Production</option>
                        </select>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Branch</label>
                        <select
                          className="w-full bg-background border border-border rounded p-2"
                          value={selectedBranch}
                          onChange={(e) => setSelectedBranch(e.target.value)}
                        >
                          {selectedProject?.branches.map(branch => (
                            <option key={branch} value={branch}>{branch}</option>
                          ))}
                        </select>
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">Build Command</label>
                        <Input
                          placeholder="npm run build"
                          defaultValue="npm run build"
                          className="bg-background"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Environment Variables</label>
                      <div className="border border-border rounded-md p-4 bg-background">
                        <div className="grid grid-cols-2 gap-2 mb-2">
                          <div className="font-semibold text-sm">Key</div>
                          <div className="font-semibold text-sm">Value</div>
                        </div>
                        <div className="grid grid-cols-2 gap-2 mb-2">
                          <Input placeholder="DATABASE_URL" defaultValue="DATABASE_URL" className="text-sm" />
                          <Input placeholder="postgres://..." defaultValue="postgres://user:password@localhost:5432/db" className="text-sm" />
                        </div>
                        <div className="grid grid-cols-2 gap-2 mb-2">
                          <Input placeholder="API_KEY" defaultValue="API_KEY" className="text-sm" />
                          <Input placeholder="..." defaultValue="sk_test_***************" className="text-sm" />
                        </div>
                        <div className="grid grid-cols-2 gap-2 mb-4">
                          <Input placeholder="NODE_ENV" defaultValue="NODE_ENV" className="text-sm" />
                          <Input placeholder="..." defaultValue="production" className="text-sm" />
                        </div>
                        <Button variant="outline" size="sm">
                          <Plus className="h-4 w-4 mr-1" />
                          Add Variable
                        </Button>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Advanced Options</label>
                      <div className="border border-border rounded-md divide-y dark:divide-border">
                        <div className="flex items-center justify-between p-3">
                          <div>
                            <div className="font-medium">Run Database Migrations</div>
                            <div className="text-sm text-muted-foreground">Automatically run migrations before deployment</div>
                          </div>
                          <input type="checkbox" className="rounded border-border bg-background h-4 w-4" defaultChecked />
                        </div>
                        <div className="flex items-center justify-between p-3">
                          <div>
                            <div className="font-medium">Cache Dependencies</div>
                            <div className="text-sm text-muted-foreground">Cache node_modules between deployments</div>
                          </div>
                          <input type="checkbox" className="rounded border-border bg-background h-4 w-4" defaultChecked />
                        </div>
                        <div className="flex items-center justify-between p-3">
                          <div>
                            <div className="font-medium">Zero Downtime Deploy</div>
                            <div className="text-sm text-muted-foreground">Deploy without interrupting users</div>
                          </div>
                          <input type="checkbox" className="rounded border-border bg-background h-4 w-4" defaultChecked />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between border-t border-border p-4">
                    <div className="flex items-center text-sm text-muted-foreground">
                      <AlertTriangle className="h-4 w-4 mr-1 text-yellow-500" />
                      {selectedEnvironment === "production" ?
                        "Deploying to production will affect live users." :
                        "This will deploy to a testing environment."}
                    </div>
                    <Button
                      onClick={handleDeploy}
                      disabled={deploying || !selectedProject}
                      className={selectedEnvironment === "production" ? "bg-red-600 hover:bg-red-700" : ""}
                    >
                      {deploying ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-1 animate-spin" />
                          Deploying...
                        </>
                      ) : (
                        <>
                          <RocketIcon className="h-4 w-4 mr-1" />
                          Deploy to {selectedEnvironment}
                        </>
                      )}
                    </Button>
                  </CardFooter>
                </Card>

                {/* Quick deployment history */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base font-normal">Recent Deployments</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {filteredDeployments.slice(0, 3).map(deployment => (
                        <div key={deployment.id} className="border border-border rounded-md p-3">
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <div className="font-medium">{deployment.projectName}</div>
                              <div className="text-sm text-muted-foreground">
                                {deployment.version} ({deployment.commitHash})
                              </div>
                            </div>
                            <div className="flex space-x-2">
                              {getEnvironmentBadge(deployment.environment)}
                              {getStatusBadge(deployment.status)}
                            </div>
                          </div>
                          <div className="flex justify-between items-center text-sm">
                            <div>
                              <span className="text-muted-foreground">Deployed by:</span> {deployment.deployedBy}
                            </div>
                            <div className="text-muted-foreground">
                              {formatDate(deployment.startedAt)}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="history">
                <Card>
                  <CardHeader>
                    <CardTitle>Deployment History</CardTitle>
                    <CardDescription>
                      View the history of all deployments across your environments
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      <div className="flex flex-col border border-border rounded-md divide-y dark:divide-border">
                        {mockDeployments.map(deployment => (
                          <div key={deployment.id} className="p-4">
                            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-2">
                              <div>
                                <div className="font-medium flex items-center">
                                  {deployment.projectName}
                                  {deployment.url && (
                                    <a href={deployment.url} target="_blank" rel="noopener noreferrer" className="ml-2 text-primary hover:text-primary/80">
                                      <ExternalLink className="h-4 w-4" />
                                    </a>
                                  )}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  {deployment.version} ({deployment.commitHash})
                                </div>
                              </div>
                              <div className="flex flex-wrap gap-2">
                                {getEnvironmentBadge(deployment.environment)}
                                {getStatusBadge(deployment.status)}
                              </div>
                            </div>

                            <div className="flex flex-col md:flex-row md:items-center justify-between text-sm mb-2">
                              <div>
                                <span className="text-muted-foreground">Deployed by:</span> {deployment.deployedBy}
                              </div>
                              <div className="text-muted-foreground">
                                Started: {formatDate(deployment.startedAt)}
                                {deployment.finishedAt && ` • Finished: ${formatDate(deployment.finishedAt)}`}
                              </div>
                            </div>

                            {deployment.logs && (
                              <div>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="text-primary p-0 h-auto"
                                  onClick={() => toggleDeploymentLogs(deployment.id)}
                                >
                                  {expandedLogDeployment === deployment.id ? (
                                    <ChevronUp className="h-4 w-4 mr-1" />
                                  ) : (
                                    <ChevronDown className="h-4 w-4 mr-1" />
                                  )}
                                  {expandedLogDeployment === deployment.id ? "Hide logs" : "Show logs"}
                                </Button>

                                {expandedLogDeployment === deployment.id && (
                                  <div className="mt-2 bg-black/90 text-gray-300 p-3 rounded-md font-mono text-xs overflow-auto max-h-60">
                                    {deployment.logs.map((log, index) => (
                                      <div key={index}>{log}</div>
                                    ))}
                                    {deployment.status === "in-progress" && (
                                      <div className="text-primary animate-pulse">
                                        Deployment in progress...
                                      </div>
                                    )}
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="settings">
                <Card>
                  <CardHeader>
                    <CardTitle>Environment Settings</CardTitle>
                    <CardDescription>
                      Configure your deployment environments and CI/CD pipelines
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Environment Configuration</h3>

                      <div className="border border-border rounded-md">
                        <div className="p-4 border-b border-border">
                          <div className="flex justify-between items-start">
                            <div>
                              <h4 className="font-medium">Development</h4>
                              <p className="text-sm text-muted-foreground">
                                For testing and development purposes
                              </p>
                            </div>
                            <Button variant="outline" size="sm">Edit</Button>
                          </div>
                          <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                              <div className="font-medium mb-1">Domain</div>
                              <div className="text-muted-foreground">dev.alias-mosaic.com</div>
                            </div>
                            <div>
                              <div className="font-medium mb-1">Auto-Deploy Branch</div>
                              <div className="text-muted-foreground">develop</div>
                            </div>
                            <div>
                              <div className="font-medium mb-1">Server Resources</div>
                              <div className="text-muted-foreground">1 CPU, 2GB RAM</div>
                            </div>
                            <div>
                              <div className="font-medium mb-1">Auto-scaling</div>
                              <div className="text-muted-foreground">Disabled</div>
                            </div>
                          </div>
                        </div>

                        <div className="p-4 border-b border-border">
                          <div className="flex justify-between items-start">
                            <div>
                              <h4 className="font-medium">Staging</h4>
                              <p className="text-sm text-muted-foreground">
                                Pre-production testing environment
                              </p>
                            </div>
                            <Button variant="outline" size="sm">Edit</Button>
                          </div>
                          <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                              <div className="font-medium mb-1">Domain</div>
                              <div className="text-muted-foreground">staging.alias-mosaic.com</div>
                            </div>
                            <div>
                              <div className="font-medium mb-1">Auto-Deploy Branch</div>
                              <div className="text-muted-foreground">main</div>
                            </div>
                            <div>
                              <div className="font-medium mb-1">Server Resources</div>
                              <div className="text-muted-foreground">2 CPU, 4GB RAM</div>
                            </div>
                            <div>
                              <div className="font-medium mb-1">Auto-scaling</div>
                              <div className="text-muted-foreground">Enabled (1-3 instances)</div>
                            </div>
                          </div>
                        </div>

                        <div className="p-4">
                          <div className="flex justify-between items-start">
                            <div>
                              <h4 className="font-medium">Production</h4>
                              <p className="text-sm text-muted-foreground">
                                Live environment for end users
                              </p>
                            </div>
                            <Button variant="outline" size="sm">Edit</Button>
                          </div>
                          <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                              <div className="font-medium mb-1">Domain</div>
                              <div className="text-muted-foreground">app.alias-mosaic.com</div>
                            </div>
                            <div>
                              <div className="font-medium mb-1">Auto-Deploy Branch</div>
                              <div className="text-muted-foreground">None (Manual deployments only)</div>
                            </div>
                            <div>
                              <div className="font-medium mb-1">Server Resources</div>
                              <div className="text-muted-foreground">4 CPU, 8GB RAM</div>
                            </div>
                            <div>
                              <div className="font-medium mb-1">Auto-scaling</div>
                              <div className="text-muted-foreground">Enabled (2-10 instances)</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">CI/CD Integrations</h3>

                      <div className="border border-border rounded-md divide-y dark:divide-border">
                        <div className="p-4 flex justify-between items-center">
                          <div className="flex items-center">
                            <div className="w-10 h-10 rounded-full bg-background flex items-center justify-center mr-3">
                              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path></svg>
                            </div>
                            <div>
                              <h4 className="font-medium">GitHub Actions</h4>
                              <p className="text-sm text-muted-foreground">Connected to alias-mosaic/project-repo</p>
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                              Active
                            </div>
                            <Button variant="outline" size="sm">Configure</Button>
                          </div>
                        </div>

                        <div className="p-4 flex justify-between items-center">
                          <div className="flex items-center">
                            <div className="w-10 h-10 rounded-full bg-background flex items-center justify-center mr-3">
                              <Terminal className="h-6 w-6" />
                            </div>
                            <div>
                              <h4 className="font-medium">Jenkins</h4>
                              <p className="text-sm text-muted-foreground">Not configured</p>
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-400">
                              Inactive
                            </div>
                            <Button variant="outline" size="sm">Connect</Button>
                          </div>
                        </div>

                        <div className="p-4 flex justify-between items-center">
                          <div className="flex items-center">
                            <div className="w-10 h-10 rounded-full bg-background flex items-center justify-center mr-3">
                              <Workflow className="h-6 w-6" />
                            </div>
                            <div>
                              <h4 className="font-medium">CircleCI</h4>
                              <p className="text-sm text-muted-foreground">Not configured</p>
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-400">
                              Inactive
                            </div>
                            <Button variant="outline" size="sm">Connect</Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          {showAssistant && (
            <div className="lg:w-1/3 h-[700px]">
              <AIAssistPanel />
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
