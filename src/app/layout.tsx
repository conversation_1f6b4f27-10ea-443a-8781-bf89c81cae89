import type { Metada<PERSON> } from "next";
import "./globals.css";
import ClientBody from "./ClientBody";
import { Providers } from "./providers";

export const metadata: Metadata = {
  title: "ALIAS HQ - MOSAIC Framework",
  description: "Central hub for ALIAS's AI-driven, ontology-centric organizational operating system",
  keywords: "ALIAS, MOSAIC, AI, ontology, orchestration, life-work synthesis",
  authors: [{ name: "ALIAS Organization" }],
  creator: "<PERSON>",
  publisher: "ALIAS Pty Ltd",
  openGraph: {
    title: "ALIAS HQ - MOSAIC Framework",
    description: "The future of organizational intelligence",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="antialiased" suppressHydrationWarning>
        <Providers>
          <ClientBody>
            {children}
          </ClientBody>
        </Providers>
      </body>
    </html>
  );
}
