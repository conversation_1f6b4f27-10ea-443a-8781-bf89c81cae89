import { MainLayout } from "@/components/layout/MainLayout";
import { NetworkOverview } from "@/components/dashboard/NetworkOverview";
import { LiveFeed } from "@/components/dashboard/LiveFeed";
import { Leaderboard } from "@/components/dashboard/Leaderboard";
import { ProjectsSummary } from "@/components/dashboard/ProjectsSummary";
import { OntologyOverview } from "@/components/dashboard/OntologyOverview";
import { AgentActivities } from "@/components/dashboard/AgentActivities";

export default function DashboardPage() {
  return (
    <MainLayout>
      <div className="p-4 md:p-6 flex flex-col space-y-4 md:space-y-6">
        <h1 className="text-2xl font-normal mb-2">ALIAS MOSAIC Dashboard</h1>
        <p className="text-muted-foreground mb-6">AI-driven, ontology-centric development platform</p>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6">
          <NetworkOverview />
          <ProjectsSummary />
          <OntologyOverview />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
          <LiveFeed />
          <AgentActivities />
        </div>

        <Leaderboard />
      </div>
    </MainLayout>
  );
}