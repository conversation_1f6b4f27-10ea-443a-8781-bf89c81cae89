"use client";

import { signIn, signUp, signOut, useSession } from "@/lib/auth-client";
import { useState } from "react";

export default function AuthTestPage() {
  const { data: session } = useSession();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [name, setName] = useState("");

  const handleSignUp = async () => {
    try {
      const result = await signUp.email({ email, password, name });
      console.log("Sign up result:", result);
    } catch (error) {
      console.error("Sign up error:", error);
    }
  };

  const handleSignIn = async () => {
    try {
      const result = await signIn.email({ email, password });
      console.log("Sign in result:", result);
    } catch (error) {
      console.error("Sign in error:", error);
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      console.log("Signed out successfully");
    } catch (error) {
      console.error("Sign out error:", error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-950 text-white p-8">
      <div className="max-w-md mx-auto">
        <h1 className="text-3xl font-bold mb-8">Auth Test Page</h1>

        {session ? (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Welcome back!</h2>
            <p>User ID: {session.user?.id}</p>
            <p>Email: {session.user?.email}</p>
            <button
              onClick={handleSignOut}
              className="w-full px-4 py-2 bg-red-600 rounded hover:bg-red-700"
            >
              Sign Out
            </button>
          </div>
        ) : (
          <div className="space-y-6">
            <div className="space-y-4">
              <input
                type="text"
                placeholder="Name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="w-full px-4 py-2 bg-gray-800 rounded border border-gray-600"
              />
              <input
                type="email"
                placeholder="Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-4 py-2 bg-gray-800 rounded border border-gray-600"
              />
              <input
                type="password"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-4 py-2 bg-gray-800 rounded border border-gray-600"
              />
            </div>

            <div className="space-y-2">
              <button
                onClick={handleSignUp}
                className="w-full px-4 py-2 bg-blue-600 rounded hover:bg-blue-700"
              >
                Sign Up
              </button>
              <button
                onClick={handleSignIn}
                className="w-full px-4 py-2 bg-green-600 rounded hover:bg-green-700"
              >
                Sign In
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}