"use client";

import { MainLayout } from "@/components/layout/MainLayout";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Link from "next/link";
import { useState } from "react";
import { signIn } from "@/lib/auth-client";
import { useRouter } from "next/navigation";
import { AlertCircle, Lock, Mail, Github } from "lucide-react";

export default function LoginPage() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    try {
      const result = await signIn.email({
        email,
        password,
        callbackURL: "/"
      });

      if (result.error) {
        setError(result.error.message || "Invalid email or password");
        setIsLoading(false);
      } else {
        // Successfully signed in
        router.push("/"); // Redirect to home page
        router.refresh(); // Refresh the page to update auth state
      }
    } catch (error) {
      setError("An error occurred during login");
      setIsLoading(false);
    }
  };

  return (
    <MainLayout>
      <div className="p-6 flex items-center justify-center min-h-[calc(100vh-200px)]">
        <Card className="max-w-md w-full bg-card border-border">
          <form onSubmit={handleSubmit}>
            <CardHeader className="border-b border-border text-center">
              <CardTitle className="text-xl font-normal">Welcome to ALIAS MOSAIC</CardTitle>
              <CardDescription className="text-muted-foreground">
                Sign in to access the platform
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6 space-y-4">
              {error && (
                <div className="bg-destructive/10 p-3 rounded-md flex items-start">
                  <AlertCircle className="h-5 w-5 text-destructive mr-2 flex-shrink-0 mt-0.5" />
                  <p className="text-sm text-destructive">{error}</p>
                </div>
              )}

              <div className="space-y-2">
                <label className="text-sm flex items-center" htmlFor="email">
                  <Mail className="h-4 w-4 mr-2 text-primary" />
                  Email Address
                </label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="bg-background border-muted"
                  placeholder="Enter your email address"
                  required
                />
                <p className="text-xs text-muted-foreground">
                  Demo accounts: <EMAIL> / <EMAIL>
                </p>
              </div>

              <div className="space-y-2">
                <label className="text-sm flex items-center" htmlFor="password">
                  <Lock className="h-4 w-4 mr-2 text-primary" />
                  Password
                </label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="bg-background border-muted"
                  placeholder="Enter your password"
                  required
                />
                <p className="text-xs text-muted-foreground">
                  Demo passwords: password123 / admin123
                </p>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="remember"
                    className="rounded border-border bg-background"
                  />
                  <label htmlFor="remember" className="text-sm">Remember me</label>
                </div>
                <a href="#" className="text-sm text-primary hover:underline">
                  Forgot password?
                </a>
              </div>

              <Button
                type="submit"
                className="w-full bg-primary text-primary-foreground hover:bg-primary/90"
                disabled={isLoading}
              >
                {isLoading ? "Signing in..." : "Sign In"}
              </Button>

              <div className="relative my-2">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-border"></div>
                </div>
                <div className="relative flex justify-center text-xs">
                  <span className="bg-card px-2 text-muted-foreground">or continue with</span>
                </div>
              </div>

              <Button
                type="button"
                variant="outline"
                className="w-full border-muted"
                onClick={() => signIn.social({ provider: "github", callbackURL: "/" })}
              >
                <Github className="h-4 w-4 mr-2" />
                GitHub
              </Button>
            </CardContent>
            <CardFooter className="flex justify-between border-t border-border p-6">
              <Link href="/">
                <Button type="button" variant="outline" className="border-muted">
                  Back to Home
                </Button>
              </Link>

              <Link href="/">
                <Button type="button" variant="ghost">
                  Need an account?
                </Button>
              </Link>
            </CardFooter>
          </form>
        </Card>
      </div>
    </MainLayout>
  );
}
