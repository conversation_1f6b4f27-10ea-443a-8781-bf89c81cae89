"use client";

import { MainLayout } from "@/components/layout/MainLayout";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useAuth } from "@/lib/hooks/useAuth";
import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { Edit, Save, User, AtSign, Calendar, Lock } from "lucide-react";

export default function ProfilePage() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  
  // All hooks must be called before any early returns
  const [isEditing, setIsEditing] = useState(false);
  const [name, setName] = useState(user?.name || "");
  const [email, setEmail] = useState(user?.email || "");

  // Redirect to login if not authenticated
  if (!isLoading && !isAuthenticated) {
    router.push("/login");
    return null;
  }

  const handleSave = () => {
    // In a real app, we would send this to the API
    setIsEditing(false);
  };

  if (isLoading) {
    return (
      <MainLayout>
        <div className="p-6 flex justify-center items-center min-h-[50vh]">
          <div className="text-center">
            <div className="inline-block rounded-full border-t-2 border-primary w-8 h-8 animate-spin"></div>
            <p className="mt-2 text-muted-foreground">Loading profile...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="p-4 md:p-6 space-y-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <h1 className="text-2xl font-light">User Profile</h1>
          <Button
            onClick={() => isEditing ? handleSave() : setIsEditing(true)}
            className="bg-primary text-black hover:bg-primary/90 ml-auto"
          >
            {isEditing ? (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            ) : (
              <>
                <Edit className="h-4 w-4 mr-2" />
                Edit Profile
              </>
            )}
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="bg-card border-border md:col-span-1">
            <CardHeader className="text-center border-b border-border">
              <CardTitle className="text-lg font-normal">Profile</CardTitle>
            </CardHeader>
            <CardContent className="p-6 flex flex-col items-center text-center">
              <div className="mb-4">
                {user?.image ? (
                  <Image
                    src={user.image}
                    alt={user.name || "User"}
                    width={100}
                    height={100}
                    className="rounded-full border-4 border-background"
                  />
                ) : (
                  <div className="w-24 h-24 rounded-full bg-primary flex items-center justify-center text-black text-2xl">
                    {user?.name ? user.name[0] : "U"}
                  </div>
                )}
              </div>

              <h2 className="text-xl font-medium">{user?.name}</h2>
              <p className="text-muted-foreground">{user?.email}</p>

              <div className="mt-6 w-full space-y-4">
                <div className="flex items-center border-t border-border pt-4">
                  <Calendar className="h-4 w-4 text-muted-foreground mr-2" />
                  <div className="text-sm">
                    <div className="text-muted-foreground">Member Since</div>
                    <div>May 16, 2025</div>
                  </div>
                </div>

                <div className="flex items-center border-t border-border pt-4">
                  <AtSign className="h-4 w-4 text-muted-foreground mr-2" />
                  <div className="text-sm">
                    <div className="text-muted-foreground">Email</div>
                    <div>{user?.email}</div>
                  </div>
                </div>

                <div className="flex items-center border-t border-border pt-4">
                  <Lock className="h-4 w-4 text-muted-foreground mr-2" />
                  <div className="text-sm">
                    <div className="text-muted-foreground">Account Type</div>
                    <div>{user?.email?.includes("admin") ? "Administrator" : "Standard User"}</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="md:col-span-2 space-y-6">
            <Card className="bg-card border-border">
              <CardHeader className="border-b border-border">
                <CardTitle className="text-lg font-normal">Account Information</CardTitle>
                <CardDescription>Update your account details</CardDescription>
              </CardHeader>
              <CardContent className="p-6 space-y-4">
                <div className="space-y-2">
                  <label className="text-sm">Full Name</label>
                  {isEditing ? (
                    <Input
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      className="bg-background"
                    />
                  ) : (
                    <div className="p-2 border border-border rounded-md bg-background/50">
                      {user?.name}
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <label className="text-sm">Email Address</label>
                  {isEditing ? (
                    <Input
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="bg-background"
                    />
                  ) : (
                    <div className="p-2 border border-border rounded-md bg-background/50">
                      {user?.email}
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <label className="text-sm">Password</label>
                  {isEditing ? (
                    <div className="space-y-4">
                      <Input
                        type="password"
                        placeholder="Current password"
                        className="bg-background"
                      />
                      <Input
                        type="password"
                        placeholder="New password"
                        className="bg-background"
                      />
                      <Input
                        type="password"
                        placeholder="Confirm new password"
                        className="bg-background"
                      />
                    </div>
                  ) : (
                    <div className="p-2 border border-border rounded-md bg-background/50">
                      ••••••••••
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="bg-card border-border">
              <CardHeader className="border-b border-border">
                <CardTitle className="text-lg font-normal">Agent Usage</CardTitle>
                <CardDescription>Your AI agent activity</CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div className="p-4 border border-border rounded-md bg-background/50">
                    <div className="text-sm text-muted-foreground mb-1">Total Interactions</div>
                    <div className="text-2xl font-light text-primary">384</div>
                  </div>

                  <div className="p-4 border border-border rounded-md bg-background/50">
                    <div className="text-sm text-muted-foreground mb-1">Agents Created</div>
                    <div className="text-2xl font-light text-primary">3</div>
                  </div>

                  <div className="p-4 border border-border rounded-md bg-background/50">
                    <div className="text-sm text-muted-foreground mb-1">Success Rate</div>
                    <div className="text-2xl font-light text-primary">94%</div>
                  </div>
                </div>

                <div>
                  <div className="text-sm font-medium mb-2">Recent Agents</div>
                  <div className="space-y-2">
                    <div className="p-3 border border-border rounded-md bg-background/50 flex items-center justify-between">
                      <div className="flex items-center">
                        <User className="h-4 w-4 text-primary mr-2" />
                        <span>Dria Support Agent</span>
                      </div>
                      <div className="text-xs text-muted-foreground">Last used: 2 hours ago</div>
                    </div>

                    <div className="p-3 border border-border rounded-md bg-background/50 flex items-center justify-between">
                      <div className="flex items-center">
                        <User className="h-4 w-4 text-primary mr-2" />
                        <span>Code Helper</span>
                      </div>
                      <div className="text-xs text-muted-foreground">Last used: Yesterday</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
