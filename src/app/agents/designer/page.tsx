"use client";

import { MainLayout } from "@/components/layout/MainLayout";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardDescription, CardFooter } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { AIAssistPanel } from "@/components/dashboard/AIAssistPanel";
import {
  Brain, ArrowLeft, Code, Database, FileText,
  Settings, Play, Save, Download, Upload,
  Edit, Plus, Trash, Copy, Check, Eye, Layers,
  Link, BookOpen, Sparkles, PanelLeft, PanelRight, User
} from "lucide-react";
import { useState } from "react";

// Agent type interface
interface AgentType {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  category: string;
  complexity: string;
}

// Template interface
interface Template {
  id: string;
  name: string;
  description: string;
  agentType: string;
  popularity: number;
  usageCount: number;
}

// Agent types
const agentTypes = [
  {
    id: "code-assistant",
    name: "Code Assistant",
    description: "Generates and optimizes code based on specifications",
    icon: <Code className="h-10 w-10" />,
    category: "development",
    complexity: "medium"
  },
  {
    id: "data-processor",
    name: "Data Processor",
    description: "Transforms and analyzes data structures",
    icon: <Database className="h-10 w-10" />,
    category: "data",
    complexity: "high"
  },
  {
    id: "documentation",
    name: "Documentation Generator",
    description: "Creates comprehensive documentation from code and specifications",
    icon: <FileText className="h-10 w-10" />,
    category: "documentation",
    complexity: "low"
  },
  {
    id: "test-generator",
    name: "Test Generator",
    description: "Generates comprehensive test cases and test scripts",
    icon: <Play className="h-10 w-10" />,
    category: "quality",
    complexity: "medium"
  },
  {
    id: "ontology-mapper",
    name: "Ontology Mapper",
    description: "Maps and transforms ontology structures",
    icon: <Layers className="h-10 w-10" />,
    category: "architecture",
    complexity: "high"
  }
];

// Agent templates
const agentTemplates = [
  {
    id: "template-1",
    name: "API Generator",
    description: "Creates RESTful APIs based on ontology entities",
    agentType: "code-assistant",
    popularity: 4.8,
    usageCount: 1243
  },
  {
    id: "template-2",
    name: "Data Schema Analyzer",
    description: "Analyzes and optimizes database schemas",
    agentType: "data-processor",
    popularity: 4.6,
    usageCount: 987
  },
  {
    id: "template-3",
    name: "UI Component Builder",
    description: "Generates React components from design specs",
    agentType: "code-assistant",
    popularity: 4.9,
    usageCount: 1582
  }
];

// Code editor mock content
const exampleAgentCode = `// MOSAIC Agent Configuration
{
  "name": "Custom Code Assistant",
  "version": "1.0.0",
  "description": "Generates optimized code based on specifications",
  "type": "code-assistant",
  "capabilities": [
    "code-generation",
    "code-optimization",
    "code-review"
  ],
  "inputFormats": ["text", "json", "typescript"],
  "outputFormats": ["typescript", "javascript", "jsx", "tsx"],
  "ontologyMapping": {
    "semantic": ["Customer", "Order", "Product"],
    "kinetic": ["CustomerRecord", "OrderProcess"],
    "dynamic": ["CustomerAPI", "OrderAPI"]
  },
  "parameters": {
    "maxTokens": 4096,
    "temperature": 0.7,
    "topP": 0.95,
    "presencePenalty": 0.1,
    "frequencyPenalty": 0.1
  },
  "hooks": {
    "beforeExecution": "validateInput",
    "afterExecution": "formatOutput"
  },
  "access": {
    "projectLevel": true,
    "ontologyAccess": "read-write",
    "codebaseAccess": "read-write"
  }
}`;

// Sample agent capabilities and permissions
const agentCapabilities = [
  { id: "cap-1", name: "Code Generation", enabled: true },
  { id: "cap-2", name: "Code Optimization", enabled: true },
  { id: "cap-3", name: "Code Review", enabled: true },
  { id: "cap-4", name: "Documentation Generation", enabled: false },
  { id: "cap-5", name: "Test Generation", enabled: false },
  { id: "cap-6", name: "Dependency Analysis", enabled: true },
];

const agentPermissions = [
  { id: "perm-1", name: "Read Ontology", enabled: true },
  { id: "perm-2", name: "Write Ontology", enabled: false },
  { id: "perm-3", name: "Read Codebase", enabled: true },
  { id: "perm-4", name: "Write Codebase", enabled: true },
  { id: "perm-5", name: "Execute Commands", enabled: false },
  { id: "perm-6", name: "Access External APIs", enabled: false },
];

export default function AgentDesignerPage() {
  const [selectedAgentType, setSelectedAgentType] = useState<AgentType | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [agentName, setAgentName] = useState("My Custom Agent");
  const [agentDescription, setAgentDescription] = useState("");
  const [designStep, setDesignStep] = useState(1);
  const [showAssistant, setShowAssistant] = useState(false);

  // Handle agent type selection
  const handleSelectAgentType = (agentType: AgentType) => {
    setSelectedAgentType(agentType);
    setAgentDescription(agentType.description);
  };

  // Handle template selection
  const handleSelectTemplate = (template: Template) => {
    setSelectedTemplate(template);
    setAgentName(template.name);
    setAgentDescription(template.description);
  };

  // Move to next step
  const handleNextStep = () => {
    setDesignStep(prev => prev + 1);
  };

  // Move to previous step
  const handlePrevStep = () => {
    setDesignStep(prev => Math.max(1, prev - 1));
  };

  return (
    <MainLayout>
      <div className="p-4 md:p-6">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="icon" className="mr-2" asChild>
            <a href="/agents">
              <ArrowLeft className="h-5 w-5" />
            </a>
          </Button>
          <div>
            <h1 className="text-2xl font-normal">Agent Designer</h1>
            <p className="text-muted-foreground">
              Create and configure custom AI agents for your MOSAIC projects
            </p>
          </div>
        </div>

        <div className="flex flex-col lg:flex-row gap-6">
          <div className={`flex-1 ${showAssistant ? "lg:w-2/3" : "w-full"}`}>
            <Card className="bg-card border-border">
              <CardHeader className="border-b border-border">
                <div className="flex justify-between items-center">
                  <CardTitle>
                    {designStep === 1 ? "Choose Agent Type" :
                     designStep === 2 ? "Configure Agent" :
                     designStep === 3 ? "Advanced Settings" :
                     "Test Agent"}
                  </CardTitle>

                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowAssistant(!showAssistant)}
                    >
                      {showAssistant ? <PanelRight className="h-4 w-4 mr-1" /> : <PanelLeft className="h-4 w-4 mr-1" />}
                      {showAssistant ? "Hide Assistant" : "Show Assistant"}
                    </Button>
                    <Button variant="outline" size="sm">
                      <Save className="h-4 w-4 mr-1" />
                      Save Draft
                    </Button>
                  </div>
                </div>

                {/* Progress indicator */}
                <div className="flex items-center mt-4">
                  {[1, 2, 3, 4].map((step) => (
                    <div key={step} className="flex-1 flex items-center">
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center
                          ${step === designStep ? "bg-primary text-primary-foreground" :
                           step < designStep ? "bg-primary/20 text-primary" :
                           "bg-secondary text-muted-foreground"}`}
                      >
                        {step}
                      </div>
                      {step < 4 && (
                        <div
                          className={`flex-1 h-1 ${step < designStep ? "bg-primary" : "bg-secondary"}`}
                        ></div>
                      )}
                    </div>
                  ))}
                </div>
              </CardHeader>

              <CardContent className="p-6">
                {designStep === 1 && (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-medium mb-4">Choose an Agent Type</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {agentTypes.map((agentType) => (
                          <div
                            key={agentType.id}
                            className={`border rounded-lg p-4 cursor-pointer transition-colors
                              ${selectedAgentType?.id === agentType.id
                                ? "border-primary bg-primary/5"
                                : "border-border hover:border-primary/50"}`}
                            onClick={() => handleSelectAgentType(agentType)}
                          >
                            <div className="flex flex-col items-center text-center">
                              <div className="p-3 rounded-full bg-primary/10 text-primary mb-3">
                                {agentType.icon}
                              </div>
                              <h4 className="font-medium mb-1">{agentType.name}</h4>
                              <p className="text-sm text-muted-foreground">{agentType.description}</p>
                              <div className="flex justify-between w-full mt-3">
                                <span className="text-xs px-2 py-1 bg-secondary rounded-full">
                                  {agentType.category}
                                </span>
                                <span className="text-xs px-2 py-1 bg-secondary rounded-full">
                                  {agentType.complexity} complexity
                                </span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {selectedAgentType && (
                      <div>
                        <h3 className="text-lg font-medium mb-4">Or Start From a Template</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          {agentTemplates
                            .filter(template => template.agentType === selectedAgentType.id)
                            .map((template) => (
                              <div
                                key={template.id}
                                className={`border rounded-lg p-4 cursor-pointer transition-colors
                                  ${selectedTemplate?.id === template.id
                                    ? "border-primary bg-primary/5"
                                    : "border-border hover:border-primary/50"}`}
                                onClick={() => handleSelectTemplate(template)}
                              >
                                <h4 className="font-medium mb-1">{template.name}</h4>
                                <p className="text-sm text-muted-foreground mb-2">{template.description}</p>
                                <div className="flex justify-between items-center">
                                  <div className="flex items-center">
                                    <div className="text-yellow-500 mr-1">★</div>
                                    <span className="text-sm">{template.popularity.toFixed(1)}</span>
                                  </div>
                                  <span className="text-xs text-muted-foreground">
                                    {template.usageCount} uses
                                  </span>
                                </div>
                              </div>
                            ))}

                          <div
                            className="border border-dashed border-border rounded-lg p-4 cursor-pointer hover:border-primary/50 transition-colors flex flex-col items-center justify-center text-center h-full"
                            onClick={() => {
                              setSelectedTemplate(null);
                              setAgentName(`Custom ${selectedAgentType.name}`);
                              setAgentDescription(`Custom ${selectedAgentType.description.toLowerCase()}`);
                            }}
                          >
                            <div className="p-3 rounded-full bg-secondary text-muted-foreground mb-2">
                              <Plus className="h-6 w-6" />
                            </div>
                            <h4 className="font-medium">Start from scratch</h4>
                            <p className="text-sm text-muted-foreground">Create a custom agent</p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {designStep === 2 && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="text-sm font-medium mb-1 block">Agent Name</label>
                        <Input
                          value={agentName}
                          onChange={(e) => setAgentName(e.target.value)}
                          className="bg-background border-muted"
                        />
                      </div>

                      <div>
                        <label className="text-sm font-medium mb-1 block">Agent Type</label>
                        <div className="bg-background border border-muted rounded-md px-3 py-2 text-muted-foreground">
                          {selectedAgentType?.name}
                        </div>
                      </div>
                    </div>

                    <div>
                      <label className="text-sm font-medium mb-1 block">Description</label>
                      <textarea
                        value={agentDescription}
                        onChange={(e) => setAgentDescription(e.target.value)}
                        className="w-full bg-background border border-muted rounded-md px-3 py-2 h-24 resize-none"
                      />
                    </div>

                    <div>
                      <h3 className="text-sm font-medium mb-3">Capabilities</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                        {agentCapabilities.map((capability) => (
                          <div key={capability.id} className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              id={capability.id}
                              checked={capability.enabled}
                              className="rounded border-muted bg-background h-4 w-4"
                            />
                            <label htmlFor={capability.id} className="text-sm">
                              {capability.name}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium mb-3">Permissions</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                        {agentPermissions.map((permission) => (
                          <div key={permission.id} className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              id={permission.id}
                              checked={permission.enabled}
                              className="rounded border-muted bg-background h-4 w-4"
                            />
                            <label htmlFor={permission.id} className="text-sm">
                              {permission.name}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {designStep === 3 && (
                  <div className="space-y-6">
                    <Tabs defaultValue="code">
                      <TabsList>
                        <TabsTrigger value="code">Configuration</TabsTrigger>
                        <TabsTrigger value="ontology">Ontology Mapping</TabsTrigger>
                        <TabsTrigger value="parameters">Parameters</TabsTrigger>
                        <TabsTrigger value="hooks">Hooks</TabsTrigger>
                      </TabsList>

                      <TabsContent value="code" className="mt-4">
                        <div className="relative">
                          <pre className="font-mono text-sm bg-black p-4 rounded-md overflow-x-auto">
                            <code className="text-muted-foreground">{exampleAgentCode}</code>
                          </pre>
                          <div className="absolute top-2 right-2 flex space-x-1">
                            <Button variant="ghost" size="icon" className="h-7 w-7 bg-background/20">
                              <Copy className="h-3.5 w-3.5" />
                            </Button>
                            <Button variant="ghost" size="icon" className="h-7 w-7 bg-background/20">
                              <Edit className="h-3.5 w-3.5" />
                            </Button>
                          </div>
                        </div>
                      </TabsContent>

                      <TabsContent value="ontology" className="mt-4">
                        <div className="border border-border rounded-md p-4">
                          <h3 className="text-sm font-medium mb-3">Ontology Entity Access</h3>
                          <p className="text-sm text-muted-foreground mb-4">
                            Select which ontology entities this agent can access and modify
                          </p>

                          <div className="space-y-4">
                            <div>
                              <h4 className="text-sm mb-2 flex items-center">
                                <span className="w-3 h-3 rounded-full bg-primary mr-2"></span>
                                Semantic Layer
                              </h4>
                              <div className="flex flex-wrap gap-2">
                                <div className="border border-primary/50 rounded-full px-2 py-1 text-xs flex items-center">
                                  Customer <Check className="h-3 w-3 ml-1 text-primary" />
                                </div>
                                <div className="border border-primary/50 rounded-full px-2 py-1 text-xs flex items-center">
                                  Order <Check className="h-3 w-3 ml-1 text-primary" />
                                </div>
                                <div className="border border-primary/50 rounded-full px-2 py-1 text-xs flex items-center">
                                  Product <Check className="h-3 w-3 ml-1 text-primary" />
                                </div>
                                <div className="border border-border rounded-full px-2 py-1 text-xs flex items-center text-muted-foreground">
                                  Invoice
                                </div>
                                <div className="border border-border rounded-full px-2 py-1 text-xs flex items-center text-muted-foreground">
                                  Payment
                                </div>
                                <Button variant="outline" size="sm" className="h-6 px-2 text-xs rounded-full">
                                  <Plus className="h-3 w-3 mr-1" />
                                  Add Entity
                                </Button>
                              </div>
                            </div>

                            <div>
                              <h4 className="text-sm mb-2 flex items-center">
                                <span className="w-3 h-3 rounded-full bg-chart-2 mr-2"></span>
                                Kinetic Layer
                              </h4>
                              <div className="flex flex-wrap gap-2">
                                <div className="border border-primary/50 rounded-full px-2 py-1 text-xs flex items-center">
                                  CustomerRecord <Check className="h-3 w-3 ml-1 text-primary" />
                                </div>
                                <div className="border border-primary/50 rounded-full px-2 py-1 text-xs flex items-center">
                                  OrderProcess <Check className="h-3 w-3 ml-1 text-primary" />
                                </div>
                                <div className="border border-border rounded-full px-2 py-1 text-xs flex items-center text-muted-foreground">
                                  ProductCatalog
                                </div>
                                <Button variant="outline" size="sm" className="h-6 px-2 text-xs rounded-full">
                                  <Plus className="h-3 w-3 mr-1" />
                                  Add Entity
                                </Button>
                              </div>
                            </div>

                            <div>
                              <h4 className="text-sm mb-2 flex items-center">
                                <span className="w-3 h-3 rounded-full bg-chart-3 mr-2"></span>
                                Dynamic Layer
                              </h4>
                              <div className="flex flex-wrap gap-2">
                                <div className="border border-primary/50 rounded-full px-2 py-1 text-xs flex items-center">
                                  CustomerAPI <Check className="h-3 w-3 ml-1 text-primary" />
                                </div>
                                <div className="border border-primary/50 rounded-full px-2 py-1 text-xs flex items-center">
                                  OrderAPI <Check className="h-3 w-3 ml-1 text-primary" />
                                </div>
                                <Button variant="outline" size="sm" className="h-6 px-2 text-xs rounded-full">
                                  <Plus className="h-3 w-3 mr-1" />
                                  Add Entity
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </TabsContent>

                      <TabsContent value="parameters" className="mt-4">
                        <div className="border border-border rounded-md p-4">
                          <h3 className="text-sm font-medium mb-3">Model Parameters</h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <label className="text-sm block mb-1">Temperature</label>
                              <div className="flex items-center">
                                <input
                                  type="range"
                                  min="0"
                                  max="10"
                                  step="0.1"
                                  defaultValue="7"
                                  className="w-full mr-2"
                                />
                                <span className="text-sm font-mono">0.7</span>
                              </div>
                              <p className="text-xs text-muted-foreground mt-1">Controls randomness: lower is more deterministic</p>
                            </div>

                            <div>
                              <label className="text-sm block mb-1">Max Tokens</label>
                              <Input
                                type="number"
                                defaultValue="4096"
                                className="bg-background border-muted"
                              />
                              <p className="text-xs text-muted-foreground mt-1">Maximum tokens in completion</p>
                            </div>

                            <div>
                              <label className="text-sm block mb-1">Top P</label>
                              <div className="flex items-center">
                                <input
                                  type="range"
                                  min="0"
                                  max="10"
                                  step="0.05"
                                  defaultValue="9.5"
                                  className="w-full mr-2"
                                />
                                <span className="text-sm font-mono">0.95</span>
                              </div>
                              <p className="text-xs text-muted-foreground mt-1">Controls diversity via nucleus sampling</p>
                            </div>

                            <div>
                              <label className="text-sm block mb-1">Frequency Penalty</label>
                              <div className="flex items-center">
                                <input
                                  type="range"
                                  min="0"
                                  max="20"
                                  step="0.1"
                                  defaultValue="1"
                                  className="w-full mr-2"
                                />
                                <span className="text-sm font-mono">0.1</span>
                              </div>
                              <p className="text-xs text-muted-foreground mt-1">How much to penalize new tokens based on frequency</p>
                            </div>
                          </div>
                        </div>
                      </TabsContent>

                      <TabsContent value="hooks" className="mt-4">
                        <div className="border border-border rounded-md p-4">
                          <h3 className="text-sm font-medium mb-3">Execution Hooks</h3>
                          <p className="text-sm text-muted-foreground mb-4">
                            Configure functions that run before and after agent execution
                          </p>

                          <div className="space-y-4">
                            <div>
                              <label className="text-sm font-medium block mb-1">Before Execution</label>
                              <select className="w-full bg-background border border-muted rounded-md px-3 py-2">
                                <option value="validateInput">validateInput</option>
                                <option value="prepareContext">prepareContext</option>
                                <option value="checkOntology">checkOntology</option>
                                <option value="custom">Custom Function</option>
                              </select>
                            </div>

                            <div>
                              <label className="text-sm font-medium block mb-1">After Execution</label>
                              <select className="w-full bg-background border border-muted rounded-md px-3 py-2">
                                <option value="formatOutput">formatOutput</option>
                                <option value="validateOutput">validateOutput</option>
                                <option value="updateOntology">updateOntology</option>
                                <option value="custom">Custom Function</option>
                              </select>
                            </div>

                            <div>
                              <label className="text-sm font-medium block mb-1">Error Handler</label>
                              <select className="w-full bg-background border border-muted rounded-md px-3 py-2">
                                <option value="defaultErrorHandler">defaultErrorHandler</option>
                                <option value="logAndRetry">logAndRetry</option>
                                <option value="fallbackToCache">fallbackToCache</option>
                                <option value="custom">Custom Function</option>
                              </select>
                            </div>
                          </div>
                        </div>
                      </TabsContent>
                    </Tabs>
                  </div>
                )}

                {designStep === 4 && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Card className="bg-background">
                          <CardHeader className="pb-2">
                            <CardTitle className="text-base font-normal">Agent Overview</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <dl className="space-y-2">
                              <div>
                                <dt className="text-sm text-muted-foreground">Name</dt>
                                <dd className="font-medium">{agentName}</dd>
                              </div>
                              <div>
                                <dt className="text-sm text-muted-foreground">Type</dt>
                                <dd>{selectedAgentType?.name}</dd>
                              </div>
                              <div>
                                <dt className="text-sm text-muted-foreground">Description</dt>
                                <dd className="text-sm">{agentDescription}</dd>
                              </div>
                              <div>
                                <dt className="text-sm text-muted-foreground">Capabilities</dt>
                                <dd>
                                  <div className="flex flex-wrap gap-1 mt-1">
                                    {agentCapabilities
                                      .filter(cap => cap.enabled)
                                      .map(cap => (
                                        <span key={cap.id} className="text-xs px-2 py-0.5 bg-secondary rounded-full">
                                          {cap.name}
                                        </span>
                                      ))
                                    }
                                  </div>
                                </dd>
                              </div>
                            </dl>
                          </CardContent>
                        </Card>

                        <div className="border border-border rounded-md mt-4 p-4">
                          <h3 className="text-sm font-medium mb-3">Test Agent</h3>
                          <div className="space-y-3">
                            <textarea
                              placeholder="Enter test input for your agent..."
                              className="w-full bg-background border border-muted rounded-md px-3 py-2 h-24 resize-none"
                            />
                            <Button>
                              <Play className="h-4 w-4 mr-1" />
                              Run Test
                            </Button>
                          </div>
                        </div>
                      </div>

                      <div>
                        <Card className="bg-background h-full">
                          <CardHeader className="pb-2">
                            <CardTitle className="text-base font-normal">Sample Interactions</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-3">
                              <div className="border border-border rounded-md p-3">
                                <div className="flex items-start gap-3">
                                  <div className="bg-primary/10 p-2 rounded-md">
                                    <User className="h-5 w-5 text-primary" />
                                  </div>
                                  <div className="flex-1">
                                    <p className="text-sm font-medium mb-1">Input</p>
                                    <p className="text-sm">Generate an API endpoint for the Customer entity that includes CRUD operations.</p>
                                  </div>
                                </div>
                              </div>

                              <div className="border border-border rounded-md p-3">
                                <div className="flex items-start gap-3">
                                  <div className="bg-primary/10 p-2 rounded-md">
                                    <Sparkles className="h-5 w-5 text-primary" />
                                  </div>
                                  <div className="flex-1">
                                    <p className="text-sm font-medium mb-1">Output</p>
                                    <div className="text-sm">
                                      <p className="mb-2">Here's a RESTful API endpoint for the Customer entity:</p>
                                      <pre className="bg-black/80 p-2 rounded-md text-xs text-muted-foreground overflow-x-auto">
{`import { Router } from 'express';
import { CustomerController } from '../controllers/customer.controller';

const router = Router();
const controller = new CustomerController();

// Get all customers
router.get('/', controller.getAll);

// Get a specific customer
router.get('/:id', controller.getById);

// Create a new customer
router.post('/', controller.create);

// Update a customer
router.put('/:id', controller.update);

// Delete a customer
router.delete('/:id', controller.delete);

export default router;`}
                                      </pre>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>

              <CardFooter className="border-t border-border p-4 flex justify-between">
                <Button
                  variant="outline"
                  onClick={handlePrevStep}
                  disabled={designStep === 1}
                >
                  Previous
                </Button>

                <div>
                  {designStep < 4 ? (
                    <Button
                      onClick={handleNextStep}
                      disabled={(designStep === 1 && !selectedAgentType) || (designStep === 2 && !agentName.trim())}
                    >
                      Continue
                    </Button>
                  ) : (
                    <Button className="bg-green-600 hover:bg-green-700">
                      Deploy Agent
                    </Button>
                  )}
                </div>
              </CardFooter>
            </Card>
          </div>

          {showAssistant && (
            <div className="lg:w-1/3 h-[700px]">
              <AIAssistPanel />
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
