import { MainLayout } from "@/components/layout/MainLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { Brain, PencilRuler, Library, Settings } from "lucide-react";

export default function AgentsPage() {
  return (
    <MainLayout>
      <div className="p-4 md:p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-light mb-2">AI Agents Dashboard</h1>
          <p className="text-muted-foreground">
            Create, manage, and deploy AI agents for decentralized inference
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
          <Link href="/agents/designer">
            <Card className="bg-card border-border h-full cursor-pointer transition-all hover:border-primary">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <PencilRuler className="mr-2 h-5 w-5 text-primary" />
                  Agent Designer
                </CardTitle>
                <CardDescription>
                  Create and customize new AI agents
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Use the visual designer to create agents with specific capabilities,
                  knowledge bases, and interaction styles. Define what tools the agent
                  can access and how it should respond to user inputs.
                </p>
                <Button className="mt-4 bg-primary text-black hover:bg-primary/90">
                  Create New Agent
                </Button>
              </CardContent>
            </Card>
          </Link>

          <Link href="/agents/management">
            <Card className="bg-card border-border h-full cursor-pointer transition-all hover:border-primary">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Settings className="mr-2 h-5 w-5 text-primary" />
                  Agent Management
                </CardTitle>
                <CardDescription>
                  Monitor and manage your deployed agents
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  View analytics, edit configurations, and monitor the performance of your
                  deployed agents. Adjust parameters, update knowledge bases, and optimize
                  for better results.
                </p>
                <Button className="mt-4 bg-primary text-black hover:bg-primary/90">
                  View Agents
                </Button>
              </CardContent>
            </Card>
          </Link>

          <Link href="/agents/library">
            <Card className="bg-card border-border h-full cursor-pointer transition-all hover:border-primary">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Library className="mr-2 h-5 w-5 text-primary" />
                  Agent Library
                </CardTitle>
                <CardDescription>
                  Browse pre-built agents and templates
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Explore a collection of pre-built agents for common tasks and use cases.
                  Import, customize, and deploy these templates to quickly get started with
                  specific AI capabilities.
                </p>
                <Button className="mt-4 bg-primary text-black hover:bg-primary/90">
                  Browse Library
                </Button>
              </CardContent>
            </Card>
          </Link>

          <Card className="bg-card border-border h-full">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Brain className="mr-2 h-5 w-5 text-primary" />
                Agent Stats
              </CardTitle>
              <CardDescription>
                Performance metrics and usage statistics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="text-sm text-muted-foreground mb-1">Active Agents</div>
                  <div className="text-2xl font-light text-primary">5</div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground mb-1">Total Interactions</div>
                  <div className="text-2xl font-light text-primary">1,287</div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground mb-1">Average Response Time</div>
                  <div className="text-2xl font-light text-primary">1.2s</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
}
