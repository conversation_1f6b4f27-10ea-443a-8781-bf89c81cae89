"use client";

import { MainLayout } from "@/components/layout/MainLayout";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  <PERSON><PERSON>hart2, ArrowLeft, Refresh<PERSON>w, Clock, CheckCircle, XCircle,
  DownloadCloud, Filter, Calendar, Settings, Terminal, ChevronDown,
  Brain, Activity, TrendingUp, AlertCircle, Layers
} from "lucide-react";
import { useState, useEffect, useRef, useMemo } from "react";
import Link from "next/link";
import { useAgentMetrics } from "@/lib/hooks/useAgentMetrics";

// Define types for metrics data
interface TimeSeriesDataPoint {
  timestamp: string;
  value: number;
}

interface AgentTypeDistribution {
  name: string;
  value: number;
}

interface AgentCall {
  id: string;
  agentType: string;
  projectName: string;
  status: string;
  username: string;
  timestamp: string;
  duration: number;
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
}

interface AgentMetrics {
  summary: {
    totalRequests: number;
    successRate: number;
    failureRate: number;
    averageLatency: number;
    totalTokensConsumed: number;
  };
  timeSeries: {
    requests: TimeSeriesDataPoint[];
    latency: TimeSeriesDataPoint[];
    successRate: TimeSeriesDataPoint[];
  };
  agentTypeDistribution: AgentTypeDistribution[];
  recentCalls: AgentCall[];
  slowCalls: AgentCall[];
  errorCalls: AgentCall[];
}

// Helper function to format date
function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

// Helper function to format duration
function formatDuration(ms: number): string {
  if (ms < 1000) return `${ms}ms`;
  return `${(ms / 1000).toFixed(2)}s`;
}

// Helper function to format tokens
function formatNumber(num: number): string {
  return new Intl.NumberFormat('en-US').format(num);
}

// Status badge component
function StatusBadge({ status }: { status: string }) {
  switch (status) {
    case "success":
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
          <CheckCircle className="h-3 w-3 mr-1" />
          Success
        </span>
      );
    case "error":
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400">
          <XCircle className="h-3 w-3 mr-1" />
          Error
        </span>
      );
    case "pending":
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400">
          <Clock className="h-3 w-3 mr-1" />
          Pending
        </span>
      );
    default:
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-400">
          {status}
        </span>
      );
  }
}

// Simple line chart component that uses HTML Canvas
function SimpleLineChart({ data, height = 120, color = '#3060D1' }: { data: TimeSeriesDataPoint[], height?: number, color?: string }) {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);

  useEffect(() => {
    if (!canvasRef.current || !data || data.length === 0) return;

    const ctx = canvasRef.current.getContext('2d');
    if (!ctx) return;

    const values = data.map(d => d.value);
    const min = Math.min(...values);
    const max = Math.max(...values);
    const range = max - min || 1;

    // Clear canvas
    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

    // Draw line
    ctx.beginPath();
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;

    data.forEach((point, i) => {
      const x = (i / (data.length - 1)) * ctx.canvas.width;
      const normalizedValue = (point.value - min) / range;
      const y = ctx.canvas.height - (normalizedValue * (ctx.canvas.height - 20)) - 10;

      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });

    ctx.stroke();

    // Draw area under the line
    ctx.lineTo(ctx.canvas.width, ctx.canvas.height);
    ctx.lineTo(0, ctx.canvas.height);
    ctx.closePath();
    ctx.fillStyle = `${color}20`;
    ctx.fill();

  }, [data, color]);

  if (!data || data.length === 0) {
    return <div className="h-[120px] flex items-center justify-center bg-background/50 rounded-md">
      <p className="text-sm text-muted-foreground">No data available</p>
    </div>;
  }

  return (
    <canvas ref={canvasRef} height={height} className="w-full" />
  );
}

// Simple pie chart component that uses HTML Canvas
function SimplePieChart({ data, height = 120 }: { data: AgentTypeDistribution[], height?: number }) {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);

  // Colors for different agent types - memoized to prevent dependency changes
  const colors = useMemo(() => [
    '#3060D1', // Primary color
    '#5A7DE9', // Lighter blue
    '#50C878', // Green
    '#F9A826', // Orange
    '#FF6B6B', // Red
  ], []);

  useEffect(() => {
    if (!canvasRef.current || !data || data.length === 0) return;

    const ctx = canvasRef.current.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

    const total = data.reduce((sum, item) => sum + item.value, 0);
    let startAngle = 0;

    // Draw pie sections
    data.forEach((item, i) => {
      const sliceAngle = (item.value / total) * 2 * Math.PI;

      ctx.beginPath();
      ctx.fillStyle = colors[i % colors.length];
      ctx.moveTo(ctx.canvas.width / 2, ctx.canvas.height / 2);
      ctx.arc(
        ctx.canvas.width / 2,
        ctx.canvas.height / 2,
        Math.min(ctx.canvas.width, ctx.canvas.height) / 2 - 10,
        startAngle,
        startAngle + sliceAngle
      );
      ctx.closePath();
      ctx.fill();

      startAngle += sliceAngle;
    });

    // Draw center circle for donut effect
    ctx.beginPath();
    ctx.fillStyle = getComputedStyle(document.documentElement).getPropertyValue('--background') || '#ffffff';
    ctx.arc(
      ctx.canvas.width / 2,
      ctx.canvas.height / 2,
      (Math.min(ctx.canvas.width, ctx.canvas.height) / 2 - 10) * 0.6, // 60% of radius
      0,
      2 * Math.PI
    );
    ctx.fill();

  }, [canvasRef, data, colors]);

  if (!data || data.length === 0) {
    return <div className="h-[120px] flex items-center justify-center bg-background/50 rounded-md">
      <p className="text-sm text-muted-foreground">No data available</p>
    </div>;
  }

  return (
    <canvas ref={canvasRef} height={height} className="w-full" />
  );
}

export default function AgentMetricsPage() {
  const { data: metricsData, isLoading } = useAgentMetrics();
  const [timeRange, setTimeRange] = useState("24h");

  const fetchMetrics = () => {
    // Placeholder function for refresh functionality
    console.log("Refreshing metrics...");
  };

  return (
    <MainLayout>
      <div className="p-4 md:p-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-6 gap-4">
          <div className="flex items-center">
            <Link href="/agents">
              <Button variant="ghost" size="icon" className="mr-2">
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-normal">Agent Metrics</h1>
              <p className="text-muted-foreground">
                Monitor performance and usage statistics for your AI agents
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <div className="bg-background border border-border rounded-md p-1 flex">
              <Button variant={timeRange === "24h" ? "secondary" : "ghost"} size="sm" onClick={() => setTimeRange("24h")}>24h</Button>
              <Button variant={timeRange === "7d" ? "secondary" : "ghost"} size="sm" onClick={() => setTimeRange("7d")}>7d</Button>
              <Button variant={timeRange === "30d" ? "secondary" : "ghost"} size="sm" onClick={() => setTimeRange("30d")}>30d</Button>
              <Button variant={timeRange === "custom" ? "secondary" : "ghost"} size="sm" onClick={() => setTimeRange("custom")}>
                <Calendar className="h-4 w-4 mr-1" />
                Custom
              </Button>
            </div>

            <Button variant="outline" size="sm" onClick={fetchMetrics}>
              <RefreshCw className={`h-4 w-4 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>

            <Button variant="outline" size="sm">
              <DownloadCloud className="h-4 w-4 mr-1" />
              Export
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium">Total Requests</h3>
                <BarChart2 className="h-4 w-4 text-muted-foreground" />
              </div>
              {isLoading ? (
                <div className="h-8 w-24 bg-muted animate-pulse rounded"></div>
              ) : (
                <div className="text-2xl font-semibold">
                  {formatNumber(metricsData?.summary.totalRequests || 0)}
                </div>
              )}
              <p className="text-xs text-muted-foreground mt-1">
                Last {timeRange}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium">Success Rate</h3>
                <CheckCircle className="h-4 w-4 text-green-500" />
              </div>
              {isLoading ? (
                <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
              ) : (
                <div className="text-2xl font-semibold">
                  {metricsData?.summary.successRate.toFixed(2) || 0}%
                </div>
              )}
              <p className="text-xs text-muted-foreground mt-1">
                <span className="text-green-500">↑ 0.3%</span> from previous period
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium">Avg. Latency</h3>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </div>
              {isLoading ? (
                <div className="h-8 w-20 bg-muted animate-pulse rounded"></div>
              ) : (
                <div className="text-2xl font-semibold">
                  {metricsData?.summary.averageLatency.toFixed(0) || 0}ms
                </div>
              )}
              <p className="text-xs text-muted-foreground mt-1">
                <span className="text-red-500">↑ 12.5ms</span> from previous period
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium">Tokens Consumed</h3>
                <Terminal className="h-4 w-4 text-muted-foreground" />
              </div>
              {isLoading ? (
                <div className="h-8 w-24 bg-muted animate-pulse rounded"></div>
              ) : (
                <div className="text-2xl font-semibold">
                  {(metricsData?.summary.totalTokensConsumed || 0) / 1000000}M
                </div>
              )}
              <p className="text-xs text-muted-foreground mt-1">
                <span className="text-green-500">↓ 2.1%</span> from previous period
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium">Error Rate</h3>
                <XCircle className="h-4 w-4 text-red-500" />
              </div>
              {isLoading ? (
                <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
              ) : (
                <div className="text-2xl font-semibold">
                  {metricsData?.summary.failureRate.toFixed(2) || 0}%
                </div>
              )}
              <p className="text-xs text-muted-foreground mt-1">
                <span className="text-green-500">↓ 0.3%</span> from previous period
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base font-normal flex items-center">
                <BarChart2 className="h-5 w-5 mr-2 text-primary" />
                Requests
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-[120px] bg-muted animate-pulse rounded"></div>
              ) : (
                <SimpleLineChart
                  data={metricsData?.timeSeries.requests || []}
                  color="#3060D1"
                />
              )}
              <div className="mt-2 text-sm text-center">Hourly request volume</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base font-normal flex items-center">
                <Activity className="h-5 w-5 mr-2 text-primary" />
                Latency
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-[120px] bg-muted animate-pulse rounded"></div>
              ) : (
                <SimpleLineChart
                  data={metricsData?.timeSeries.latency || []}
                  color="#F9A826"
                />
              )}
              <div className="mt-2 text-sm text-center">Average response time (ms)</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base font-normal flex items-center">
                <TrendingUp className="h-5 w-5 mr-2 text-primary" />
                Success Rate
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-[120px] bg-muted animate-pulse rounded"></div>
              ) : (
                <SimpleLineChart
                  data={metricsData?.timeSeries.successRate || []}
                  color="#50C878"
                />
              )}
              <div className="mt-2 text-sm text-center">Percentage of successful requests</div>
            </CardContent>
          </Card>
        </div>

        {/* Agent Type Distribution */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base font-normal flex items-center">
                <Brain className="h-5 w-5 mr-2 text-primary" />
                Agent Type Distribution
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {isLoading ? (
                  <div className="h-[120px] bg-muted animate-pulse rounded"></div>
                ) : (
                  <SimplePieChart data={metricsData?.agentTypeDistribution || []} />
                )}

                <div className="space-y-2">
                  {isLoading ? (
                    Array(5).fill(0).map((_, i) => (
                      <div key={i} className="h-5 bg-muted animate-pulse rounded"></div>
                    ))
                  ) : (
                    metricsData?.agentTypeDistribution.map((item, i) => (
                      <div key={i} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div
                            className="w-3 h-3 rounded-full mr-2"
                            style={{ backgroundColor: ['#3060D1', '#5A7DE9', '#50C878', '#F9A826', '#FF6B6B'][i % 5] }}
                          ></div>
                          <span className="text-sm">{item.name}</span>
                        </div>
                        <span className="text-sm">{item.value}%</span>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base font-normal flex items-center">
                <AlertCircle className="h-5 w-5 mr-2 text-primary" />
                Issues Overview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium mb-2">Slowest Calls</h3>
                  {isLoading ? (
                    Array(3).fill(0).map((_, i) => (
                      <div key={i} className="h-8 mb-2 bg-muted animate-pulse rounded"></div>
                    ))
                  ) : (
                    <div className="space-y-2">
                      {metricsData?.slowCalls.slice(0, 3).map((call) => (
                        <div key={call._id} className="flex justify-between items-center text-sm border-b border-border pb-2">
                          <div className="truncate max-w-[200px]">
                            <span className="font-medium">{call.agentType}</span>
                            <span className="text-muted-foreground"> • {call.projectName}</span>
                          </div>
                          <div className="text-yellow-500 font-semibold">
                            {formatDuration(call.duration)}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                <div>
                  <h3 className="text-sm font-medium mb-2">Recent Errors</h3>
                  {isLoading ? (
                    Array(3).fill(0).map((_, i) => (
                      <div key={i} className="h-8 mb-2 bg-muted animate-pulse rounded"></div>
                    ))
                  ) : (
                    <div className="space-y-2">
                      {(metricsData?.errorCalls || []).length > 0 ? (
                        metricsData?.errorCalls.slice(0, 3).map((call) => (
                          <div key={call._id} className="flex justify-between items-center text-sm border-b border-border pb-2">
                            <div className="truncate max-w-[200px]">
                              <span className="font-medium">{call.agentType}</span>
                              <span className="text-muted-foreground"> • {call.projectName}</span>
                            </div>
                            <div className="text-red-500">
                              {formatDate(new Date(call.timestamp).toISOString())}
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-sm text-muted-foreground">No recent errors</div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Calls Table */}
        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-base font-normal flex items-center">
                <Layers className="h-5 w-5 mr-2 text-primary" />
                Recent Agent Calls
              </CardTitle>

              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-1" />
                  Filter
                </Button>
                <Button variant="outline" size="sm">
                  <Settings className="h-4 w-4 mr-1" />
                  Columns
                </Button>
                <Button variant="outline" size="sm">
                  <DownloadCloud className="h-4 w-4 mr-1" />
                  Export
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-border">
                    <th className="text-left p-4 text-sm font-medium">Agent Type</th>
                    <th className="text-left p-4 text-sm font-medium">Project</th>
                    <th className="text-left p-4 text-sm font-medium">Status</th>
                    <th className="text-left p-4 text-sm font-medium">User</th>
                    <th className="text-left p-4 text-sm font-medium">Time</th>
                    <th className="text-right p-4 text-sm font-medium">Duration</th>
                    <th className="text-right p-4 text-sm font-medium">Tokens</th>
                  </tr>
                </thead>
                <tbody>
                  {isLoading ? (
                    Array(5).fill(0).map((_, i) => (
                      <tr key={i} className="border-b border-border">
                        <td colSpan={7} className="p-4">
                          <div className="h-6 bg-muted animate-pulse rounded"></div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    metricsData?.recentCalls.map((call) => (
                      <tr key={call._id} className="border-b border-border hover:bg-muted/20">
                        <td className="p-4 text-sm">{call.agentType}</td>
                        <td className="p-4 text-sm">{call.projectName}</td>
                        <td className="p-4 text-sm">
                          <StatusBadge status={call.status} />
                        </td>
                        <td className="p-4 text-sm">{call.username}</td>
                        <td className="p-4 text-sm">{formatDate(new Date(call.timestamp).toISOString())}</td>
                        <td className="p-4 text-sm text-right font-mono">{formatDuration(call.duration)}</td>
                        <td className="p-4 text-sm text-right font-mono">{formatNumber(call.totalTokens)}</td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <div className="p-4 border-t border-border flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                Showing 1-{metricsData?.recentCalls.length || 0} of {metricsData?.summary.totalRequests || 0} calls
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" disabled>Previous</Button>
                <Button variant="outline" size="sm">Next</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
