"use client";

import { MainLayout } from "@/components/layout/MainLayout";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Link from "next/link";
import { ArrowLeft, Search, Star, Download, MessageSquare, Book, Code, FileText, HelpCircle, Zap, BarChart as Bar<PERSON><PERSON><PERSON><PERSON> } from "lucide-react";
import { Wrench as Tool } from "lucide-react";
import { useState } from "react";

const agentTemplates = [
  {
    id: 1,
    name: "Documentation Assistant",
    description: "Helps users navigate and understand project documentation",
    category: "Knowledge Base",
    icon: <Book className="h-5 w-5 text-primary" />,
    popularity: 4.8,
    installations: 1240,
    author: "Dria Team",
    features: ["Documentation search", "Code examples", "Step-by-step guides"]
  },
  {
    id: 2,
    name: "Code Helper",
    description: "Assists with coding tasks, debugging, and best practices",
    category: "Development",
    icon: <Code className="h-5 w-5 text-primary" />,
    popularity: 4.7,
    installations: 895,
    author: "Dria Team",
    features: ["Code generation", "Debugging assistance", "Language support"]
  },
  {
    id: 3,
    name: "Technical Support",
    description: "Provides technical support and troubleshooting assistance",
    category: "Support",
    icon: <HelpCircle className="h-5 w-5 text-primary" />,
    popularity: 4.9,
    installations: 1650,
    author: "Dria Team",
    features: ["Error diagnostics", "Guided troubleshooting", "System information"]
  },
  {
    id: 4,
    name: "API Reference",
    description: "Specialized in API documentation and usage examples",
    category: "Development",
    icon: <FileText className="h-5 w-5 text-primary" />,
    popularity: 4.6,
    installations: 720,
    author: "Dria Team",
    features: ["API endpoints", "Parameter descriptions", "Response examples"]
  },
  {
    id: 5,
    name: "Data Analyst",
    description: "Helps with data analysis, visualization, and insights",
    category: "Data",
    icon: <BarChartIcon className="h-5 w-5 text-primary" />,
    popularity: 4.5,
    installations: 680,
    author: "Community",
    features: ["Data processing", "Chart generation", "Statistical analysis"]
  },
  {
    id: 6,
    name: "Tool Integration Assistant",
    description: "Helps integrate external tools and services with your project",
    category: "Development",
    icon: <Tool className="h-5 w-5 text-primary" />,
    popularity: 4.4,
    installations: 570,
    author: "Community",
    features: ["API integration", "Authentication setup", "Webhook configuration"]
  }
];

const BarChart = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <line x1="18" y1="20" x2="18" y2="10"></line>
    <line x1="12" y1="20" x2="12" y2="4"></line>
    <line x1="6" y1="20" x2="6" y2="14"></line>
    <line x1="3" y1="20" x2="21" y2="20"></line>
  </svg>
);

export default function AgentLibraryPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");

  const categories = ["All", "Knowledge Base", "Development", "Support", "Data"];

  const filteredAgents = agentTemplates.filter(agent => {
    const matchesSearch = agent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         agent.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === "All" || agent.category === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  return (
    <MainLayout>
      <div className="p-4 md:p-6 space-y-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between space-y-4 md:space-y-0">
          <div className="flex items-center">
            <Link href="/agents">
              <Button variant="ghost" size="icon" className="mr-2">
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <h1 className="text-2xl font-light">Agent Library</h1>
          </div>

          <div className="relative flex-1 md:w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search templates..."
              className="pl-8 bg-background"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          {categories.map(category => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              className={selectedCategory === category ? "bg-primary text-black" : ""}
              onClick={() => setSelectedCategory(category)}
            >
              {category}
            </Button>
          ))}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredAgents.map(agent => (
            <Card key={agent.id} className="bg-card border-border overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div className="p-2 rounded-md bg-primary/10">
                    {agent.icon}
                  </div>
                  <div className="flex items-center text-amber-400">
                    <Star className="h-4 w-4 fill-current" />
                    <span className="ml-1 text-sm">{agent.popularity}</span>
                  </div>
                </div>
                <CardTitle className="mt-2">{agent.name}</CardTitle>
                <CardDescription>{agent.description}</CardDescription>
              </CardHeader>

              <CardContent className="pb-2">
                <div className="text-xs text-muted-foreground">Features</div>
                <ul className="mt-1 space-y-1">
                  {agent.features.map((feature, index) => (
                    <li key={index} className="text-sm flex items-center">
                      <Zap className="h-3 w-3 text-primary mr-1.5" />
                      {feature}
                    </li>
                  ))}
                </ul>

                <div className="flex items-center justify-between mt-4 text-sm">
                  <div className="text-muted-foreground">
                    By <span className="text-foreground">{agent.author}</span>
                  </div>
                  <div className="flex items-center text-muted-foreground">
                    <Download className="h-3 w-3 mr-1" />
                    {agent.installations.toLocaleString()}
                  </div>
                </div>
              </CardContent>

              <CardFooter className="pt-2">
                <Button className="w-full bg-primary text-black hover:bg-primary/90">
                  Use Template
                </Button>
              </CardFooter>
            </Card>
          ))}

          {filteredAgents.length === 0 && (
            <div className="col-span-full flex flex-col items-center justify-center py-12 text-center">
              <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium">No agents found</h3>
              <p className="text-muted-foreground">
                No agent templates match your search criteria.
              </p>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
