"use client";

import { MainLayout } from "@/components/layout/MainLayout";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import Link from "next/link";
import { ArrowLeft, Search, Settings, Edit, Play, Pause, PauseCircle, PlayCircle, MoreVertical, BarChart2, MessageSquare } from "lucide-react";

// Agent interface
interface Agent {
  id: number;
  name: string;
  description: string;
  status: string;
  interactions: number;
  successRate: number;
  averageResponseTime: string;
  lastUpdated: string;
  model: string;
}

const agentsData = [
  {
    id: 1,
    name: "Dria Support Agent",
    description: "A helpful assistant for Dria platform users",
    status: "active",
    interactions: 756,
    successRate: 92,
    averageResponseTime: "1.3s",
    lastUpdated: "2 days ago",
    model: "GEMINI-2.0-FLASH",
  },
  {
    id: 2,
    name: "Node Setup Assistant",
    description: "Specialized agent for node setup and configuration",
    status: "active",
    interactions: 342,
    successRate: 95,
    averageResponseTime: "0.9s",
    lastUpdated: "5 days ago",
    model: "LLAMA3.1:8B-INSTRUCT-Q4_K_M",
  },
  {
    id: 3,
    name: "API Documentation Helper",
    description: "Agent for API documentation and usage examples",
    status: "paused",
    interactions: 189,
    successRate: 87,
    averageResponseTime: "1.8s",
    lastUpdated: "1 week ago",
    model: "LLAMA3.2:1B-INSTRUCT-Q4_K_M",
  },
];

export default function AgentManagementPage() {
  return (
    <MainLayout>
      <div className="p-4 md:p-6 space-y-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between space-y-4 md:space-y-0">
          <div className="flex items-center">
            <Link href="/agents">
              <Button variant="ghost" size="icon" className="mr-2">
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <h1 className="text-2xl font-light">Agent Management</h1>
          </div>

          <div className="flex space-x-2 w-full md:w-auto">
            <div className="relative flex-1 md:w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input placeholder="Search agents..." className="pl-8 bg-background" />
            </div>
            <Link href="/agents/designer">
              <Button className="bg-primary text-black hover:bg-primary/90">
                New Agent
              </Button>
            </Link>
          </div>
        </div>

        <Tabs defaultValue="all" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="all">All Agents</TabsTrigger>
            <TabsTrigger value="active">Active</TabsTrigger>
            <TabsTrigger value="paused">Paused</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4">
            {agentsData.map(agent => (
              <AgentCard key={agent.id} agent={agent} />
            ))}
          </TabsContent>

          <TabsContent value="active" className="space-y-4">
            {agentsData.filter(agent => agent.status === "active").map(agent => (
              <AgentCard key={agent.id} agent={agent} />
            ))}
          </TabsContent>

          <TabsContent value="paused" className="space-y-4">
            {agentsData.filter(agent => agent.status === "paused").map(agent => (
              <AgentCard key={agent.id} agent={agent} />
            ))}
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
}

function AgentCard({ agent }: { agent: Agent }) {
  return (
    <Card className="bg-card border-border">
      <CardContent className="p-0">
        <div className="p-4 md:p-6 flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="flex items-center mb-2">
              <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center text-black font-medium mr-3">
                {agent.name.charAt(0)}
              </div>
              <div>
                <h3 className="font-medium">{agent.name}</h3>
                <p className="text-sm text-muted-foreground">{agent.description}</p>
              </div>
            </div>

            <div className="flex flex-wrap gap-4 mt-4">
              <div>
                <div className="text-xs text-muted-foreground mb-1">Status</div>
                <div className="flex items-center">
                  {agent.status === "active" ? (
                    <>
                      <div className="w-2 h-2 rounded-full bg-green-500 mr-1.5"></div>
                      <span className="text-sm">Active</span>
                    </>
                  ) : (
                    <>
                      <div className="w-2 h-2 rounded-full bg-amber-500 mr-1.5"></div>
                      <span className="text-sm">Paused</span>
                    </>
                  )}
                </div>
              </div>

              <div>
                <div className="text-xs text-muted-foreground mb-1">Model</div>
                <div className="text-sm">{agent.model}</div>
              </div>

              <div>
                <div className="text-xs text-muted-foreground mb-1">Interactions</div>
                <div className="text-sm">{agent.interactions.toLocaleString()}</div>
              </div>

              <div>
                <div className="text-xs text-muted-foreground mb-1">Success Rate</div>
                <div className="text-sm">{agent.successRate}%</div>
              </div>

              <div>
                <div className="text-xs text-muted-foreground mb-1">Response Time</div>
                <div className="text-sm">{agent.averageResponseTime}</div>
              </div>

              <div>
                <div className="text-xs text-muted-foreground mb-1">Last Updated</div>
                <div className="text-sm">{agent.lastUpdated}</div>
              </div>
            </div>
          </div>

          <div className="flex md:flex-col items-center md:items-stretch gap-2 self-end md:self-center">
            <Button variant="outline" size="icon">
              <BarChart2 className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon">
              <MessageSquare className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon">
              <Edit className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon">
              {agent.status === "active" ? (
                <PauseCircle className="h-4 w-4" />
              ) : (
                <PlayCircle className="h-4 w-4" />
              )}
            </Button>
            <Button variant="outline" size="icon">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
