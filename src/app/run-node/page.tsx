"use client";

import { MainLayout } from "@/components/layout/MainLayout";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { useState } from "react";
import { ChevronDown, ChevronUp, Copy, CheckCircle, Server, HardDrive, Globe, Database, Cpu, Shield } from "lucide-react";

// Simple accordion component
const Accordion = ({ title, children, icon }: { title: string, children: React.ReactNode, icon: React.ReactNode }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="border border-border rounded-md overflow-hidden mb-4">
      <button
        className="w-full px-4 py-3 flex items-center justify-between bg-background hover:bg-background/80 transition-colors"
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex items-center">
          {icon}
          <h3 className="text-sm font-medium ml-2">{title}</h3>
        </div>
        {isOpen ? (
          <ChevronUp className="h-5 w-5 text-primary" />
        ) : (
          <ChevronDown className="h-5 w-5 text-muted-foreground" />
        )}
      </button>
      {isOpen && (
        <div className="px-4 py-3 bg-background/50 border-t border-border">
          {children}
        </div>
      )}
    </div>
  );
};

// Code block with copy functionality
const CodeBlock = ({ code }: { code: string }) => {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(code.trim());
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="relative">
      <pre className="bg-black/80 p-4 rounded-md text-xs text-muted-foreground overflow-x-auto">
        <code>{code}</code>
      </pre>
      <button
        onClick={copyToClipboard}
        className="absolute top-2 right-2 p-1.5 rounded-md bg-background/10 hover:bg-background/20 transition-colors"
        aria-label="Copy code"
      >
        {copied ? (
          <CheckCircle className="h-4 w-4 text-green-500" />
        ) : (
          <Copy className="h-4 w-4 text-muted-foreground" />
        )}
      </button>
    </div>
  );
};

export default function RunNodePage() {
  return (
    <MainLayout>
      <div className="p-4 md:p-6 flex flex-col items-center max-w-4xl mx-auto">
        <Card className="w-full bg-card border-border">
          <CardHeader className="border-b border-border">
            <CardTitle className="text-xl font-normal">How to Run a Dria Node</CardTitle>
            <CardDescription>
              Complete guide to setting up and running a node on the Dria decentralized AI inference network
            </CardDescription>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-6 mb-8">
              <section className="mb-6">
                <h2 className="text-lg font-medium mb-2">Overview</h2>
                <p className="text-muted-foreground mb-4">
                  Running a Dria node allows you to participate in the decentralized AI inference network,
                  earn rewards, and contribute to a more resilient and distributed AI ecosystem. This guide
                  will walk you through the complete setup process.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div className="p-4 border border-border rounded-md bg-background/50 flex items-start">
                    <Server className="h-5 w-5 text-primary mr-3 mt-0.5" />
                    <div>
                      <h3 className="text-sm font-medium mb-1">Node Hosting</h3>
                      <p className="text-xs text-muted-foreground">
                        Host AI models and contribute computational resources to the network.
                      </p>
                    </div>
                  </div>

                  <div className="p-4 border border-border rounded-md bg-background/50 flex items-start">
                    <Database className="h-5 w-5 text-primary mr-3 mt-0.5" />
                    <div>
                      <h3 className="text-sm font-medium mb-1">Earn Rewards</h3>
                      <p className="text-xs text-muted-foreground">
                        Get compensated in $DRIA tokens for successfully completing inference tasks.
                      </p>
                    </div>
                  </div>

                  <div className="p-4 border border-border rounded-md bg-background/50 flex items-start">
                    <Globe className="h-5 w-5 text-primary mr-3 mt-0.5" />
                    <div>
                      <h3 className="text-sm font-medium mb-1">Network Participation</h3>
                      <p className="text-xs text-muted-foreground">
                        Join a global network of node operators supporting decentralized AI.
                      </p>
                    </div>
                  </div>
                </div>
              </section>

              <section>
                <h2 className="text-lg font-medium mb-2">Prerequisites</h2>
                <Accordion
                  title="Hardware Requirements"
                  icon={<HardDrive className="h-5 w-5 text-primary" />}
                >
                  <div className="space-y-4">
                    <p className="text-sm">
                      The hardware requirements depend on which AI models you plan to run. Here are the minimum specifications:
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="p-3 border border-border rounded-md">
                        <h4 className="text-sm font-medium mb-1">Basic Node (Smaller Models)</h4>
                        <ul className="text-xs text-muted-foreground space-y-1">
                          <li>• CPU: 4+ cores</li>
                          <li>• RAM: 16GB+</li>
                          <li>• Storage: 100GB SSD</li>
                          <li>• GPU: NVIDIA/AMD with 8GB+ VRAM</li>
                        </ul>
                      </div>

                      <div className="p-3 border border-border rounded-md">
                        <h4 className="text-sm font-medium mb-1">Advanced Node (Larger Models)</h4>
                        <ul className="text-xs text-muted-foreground space-y-1">
                          <li>• CPU: 8+ cores</li>
                          <li>• RAM: 32GB+</li>
                          <li>• Storage: 500GB SSD</li>
                          <li>• GPU: NVIDIA RTX 3080/4080 or better</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </Accordion>

                <Accordion
                  title="Software Requirements"
                  icon={<Cpu className="h-5 w-5 text-primary" />}
                >
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-start">
                      <span className="text-primary mr-2">•</span>
                      <span>
                        <strong>Operating System:</strong> Linux (Ubuntu 20.04 or later recommended), macOS, or Windows
                      </span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-primary mr-2">•</span>
                      <span>
                        <strong>Docker:</strong> Latest stable version
                      </span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-primary mr-2">•</span>
                      <span>
                        <strong>NVIDIA CUDA:</strong> 11.7+ (for GPU support)
                      </span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-primary mr-2">•</span>
                      <span>
                        <strong>Node.js:</strong> v18.0 or higher
                      </span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-primary mr-2">•</span>
                      <span>
                        <strong>Git:</strong> Latest version
                      </span>
                    </li>
                  </ul>

                  <div className="mt-4">
                    <p className="text-sm font-medium mb-2">Install prerequisites on Ubuntu:</p>
                    <CodeBlock code={`# Update package lists
sudo apt update && sudo apt upgrade -y

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Docker
sudo apt-get install docker.io -y
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker $USER

# Install CUDA (if using NVIDIA GPU)
wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2004/x86_64/cuda-ubuntu2004.pin
sudo mv cuda-ubuntu2004.pin /etc/apt/preferences.d/cuda-repository-pin-600
wget https://developer.download.nvidia.com/compute/cuda/11.7.1/local_installers/cuda-repo-ubuntu2004-11-7-local_11.7.1-515.65.01-1_amd64.deb
sudo dpkg -i cuda-repo-ubuntu2004-11-7-local_11.7.1-515.65.01-1_amd64.deb
sudo cp /var/cuda-repo-ubuntu2004-11-7-local/cuda-*-keyring.gpg /usr/share/keyrings/
sudo apt-get update
sudo apt-get -y install cuda`} />
                  </div>
                </Accordion>

                <Accordion
                  title="Wallet Setup"
                  icon={<Shield className="h-5 w-5 text-primary" />}
                >
                  <p className="text-sm mb-3">
                    You need a cryptocurrency wallet that supports the Dria network to receive rewards.
                    Follow these steps to set up a wallet:
                  </p>

                  <ol className="space-y-3 text-sm">
                    <li className="flex items-start">
                      <span className="bg-primary/20 text-primary rounded-full w-5 h-5 flex items-center justify-center mr-2 flex-shrink-0 mt-0.5">1</span>
                      <div>
                        <p className="font-medium">Create a wallet</p>
                        <p className="text-muted-foreground text-xs">
                          Install MetaMask or another compatible wallet and create a new wallet.
                        </p>
                      </div>
                    </li>

                    <li className="flex items-start">
                      <span className="bg-primary/20 text-primary rounded-full w-5 h-5 flex items-center justify-center mr-2 flex-shrink-0 mt-0.5">2</span>
                      <div>
                        <p className="font-medium">Add Dria Network</p>
                        <p className="text-muted-foreground text-xs">
                          Add the Dria network to your wallet with the following details:
                        </p>
                        <div className="mt-2 p-2 bg-background rounded-md text-xs">
                          <p><strong>Network Name:</strong> Dria Network</p>
                          <p><strong>RPC URL:</strong> https://rpc.dria.co</p>
                          <p><strong>Chain ID:</strong> 62522</p>
                          <p><strong>Symbol:</strong> DRIA</p>
                          <p><strong>Block Explorer:</strong> https://explorer.dria.co</p>
                        </div>
                      </div>
                    </li>

                    <li className="flex items-start">
                      <span className="bg-primary/20 text-primary rounded-full w-5 h-5 flex items-center justify-center mr-2 flex-shrink-0 mt-0.5">3</span>
                      <div>
                        <p className="font-medium">Secure your wallet</p>
                        <p className="text-muted-foreground text-xs">
                          Store your seed phrase offline in a secure location and never share it with anyone.
                        </p>
                      </div>
                    </li>
                  </ol>
                </Accordion>
              </section>

              <section>
                <h2 className="text-lg font-medium mb-2">Installation Steps</h2>

                <div className="space-y-4">
                  <div className="bg-background p-4 rounded-md border border-border">
                    <p className="text-sm font-medium mb-2 text-primary">Step 1: Clone the Repository</p>
                    <CodeBlock code={`# Clone the repository
git clone https://github.com/firstbatchxyz/dria
cd dria`} />
                  </div>

                  <div className="bg-background p-4 rounded-md border border-border">
                    <p className="text-sm font-medium mb-2 text-primary">Step 2: Install Dependencies</p>
                    <CodeBlock code={`# Install dependencies
npm install`} />
                  </div>

                  <div className="bg-background p-4 rounded-md border border-border">
                    <p className="text-sm font-medium mb-2 text-primary">Step 3: Configure Your Node</p>
                    <p className="text-xs text-muted-foreground mb-3">
                      Create a configuration file by copying the example and edit it with your wallet address and preferences.
                    </p>
                    <CodeBlock code={`# Create a configuration file
cp config.example.json config.json

# Edit your configuration - replace with your favorite editor
nano config.json`} />

                    <div className="mt-4">
                      <p className="text-xs text-muted-foreground mb-1">
                        Here's an example configuration:
                      </p>
                      <CodeBlock code={`{
  "wallet": "0xYourWalletAddressHere",
  "node": {
    "name": "My Dria Node",
    "models": ["llama3.1-8b", "gemini-2.0-flash"],
    "max_gpu_memory": "80%",
    "max_concurrent_tasks": 4,
    "log_level": "info"
  },
  "network": {
    "endpoint": "https://mainnet.dria.co",
    "telemetry": true,
    "heartbeat_interval": 60
  }
}`} />
                    </div>
                  </div>

                  <div className="bg-background p-4 rounded-md border border-border">
                    <p className="text-sm font-medium mb-2 text-primary">Step 4: Start Your Node</p>
                    <CodeBlock code={`# Start the node
npm run start

# Or run as a background service
npm run start:daemon`} />

                    <p className="text-xs text-muted-foreground mt-3">
                      Your node will download the required models and connect to the network. This may take some time
                      depending on your internet connection and the models you've configured.
                    </p>
                  </div>
                </div>
              </section>

              <section>
                <h2 className="text-lg font-medium mb-2">Advanced Configuration</h2>

                <Accordion
                  title="Monitoring & Maintenance"
                  icon={<Server className="h-5 w-5 text-primary" />}
                >
                  <p className="text-sm mb-3">
                    Monitor your node's performance and status using the following commands:
                  </p>

                  <CodeBlock code={`# Check node status
npm run status

# View logs
npm run logs

# Update to the latest version
git pull
npm install
npm run restart`} />

                  <p className="text-sm mt-3">
                    For production deployments, consider using a process manager like PM2:
                  </p>

                  <CodeBlock code={`# Install PM2
npm install -g pm2

# Start with PM2
pm2 start npm --name "dria-node" -- run start

# Set to auto-start on system boot
pm2 startup
pm2 save`} />
                </Accordion>

                <Accordion
                  title="Troubleshooting"
                  icon={<HardDrive className="h-5 w-5 text-primary" />}
                >
                  <div className="space-y-4">
                    <div>
                      <h4 className="text-sm font-medium mb-1">Connection Issues</h4>
                      <p className="text-xs text-muted-foreground mb-2">
                        If your node can't connect to the network:
                      </p>
                      <ul className="text-xs text-muted-foreground list-disc pl-5 space-y-1">
                        <li>Check your internet connection</li>
                        <li>Verify the network endpoint in your config</li>
                        <li>Ensure your firewall allows outgoing connections</li>
                        <li>Try restarting the node with `npm run restart`</li>
                      </ul>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium mb-1">Model Loading Failures</h4>
                      <p className="text-xs text-muted-foreground mb-2">
                        If models fail to load:
                      </p>
                      <ul className="text-xs text-muted-foreground list-disc pl-5 space-y-1">
                        <li>Verify you have enough disk space and GPU memory</li>
                        <li>Check the logs for specific error messages</li>
                        <li>Try with a smaller model first</li>
                        <li>Update to the latest version of the node software</li>
                      </ul>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium mb-1">Common Error Messages</h4>
                      <div className="space-y-2">
                        <div className="p-2 bg-background rounded-md">
                          <p className="text-xs text-red-500 font-mono mb-1">Error: CUDA out of memory</p>
                          <p className="text-xs text-muted-foreground">
                            Your GPU doesn't have enough memory. Try reducing `max_gpu_memory` in config or use a smaller model.
                          </p>
                        </div>

                        <div className="p-2 bg-background rounded-md">
                          <p className="text-xs text-red-500 font-mono mb-1">Error: Network connection refused</p>
                          <p className="text-xs text-muted-foreground">
                            Check your network settings and ensure the Dria network is operational.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </Accordion>
              </section>
            </div>

            <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
              <Link href="/">
                <Button variant="outline" className="border-muted w-full sm:w-auto">
                  Back to Dashboard
                </Button>
              </Link>

              <div className="flex gap-4">
                <Link href="https://docs.dria.co" target="_blank" rel="noopener noreferrer">
                  <Button variant="outline" className="border-primary text-primary w-full sm:w-auto">
                    Documentation
                  </Button>
                </Link>

                <Link href="https://github.com/firstbatchxyz/dria" target="_blank" rel="noopener noreferrer">
                  <Button className="bg-primary text-black hover:bg-primary/90 w-full sm:w-auto">
                    GitHub Repository
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
