"use client";

import { MainLayout } from "@/components/layout/MainLayout";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Layers, Search, Plus, Filter, DownloadCloud,
  FileText, Database, Settings, Network, Workflow,
  Users, Share2
} from "lucide-react";
import { useState } from "react";
import { OntologyVisualizer } from "@/components/ontology/OntologyVisualizer";
import { CollaborativeOntologyVisualizer } from "@/components/ontology/CollaborativeOntologyVisualizer";

const ontologyEntities = {
  semantic: [
    { id: "sem-1", name: "Customer", type: "Semantic", layer: "Business Concept", connections: 12 },
    { id: "sem-2", name: "Product", type: "Semantic", layer: "Business Concept", connections: 8 },
    { id: "sem-3", name: "Order", type: "Semantic", layer: "Business Concept", connections: 15 },
    { id: "sem-4", name: "Invoice", type: "Semantic", layer: "Business Concept", connections: 7 },
    { id: "sem-5", name: "Account", type: "Semantic", layer: "Business Concept", connections: 10 },
    { id: "sem-6", name: "Payment", type: "Semantic", layer: "Business Concept", connections: 9 },
    { id: "sem-7", name: "Shipment", type: "Semantic", layer: "Business Concept", connections: 6 },
    { id: "sem-8", name: "User", type: "Semantic", layer: "Business Concept", connections: 11 }
  ],
  kinetic: [
    { id: "kin-1", name: "CustomerRecord", type: "Kinetic", layer: "Domain Object", connections: 8 },
    { id: "kin-2", name: "ProductItem", type: "Kinetic", layer: "Domain Object", connections: 7 },
    { id: "kin-3", name: "OrderProcess", type: "Kinetic", layer: "Process", connections: 12 },
    { id: "kin-4", name: "InvoiceGenerator", type: "Kinetic", layer: "Service", connections: 5 },
    { id: "kin-5", name: "AccountManager", type: "Kinetic", layer: "Service", connections: 9 },
    { id: "kin-6", name: "PaymentProcessor", type: "Kinetic", layer: "Service", connections: 8 }
  ],
  dynamic: [
    { id: "dyn-1", name: "CustomerAPI", type: "Dynamic", layer: "Interface", connections: 7 },
    { id: "dyn-2", name: "ProductCatalog", type: "Dynamic", layer: "Interface", connections: 6 },
    { id: "dyn-3", name: "OrderWorkflow", type: "Dynamic", layer: "Workflow", connections: 10 },
    { id: "dyn-4", name: "PaymentGateway", type: "Dynamic", layer: "External System", connections: 5 }
  ]
};

// Sample data for ontology visualization
const mockOntologyData = {
  nodes: [
    { id: "customer", label: "Customer", type: "entity", layer: "semantic", radius: 8 },
    { id: "order", label: "Order", type: "entity", layer: "semantic", radius: 8 },
    { id: "product", label: "Product", type: "entity", layer: "semantic", radius: 8 },
    { id: "payment", label: "Payment", type: "entity", layer: "semantic", radius: 8 },
    { id: "invoice", label: "Invoice", type: "entity", layer: "semantic", radius: 8 },
    { id: "customerRecord", label: "CustomerRecord", type: "entity", layer: "kinetic", radius: 7 },
    { id: "orderProcess", label: "OrderProcess", type: "process", layer: "kinetic", radius: 7 },
    { id: "productCatalog", label: "ProductCatalog", type: "entity", layer: "kinetic", radius: 7 },
    { id: "paymentProcessor", label: "PaymentProcessor", type: "service", layer: "kinetic", radius: 7 },
    { id: "invoiceGenerator", label: "InvoiceGenerator", type: "service", layer: "kinetic", radius: 7 },
    { id: "customerAPI", label: "CustomerAPI", type: "interface", layer: "dynamic", radius: 6 },
    { id: "orderAPI", label: "OrderAPI", type: "interface", layer: "dynamic", radius: 6 },
    { id: "productAPI", label: "ProductAPI", type: "interface", layer: "dynamic", radius: 6 },
    { id: "paymentGateway", label: "PaymentGateway", type: "external", layer: "dynamic", radius: 6 },
  ],
  links: [
    { source: "customer", target: "customerRecord", type: "implements", strength: 1 },
    { source: "customerRecord", target: "customerAPI", type: "exposes", strength: 1 },
    { source: "customer", target: "order", type: "places", strength: 0.7 },
    { source: "order", target: "orderProcess", type: "processes", strength: 1 },
    { source: "orderProcess", target: "orderAPI", type: "exposes", strength: 1 },
    { source: "order", target: "invoice", type: "generates", strength: 0.7 },
    { source: "invoice", target: "invoiceGenerator", type: "implements", strength: 1 },
    { source: "order", target: "product", type: "contains", strength: 0.5 },
    { source: "product", target: "productCatalog", type: "catalogs", strength: 1 },
    { source: "productCatalog", target: "productAPI", type: "exposes", strength: 1 },
    { source: "order", target: "payment", type: "requires", strength: 0.7 },
    { source: "payment", target: "paymentProcessor", type: "processes", strength: 1 },
    { source: "paymentProcessor", target: "paymentGateway", type: "connects", strength: 1 },
  ]
};

export default function OntologyPage() {
  const [activeTab, setActiveTab] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [viewMode, setViewMode] = useState("grid"); // "grid" or "graph"
  const [collaborationEnabled, setCollaborationEnabled] = useState(true);

  // Filter entities based on search term
  const filteredEntities = {
    semantic: ontologyEntities.semantic.filter(entity =>
      entity.name.toLowerCase().includes(searchTerm.toLowerCase())
    ),
    kinetic: ontologyEntities.kinetic.filter(entity =>
      entity.name.toLowerCase().includes(searchTerm.toLowerCase())
    ),
    dynamic: ontologyEntities.dynamic.filter(entity =>
      entity.name.toLowerCase().includes(searchTerm.toLowerCase())
    )
  };

  // Combined entities for "All" tab
  const allEntities = [
    ...filteredEntities.semantic,
    ...filteredEntities.kinetic,
    ...filteredEntities.dynamic
  ];

  // Get active entities based on current tab
  const getActiveEntities = () => {
    switch(activeTab) {
      case "semantic": return filteredEntities.semantic;
      case "kinetic": return filteredEntities.kinetic;
      case "dynamic": return filteredEntities.dynamic;
      default: return allEntities;
    }
  };

  return (
    <MainLayout>
      <div className="p-4 md:p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-normal mb-1">Ontology Manager</h1>
            <p className="text-muted-foreground">
              Manage and visualize your organizational knowledge graph
            </p>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant={collaborationEnabled ? "default" : "outline"}
              size="sm"
              className="text-xs"
              onClick={() => setCollaborationEnabled(!collaborationEnabled)}
            >
              <Users className="h-4 w-4 mr-1" />
              {collaborationEnabled ? "Collaboration On" : "Collaboration Off"}
            </Button>
            <Button variant="outline" className="text-xs font-light border-muted px-4">
              <Share2 className="h-4 w-4 mr-1" />
              SHARE
            </Button>
            <Button variant="outline" className="text-xs font-light border-muted px-4">
              <DownloadCloud className="h-4 w-4 mr-1" />
              EXPORT
            </Button>
            <Button className="text-xs font-light px-4 text-primary-foreground">
              <Plus className="h-4 w-4 mr-1" />
              NEW ENTITY
            </Button>
          </div>
        </div>

        <Tabs defaultValue="entities" className="space-y-4">
          <TabsList>
            <TabsTrigger value="entities">Entities</TabsTrigger>
            <TabsTrigger value="visualization">Visualization</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="entities" className="space-y-4">
            <div className="flex flex-col md:flex-row gap-6">
              {/* Sidebar */}
              <div className="w-full md:w-64 lg:w-72 flex-shrink-0">
                <Card className="bg-card border-border">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base font-normal flex items-center">
                      <Layers className="h-5 w-5 mr-2 text-primary" />
                      Ontology Layers
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="relative mb-4">
                      <Input
                        className="pl-8 text-sm bg-background border-muted"
                        placeholder="Search entities..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                      <Search className="w-4 h-4 absolute left-2 top-3 text-muted-foreground" />
                    </div>

                    <div className="space-y-1">
                      <Button
                        variant={activeTab === "all" ? "default" : "ghost"}
                        className="w-full justify-start text-sm h-9 px-2"
                        onClick={() => setActiveTab("all")}
                      >
                        <Network className="h-4 w-4 mr-2" />
                        All Entities
                        <span className="ml-auto text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full">
                          {allEntities.length}
                        </span>
                      </Button>

                      <Button
                        variant={activeTab === "semantic" ? "default" : "ghost"}
                        className="w-full justify-start text-sm h-9 px-2"
                        onClick={() => setActiveTab("semantic")}
                      >
                        <FileText className="h-4 w-4 mr-2" />
                        Semantic Layer
                        <span className="ml-auto text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full">
                          {filteredEntities.semantic.length}
                        </span>
                      </Button>

                      <Button
                        variant={activeTab === "kinetic" ? "default" : "ghost"}
                        className="w-full justify-start text-sm h-9 px-2"
                        onClick={() => setActiveTab("kinetic")}
                      >
                        <Database className="h-4 w-4 mr-2" />
                        Kinetic Layer
                        <span className="ml-auto text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full">
                          {filteredEntities.kinetic.length}
                        </span>
                      </Button>

                      <Button
                        variant={activeTab === "dynamic" ? "default" : "ghost"}
                        className="w-full justify-start text-sm h-9 px-2"
                        onClick={() => setActiveTab("dynamic")}
                      >
                        <Workflow className="h-4 w-4 mr-2" />
                        Dynamic Layer
                        <span className="ml-auto text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full">
                          {filteredEntities.dynamic.length}
                        </span>
                      </Button>
                    </div>

                    <div className="border-t border-border mt-4 pt-4">
                      <p className="text-sm font-medium mb-2">Quick Filters</p>
                      <div className="space-y-1">
                        <Button variant="ghost" className="w-full justify-start text-sm h-8 px-2">
                          <span className="w-3 h-3 rounded-full bg-primary mr-2"></span>
                          Business Concepts
                        </Button>
                        <Button variant="ghost" className="w-full justify-start text-sm h-8 px-2">
                          <span className="w-3 h-3 rounded-full bg-chart-2 mr-2"></span>
                          Domain Objects
                        </Button>
                        <Button variant="ghost" className="w-full justify-start text-sm h-8 px-2">
                          <span className="w-3 h-3 rounded-full bg-chart-3 mr-2"></span>
                          Services
                        </Button>
                        <Button variant="ghost" className="w-full justify-start text-sm h-8 px-2">
                          <span className="w-3 h-3 rounded-full bg-chart-4 mr-2"></span>
                          Interfaces
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Main content */}
              <div className="flex-1">
                <Card className="bg-card border-border">
                  <CardHeader className="pb-2 flex flex-row items-center justify-between">
                    <CardTitle className="text-base font-normal">
                      {activeTab === "all" ? "All Entities" :
                      activeTab === "semantic" ? "Semantic Layer" :
                      activeTab === "kinetic" ? "Kinetic Layer" :
                      "Dynamic Layer"}
                    </CardTitle>
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <Filter className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <Settings className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {getActiveEntities().map((entity) => (
                        <Card key={entity.id} className="bg-background">
                          <CardContent className="p-4">
                            <div className="flex items-start justify-between">
                              <div>
                                <h3 className="font-medium">{entity.name}</h3>
                                <p className="text-xs text-muted-foreground">{entity.layer}</p>
                              </div>
                              <div className={`px-2 py-1 rounded-full text-xs
                                ${entity.type === "Semantic" ? "bg-primary/10 text-primary" :
                                entity.type === "Kinetic" ? "bg-chart-2/10 text-chart-2" :
                                "bg-chart-3/10 text-chart-3"}`}
                              >
                                {entity.type}
                              </div>
                            </div>
                            <div className="mt-3 pt-3 border-t border-border flex items-center justify-between">
                              <span className="text-xs">
                                <span className="text-muted-foreground">Connections:</span> {entity.connections}
                              </span>
                              <Button variant="ghost" size="sm" className="h-7 px-2 text-xs">
                                View Details
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>

                    {getActiveEntities().length === 0 && (
                      <div className="flex flex-col items-center justify-center py-12">
                        <Search className="h-12 w-12 text-muted-foreground opacity-20 mb-4" />
                        <h3 className="text-lg font-medium mb-1">No entities found</h3>
                        <p className="text-muted-foreground text-center max-w-md">
                          No ontology entities match your search criteria. Try adjusting your filters or create a new entity.
                        </p>
                        <Button className="mt-4">
                          <Plus className="h-4 w-4 mr-1" />
                          Create New Entity
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="visualization">
            {collaborationEnabled ? (
              <CollaborativeOntologyVisualizer
                data={mockOntologyData}
                height={600}
                projectId="enterprise-crm"
              />
            ) : (
              <OntologyVisualizer
                data={mockOntologyData}
                height={600}
              />
            )}

            <div className="mt-4 flex justify-between items-center">
              <p className="text-sm text-muted-foreground">
                {collaborationEnabled ?
                  "Collaborative mode: You can see other users' cursors and changes in real-time." :
                  "Solo mode: You're working alone. Enable collaboration to work with others."}
              </p>
              <Button
                variant={collaborationEnabled ? "default" : "outline"}
                size="sm"
                onClick={() => setCollaborationEnabled(!collaborationEnabled)}
              >
                <Users className="h-4 w-4 mr-1" />
                {collaborationEnabled ? "Disable Collaboration" : "Enable Collaboration"}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="templates">
            <Card>
              <CardHeader>
                <CardTitle>Ontology Templates</CardTitle>
                <CardDescription>Ready-to-use ontology patterns for common domains</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card className="bg-background">
                    <CardContent className="p-4">
                      <h3 className="font-medium mb-1">E-commerce Domain</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Includes customer, product, order, and payment entities with standard relationships.
                      </p>
                      <Button variant="outline" size="sm">Use Template</Button>
                    </CardContent>
                  </Card>

                  <Card className="bg-background">
                    <CardContent className="p-4">
                      <h3 className="font-medium mb-1">Healthcare Domain</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Patient, provider, appointment, and medical record entities with FHIR compliance.
                      </p>
                      <Button variant="outline" size="sm">Use Template</Button>
                    </CardContent>
                  </Card>

                  <Card className="bg-background">
                    <CardContent className="p-4">
                      <h3 className="font-medium mb-1">Financial Services</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Account, transaction, customer, and product entities with financial relationships.
                      </p>
                      <Button variant="outline" size="sm">Use Template</Button>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings">
            <Card>
              <CardHeader>
                <CardTitle>Ontology Settings</CardTitle>
                <CardDescription>Configure your ontology preferences</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium mb-1">Visualization Settings</h3>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <label className="text-sm">Default view mode</label>
                        <select className="bg-background border border-border rounded-md px-2 py-1">
                          <option>Graph</option>
                          <option>Grid</option>
                          <option>Tree</option>
                        </select>
                      </div>
                      <div className="flex items-center justify-between">
                        <label className="text-sm">Show relationships</label>
                        <input type="checkbox" className="rounded border-border bg-background h-4 w-4" defaultChecked />
                      </div>
                      <div className="flex items-center justify-between">
                        <label className="text-sm">Real-time collaboration</label>
                        <input
                          type="checkbox"
                          className="rounded border-border bg-background h-4 w-4"
                          checked={collaborationEnabled}
                          onChange={(e) => setCollaborationEnabled(e.target.checked)}
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium mb-1">System Integration</h3>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <label className="text-sm">Sync with codebase</label>
                        <input type="checkbox" className="rounded border-border bg-background h-4 w-4" defaultChecked />
                      </div>
                      <div className="flex items-center justify-between">
                        <label className="text-sm">Generate TypeScript types</label>
                        <input type="checkbox" className="rounded border-border bg-background h-4 w-4" defaultChecked />
                      </div>
                    </div>
                  </div>

                  <Button>Save Settings</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
}
