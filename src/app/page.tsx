import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-950 text-white">
      {/* Navigation */}
      <nav className="border-b border-gray-800">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="text-2xl font-bold">
              <span className="text-blue-400">ALIAS</span> HQ
            </div>
            <div className="flex gap-4">
              <Link href="/agents" className="px-4 py-2 bg-blue-600 rounded hover:bg-blue-700 transition-colors">
                Dashboard
              </Link>
              <Link href="/ontology" className="px-4 py-2 border border-gray-600 rounded hover:bg-gray-800 transition-colors">
                Explore
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, #3b82f6 1px, transparent 0)`,
            backgroundSize: '40px 40px'
          }} />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            {/* Main Headline */}
            <div className="space-y-6 mb-12">
              <h1 className="text-6xl md:text-8xl font-bold leading-tight">
                <span className="bg-gradient-to-r from-blue-400 via-blue-300 to-purple-400 bg-clip-text text-transparent">
                  ALIAS
                </span>
                <br />
                <span className="text-white">
                  Where Human Potential
                </span>
                <br />
                <span className="text-gray-400">
                  Meets AI Precision
                </span>
              </h1>
              
              <p className="text-xl md:text-2xl text-gray-300 leading-relaxed max-w-3xl mx-auto">
                The world's first organizational operating system that synthesizes 
                life and work into unified intelligence through the MOSAIC framework.
              </p>
            </div>

            {/* Key Metrics */}
            <div className="grid grid-cols-3 gap-8 py-8 border-y border-gray-800 mb-12 max-w-2xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-400 mb-2">45</div>
                <p className="text-sm text-gray-400">AI Agents Active</p>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-400 mb-2">321</div>
                <p className="text-sm text-gray-400">Organizations</p>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-400 mb-2">230</div>
                <p className="text-sm text-gray-400">Knowledge Entities</p>
              </div>
            </div>

            {/* Call to Actions */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Link href="/agents" className="px-8 py-4 bg-blue-600 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                Enter Command Center →
              </Link>
              <Link href="/ontology" className="px-8 py-4 border border-gray-600 rounded-lg font-semibold hover:bg-gray-800 transition-colors">
                Experience MOSAIC
              </Link>
            </div>

            {/* Features */}
            <div className="flex flex-wrap justify-center gap-6 text-sm text-gray-400">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-400 rounded-full mr-2" />
                11-Stage Lifecycle Management
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-purple-400 rounded-full mr-2" />
                Real-time Intelligence
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-400 rounded-full mr-2" />
                Life-Work Synthesis
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Features Preview */}
      <section className="py-20 bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-6">Organizational Intelligence Platform</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Transform your organization with AI-native capabilities that learn, adapt, 
              and evolve with your unique business needs.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="p-8 bg-gray-800 rounded-lg border border-gray-700">
              <h3 className="text-xl font-bold mb-4 text-blue-400">AI Agent Orchestra</h3>
              <p className="text-gray-300 mb-4">
                Deploy specialized AI agents that collaborate seamlessly across your entire organizational ecosystem.
              </p>
              <Link href="/agents" className="text-blue-400 hover:text-blue-300">
                Explore Agents →
              </Link>
            </div>

            <div className="p-8 bg-gray-800 rounded-lg border border-gray-700">
              <h3 className="text-xl font-bold mb-4 text-purple-400">MOSAIC Framework</h3>
              <p className="text-gray-300 mb-4">
                11-stage lifecycle management that guides your projects from conception to evolution.
              </p>
              <Link href="/projects" className="text-purple-400 hover:text-purple-300">
                View Framework →
              </Link>
            </div>

            <div className="p-8 bg-gray-800 rounded-lg border border-gray-700">
              <h3 className="text-xl font-bold mb-4 text-green-400">Real-time Intelligence</h3>
              <p className="text-gray-300 mb-4">
                Live insights and predictive analytics that anticipate needs before they become critical.
              </p>
              <Link href="/dashboard" className="text-green-400 hover:text-green-300">
                View Dashboard →
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-8 border-t border-gray-800">
        <div className="container mx-auto px-4 text-center">
          <p className="text-gray-400">
            © 2024 ALIAS Organization. The future of organizational intelligence.
          </p>
        </div>
      </footer>
    </div>
  );
}
