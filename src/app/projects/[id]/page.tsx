"use client";

import { MainLayout } from "@/components/layout/MainLayout";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON><PERSON>le, CardDescription, CardFooter } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  FileCode, ArrowLeft, CheckCircle, Clock, AlertTriangle,
  Calendar, User, Users, FileText, Code, BarChart2, Brain,
  Star, Share2, Edit, MessageSquare, Activity, Layers, Database,
  Eye, Download, Workflow, ShieldCheck, GitCommit, FileJson
} from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useState, useEffect } from "react";

// Project type interface
interface Project {
  id: string;
  name: string;
  description: string;
  longDescription: string;
  client: string;
  phase: string;
  progress: number;
  status: string;
  team?: Array<{
    id: number;
    name: string;
    role: string;
    avatar: string;
  }>;
  timeline?: Array<{
    phase: string;
    startDate: string;
    endDate: string;
    status: string;
  }>;
  [key: string]: unknown;
}

// Mock project data (would come from an API in a real app)
const projects = [
  {
    id: "1",
    name: "Enterprise CRM Portal",
    description: "Customer relationship management system with MOSAIC-powered ontology",
    longDescription: "A comprehensive customer relationship management portal built for Acme Corp. The system leverages ALIAS MOSAIC's ontology-driven development approach to create a flexible, scalable solution that integrates with existing enterprise systems while providing a modern user experience.",
    client: "Acme Corp",
    phase: "Implementation",
    progress: 75,
    status: "active",
    team: [
      { id: 1, name: "Sarah Chen", role: "Project Lead", avatar: "" },
      { id: 2, name: "Michael Rodriguez", role: "Technical Architect", avatar: "" },
      { id: 3, name: "David Park", role: "Frontend Developer", avatar: "" },
      { id: 4, name: "Lisa Johnson", role: "Backend Developer", avatar: "" },
      { id: 5, name: "Alex Mercer", role: "UX Designer", avatar: "" },
      { id: 6, name: "Samantha Wu", role: "Data Scientist", avatar: "" },
      { id: 7, name: "Thomas Reid", role: "QA Engineer", avatar: "" },
      { id: 8, name: "Olivia Martinez", role: "Business Analyst", avatar: "" }
    ],
    dueDate: "May 30, 2025",
    startDate: "Jan 15, 2025",
    priority: "High",
    aiAssistance: 85,
    budget: {
      allocated: 250000,
      spent: 180000,
      projected: 240000,
      currency: "USD"
    },
    keyMetrics: {
      velocity: 92,
      defects: 12,
      testCoverage: 87,
      stakeholderSatisfaction: 4.8
    },
    recentActivity: [
      { id: 1, type: "code", action: "Frontend component library update", user: "David Park", time: "2 hours ago" },
      { id: 2, type: "meeting", action: "Weekly progress review with client", user: "Sarah Chen", time: "Yesterday" },
      { id: 3, type: "ontology", action: "Customer entity schema update", user: "AI Assistant", time: "2 days ago" },
      { id: 4, type: "testing", action: "API integration tests completed", user: "Thomas Reid", time: "3 days ago" },
      { id: 5, type: "deployment", action: "Staging environment update", user: "Lisa Johnson", time: "5 days ago" }
    ],
    ontologyStats: {
      totalEntities: 86,
      relationships: 124,
      semanticEntities: 28,
      kineticEntities: 42,
      dynamicEntities: 16
    },
    agentContributions: {
      codeGeneration: 68,
      documentationCreation: 92,
      testCaseGeneration: 74,
      requirementsAnalysis: 85,
      dataModelGeneration: 90
    },
    risks: [
      { id: 1, name: "API integration delay", severity: "medium", mitigation: "Added resources to integration team" },
      { id: 2, name: "Performance concerns", severity: "low", mitigation: "Implemented caching strategy" }
    ]
  }
];

// Function to get project by ID
function getProject(id: string) {
  return projects.find(p => p.id === id) || null;
}

// Function to render status badge
function getStatusBadge(status: string) {
  switch (status) {
    case "active":
      return (
        <div className="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs flex items-center">
          <Clock className="h-3 w-3 mr-1" />
          Active
        </div>
      );
    case "completed":
      return (
        <div className="px-2 py-1 bg-green-500/10 text-green-500 rounded-full text-xs flex items-center">
          <CheckCircle className="h-3 w-3 mr-1" />
          Completed
        </div>
      );
    case "pending":
      return (
        <div className="px-2 py-1 bg-yellow-500/10 text-yellow-500 rounded-full text-xs flex items-center">
          <Clock className="h-3 w-3 mr-1" />
          Pending
        </div>
      );
    case "issue":
      return (
        <div className="px-2 py-1 bg-red-500/10 text-red-500 rounded-full text-xs flex items-center">
          <AlertTriangle className="h-3 w-3 mr-1" />
          Issue
        </div>
      );
    default:
      return null;
  }
}

// Function to get phase color
function getPhaseColor(phase: string) {
  switch (phase) {
    case "Initiation": return "bg-chart-1";
    case "Planning": return "bg-chart-2";
    case "Design": return "bg-chart-3";
    case "Implementation": return "bg-chart-4";
    case "Validation": return "bg-chart-5";
    case "Handover": return "bg-green-500";
    case "Support": return "bg-blue-400";
    default: return "bg-muted";
  }
}

export default function ProjectDetailPage() {
  const params = useParams();
  const [project, setProject] = useState<any>(null);

  useEffect(() => {
    if (params?.id) {
      const projectData = getProject(params.id as string);
      setProject(projectData);
    }
  }, [params?.id]);

  if (!project) {
    return (
      <MainLayout>
        <div className="p-4 md:p-6 flex flex-col items-center justify-center min-h-[50vh]">
          <AlertTriangle className="h-16 w-16 text-muted-foreground mb-4" />
          <h1 className="text-2xl font-medium mb-2">Project Not Found</h1>
          <p className="text-muted-foreground mb-6">The project you're looking for doesn't exist or you don't have access to it.</p>
          <Link href="/projects">
            <Button>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Projects
            </Button>
          </Link>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="p-4 md:p-6">
        {/* Project header */}
        <div className="flex flex-col md:flex-row md:items-start md:justify-between mb-6 gap-4">
          <div>
            <div className="flex items-center mb-2">
              <Link href="/projects" className="mr-4">
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <ArrowLeft className="h-4 w-4" />
                </Button>
              </Link>
              <h1 className="text-2xl font-normal">{project.name}</h1>
              {getStatusBadge(project.status)}
            </div>
            <p className="text-muted-foreground max-w-2xl">
              {project.longDescription || project.description}
            </p>
          </div>

          <div className="flex items-center space-x-2">
            <Button variant="outline" className="text-xs font-light border-muted">
              <Share2 className="h-4 w-4 mr-1" />
              Share
            </Button>
            <Button variant="outline" className="text-xs font-light border-muted">
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </Button>
            <Button className="text-xs font-light text-primary-foreground">
              <Brain className="h-4 w-4 mr-1" />
              AI Assist
            </Button>
          </div>
        </div>

        {/* Project overview row */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
          <InfoCard
            icon={<Calendar className="h-5 w-5 text-primary" />}
            title="Timeline"
            value={`${project.startDate} - ${project.dueDate}`}
            subtitle={`Phase: ${project.phase}`}
            indicator={<div className={`w-2 h-2 rounded-full ${getPhaseColor(project.phase)}`}></div>}
          />

          <InfoCard
            icon={<Users className="h-5 w-5 text-primary" />}
            title="Team"
            value={`${project.team?.length || 0} Members`}
            subtitle={`Lead: ${project.team?.find((t: any) => t.role.includes("Lead"))?.name || "Unassigned"}`}
          />

          <InfoCard
            icon={<User className="h-5 w-5 text-primary" />}
            title="Client"
            value={project.client}
            subtitle={`Priority: ${project.priority}`}
          />

          <InfoCard
            icon={<Brain className="h-5 w-5 text-primary" />}
            title="AI Assistance"
            value={`${project.aiAssistance}%`}
            subtitle="Automated workflows"
            indicator={
              <div className="w-full h-1 bg-secondary rounded-full overflow-hidden">
                <div
                  className="h-full bg-primary"
                  style={{ width: `${project.aiAssistance}%` }}
                ></div>
              </div>
            }
          />

          <InfoCard
            icon={<BarChart2 className="h-5 w-5 text-primary" />}
            title="Progress"
            value={`${project.progress}% Complete`}
            subtitle={`Budget: $${(project.budget as { spent?: number; allocated?: number })?.spent?.toLocaleString() || '0'} / $${(project.budget as { spent?: number; allocated?: number })?.allocated?.toLocaleString() || '0'}`}
            indicator={
              <div className="w-full h-1 bg-secondary rounded-full overflow-hidden">
                <div
                  className="h-full bg-primary"
                  style={{ width: `${project.progress}%` }}
                ></div>
              </div>
            }
          />
        </div>

        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="team">Team</TabsTrigger>
            <TabsTrigger value="ontology">Ontology</TabsTrigger>
            <TabsTrigger value="ai-agents">AI Agents</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Key metrics */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base font-normal flex items-center">
                    <BarChart2 className="h-5 w-5 mr-2 text-primary" />
                    Key Metrics
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <MetricItem
                      label="Sprint Velocity"
                      value={(project.keyMetrics as { velocity?: number })?.velocity || 0}
                      icon={<Activity className="h-4 w-4 text-primary" />}
                      max={100}
                    />
                    <MetricItem
                      label="Open Defects"
                      value={(project.keyMetrics as { defects?: number })?.defects || 0}
                      icon={<AlertTriangle className="h-4 w-4 text-yellow-500" />}
                      max={50}
                      inverse
                    />
                    <MetricItem
                      label="Test Coverage"
                      value={(project.keyMetrics as { testCoverage?: number })?.testCoverage || 0}
                      icon={<ShieldCheck className="h-4 w-4 text-green-500" />}
                      max={100}
                    />
                    <MetricItem
                      label="Stakeholder Satisfaction"
                      value={((project.keyMetrics as { stakeholderSatisfaction?: number })?.stakeholderSatisfaction || 0) * 20}
                      icon={<Star className="h-4 w-4 text-yellow-500" />}
                      max={100}
                      suffix={`${(project.keyMetrics as { stakeholderSatisfaction?: number })?.stakeholderSatisfaction || 0}/5`}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Recent Activity */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base font-normal flex items-center">
                    <Activity className="h-5 w-5 mr-2 text-primary" />
                    Recent Activity
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {(project.recentActivity as any[])?.map((activity: any) => (
                      <div key={activity.id} className="flex items-start space-x-3 pb-3 border-b border-border last:border-0">
                        <div className="bg-primary/10 p-1.5 rounded text-primary">
                          {activity.type === "code" && <Code className="h-4 w-4" />}
                          {activity.type === "meeting" && <MessageSquare className="h-4 w-4" />}
                          {activity.type === "ontology" && <Database className="h-4 w-4" />}
                          {activity.type === "testing" && <ShieldCheck className="h-4 w-4" />}
                          {activity.type === "deployment" && <GitCommit className="h-4 w-4" />}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium">{activity.action}</p>
                          <div className="flex justify-between text-xs text-muted-foreground">
                            <span>{activity.user}</span>
                            <span>{activity.time}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Risks and Issues */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base font-normal flex items-center">
                    <AlertTriangle className="h-5 w-5 mr-2 text-primary" />
                    Risks & Issues
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {(project.risks as any[])?.length === 0 ? (
                    <div className="flex flex-col items-center justify-center p-6 text-center">
                      <CheckCircle className="h-12 w-12 text-green-500 mb-2 opacity-50" />
                      <p className="text-muted-foreground">No active risks or issues</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {(project.risks as any[])?.map((risk: any) => (
                        <div key={risk.id} className="border border-border rounded-md p-3">
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="font-medium">{risk.name}</h3>
                            <span className={`text-xs px-2 py-0.5 rounded-full ${
                              risk.severity === "high" ? "bg-red-500/10 text-red-500" :
                              risk.severity === "medium" ? "bg-yellow-500/10 text-yellow-500" :
                              "bg-green-500/10 text-green-500"
                            }`}>
                              {risk.severity}
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            Mitigation: {risk.mitigation}
                          </p>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="team">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base font-normal flex items-center">
                  <Users className="h-5 w-5 mr-2 text-primary" />
                  Team Members
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {(project.team as any[])?.map((member: any) => (
                    <div key={member.id} className="border border-border rounded-md p-4 flex flex-col items-center">
                      <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center text-primary mb-3">
                        {member.avatar ? (
                          <img src={member.avatar} alt={member.name} className="w-full h-full rounded-full object-cover" />
                        ) : (
                          <span className="text-xl font-medium">{member.name.charAt(0)}</span>
                        )}
                      </div>
                      <h3 className="font-medium text-center">{member.name}</h3>
                      <p className="text-sm text-muted-foreground text-center">{member.role}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="ontology">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base font-normal flex items-center">
                  <Layers className="h-5 w-5 mr-2 text-primary" />
                  Project Ontology
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium mb-3">Ontology Structure</h3>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="w-3 h-3 rounded-full bg-primary mr-2"></div>
                          <span className="text-sm">Semantic Layer</span>
                        </div>
                        <span className="font-medium">{project.ontologyStats.semanticEntities}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="w-3 h-3 rounded-full bg-chart-2 mr-2"></div>
                          <span className="text-sm">Kinetic Layer</span>
                        </div>
                        <span className="font-medium">{project.ontologyStats.kineticEntities}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="w-3 h-3 rounded-full bg-chart-3 mr-2"></div>
                          <span className="text-sm">Dynamic Layer</span>
                        </div>
                        <span className="font-medium">{project.ontologyStats.dynamicEntities}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="w-3 h-3 rounded-full bg-chart-4 mr-2"></div>
                          <span className="text-sm">Relationships</span>
                        </div>
                        <span className="font-medium">{project.ontologyStats.relationships}</span>
                      </div>
                    </div>

                    <div className="mt-6">
                      <Button className="mr-2">
                        <Eye className="h-4 w-4 mr-1" />
                        View Ontology
                      </Button>
                      <Button variant="outline">
                        <Download className="h-4 w-4 mr-1" />
                        Export
                      </Button>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium mb-3">Key Entities</h3>
                    <div className="bg-black/80 p-4 rounded-md text-xs text-muted-foreground overflow-x-auto">
                      <pre>{JSON.stringify({
                        "Customer": {
                          "type": "SemanticEntity",
                          "properties": ["id", "name", "contact", "accountLevel"],
                          "relationships": ["has_orders", "has_invoices"]
                        },
                        "Order": {
                          "type": "SemanticEntity",
                          "properties": ["id", "date", "amount", "status"],
                          "relationships": ["belongs_to_customer", "contains_products"]
                        },
                        "CustomerRecord": {
                          "type": "KineticEntity",
                          "properties": ["lastUpdated", "dataSource"],
                          "operations": ["update", "retrieve", "archive"]
                        }
                      }, null, 2)}</pre>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="ai-agents">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base font-normal flex items-center">
                  <Brain className="h-5 w-5 mr-2 text-primary" />
                  AI Agent Contributions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium mb-3">Agent Contribution by Area</h3>
                    <div className="space-y-4">
                      <MetricItem
                        label="Code Generation"
                        value={project.agentContributions.codeGeneration}
                        icon={<Code className="h-4 w-4 text-primary" />}
                        max={100}
                      />
                      <MetricItem
                        label="Documentation"
                        value={project.agentContributions.documentationCreation}
                        icon={<FileText className="h-4 w-4 text-primary" />}
                        max={100}
                      />
                      <MetricItem
                        label="Test Case Generation"
                        value={project.agentContributions.testCaseGeneration}
                        icon={<ShieldCheck className="h-4 w-4 text-primary" />}
                        max={100}
                      />
                      <MetricItem
                        label="Requirements Analysis"
                        value={project.agentContributions.requirementsAnalysis}
                        icon={<FileJson className="h-4 w-4 text-primary" />}
                        max={100}
                      />
                      <MetricItem
                        label="Data Model Generation"
                        value={project.agentContributions.dataModelGeneration}
                        icon={<Database className="h-4 w-4 text-primary" />}
                        max={100}
                      />
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium mb-3">Agent Operations</h3>
                    <div className="space-y-3">
                      <div className="border border-border rounded-md p-4">
                        <div className="flex items-center">
                          <div className="p-2 bg-primary/10 text-primary rounded-md mr-3">
                            <FileJson className="h-5 w-5" />
                          </div>
                          <div>
                            <h4 className="font-medium">Requirements Agent</h4>
                            <p className="text-sm text-muted-foreground">
                              Processes client requirements and generates ontology entities
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="border border-border rounded-md p-4">
                        <div className="flex items-center">
                          <div className="p-2 bg-primary/10 text-primary rounded-md mr-3">
                            <Code className="h-5 w-5" />
                          </div>
                          <div>
                            <h4 className="font-medium">Code Assistant</h4>
                            <p className="text-sm text-muted-foreground">
                              Generates code based on ontology models and specifications
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="border border-border rounded-md p-4">
                        <div className="flex items-center">
                          <div className="p-2 bg-primary/10 text-primary rounded-md mr-3">
                            <ShieldCheck className="h-5 w-5" />
                          </div>
                          <div>
                            <h4 className="font-medium">QA Tester</h4>
                            <p className="text-sm text-muted-foreground">
                              Creates and runs test cases for components and integrations
                            </p>
                          </div>
                        </div>
                      </div>

                      <Button className="w-full">
                        <Brain className="h-4 w-4 mr-1" />
                        Create Custom Agent
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documents">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base font-normal flex items-center">
                  <FileText className="h-5 w-5 mr-2 text-primary" />
                  Project Documents
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <DocumentCard
                    title="Project Specification"
                    type="document"
                    updatedAt="May 15, 2025"
                    updatedBy="Sarah Chen"
                  />
                  <DocumentCard
                    title="System Architecture"
                    type="diagram"
                    updatedAt="May 10, 2025"
                    updatedBy="Michael Rodriguez"
                  />
                  <DocumentCard
                    title="API Documentation"
                    type="api"
                    updatedAt="May 8, 2025"
                    updatedBy="Lisa Johnson"
                  />
                  <DocumentCard
                    title="User Interface Mockups"
                    type="design"
                    updatedAt="May 5, 2025"
                    updatedBy="Alex Mercer"
                  />
                  <DocumentCard
                    title="Database Schema"
                    type="database"
                    updatedAt="May 3, 2025"
                    updatedBy="David Park"
                  />
                  <DocumentCard
                    title="Test Plan"
                    type="test"
                    updatedAt="May 1, 2025"
                    updatedBy="Thomas Reid"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
}

// Helper component for info cards
function InfoCard({ icon, title, value, subtitle, indicator = null }: {
  icon: React.ReactNode;
  title: string;
  value: string;
  subtitle: string;
  indicator?: React.ReactNode | null;
}) {
  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-start mb-2">
          <div className="p-2 rounded-md bg-primary/10 mr-3">
            {icon}
          </div>
          <div>
            <h3 className="text-sm text-muted-foreground">{title}</h3>
            <div className="font-medium">{value}</div>
          </div>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-xs text-muted-foreground">{subtitle}</span>
          {indicator}
        </div>
      </CardContent>
    </Card>
  );
}

// Helper component for metrics with progress bars
function MetricItem({ label, value, max = 100, icon, suffix = null, inverse = false }: {
  label: string;
  value: number;
  max?: number;
  icon: React.ReactNode;
  suffix?: string | null;
  inverse?: boolean;
}) {
  const percentage = Math.min(100, Math.max(0, (value / max) * 100));

  return (
    <div className="space-y-1">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          {icon && <span className="mr-2">{icon}</span>}
          <span className="text-sm">{label}</span>
        </div>
        <span className="text-sm font-medium">{suffix || `${value}%`}</span>
      </div>
      <div className="h-2 w-full bg-secondary rounded-full overflow-hidden">
        <div
          className={`h-full ${inverse ? 'bg-yellow-500' : 'bg-primary'}`}
          style={{ width: `${percentage}%` }}
        ></div>
      </div>
    </div>
  );
}

// Helper component for document cards
function DocumentCard({ title, type, updatedAt, updatedBy }: any) {
  const getIcon = () => {
    switch (type) {
      case "document": return <FileText className="h-6 w-6" />;
      case "diagram": return <Workflow className="h-6 w-6" />;
      case "api": return <Code className="h-6 w-6" />;
      case "design": return <FileCode className="h-6 w-6" />;
      case "database": return <Database className="h-6 w-6" />;
      case "test": return <ShieldCheck className="h-6 w-6" />;
      default: return <FileText className="h-6 w-6" />;
    }
  };

  return (
    <div className="border border-border rounded-md p-4 hover:border-primary/50 transition-colors">
      <div className="flex items-center space-x-3">
        <div className="p-2 bg-primary/10 text-primary rounded-md">
          {getIcon()}
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="font-medium truncate">{title}</h3>
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>{updatedBy}</span>
            <span>{updatedAt}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
