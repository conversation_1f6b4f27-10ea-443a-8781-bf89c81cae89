"use client";

import { MainLayout } from "@/components/layout/MainLayout";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON><PERSON>le, CardDescription, CardFooter } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  FileCode, Search, Plus, Calendar, CheckCircle,
  Clock, AlertTriangle, User, Users, FileText,
  Code, BarChart2, <PERSON>, <PERSON>
} from "lucide-react";
import Link from "next/link";

// Mock project data
const projects = [
  {
    id: 1,
    name: "Enterprise CRM Portal",
    description: "Customer relationship management system with MOSAIC-powered ontology",
    client: "Acme Corp",
    phase: "Implementation",
    progress: 75,
    status: "active",
    team: 8,
    dueDate: "May 30, 2025",
    priority: "High",
    aiAssistance: 85
  },
  {
    id: 2,
    name: "Healthcare Analytics Platform",
    description: "AI-driven healthcare data analysis platform with FHIR integration",
    client: "MedTech Inc",
    phase: "Design",
    progress: 40,
    status: "active",
    team: 6,
    dueDate: "July 15, 2025",
    priority: "Critical",
    aiAssistance: 92
  },
  {
    id: 3,
    name: "Financial Services Dashboard",
    description: "Real-time financial data visualization and analytics dashboard",
    client: "Global Finance",
    phase: "Planning",
    progress: 20,
    status: "active",
    team: 5,
    dueDate: "August 10, 2025",
    priority: "Medium",
    aiAssistance: 78
  },
  {
    id: 4,
    name: "E-commerce Marketplace",
    description: "Multi-vendor e-commerce platform with integrated payment processing",
    client: "ShopDirect",
    phase: "Validation",
    progress: 90,
    status: "active",
    team: 10,
    dueDate: "June 5, 2025",
    priority: "High",
    aiAssistance: 65
  },
  {
    id: 5,
    name: "Government Portal Redesign",
    description: "User-centered redesign of government services portal",
    client: "State Dept",
    phase: "Handover",
    progress: 95,
    status: "pending",
    team: 7,
    dueDate: "May 25, 2025",
    priority: "Medium",
    aiAssistance: 70
  },
  {
    id: 6,
    name: "Logistics Management System",
    description: "End-to-end logistics and supply chain management application",
    client: "Global Shipping",
    phase: "Support",
    progress: 100,
    status: "completed",
    team: 4,
    dueDate: "Completed",
    priority: "Closed",
    aiAssistance: 80
  }
];

// Function to render status badge
function getStatusBadge(status: string) {
  switch (status) {
    case "active":
      return (
        <div className="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs flex items-center">
          <Clock className="h-3 w-3 mr-1" />
          Active
        </div>
      );
    case "completed":
      return (
        <div className="px-2 py-1 bg-green-500/10 text-green-500 rounded-full text-xs flex items-center">
          <CheckCircle className="h-3 w-3 mr-1" />
          Completed
        </div>
      );
    case "pending":
      return (
        <div className="px-2 py-1 bg-yellow-500/10 text-yellow-500 rounded-full text-xs flex items-center">
          <Clock className="h-3 w-3 mr-1" />
          Pending
        </div>
      );
    case "issue":
      return (
        <div className="px-2 py-1 bg-red-500/10 text-red-500 rounded-full text-xs flex items-center">
          <AlertTriangle className="h-3 w-3 mr-1" />
          Issue
        </div>
      );
    default:
      return null;
  }
}

// Function to get phase color
function getPhaseColor(phase: string) {
  switch (phase) {
    case "Initiation": return "bg-chart-1 text-white";
    case "Planning": return "bg-chart-2 text-white";
    case "Design": return "bg-chart-3 text-white";
    case "Implementation": return "bg-chart-4 text-white";
    case "Validation": return "bg-chart-5 text-white";
    case "Handover": return "bg-green-500 text-white";
    case "Support": return "bg-blue-400 text-white";
    default: return "bg-muted text-muted-foreground";
  }
}

export default function ProjectsPage() {
  return (
    <MainLayout>
      <div className="p-4 md:p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-normal mb-1">Projects</h1>
            <p className="text-muted-foreground">
              Manage your projects across the MOSAIC lifecycle
            </p>
          </div>

          <div className="flex items-center space-x-2">
            <div className="relative">
              <Input
                className="pl-8 bg-background border-muted w-[200px] md:w-[300px]"
                placeholder="Search projects..."
              />
              <Search className="w-4 h-4 absolute left-2 top-3 text-muted-foreground" />
            </div>
            <Button className="text-primary-foreground">
              <Plus className="h-4 w-4 mr-1" />
              New Project
            </Button>
          </div>
        </div>

        <Tabs defaultValue="all" className="mb-6">
          <TabsList className="grid grid-cols-4 md:w-[400px]">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="active">Active</TabsTrigger>
            <TabsTrigger value="pending">Pending</TabsTrigger>
            <TabsTrigger value="completed">Completed</TabsTrigger>
          </TabsList>

          <TabsContent value="all">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {projects.map((project) => (
                <ProjectCard key={project.id} project={project} />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="active">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {projects.filter(p => p.status === "active").map((project) => (
                <ProjectCard key={project.id} project={project} />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="pending">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {projects.filter(p => p.status === "pending").map((project) => (
                <ProjectCard key={project.id} project={project} />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="completed">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {projects.filter(p => p.status === "completed").map((project) => (
                <ProjectCard key={project.id} project={project} />
              ))}
            </div>
          </TabsContent>
        </Tabs>

        <div className="mt-10">
          <h2 className="text-xl font-normal mb-4">MOSAIC Project Lifecycle</h2>
          <div className="grid grid-cols-1 md:grid-cols-7 gap-2 md:gap-4">
            <LifecyclePhaseCard
              title="Initiation & Discovery"
              number="1"
              color="bg-chart-1"
              description="Capture requirements, establish project scope"
              icon={<FileText className="h-8 w-8" />}
            />
            <LifecyclePhaseCard
              title="Planning & Framework"
              number="2"
              color="bg-chart-2"
              description="Set up sprints, define ontology backbone"
              icon={<Calendar className="h-8 w-8" />}
            />
            <LifecyclePhaseCard
              title="Design & Architecture"
              number="3"
              color="bg-chart-3"
              description="Technical design, schemas, API endpoints"
              icon={<Code className="h-8 w-8" />}
            />
            <LifecyclePhaseCard
              title="Implementation"
              number="4"
              color="bg-chart-4"
              description="Iterative development with MOSAIC AI assistance"
              icon={<FileCode className="h-8 w-8" />}
            />
            <LifecyclePhaseCard
              title="Validation & Testing"
              number="5"
              color="bg-chart-5"
              description="QA, performance testing, compliance checks"
              icon={<CheckCircle className="h-8 w-8" />}
            />
            <LifecyclePhaseCard
              title="Handover & Training"
              number="6"
              color="bg-green-500"
              description="Knowledge base, documentation, training"
              icon={<Users className="h-8 w-8" />}
            />
            <LifecyclePhaseCard
              title="Support & Optimization"
              number="7"
              color="bg-blue-400"
              description="Performance monitoring, predictive analytics"
              icon={<BarChart2 className="h-8 w-8" />}
            />
          </div>
        </div>
      </div>
    </MainLayout>
  );
}

// Project card component
function ProjectCard({ project }: any) {
  return (
    <Card className="bg-card border-border overflow-hidden">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div className="space-y-1">
            <CardTitle className="text-lg font-normal">{project.name}</CardTitle>
            <CardDescription className="line-clamp-2">{project.description}</CardDescription>
          </div>
          {getStatusBadge(project.status)}
        </div>
      </CardHeader>
      <CardContent className="pb-0">
        <div className="space-y-4">
          <div className="w-full bg-secondary rounded-full h-2 overflow-hidden">
            <div
              className="h-full bg-primary"
              style={{ width: `${project.progress}%` }}
            ></div>
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center">
              <User className="h-4 w-4 mr-2 text-muted-foreground" />
              <span className="text-muted-foreground">Client:</span>
              <span className="ml-1 truncate">{project.client}</span>
            </div>
            <div className="flex items-center">
              <Users className="h-4 w-4 mr-2 text-muted-foreground" />
              <span className="text-muted-foreground">Team:</span>
              <span className="ml-1">{project.team} members</span>
            </div>
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
              <span className="text-muted-foreground">Due:</span>
              <span className="ml-1">{project.dueDate}</span>
            </div>
            <div className="flex items-center">
              <Brain className="h-4 w-4 mr-2 text-muted-foreground" />
              <span className="text-muted-foreground">AI:</span>
              <span className="ml-1">{project.aiAssistance}%</span>
            </div>
          </div>

          <div className="flex items-center">
            <div className={`px-2 py-1 text-xs rounded ${getPhaseColor(project.phase)}`}>
              Phase: {project.phase}
            </div>
            <div className="ml-auto">
              <Star className="h-4 w-4 text-yellow-500" />
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between pt-4 pb-4 border-t border-border mt-4">
        <Button variant="ghost" className="text-xs h-8 font-light">
          View Details
        </Button>
        <Button variant="outline" className="text-xs h-8 font-light">
          Open Dashboard
        </Button>
      </CardFooter>
    </Card>
  );
}

// Lifecycle phase card component
function LifecyclePhaseCard({ title, number, color, description, icon }: any) {
  return (
    <Card className="bg-card border-border overflow-hidden relative">
      <div className={`h-2 ${color} w-full absolute top-0 left-0`} />
      <CardContent className="pt-6 pb-4 text-center">
        <div className={`w-10 h-10 rounded-full ${color} mx-auto flex items-center justify-center mb-3`}>
          {icon}
        </div>
        <div className={`w-7 h-7 rounded-full border-2 ${color} text-foreground flex items-center justify-center mx-auto mb-2`}>
          {number}
        </div>
        <h3 className="font-medium text-sm mb-1">{title}</h3>
        <p className="text-xs text-muted-foreground">{description}</p>
      </CardContent>
    </Card>
  );
}
