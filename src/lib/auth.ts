import { betterAuth } from "better-auth";

export const auth = betterAuth({
  // Temporarily use in-memory database until Convex adapter is properly configured
  database: {
    provider: "sqlite",
    url: ":memory:",
  },
  
  emailAndPassword: {
    enabled: true,
    autoSignIn: true,
  },
  
  socialProviders: {
    // You can add OAuth providers here later
    // github: {
    //   clientId: process.env.GITHUB_CLIENT_ID!,
    //   clientSecret: process.env.GITHUB_CLIENT_SECRET!,
    // },
  },
  
  session: {
    expiresIn: 60 * 60 * 24 * 30, // 30 days
    updateAge: 60 * 60 * 24, // Update session every 24 hours
  },
});