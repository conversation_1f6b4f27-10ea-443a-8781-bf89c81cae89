"use client";

import Link from "next/link";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Github, Menu, X, Brain, LogOut, User, Database, FileCode, BarChart, FileSearch, Network, Layers, RocketIcon } from "lucide-react";
import { useState } from "react";
import { useAuth } from "@/lib/hooks/useAuth";
import { ThemeToggle } from "@/components/ui/theme-toggle";

export function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { user, isAuthenticated, signOut } = useAuth();
  const [userMenuOpen, setUserMenuOpen] = useState(false);

  const handleSignOut = () => {
    signOut();
  };

  return (
    <header className="flex items-center justify-between py-4 px-4 md:px-6 border-b border-border relative">
      <div className="flex items-center space-x-4">
        <Link href="/" className="flex items-center">
          <Image
            src="/images/alias-logo.svg"
            alt="ALIAS MOSAIC Logo"
            width={50}
            height={30}
            className="mr-2"
            priority
          />
          <span className="text-xl font-normal tracking-wide">ALIAS MOSAIC</span>
        </Link>
        <div className="hidden md:block ml-8 text-sm text-muted-foreground tracking-widest font-light">
          ONTOLOGY-DRIVEN DEVELOPMENT
        </div>
      </div>

      <div className="hidden md:flex items-center space-x-4">
        <ThemeToggle />

        <Link href="https://github.com/alias-mosaic" target="_blank" rel="noopener noreferrer">
          <Button variant="ghost" size="icon" className="text-muted-foreground">
            <Github className="h-5 w-5" />
          </Button>
        </Link>

        <Link href="/ontology">
          <Button variant="outline" className="text-xs font-light tracking-wide border-muted px-4">
            <Layers className="h-4 w-4 mr-1" />
            ONTOLOGY
          </Button>
        </Link>

        <Link href="/agents">
          <Button variant="outline" className="text-xs font-light tracking-wide border-muted px-4">
            <Brain className="h-4 w-4 mr-1" />
            AGENTS
          </Button>
        </Link>

        <Link href="/projects">
          <Button variant="outline" className="text-xs font-light tracking-wide border-muted px-4">
            <FileCode className="h-4 w-4 mr-1" />
            PROJECTS
          </Button>
        </Link>

        <Link href="/knowledge-base">
          <Button variant="outline" className="text-xs font-light tracking-wide border-muted px-4">
            <Database className="h-4 w-4 mr-1" />
            KNOWLEDGE BASE
          </Button>
        </Link>

        <Link href="/deploy">
          <Button variant="outline" className="text-xs font-light tracking-wide border-muted px-4">
            <RocketIcon className="h-4 w-4 mr-1" />
            DEPLOY
          </Button>
        </Link>

        <Link href="/agents/metrics">
          <Button variant="outline" className="text-xs font-light tracking-wide border-muted px-4">
            <BarChart className="h-4 w-4 mr-1" />
            METRICS
          </Button>
        </Link>

        {!isAuthenticated ? (
          <Link href="/login">
            <Button variant="outline" className="text-xs font-light tracking-wide border-muted px-4">
              LOGIN
            </Button>
          </Link>
        ) : (
          <div className="relative">
            <button
              className="flex items-center space-x-2 focus:outline-none"
              onClick={() => setUserMenuOpen(!userMenuOpen)}
            >
              {user?.image ? (
                <Image
                  src={user.image}
                  alt={user.name || "User"}
                  width={32}
                  height={32}
                  className="rounded-full border border-border"
                />
              ) : (
                <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground">
                  {user?.name ? user.name[0] : "U"}
                </div>
              )}
              <span className="text-sm hidden lg:inline">{user?.name}</span>
            </button>

            {userMenuOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-card rounded-md shadow-lg py-1 border border-border z-50">
                <div className="px-4 py-3 border-b border-border">
                  <p className="text-sm font-medium">{user?.name}</p>
                  <p className="text-xs text-muted-foreground truncate">{user?.email}</p>
                </div>
                <Link
                  href="/profile"
                  className="block px-4 py-2 text-sm hover:bg-secondary flex items-center"
                  onClick={() => setUserMenuOpen(false)}
                >
                  <User className="h-4 w-4 mr-2" />
                  Profile
                </Link>
                <button
                  className="block w-full text-left px-4 py-2 text-sm hover:bg-secondary flex items-center text-destructive"
                  onClick={handleSignOut}
                >
                  <LogOut className="h-4 w-4 mr-2" />
                  Sign out
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      <div className="md:hidden flex items-center space-x-2">
        <ThemeToggle />

        <Button
          variant="ghost"
          size="icon"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        >
          {mobileMenuOpen ? (
            <X className="h-6 w-6" />
          ) : (
            <Menu className="h-6 w-6" />
          )}
        </Button>
      </div>

      {mobileMenuOpen && (
        <div className="md:hidden absolute top-full left-0 right-0 bg-background border-b border-border z-50">
          <div className="flex flex-col p-4 space-y-4">
            <Link href="/ontology" className="px-2 py-1 text-sm flex items-center" onClick={() => setMobileMenuOpen(false)}>
              <Layers className="h-4 w-4 mr-2" />
              ONTOLOGY
            </Link>
            <Link href="/agents" className="px-2 py-1 text-sm flex items-center" onClick={() => setMobileMenuOpen(false)}>
              <Brain className="h-4 w-4 mr-2" />
              AGENTS
            </Link>
            <Link href="/projects" className="px-2 py-1 text-sm flex items-center" onClick={() => setMobileMenuOpen(false)}>
              <FileCode className="h-4 w-4 mr-2" />
              PROJECTS
            </Link>
            <Link href="/knowledge-base" className="px-2 py-1 text-sm flex items-center" onClick={() => setMobileMenuOpen(false)}>
              <Database className="h-4 w-4 mr-2" />
              KNOWLEDGE BASE
            </Link>
            <Link href="/deploy" className="px-2 py-1 text-sm flex items-center" onClick={() => setMobileMenuOpen(false)}>
              <RocketIcon className="h-4 w-4 mr-2" />
              DEPLOY
            </Link>
            <Link href="/agents/metrics" className="px-2 py-1 text-sm flex items-center" onClick={() => setMobileMenuOpen(false)}>
              <BarChart className="h-4 w-4 mr-2" />
              METRICS
            </Link>

            {!isAuthenticated ? (
              <Link href="/login" className="px-2 py-1 text-sm" onClick={() => setMobileMenuOpen(false)}>
                LOGIN
              </Link>
            ) : (
              <>
                <Link href="/profile" className="px-2 py-1 text-sm flex items-center" onClick={() => setMobileMenuOpen(false)}>
                  <User className="h-4 w-4 mr-2" />
                  Profile ({user?.name})
                </Link>
                <button
                  className="px-2 py-1 text-sm flex items-center text-destructive"
                  onClick={() => {
                    setMobileMenuOpen(false);
                    handleSignOut();
                  }}
                >
                  <LogOut className="h-4 w-4 mr-2" />
                  Sign out
                </button>
              </>
            )}

            <Link
              href="https://github.com/alias-mosaic"
              target="_blank"
              rel="noopener noreferrer"
              className="px-2 py-1 text-sm flex items-center"
              onClick={() => setMobileMenuOpen(false)}
            >
              <Github className="h-4 w-4 mr-2" />
              GITHUB
            </Link>
          </div>
        </div>
      )}
    </header>
  );
}
