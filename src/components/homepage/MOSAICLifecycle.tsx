"use client";

import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Lightbulb, Target, Users, Palette, Code, Wrench, 
  Rocket, TrendingUp, RefreshCw, Award, BarChart3
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";

const mosaicStages = [
  {
    id: 1,
    name: "Conceive",
    description: "Ideation and vision crystallization",
    icon: Lightbulb,
    color: "from-purple-500 to-purple-600",
    bgColor: "bg-purple-500/10",
    borderColor: "border-purple-500/20",
    projects: 12,
    status: "active"
  },
  {
    id: 2,
    name: "Define",
    description: "Requirements and scope definition",
    icon: Target,
    color: "from-blue-500 to-blue-600",
    bgColor: "bg-blue-500/10",
    borderColor: "border-blue-500/20",
    projects: 8,
    status: "active"
  },
  {
    id: 3,
    name: "Collaborate",
    description: "Team formation and planning",
    icon: Users,
    color: "from-green-500 to-green-600",
    bgColor: "bg-green-500/10",
    borderColor: "border-green-500/20",
    projects: 15,
    status: "active"
  },
  {
    id: 4,
    name: "Design",
    description: "Architecture and UX design",
    icon: Palette,
    color: "from-pink-500 to-pink-600",
    bgColor: "bg-pink-500/10",
    borderColor: "border-pink-500/20",
    projects: 6,
    status: "active"
  },
  {
    id: 5,
    name: "Develop",
    description: "Implementation and coding",
    icon: Code,
    color: "from-orange-500 to-orange-600",
    bgColor: "bg-orange-500/10",
    borderColor: "border-orange-500/20",
    projects: 23,
    status: "active"
  },
  {
    id: 6,
    name: "Test",
    description: "Quality assurance and validation",
    icon: Wrench,
    color: "from-cyan-500 to-cyan-600",
    bgColor: "bg-cyan-500/10",
    borderColor: "border-cyan-500/20",
    projects: 11,
    status: "active"
  },
  {
    id: 7,
    name: "Deploy",
    description: "Release and go-live",
    icon: Rocket,
    color: "from-red-500 to-red-600",
    bgColor: "bg-red-500/10",
    borderColor: "border-red-500/20",
    projects: 4,
    status: "active"
  },
  {
    id: 8,
    name: "Optimize",
    description: "Performance tuning and enhancement",
    icon: TrendingUp,
    color: "from-indigo-500 to-indigo-600",
    bgColor: "bg-indigo-500/10",
    borderColor: "border-indigo-500/20",
    projects: 7,
    status: "active"
  },
  {
    id: 9,
    name: "Maintain",
    description: "Ongoing support and updates",
    icon: RefreshCw,
    color: "from-teal-500 to-teal-600",
    bgColor: "bg-teal-500/10",
    borderColor: "border-teal-500/20",
    projects: 19,
    status: "active"
  },
  {
    id: 10,
    name: "Measure",
    description: "Analytics and performance tracking",
    icon: BarChart3,
    color: "from-yellow-500 to-yellow-600",
    bgColor: "bg-yellow-500/10",
    borderColor: "border-yellow-500/20",
    projects: 9,
    status: "active"
  },
  {
    id: 11,
    name: "Evolve",
    description: "Continuous improvement and growth",
    icon: Award,
    color: "from-emerald-500 to-emerald-600",
    bgColor: "bg-emerald-500/10",
    borderColor: "border-emerald-500/20",
    projects: 5,
    status: "active"
  }
];

export function MOSAICLifecycle() {
  const [selectedStage, setSelectedStage] = useState<number | null>(null);

  return (
    <section className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-6">
            <RefreshCw className="h-4 w-4 mr-2" />
            MOSAIC Framework
          </div>
          
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="text-foreground">11-Stage</span>{" "}
            <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              Lifecycle Management
            </span>
          </h2>
          
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            From conception to evolution, ALIAS orchestrates every stage of your organizational journey 
            with AI-powered precision and human-centered design.
          </p>
        </div>

        {/* Lifecycle Visualization */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
          {mosaicStages.map((stage, index) => {
            const Icon = stage.icon;
            const isSelected = selectedStage === stage.id;
            
            return (
              <Card
                key={stage.id}
                className={`group cursor-pointer transition-all duration-300 hover:scale-105 ${
                  isSelected ? stage.borderColor : "border-border"
                } ${stage.bgColor} hover:shadow-lg`}
                onClick={() => setSelectedStage(isSelected ? null : stage.id)}
              >
                <CardContent className="p-6">
                  {/* Stage Number & Icon */}
                  <div className="flex items-center justify-between mb-4">
                    <div className={`w-12 h-12 rounded-full bg-gradient-to-r ${stage.color} flex items-center justify-center text-white shadow-lg`}>
                      <Icon className="h-6 w-6" />
                    </div>
                    <div className="text-right">
                      <div className="text-xs text-muted-foreground">Stage</div>
                      <div className="text-lg font-bold text-primary">{stage.id}</div>
                    </div>
                  </div>

                  {/* Stage Info */}
                  <div className="space-y-2">
                    <h3 className="text-lg font-semibold group-hover:text-primary transition-colors">
                      {stage.name}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {stage.description}
                    </p>
                  </div>

                  {/* Projects Count */}
                  <div className="mt-4 pt-4 border-t border-border/50">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Active Projects</span>
                      <span className="font-medium text-primary">{stage.projects}</span>
                    </div>
                  </div>

                  {/* Progress Bar */}
                  <div className="mt-2">
                    <div className="w-full bg-border/30 rounded-full h-1">
                      <div 
                        className={`h-1 rounded-full bg-gradient-to-r ${stage.color} transition-all duration-500`}
                        style={{ width: `${(stage.projects / 23) * 100}%` }}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Selected Stage Details */}
        {selectedStage && (
          <Card className="mb-12 border-primary/20 bg-primary/5">
            <CardContent className="p-8">
              {(() => {
                const stage = mosaicStages.find(s => s.id === selectedStage);
                if (!stage) return null;
                const Icon = stage.icon;
                
                return (
                  <div className="flex items-start gap-6">
                    <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${stage.color} flex items-center justify-center text-white shadow-lg flex-shrink-0`}>
                      <Icon className="h-8 w-8" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold mb-2">Stage {stage.id}: {stage.name}</h3>
                      <p className="text-muted-foreground mb-4">{stage.description}</p>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="text-center p-4 bg-card rounded-lg">
                          <div className="text-2xl font-bold text-primary">{stage.projects}</div>
                          <div className="text-sm text-muted-foreground">Active Projects</div>
                        </div>
                        <div className="text-center p-4 bg-card rounded-lg">
                          <div className="text-2xl font-bold text-primary">12</div>
                          <div className="text-sm text-muted-foreground">AI Agents</div>
                        </div>
                        <div className="text-center p-4 bg-card rounded-lg">
                          <div className="text-2xl font-bold text-primary">98%</div>
                          <div className="text-sm text-muted-foreground">Success Rate</div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })()}
            </CardContent>
          </Card>
        )}

        {/* Action Buttons */}
        <div className="text-center">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/projects">
              <Button size="lg" className="w-full sm:w-auto">
                View All Projects
                <BarChart3 className="ml-2 h-4 w-4" />
              </Button>
            </Link>
            
            <Link href="/agents">
              <Button variant="outline" size="lg" className="w-full sm:w-auto">
                Manage Agents
                <Users className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}