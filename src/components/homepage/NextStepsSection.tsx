"use client";

import { <PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  ArrowRight, Calendar, MessageSquare, Play, 
  BookOpen, Users, Rocket, Zap, Clock, CheckCircle 
} from "lucide-react";
import Link from "next/link";

const pathways = [
  {
    title: "Quick Start",
    description: "Get up and running with ALIAS in under 15 minutes",
    time: "15 min",
    icon: Zap,
    color: "from-green-500 to-green-600",
    bgColor: "bg-green-500/10",
    borderColor: "border-green-500/20",
    steps: [
      "Create your account",
      "Deploy first AI agent",
      "Import existing data",
      "Start first project"
    ],
    cta: "Start Free Trial",
    link: "/agents",
    primary: true
  },
  {
    title: "Guided Demo",
    description: "Experience the full ALIAS ecosystem with expert guidance",
    time: "30 min",
    icon: Play,
    color: "from-blue-500 to-blue-600",
    bgColor: "bg-blue-500/10",
    borderColor: "border-blue-500/20",
    steps: [
      "Live platform tour",
      "MOSAIC framework walkthrough",
      "Custom use case discussion",
      "Implementation planning"
    ],
    cta: "Schedule Demo",
    link: "/demo",
    primary: false
  },
  {
    title: "Enterprise Setup",
    description: "Full organizational deployment with dedicated support",
    time: "1-2 weeks",
    icon: Rocket,
    color: "from-purple-500 to-purple-600",
    bgColor: "bg-purple-500/10",
    borderColor: "border-purple-500/20",
    steps: [
      "Requirements analysis",
      "Custom agent development",
      "Team training program",
      "Ongoing optimization"
    ],
    cta: "Contact Sales",
    link: "/contact",
    primary: false
  }
];

const resources = [
  {
    title: "Documentation",
    description: "Comprehensive guides and API references",
    icon: BookOpen,
    link: "/docs"
  },
  {
    title: "Community",
    description: "Join thousands of ALIAS users worldwide",
    icon: Users,
    link: "/community"
  },
  {
    title: "Support",
    description: "24/7 technical support and consultation",
    icon: MessageSquare,
    link: "/support"
  }
];

export function NextStepsSection() {
  return (
    <section className="py-20 bg-gradient-to-br from-muted/30 to-background">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-6">
            <Rocket className="h-4 w-4 mr-2" />
            Get Started
          </div>
          
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="text-foreground">Your Journey to</span>{" "}
            <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              Organizational Intelligence
            </span>
          </h2>
          
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Choose your path to transformation. Whether you want to start immediately, 
            explore with guidance, or plan a full enterprise deployment.
          </p>
        </div>

        {/* Pathway Options */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {pathways.map((pathway, index) => {
            const Icon = pathway.icon;
            return (
              <Card
                key={index}
                className={`group hover:scale-105 transition-all duration-300 cursor-pointer relative ${
                  pathway.primary ? 'ring-2 ring-primary/20 shadow-lg' : ''
                } ${pathway.bgColor} ${pathway.borderColor} hover:shadow-xl`}
              >
                {pathway.primary && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div className="bg-primary text-primary-foreground px-4 py-1 rounded-full text-sm font-medium">
                      Recommended
                    </div>
                  </div>
                )}
                
                <CardContent className="p-8">
                  {/* Header */}
                  <div className="text-center mb-6">
                    <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r ${pathway.color} text-white shadow-lg mb-4`}>
                      <Icon className="h-8 w-8" />
                    </div>
                    
                    <h3 className="text-xl font-bold mb-2 group-hover:text-primary transition-colors">
                      {pathway.title}
                    </h3>
                    
                    <p className="text-muted-foreground mb-4">
                      {pathway.description}
                    </p>
                    
                    <div className="inline-flex items-center px-3 py-1 bg-card/50 rounded-full text-sm">
                      <Clock className="h-4 w-4 mr-2 text-primary" />
                      {pathway.time}
                    </div>
                  </div>

                  {/* Steps */}
                  <div className="space-y-3 mb-8">
                    {pathway.steps.map((step, stepIndex) => (
                      <div key={stepIndex} className="flex items-center text-sm">
                        <CheckCircle className="h-4 w-4 text-primary mr-3 flex-shrink-0" />
                        <span className="text-muted-foreground">{step}</span>
                      </div>
                    ))}
                  </div>

                  {/* CTA */}
                  <Link href={pathway.link}>
                    <Button 
                      className={`w-full group/btn ${
                        pathway.primary ? '' : 'variant-outline'
                      }`}
                      variant={pathway.primary ? 'default' : 'outline'}
                    >
                      {pathway.cta}
                      <ArrowRight className="ml-2 h-4 w-4 group-hover/btn:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Resources Section */}
        <Card className="bg-gradient-to-r from-primary/5 to-secondary/5 border-primary/20 mb-12">
          <CardContent className="p-12">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold mb-4">Additional Resources</h3>
              <p className="text-muted-foreground">
                Everything you need to succeed with ALIAS
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {resources.map((resource, index) => {
                const Icon = resource.icon;
                return (
                  <Link key={index} href={resource.link}>
                    <Card className="group hover:shadow-md transition-all duration-300 cursor-pointer bg-card/50 hover:bg-card">
                      <CardContent className="p-6 text-center">
                        <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 text-primary mb-4 group-hover:scale-110 transition-transform">
                          <Icon className="h-6 w-6" />
                        </div>
                        <h4 className="font-semibold mb-2 group-hover:text-primary transition-colors">
                          {resource.title}
                        </h4>
                        <p className="text-sm text-muted-foreground">
                          {resource.description}
                        </p>
                      </CardContent>
                    </Card>
                  </Link>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Final CTA */}
        <div className="text-center">
          <div className="max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold mb-4">
              Ready to Transform Your Organization?
            </h3>
            <p className="text-muted-foreground mb-8">
              Join the future of organizational intelligence. Start your journey today.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/agents">
                <Button size="lg" className="group w-full sm:w-auto">
                  Start Free Trial
                  <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>
              
              <Link href="/contact">
                <Button variant="outline" size="lg" className="w-full sm:w-auto">
                  <Calendar className="mr-2 h-4 w-4" />
                  Schedule Consultation
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}