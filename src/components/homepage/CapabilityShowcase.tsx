"use client";

import { <PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Brain, Network, Eye, Zap, Database, Users, 
  BarChart3, Shield, Lightbulb, RefreshCw, ArrowRight 
} from "lucide-react";
import Link from "next/link";

const capabilities = [
  {
    title: "AI Agent Orchestra",
    description: "Deploy specialized AI agents that collaborate seamlessly across your entire organizational ecosystem.",
    icon: Brain,
    features: ["45+ Agent Types", "Real-time Collaboration", "Auto-scaling", "Performance Analytics"],
    color: "from-purple-500 to-purple-600",
    bgColor: "bg-purple-500/10",
    borderColor: "border-purple-500/20",
    link: "/agents"
  },
  {
    title: "Knowledge Representation",
    description: "Dynamic ontology systems that evolve with your organization's growing intelligence and complexity.",
    icon: Network,
    features: ["NERO-PRISM-ICL Architecture", "Auto-ontology Generation", "Semantic Linking", "Version Control"],
    color: "from-blue-500 to-blue-600",
    bgColor: "bg-blue-500/10",
    borderColor: "border-blue-500/20",
    link: "/ontology"
  },
  {
    title: "Real-time Intelligence",
    description: "Live insights and predictive analytics that anticipate needs before they become critical.",
    icon: Eye,
    features: ["Predictive Analytics", "Anomaly Detection", "Real-time Dashboards", "Custom Alerts"],
    color: "from-green-500 to-green-600",
    bgColor: "bg-green-500/10",
    borderColor: "border-green-500/20",
    link: "/agents/metrics"
  },
  {
    title: "Lightning Performance",
    description: "Sub-second response times powered by edge computing and optimized AI inference engines.",
    icon: Zap,
    features: ["< 2.3s Response", "Edge Computing", "Auto-optimization", "Global CDN"],
    color: "from-yellow-500 to-yellow-600",
    bgColor: "bg-yellow-500/10",
    borderColor: "border-yellow-500/20",
    link: "/run-node"
  },
  {
    title: "Unified Data Layer",
    description: "Converged data architecture that unifies structured and unstructured information seamlessly.",
    icon: Database,
    features: ["Real-time Sync", "Multi-source Integration", "Automatic Indexing", "Query Optimization"],
    color: "from-indigo-500 to-indigo-600",
    bgColor: "bg-indigo-500/10",
    borderColor: "border-indigo-500/20",
    link: "/knowledge-base"
  },
  {
    title: "Collaborative Intelligence",
    description: "Human-AI collaboration tools that amplify team capabilities and accelerate decision-making.",
    icon: Users,
    features: ["Shared Workspaces", "AI-assisted Decisions", "Role-based Access", "Activity Tracking"],
    color: "from-pink-500 to-pink-600",
    bgColor: "bg-pink-500/10",
    borderColor: "border-pink-500/20",
    link: "/projects"
  }
];

const stats = [
  { label: "Processing Speed", value: "2.3s", unit: "avg", icon: Zap },
  { label: "Accuracy Rate", value: "99.7", unit: "%", icon: BarChart3 },
  { label: "Uptime", value: "99.9", unit: "%", icon: Shield },
  { label: "Innovation Index", value: "A+", unit: "grade", icon: Lightbulb }
];

export function CapabilityShowcase() {
  return (
    <section className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-6">
            <RefreshCw className="h-4 w-4 mr-2" />
            Core Capabilities
          </div>
          
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="text-foreground">Organizational</span>{" "}
            <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              Superpowers
            </span>
          </h2>
          
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Transform your organization with AI-native capabilities that learn, adapt, 
            and evolve with your unique business needs and objectives.
          </p>
        </div>

        {/* Performance Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <Card key={index} className="text-center bg-card/50 backdrop-blur-sm">
                <CardContent className="p-6">
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 text-primary mb-4">
                    <Icon className="h-6 w-6" />
                  </div>
                  <div className="text-2xl font-bold text-primary mb-1">
                    {stat.value}
                    <span className="text-lg text-muted-foreground ml-1">{stat.unit}</span>
                  </div>
                  <div className="text-sm text-muted-foreground">{stat.label}</div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Capabilities Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {capabilities.map((capability, index) => {
            const Icon = capability.icon;
            return (
              <Card
                key={index}
                className={`group hover:scale-105 transition-all duration-300 cursor-pointer ${capability.bgColor} ${capability.borderColor} hover:shadow-xl`}
              >
                <CardContent className="p-8">
                  {/* Icon */}
                  <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r ${capability.color} text-white shadow-lg mb-6`}>
                    <Icon className="h-8 w-8" />
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-bold mb-3 group-hover:text-primary transition-colors">
                    {capability.title}
                  </h3>
                  
                  <p className="text-muted-foreground mb-6 leading-relaxed">
                    {capability.description}
                  </p>

                  {/* Features */}
                  <div className="space-y-2 mb-6">
                    {capability.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center text-sm">
                        <div className={`w-1.5 h-1.5 rounded-full bg-gradient-to-r ${capability.color} mr-3`} />
                        <span className="text-muted-foreground">{feature}</span>
                      </div>
                    ))}
                  </div>

                  {/* Action */}
                  <Link href={capability.link}>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="group/btn w-full justify-between hover:bg-primary/10"
                    >
                      Explore Capability
                      <ArrowRight className="h-4 w-4 group-hover/btn:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Integration Showcase */}
        <Card className="bg-gradient-to-r from-primary/10 via-secondary/5 to-primary/10 border-primary/20">
          <CardContent className="p-12 text-center">
            <div className="max-w-4xl mx-auto">
              <h3 className="text-2xl md:text-3xl font-bold mb-6">
                <span className="text-foreground">Seamless Integration</span>{" "}
                <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                  Ecosystem
                </span>
              </h3>
              
              <p className="text-lg text-muted-foreground mb-8">
                ALIAS integrates with your existing tools and workflows, creating a unified 
                intelligence layer that enhances rather than replaces your current systems.
              </p>

              {/* Integration Icons/Logos */}
              <div className="flex justify-center items-center gap-8 mb-8 opacity-60">
                {["API", "Slack", "GitHub", "Jira", "Notion", "Teams"].map((integration, index) => (
                  <div key={index} className="px-4 py-2 bg-card/50 rounded-lg text-sm font-medium">
                    {integration}
                  </div>
                ))}
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/deploy">
                  <Button size="lg" className="w-full sm:w-auto">
                    Start Integration
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
                
                <Button variant="outline" size="lg" className="w-full sm:w-auto">
                  View Documentation
                  <Eye className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
}