"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Counter } from "@/components/ui/Counter";
import { Star, Quote, TrendingUp, Award, Users, Building } from "lucide-react";

const testimonials = [
  {
    quote: "ALIAS has transformed how we think about organizational intelligence. The MOSAIC framework gave us clarity we never had before.",
    author: "<PERSON>",
    role: "CTO, TechVision Corp",
    company: "TechVision",
    rating: 5,
    avatar: "SC"
  },
  {
    quote: "The real-time collaboration features and AI agents have increased our team productivity by 400%. It's like having a digital twin of our entire operation.",
    author: "<PERSON>",
    role: "VP of Operations, InnovateLabs",
    company: "InnovateLabs", 
    rating: 5,
    avatar: "MR"
  },
  {
    quote: "From concept to deployment in weeks, not months. ALIAS's lifecycle management is a game-changer for complex projects.",
    author: "Dr. <PERSON>",
    role: "Research Director, FutureState Institute",
    company: "FutureState",
    rating: 5,
    avatar: "EW"
  }
];

const achievements = [
  {
    metric: "321",
    label: "Organizations Transformed",
    icon: Building,
    color: "text-blue-500"
  },
  {
    metric: "10M+",
    label: "Tasks Automated",
    icon: TrendingUp,
    color: "text-green-500"
  },
  {
    metric: "99.7%",
    label: "Success Rate",
    icon: Award,
    color: "text-yellow-500"
  },
  {
    metric: "$2.4M",
    label: "Average ROI",
    icon: TrendingUp,
    color: "text-purple-500"
  }
];

const recognitions = [
  "AI Innovation Award 2024",
  "Best Enterprise Platform",
  "Top 10 Future of Work Tools",
  "Excellence in AI Ethics"
];

export function SocialProofSection() {
  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-6">
            <Award className="h-4 w-4 mr-2" />
            Proven Results
          </div>
          
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="text-foreground">Trusted by</span>{" "}
            <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              Industry Leaders
            </span>
          </h2>
          
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Join hundreds of forward-thinking organizations that have already transformed 
            their operations with ALIAS's organizational intelligence platform.
          </p>
        </div>

        {/* Achievement Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          {achievements.map((achievement, index) => {
            const Icon = achievement.icon;
            return (
              <Card key={index} className="text-center bg-gradient-to-br from-card to-card/50">
                <CardContent className="p-6">
                  <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 mb-4`}>
                    <Icon className={`h-6 w-6 ${achievement.color}`} />
                  </div>
                  <div className={`text-2xl md:text-3xl font-bold mb-2 ${achievement.color}`}>
                    {achievement.metric.includes('+') || achievement.metric.includes('%') || achievement.metric.includes('$') 
                      ? achievement.metric 
                      : <Counter value={Number.parseInt(achievement.metric)} />
                    }
                  </div>
                  <div className="text-sm text-muted-foreground">{achievement.label}</div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="group hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-card to-card/50">
              <CardContent className="p-8">
                {/* Quote Icon */}
                <div className="flex items-center justify-between mb-6">
                  <Quote className="h-8 w-8 text-primary/30" />
                  
                  {/* Rating */}
                  <div className="flex">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                </div>

                {/* Quote */}
                <blockquote className="text-muted-foreground mb-6 leading-relaxed">
                  "{testimonial.quote}"
                </blockquote>

                {/* Author */}
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center text-primary font-medium mr-4">
                    {testimonial.avatar}
                  </div>
                  <div>
                    <div className="font-semibold text-foreground">{testimonial.author}</div>
                    <div className="text-sm text-muted-foreground">{testimonial.role}</div>
                    <div className="text-xs text-primary font-medium">{testimonial.company}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Recognition Section */}
        <Card className="bg-gradient-to-r from-primary/5 via-secondary/5 to-primary/5 border-primary/20">
          <CardContent className="p-12">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold mb-4">Industry Recognition</h3>
              <p className="text-muted-foreground">
                Recognized by leading industry analysts and organizations worldwide
              </p>
            </div>

            {/* Recognition Badges */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
              {recognitions.map((recognition, index) => (
                <div key={index} className="text-center p-4 bg-card/50 rounded-lg">
                  <Award className="h-8 w-8 text-primary mx-auto mb-2" />
                  <div className="text-sm font-medium">{recognition}</div>
                </div>
              ))}
            </div>

            {/* Trust Indicators */}
            <div className="flex flex-wrap justify-center items-center gap-8 text-sm text-muted-foreground">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2" />
                SOC 2 Type II Certified
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-2" />
                GDPR Compliant
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-purple-500 rounded-full mr-2" />
                ISO 27001 Certified
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2" />
                Enterprise Grade Security
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Customer Logos */}
        <div className="text-center mt-12">
          <p className="text-sm text-muted-foreground mb-6">Trusted by teams at</p>
          <div className="flex justify-center items-center gap-8 opacity-50">
            {["TechCorp", "InnovateLabs", "FutureState", "DataDriven", "AI Systems", "NextGen"].map((company, index) => (
              <div key={index} className="px-4 py-2 bg-card/30 rounded text-sm font-medium">
                {company}
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}