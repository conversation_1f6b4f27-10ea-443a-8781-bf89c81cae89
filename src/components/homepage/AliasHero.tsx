"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import Globe from "@/components/ui/globe/Globe";
import { Counter } from "@/components/ui/Counter";
import { useStats } from "@/lib/hooks/useStats";
import { ArrowRight, Brain, Zap, Globe2, Users, Layers3 } from "lucide-react";
import Link from "next/link";

export function AliasHero() {
  const { data } = useStats();

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-background via-background to-background/95">
      {/* Background gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5" />
      
      {/* Animated grid background */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgb(var(--primary)) 1px, transparent 0)`,
          backgroundSize: '40px 40px'
        }} />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          
          {/* Left Column - Content */}
          <div className="flex flex-col space-y-8">
            {/* Tagline */}
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium w-fit">
              <Zap className="h-4 w-4 mr-2" />
              The Future of Organizational Intelligence
            </div>

            {/* Main Headline */}
            <div className="space-y-4">
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold leading-tight">
                <span className="bg-gradient-to-r from-primary via-primary/90 to-secondary bg-clip-text text-transparent">
                  ALIAS
                </span>
                <br />
                <span className="text-foreground">
                  Where Human
                </span>
                <br />
                <span className="text-muted-foreground">
                  Potential Meets
                </span>
                <br />
                <span className="bg-gradient-to-r from-secondary via-primary to-primary bg-clip-text text-transparent">
                  AI Precision
                </span>
              </h1>
              
              <p className="text-xl md:text-2xl text-muted-foreground leading-relaxed max-w-2xl">
                The world's first organizational operating system that synthesizes 
                life and work into unified intelligence through the MOSAIC framework.
              </p>
            </div>

            {/* Live Metrics */}
            <div className="grid grid-cols-3 gap-6 py-6 border-y border-border/50">
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Brain className="h-5 w-5 text-primary mr-2" />
                  <Counter 
                    value={data?.metrics?.aiAgents || 45} 
                    className="text-2xl font-bold text-primary"
                  />
                </div>
                <p className="text-sm text-muted-foreground">AI Agents Active</p>
              </div>
              
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Users className="h-5 w-5 text-primary mr-2" />
                  <Counter 
                    value={data?.metrics?.activeUsers || 321} 
                    className="text-2xl font-bold text-primary"
                  />
                </div>
                <p className="text-sm text-muted-foreground">Organizations</p>
              </div>
              
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Layers3 className="h-5 w-5 text-primary mr-2" />
                  <Counter 
                    value={data?.metrics?.ontologyEntities || 230} 
                    className="text-2xl font-bold text-primary"
                  />
                </div>
                <p className="text-sm text-muted-foreground">Knowledge Entities</p>
              </div>
            </div>

            {/* Primary CTAs */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/agents">
                <Button size="lg" className="group w-full sm:w-auto">
                  Enter Command Center
                  <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>
              
              <Link href="/ontology">
                <Button variant="outline" size="lg" className="w-full sm:w-auto">
                  Experience MOSAIC
                  <Globe2 className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </div>

            {/* Value Proposition */}
            <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-primary rounded-full mr-2" />
                11-Stage Lifecycle Management
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-secondary rounded-full mr-2" />
                Real-time Intelligence
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-primary rounded-full mr-2" />
                Life-Work Synthesis
              </div>
            </div>
          </div>

          {/* Right Column - Interactive Globe */}
          <div className="relative">
            <div className="relative w-full aspect-square max-w-lg mx-auto">
              {/* Globe Container */}
              <div className="absolute inset-0 rounded-full bg-gradient-to-br from-primary/20 to-secondary/20 backdrop-blur-sm border border-primary/20">
                <Globe />
              </div>
              
              {/* Floating Labels */}
              <div className="absolute top-1/4 -left-4 bg-card border border-border rounded-lg px-3 py-2 shadow-lg backdrop-blur-sm">
                <div className="flex items-center text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse" />
                  <span className="text-muted-foreground">Global Agents:</span>
                  <Counter value={data?.metrics?.aiAgents || 45} className="ml-1 font-medium" />
                </div>
              </div>
              
              <div className="absolute bottom-1/4 -right-4 bg-card border border-border rounded-lg px-3 py-2 shadow-lg backdrop-blur-sm">
                <div className="flex items-center text-sm">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse" />
                  <span className="text-muted-foreground">Projects:</span>
                  <Counter value={89} className="ml-1 font-medium" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-1 h-16 bg-gradient-to-b from-primary to-transparent rounded-full" />
      </div>
    </section>
  );
}