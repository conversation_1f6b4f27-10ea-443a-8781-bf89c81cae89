"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { NetworkOverview } from "@/components/dashboard/NetworkOverview";
import { LiveFeed } from "@/components/dashboard/LiveFeed";
import { AgentActivities } from "@/components/dashboard/AgentActivities";
import { OntologyOverview } from "@/components/dashboard/OntologyOverview";
import { But<PERSON> } from "@/components/ui/button";
import Globe from "@/components/ui/globe/Globe";
import { Counter } from "@/components/ui/Counter";
import { useStats } from "@/lib/hooks/useStats";
import { 
  Activity, Brain, Database, TrendingUp, Zap, 
  ArrowRight, Eye, Settings, BarChart3 
} from "lucide-react";
import Link from "next/link";

export function IntelligenceDashboard() {
  const { data } = useStats();

  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-6">
            <Brain className="h-4 w-4 mr-2" />
            Real-time Intelligence
          </div>
          
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="text-foreground">Command Center</span>{" "}
            <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              Dashboard
            </span>
          </h2>
          
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
            Monitor, analyze, and optimize your entire organizational ecosystem 
            in real-time with AI-powered insights and predictive analytics.
          </p>

          <Link href="/agents">
            <Button size="lg" className="group">
              Access Full Dashboard
              <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>
        </div>

        {/* Main Dashboard Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-12">
          {/* Globe Visualization - Takes full height */}
          <div className="lg:col-span-1">
            <Card className="h-full bg-gradient-to-br from-primary/5 to-secondary/5 border-primary/20">
              <CardHeader>
                <CardTitle className="flex items-center text-lg">
                  <Activity className="h-5 w-5 mr-2 text-primary" />
                  Global Activity
                </CardTitle>
              </CardHeader>
              <CardContent className="flex-1">
                <div className="relative h-80 lg:h-96">
                  <Globe />
                  
                  {/* Floating Stats */}
                  <div className="absolute top-4 right-4 bg-card/80 backdrop-blur-sm border border-border rounded-lg p-3">
                    <div className="text-center">
                      <Counter 
                        value={data?.metrics?.activeUsers || 321} 
                        className="text-lg font-bold text-primary"
                      />
                      <div className="text-xs text-muted-foreground">Live Users</div>
                    </div>
                  </div>
                  
                  <div className="absolute bottom-4 left-4 bg-card/80 backdrop-blur-sm border border-border rounded-lg p-3">
                    <div className="text-center">
                      <Counter 
                        value={89} 
                        className="text-lg font-bold text-secondary"
                      />
                      <div className="text-xs text-muted-foreground">Active Projects</div>
                    </div>
                  </div>
                </div>
                
                <div className="mt-4 pt-4 border-t border-border/50">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse" />
                        <span className="font-medium">Online</span>
                      </div>
                      <Counter value={45} className="text-green-500" />
                    </div>
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse" />
                        <span className="font-medium">Processing</span>
                      </div>
                      <Counter value={12} className="text-blue-500" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Dashboard Components */}
          <div className="lg:col-span-2 space-y-6">
            {/* System Metrics */}
            <NetworkOverview />
            
            {/* Live Intelligence Feed */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-6">
                <LiveFeed />
              </div>
              <div className="space-y-6">
                <AgentActivities />
              </div>
            </div>
          </div>
        </div>

        {/* Secondary Dashboard Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-12">
          <OntologyOverview />
          
          {/* Performance Insights */}
          <Card className="bg-gradient-to-br from-secondary/5 to-primary/5 border-secondary/20">
            <CardHeader>
              <CardTitle className="flex items-center text-lg">
                <TrendingUp className="h-5 w-5 mr-2 text-secondary" />
                Performance Insights
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Key Metrics */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-card/50 rounded-lg">
                    <div className="flex items-center justify-center mb-2">
                      <Zap className="h-5 w-5 text-yellow-500 mr-2" />
                      <Counter value={98} className="text-2xl font-bold text-yellow-500" />
                      <span className="text-xl text-yellow-500">%</span>
                    </div>
                    <div className="text-sm text-muted-foreground">Efficiency Score</div>
                  </div>
                  
                  <div className="text-center p-4 bg-card/50 rounded-lg">
                    <div className="flex items-center justify-center mb-2">
                      <Database className="h-5 w-5 text-blue-500 mr-2" />
                      <Counter value={2.3} className="text-2xl font-bold text-blue-500" />
                      <span className="text-xl text-blue-500">s</span>
                    </div>
                    <div className="text-sm text-muted-foreground">Avg Response</div>
                  </div>
                </div>

                {/* Trending Topics */}
                <div>
                  <h4 className="font-medium mb-3 text-sm text-muted-foreground">Trending Now</h4>
                  <div className="space-y-2">
                    {["AI Agent Optimization", "Knowledge Graph Evolution", "Real-time Collaboration"].map((topic, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-card/30 rounded">
                        <span className="text-sm">{topic}</span>
                        <div className="flex items-center text-xs text-muted-foreground">
                          <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                          +{12 + index * 3}%
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="text-center">
          <div className="inline-flex bg-card border border-border rounded-lg p-1 shadow-sm">
            <Link href="/agents/metrics">
              <Button variant="ghost" size="sm" className="flex items-center">
                <BarChart3 className="h-4 w-4 mr-2" />
                Detailed Analytics
              </Button>
            </Link>
            <Link href="/ontology">
              <Button variant="ghost" size="sm" className="flex items-center">
                <Eye className="h-4 w-4 mr-2" />
                Ontology Explorer
              </Button>
            </Link>
            <Link href="/agents/management">
              <Button variant="ghost" size="sm" className="flex items-center">
                <Settings className="h-4 w-4 mr-2" />
                Agent Management
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}