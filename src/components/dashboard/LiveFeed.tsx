"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Activity, ArrowRight, RefreshCw } from "lucide-react";
import { useState, useEffect } from "react";
import { useStats } from "@/lib/hooks/useStats";
import { Skeleton } from "@/components/ui/skeleton";
import Link from "next/link";
import dynamic from "next/dynamic";

// Dynamically import the Globe component with no SSR
const Globe = dynamic(() => import("@/components/ui/globe/Globe"), { ssr: false });

export function LiveFeed() {
  const { data, isLoading, error } = useStats();
  const [isGlobeLoading, setIsGlobeLoading] = useState(true);

  // Handle Globe loaded event
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsGlobeLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Prepare activity points data for the Globe
  const activityPoints = data?.projectActivities?.map((activity: any) => ({
    lat: activity.location.lat,
    lng: activity.location.lng,
    size: activity.importance * 4, // Size based on importance
    color: activity.type === "development" ? "#3060D1" :
           activity.type === "testing" ? "#5A7DE9" :
           activity.type === "deployment" ? "#50C878" : "#F9A826",
    label: `${activity.project}: ${activity.action}`,
  })) || [];

  return (
    <Card className="bg-card border-border overflow-hidden h-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-base font-normal flex items-center">
          <Activity className="h-5 w-5 mr-2 text-primary" />
          Global Project Activities
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0 relative">
        {(isLoading || isGlobeLoading) && (
          <div className="absolute inset-0 z-10 flex items-center justify-center bg-card/80">
            <div className="flex flex-col items-center">
              <RefreshCw className="h-8 w-8 text-primary animate-spin mb-2" />
              <p className="text-sm text-muted-foreground">Loading visualization...</p>
            </div>
          </div>
        )}

        <div className="w-full aspect-[16/10] relative overflow-hidden">
          <Globe points={activityPoints as any} />
        </div>

        <div className="p-4 border-t border-border">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-sm font-medium">Recent Activities</h4>
            <p className="text-xs text-muted-foreground flex items-center">
              <RefreshCw className="h-3 w-3 mr-1" /> Live updates
            </p>
          </div>

          {isLoading ? (
            <div className="space-y-2">
              {[1, 2, 3].map((i) => (
                <Skeleton key={i} className="h-6 w-full" />
              ))}
            </div>
          ) : (
            <div className="space-y-2">
              {(data?.recentActivities || []).slice(0, 3).map((activity, idx) => (
                <div key={idx} className="flex items-center justify-between text-xs">
                  <span className="flex items-center">
                    <span
                      className="h-2 w-2 rounded-full mr-2"
                      style={{
                        backgroundColor:
                          activity.type === "system" ? "var(--primary)" :
                          activity.type === "project" ? "var(--chart-2)" :
                          activity.type === "deployment" ? "var(--chart-3)" :
                          activity.type === "agent" ? "var(--chart-4)" : "var(--chart-5)"
                      }}
                    ></span>
                    {activity.action}
                  </span>
                  <span className="text-muted-foreground">{activity.time}</span>
                </div>
              ))}
            </div>
          )}

          <div className="mt-4 text-right">
            <Link href="/projects/activities" className="text-xs text-primary flex items-center justify-end hover:underline">
              View all activities <ArrowRight className="h-3 w-3 ml-1" />
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
