"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Trophy, BarChart2, ArrowUp, ArrowDown, Minus, Info } from "lucide-react";
import { useStats } from "@/lib/hooks/useStats";
import { Skeleton } from "@/components/ui/skeleton";

export function Leaderboard() {
  const { data, isLoading, error } = useStats();

  // Mock project performance data
  const projects = isLoading ? [] : data?.projectPerformance || [
    {
      id: 1,
      name: "Enterprise CRM Portal",
      velocity: 92,
      qualityScore: 97,
      budget: "On track",
      budgetStatus: "stable",
      timeline: "Ahead",
      timelineStatus: "positive",
      stakeholderSatisfaction: 4.8
    },
    {
      id: 2,
      name: "Healthcare Analytics Platform",
      velocity: 87,
      qualityScore: 94,
      budget: "10% under",
      budgetStatus: "positive",
      timeline: "On track",
      timelineStatus: "stable",
      stakeholderSatisfaction: 4.7
    },
    {
      id: 3,
      name: "Financial Services Dashboard",
      velocity: 76,
      qualityScore: 90,
      budget: "5% over",
      budgetStatus: "negative",
      timeline: "Delayed",
      timelineStatus: "negative",
      stakeholderSatisfaction: 4.2
    },
    {
      id: 4,
      name: "E-commerce Marketplace",
      velocity: 95,
      qualityScore: 92,
      budget: "On track",
      budgetStatus: "stable",
      timeline: "Ahead",
      timelineStatus: "positive",
      stakeholderSatisfaction: 4.9
    },
    {
      id: 5,
      name: "Government Portal Redesign",
      velocity: 81,
      qualityScore: 88,
      budget: "2% under",
      budgetStatus: "positive",
      timeline: "On track",
      timelineStatus: "stable",
      stakeholderSatisfaction: 4.5
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "positive":
        return <ArrowUp className="h-4 w-4 text-green-500" />;
      case "negative":
        return <ArrowDown className="h-4 w-4 text-red-500" />;
      case "stable":
      default:
        return <Minus className="h-4 w-4 text-yellow-500" />;
    }
  };

  return (
    <Card className="bg-card border-border overflow-hidden">
      <CardHeader className="pb-2">
        <CardTitle className="text-base font-normal flex items-center">
          <BarChart2 className="h-5 w-5 mr-2 text-primary" />
          Project Performance Metrics
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="border-border hover:bg-transparent">
                <TableHead className="w-[40px] text-center">#</TableHead>
                <TableHead>Project Name</TableHead>
                <TableHead className="text-center">Velocity</TableHead>
                <TableHead className="text-center">Quality Score</TableHead>
                <TableHead className="text-center">Budget</TableHead>
                <TableHead className="text-center">Timeline</TableHead>
                <TableHead className="text-center">Satisfaction</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                // Loading skeletons
                Array.from({ length: 5 }).map((_, index) => (
                  <TableRow key={index} className="border-border">
                    <TableCell className="text-center">
                      <Skeleton className="h-6 w-6 rounded-full mx-auto" />
                    </TableCell>
                    <TableCell><Skeleton className="h-5 w-40" /></TableCell>
                    <TableCell className="text-center"><Skeleton className="h-5 w-12 mx-auto" /></TableCell>
                    <TableCell className="text-center"><Skeleton className="h-5 w-12 mx-auto" /></TableCell>
                    <TableCell className="text-center"><Skeleton className="h-5 w-16 mx-auto" /></TableCell>
                    <TableCell className="text-center"><Skeleton className="h-5 w-16 mx-auto" /></TableCell>
                    <TableCell className="text-center"><Skeleton className="h-5 w-10 mx-auto" /></TableCell>
                  </TableRow>
                ))
              ) : (
                projects.map((project, index) => (
                  <TableRow key={(project as any).id || (project as any)._id || index} className="border-border hover:bg-background/50">
                    <TableCell className="text-center">
                      {index === 0 ? (
                        <div className="h-6 w-6 bg-yellow-500/20 text-yellow-500 rounded-full flex items-center justify-center mx-auto">
                          <Trophy className="h-3 w-3" />
                        </div>
                      ) : (
                        <div className="text-muted-foreground text-center">{index + 1}</div>
                      )}
                    </TableCell>
                    <TableCell className="font-medium">{project.name}</TableCell>
                    <TableCell className="text-center">
                      <div className="flex items-center justify-center">
                        <div className="inline-block h-2 w-12 bg-secondary rounded-full mr-2 overflow-hidden">
                          <div
                            className="h-full bg-primary"
                            style={{ width: `${project.velocity}%` }}
                          ></div>
                        </div>
                        <span>{project.velocity}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-center">{project.qualityScore}</TableCell>
                    <TableCell className="text-center">
                      <div className="flex items-center justify-center">
                        {getStatusIcon(project.budgetStatus)}
                        <span className="ml-1">{project.budget}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="flex items-center justify-center">
                        {getStatusIcon(project.timelineStatus)}
                        <span className="ml-1">{project.timeline}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="flex items-center justify-center">
                        <span className="text-yellow-500 mr-1">★</span>
                        <span>{project.stakeholderSatisfaction}</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
