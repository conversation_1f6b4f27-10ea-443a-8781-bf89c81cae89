"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, CircleUser, Code, Database, Server } from "lucide-react";
import { useStats } from "@/lib/hooks/useStats";
import { Skeleton } from "@/components/ui/skeleton";
import { Counter } from "@/components/ui/Counter";

export function NetworkOverview() {
  const { data, isLoading, error } = useStats();

  return (
    <Card className="bg-card border-border">
      <CardHeader className="pb-2">
        <CardTitle className="text-base font-normal flex items-center">
          <BarChart className="h-5 w-5 mr-2 text-primary" />
          System Metrics
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="flex flex-col items-center p-2">
            {isLoading ? (
              <Skeleton className="h-16 w-16 rounded-full" />
            ) : (
              <>
                <div className="flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 text-primary mb-2">
                  <CircleUser className="h-6 w-6" />
                </div>
                <Counter
                  value={data?.metrics?.activeUsers || 321}
                  className="text-xl font-medium"
                />
                <span className="text-xs text-muted-foreground mt-1 text-center">Active Users</span>
              </>
            )}
          </div>

          <div className="flex flex-col items-center p-2">
            {isLoading ? (
              <Skeleton className="h-16 w-16 rounded-full" />
            ) : (
              <>
                <div className="flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 text-primary mb-2">
                  <Database className="h-6 w-6" />
                </div>
                <Counter
                  value={data?.metrics?.ontologyEntities || 230}
                  className="text-xl font-medium"
                />
                <span className="text-xs text-muted-foreground mt-1 text-center">Ontology Entities</span>
              </>
            )}
          </div>

          <div className="flex flex-col items-center p-2">
            {isLoading ? (
              <Skeleton className="h-16 w-16 rounded-full" />
            ) : (
              <>
                <div className="flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 text-primary mb-2">
                  <Code className="h-6 w-6" />
                </div>
                <Counter
                  value={data?.metrics?.componentsGenerated || 1482}
                  className="text-xl font-medium"
                />
                <span className="text-xs text-muted-foreground mt-1 text-center">Components</span>
              </>
            )}
          </div>

          <div className="flex flex-col items-center p-2">
            {isLoading ? (
              <Skeleton className="h-16 w-16 rounded-full" />
            ) : (
              <>
                <div className="flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 text-primary mb-2">
                  <Server className="h-6 w-6" />
                </div>
                <Counter
                  value={data?.metrics?.aiAgents || 45}
                  className="text-xl font-medium"
                />
                <span className="text-xs text-muted-foreground mt-1 text-center">AI Agents</span>
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
