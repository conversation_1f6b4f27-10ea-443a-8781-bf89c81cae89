"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { BarChart3, Check<PERSON>ir<PERSON>, Clock, AlertTriangle } from "lucide-react";
import { useStats } from "@/lib/hooks/useStats";
import { Skeleton } from "@/components/ui/skeleton";

export function ProjectsSummary() {
  const { data, isLoading, error } = useStats();

  const projects = {
    active: isLoading ? 0 : data?.projects?.active || 12,
    completed: isLoading ? 0 : data?.projects?.completed || 34,
    pending: isLoading ? 0 : data?.projects?.pending || 8,
    issues: isLoading ? 0 : data?.projects?.issues || 3,
  };

  // Calculate percentage for progress bar
  const total = projects.active + projects.completed + projects.pending;
  const completedPercentage = (projects.completed / total) * 100;
  const activePercentage = (projects.active / total) * 100;
  const pendingPercentage = (projects.pending / total) * 100;

  return (
    <Card className="bg-card border-border overflow-hidden">
      <CardHeader className="pb-2">
        <CardTitle className="text-base font-normal flex items-center">
          <BarChart3 className="h-5 w-5 mr-2 text-primary" />
          Project Summary
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-12 w-full" />
            <div className="flex gap-2">
              <Skeleton className="h-16 w-1/4" />
              <Skeleton className="h-16 w-1/4" />
              <Skeleton className="h-16 w-1/4" />
              <Skeleton className="h-16 w-1/4" />
            </div>
          </div>
        ) : (
          <>
            <div className="h-2 w-full bg-secondary rounded-full overflow-hidden mb-4">
              <div className="flex h-full">
                <div
                  className="h-full bg-green-500"
                  style={{ width: `${completedPercentage}%` }}
                ></div>
                <div
                  className="h-full bg-blue-500"
                  style={{ width: `${activePercentage}%` }}
                ></div>
                <div
                  className="h-full bg-yellow-500"
                  style={{ width: `${pendingPercentage}%` }}
                ></div>
              </div>
            </div>
            <div className="grid grid-cols-4 gap-1">
              <div className="flex flex-col items-center p-2">
                <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 text-primary mb-2">
                  <Clock className="h-5 w-5" />
                </div>
                <span className="text-lg font-medium">{projects.active}</span>
                <span className="text-xs text-muted-foreground">Active</span>
              </div>
              <div className="flex flex-col items-center p-2">
                <div className="flex items-center justify-center w-10 h-10 rounded-full bg-green-500/10 text-green-500 mb-2">
                  <CheckCircle className="h-5 w-5" />
                </div>
                <span className="text-lg font-medium">{projects.completed}</span>
                <span className="text-xs text-muted-foreground">Completed</span>
              </div>
              <div className="flex flex-col items-center p-2">
                <div className="flex items-center justify-center w-10 h-10 rounded-full bg-yellow-500/10 text-yellow-500 mb-2">
                  <Clock className="h-5 w-5" />
                </div>
                <span className="text-lg font-medium">{projects.pending}</span>
                <span className="text-xs text-muted-foreground">Pending</span>
              </div>
              <div className="flex flex-col items-center p-2">
                <div className="flex items-center justify-center w-10 h-10 rounded-full bg-red-500/10 text-red-500 mb-2">
                  <AlertTriangle className="h-5 w-5" />
                </div>
                <span className="text-lg font-medium">{projects.issues}</span>
                <span className="text-xs text-muted-foreground">Issues</span>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
