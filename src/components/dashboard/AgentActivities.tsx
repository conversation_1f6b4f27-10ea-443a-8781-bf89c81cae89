"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON>, Clock, Check, AlertCircle, User, Bo<PERSON> } from "lucide-react";
import { useStats } from "@/lib/hooks/useStats";
import { Skeleton } from "@/components/ui/skeleton";
import Link from "next/link";

export function AgentActivities() {
  const { data, isLoading, error } = useStats();

  // Mock agent activity data
  const activities = isLoading ? [] : data?.agentActivities || [
    {
      id: 1,
      agent: "Meeting Notes Agent",
      action: "Summarized client meeting",
      project: "HealthTech Portal",
      timestamp: "10 min ago",
      status: "completed"
    },
    {
      id: 2,
      agent: "Code Assistant",
      action: "Generated API endpoint",
      project: "Financial Dashboard",
      timestamp: "24 min ago",
      status: "completed"
    },
    {
      id: 3,
      agent: "QA Tester",
      action: "Running regression tests",
      project: "E-commerce Platform",
      timestamp: "1 hour ago",
      status: "in-progress"
    },
    {
      id: 4,
      agent: "<PERSON><PERSON><PERSON> Designer",
      action: "Database optimization",
      project: "Logistics System",
      timestamp: "2 hours ago",
      status: "failed"
    },
    {
      id: 5,
      agent: "UI Assistant",
      action: "Component generation",
      project: "Admin Dashboard",
      timestamp: "3 hours ago",
      status: "completed"
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <Check className="h-4 w-4 text-green-500" />;
      case "in-progress":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case "failed":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-muted-foreground" />;
    }
  };

  return (
    <Card className="bg-card border-border overflow-hidden">
      <CardHeader className="pb-2">
        <CardTitle className="text-base font-normal flex items-center">
          <Brain className="h-5 w-5 mr-2 text-primary" />
          Agent Activities
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-3">
            {[1, 2, 3, 4].map((i) => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
          </div>
        ) : (
          <div className="space-y-1">
            {activities.map((activity) => (
              <div
                key={(activity as any).id || (activity as any)._id}
                className="flex items-center justify-between p-2 rounded-md hover:bg-background transition-colors"
              >
                <div className="flex items-center">
                  <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                    <Bot className="h-4 w-4 text-primary" />
                  </div>
                  <div>
                    <div className="flex items-center">
                      <span className="text-sm font-medium mr-2">{activity.agent}</span>
                      {getStatusIcon(activity.status)}
                    </div>
                    <p className="text-xs text-muted-foreground">{activity.action}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-xs text-muted-foreground">{activity.timestamp}</p>
                  <p className="text-xs">{activity.project}</p>
                </div>
              </div>
            ))}

            <div className="mt-3 pt-3 border-t border-border">
              <Link href="/agents" className="text-xs text-primary hover:underline">
                View all agent activities
              </Link>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
