"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Layers, Database, Network, ArrowRight } from "lucide-react";
import { useStats } from "@/lib/hooks/useStats";
import { Skeleton } from "@/components/ui/skeleton";
import Link from "next/link";

export function OntologyOverview() {
  const { data, isLoading, error } = useStats();

  const ontology = {
    semanticEntities: isLoading ? 0 : data?.ontology?.semanticEntities || 124,
    kineticEntities: isLoading ? 0 : data?.ontology?.kineticEntities || 67,
    dynamicEntities: isLoading ? 0 : data?.ontology?.dynamicEntities || 39,
    relationships: isLoading ? 0 : data?.ontology?.relationships || 215,
  };

  return (
    <Card className="bg-card border-border overflow-hidden">
      <CardHeader className="pb-2">
        <CardTitle className="text-base font-normal flex items-center">
          <Layers className="h-5 w-5 mr-2 text-primary" />
          Ontology Structure
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-8 w-1/2" />
          </div>
        ) : (
          <>
            <div className="space-y-3 mb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-primary mr-2"></div>
                  <span className="text-sm">Semantic Layer</span>
                </div>
                <span className="font-medium">{ontology.semanticEntities}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-chart-2 mr-2"></div>
                  <span className="text-sm">Kinetic Layer</span>
                </div>
                <span className="font-medium">{ontology.kineticEntities}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-chart-3 mr-2"></div>
                  <span className="text-sm">Dynamic Layer</span>
                </div>
                <span className="font-medium">{ontology.dynamicEntities}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-chart-4 mr-2"></div>
                  <span className="text-sm">Relationships</span>
                </div>
                <span className="font-medium">{ontology.relationships}</span>
              </div>
            </div>
            <div className="mt-3 pt-3 border-t border-border">
              <Link href="/ontology" className="flex items-center text-sm text-primary hover:underline">
                View full ontology <ArrowRight className="h-4 w-4 ml-1" />
              </Link>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
