"use client";

import { useState, useRef, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Brain, ArrowUp, <PERSON>fresh<PERSON><PERSON>, <PERSON>py, Check, Archive, Sparkles, MessageSquare } from "lucide-react";

interface Message {
  id: string;
  role: "user" | "assistant" | "system";
  content: string;
  timestamp: Date;
}

interface Suggestion {
  id: string;
  text: string;
}

interface AIAssistPanelProps {
  context?: string;
  onSuggestionSelect?: (suggestion: string) => void;
}

export function AIAssistPanel({ context, onSuggestionSelect }: AIAssistPanelProps) {
  const [input, setInput] = useState("");
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "welcome",
      role: "assistant",
      content: "Hello! I'm your ALIAS MOSAIC AI assistant. How can I help you with your project today?",
      timestamp: new Date()
    }
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const [suggestions, setSuggestions] = useState<Suggestion[]>([
    { id: "s1", text: "Generate an ontology entity" },
    { id: "s2", text: "Create a project timeline" },
    { id: "s3", text: "Analyze my project's architecture" },
    { id: "s4", text: "Suggest improvements for my data model" }
  ]);
  const [copiedId, setCopiedId] = useState<string | null>(null);

  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Handle message submission
  const handleSendMessage = () => {
    if (!input.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: input,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInput("");
    setIsLoading(true);

    // Simulate AI response after a delay
    setTimeout(() => {
      const responses = [
        "I've analyzed your ontology and found some potential improvements. Would you like me to suggest specific changes?",
        "Based on your project context, I recommend creating a kinetic layer entity to handle the business process flow. Want me to generate a template?",
        "I can see some optimization opportunities in your data model. The current structure could be more efficient with a few adjustments.",
        "I've identified some patterns in your project that might benefit from AI agent automation. Would you like me to elaborate?",
        "Looking at your timeline, there might be a dependency issue between these two tasks. Let me suggest a better sequence."
      ];

      const assistantMessage: Message = {
        id: Date.now().toString(),
        role: "assistant",
        content: responses[Math.floor(Math.random() * responses.length)],
        timestamp: new Date()
      };

      setMessages(prev => [...prev, assistantMessage]);

      // Update suggestions based on conversation
      setSuggestions([
        { id: `s${Date.now()}-1`, text: "Show me the changes" },
        { id: `s${Date.now()}-2`, text: "Generate code for this" },
        { id: `s${Date.now()}-3`, text: "Export this to my ontology" },
        { id: `s${Date.now()}-4`, text: "Create a visualization" }
      ]);

      setIsLoading(false);
    }, 1500);
  };

  // Handle suggestion click
  const handleSuggestionClick = (suggestion: Suggestion) => {
    setInput(suggestion.text);
    if (onSuggestionSelect) {
      onSuggestionSelect(suggestion.text);
    }
  };

  // Handle copy message
  const handleCopyMessage = (messageId: string, content: string) => {
    navigator.clipboard.writeText(content);
    setCopiedId(messageId);
    setTimeout(() => setCopiedId(null), 2000);
  };

  return (
    <Card className="bg-card border-border flex flex-col h-full">
      <CardHeader className="pb-2 flex flex-row items-center justify-between">
        <CardTitle className="text-base font-normal flex items-center">
          <Brain className="h-5 w-5 mr-2 text-primary" />
          AI Assistant
        </CardTitle>
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <Archive className="h-4 w-4" />
        </Button>
      </CardHeader>
      <CardContent className="flex-grow overflow-y-auto p-3">
        <div className="space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
            >
              <div
                className={`max-w-[75%] rounded-lg px-3 py-2 text-sm relative group
                  ${message.role === "user"
                    ? "bg-primary text-primary-foreground"
                    : "bg-secondary text-secondary-foreground"
                  }`}
              >
                <p>{message.content}</p>
                <div className="mt-1 text-xs opacity-70">
                  {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </div>

                {message.role === "assistant" && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() => handleCopyMessage(message.id, message.content)}
                  >
                    {copiedId === message.id ? (
                      <Check className="h-3 w-3" />
                    ) : (
                      <Copy className="h-3 w-3" />
                    )}
                  </Button>
                )}
              </div>
            </div>
          ))}
          {isLoading && (
            <div className="flex justify-start">
              <div className="bg-secondary max-w-[75%] rounded-lg px-3 py-2 text-sm">
                <RefreshCw className="h-4 w-4 animate-spin text-primary" />
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
      </CardContent>

      <div className="p-3 border-t border-border">
        <div className="flex flex-wrap gap-2 mb-3">
          {suggestions.map((suggestion) => (
            <button
              key={suggestion.id}
              className="px-2 py-1 text-xs bg-primary/10 text-primary rounded-full hover:bg-primary/20 transition-colors"
              onClick={() => handleSuggestionClick(suggestion)}
            >
              <Sparkles className="h-3 w-3 inline-block mr-1" />
              {suggestion.text}
            </button>
          ))}
        </div>

        <div className="flex gap-2">
          <Input
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Ask the MOSAIC assistant..."
            className="flex-grow bg-background border-muted"
            onKeyDown={(e) => {
              if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage();
              }
            }}
          />
          <Button
            onClick={handleSendMessage}
            disabled={isLoading || !input.trim()}
            className="bg-primary text-primary-foreground"
          >
            <ArrowUp className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </Card>
  );
}
