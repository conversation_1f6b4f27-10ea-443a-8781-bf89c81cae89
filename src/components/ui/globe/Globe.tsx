"use client";

import React, { useRef, useEffect, useState } from "react";
import { <PERSON>vas, useThree } from "@react-three/fiber";
import { OrbitControls, Html } from "@react-three/drei";
import * as THREE from "three";

// Earth radius
const EARTH_RADIUS = 5;
const DEFAULT_COLOR = "#3060D1"; // Default color for points

// Activity point component
function ActivityPoint({ lat, lng, size = 0.15, color = DEFAULT_COLOR, label, rotation }: any) {
  const [showTooltip, setShowTooltip] = useState(false);

  // Convert lat/lng to 3D position
  const phi = (90 - lat) * (Math.PI / 180);
  const theta = (lng + 180) * (Math.PI / 180);

  const x = -(EARTH_RADIUS * Math.sin(phi) * Math.cos(theta));
  const y = EARTH_RADIUS * Math.cos(phi);
  const z = EARTH_RADIUS * Math.sin(phi) * Math.sin(theta);

  // Small random offset to avoid z-fighting
  const offset = Math.random() * 0.05;

  return (
    <group position={[x, y, z]} rotation={rotation}>
      <mesh
        position={[0, 0, offset]}
        onPointerOver={() => setShowTooltip(true)}
        onPointerOut={() => setShowTooltip(false)}
      >
        <sphereGeometry args={[size, 16, 16]} />
        <meshBasicMaterial color={color} transparent opacity={0.8} />
        {showTooltip && label && (
          <Html
            position={[0, size + 0.5, 0]}
            style={{
              background: 'rgba(0, 0, 0, 0.8)',
              padding: '8px 12px',
              borderRadius: '4px',
              color: 'white',
              fontSize: '12px',
              pointerEvents: 'none',
              whiteSpace: 'nowrap',
              transform: 'translate(-50%, -100%)',
              maxWidth: '200px'
            }}
          >
            {label}
          </Html>
        )}
      </mesh>
    </group>
  );
}

// Earth component with wireframe
function Earth({ points = [], children }: any) {
  const earthRef = useRef();
  const [rotation, setRotation] = useState([0, 0, 0]);

  // Rotate the earth slowly
  useEffect(() => {
    const interval = setInterval(() => {
      setRotation(prev => [prev[0], prev[1] - 0.002, prev[2]]);
    }, 30);

    return () => clearInterval(interval);
  }, []);

  return (
    <group ref={earthRef} rotation={rotation as any}>
      {/* Earth wireframe sphere */}
      <mesh>
        <sphereGeometry args={[EARTH_RADIUS, 64, 64]} />
        <meshBasicMaterial
          color="#30354d"
          wireframe
          transparent
          opacity={0.3}
        />
      </mesh>

      {/* Activity points */}
      {points.map((point: any, index: number) => (
        <ActivityPoint
          key={index}
          lat={point.lat}
          lng={point.lng}
          size={point.size || 0.15}
          color={point.color || DEFAULT_COLOR}
          label={point.label}
          rotation={[-rotation[0], -rotation[1], -rotation[2]]} // Counter-rotate to keep points facing the camera
        />
      ))}

      {children}
    </group>
  );
}

// Main globe component
export default function Globe({ points = [] }: any) {
  return (
    <Canvas
      camera={{
        position: [0, 0, 16],
        fov: 45,
        near: 0.1,
        far: 1000
      }}
      style={{ background: "transparent" }}
    >
      <ambientLight intensity={0.8} />
      <spotLight position={[10, 10, 10]} angle={0.15} penumbra={1} />
      <Earth points={points} />
      <OrbitControls
        enableZoom={false}
        enablePan={false}
        rotateSpeed={0.4}
        autoRotate
        autoRotateSpeed={0.5}
      />
    </Canvas>
  );
}
