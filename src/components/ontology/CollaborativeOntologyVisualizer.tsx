"use client";

import { useState, useRef, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { RefreshCw, ZoomIn, ZoomOut, Maximize, Minimize, Filter, Download, Users, User } from "lucide-react";
import { OntologyVisualizer, type OntologyGraph } from "./OntologyVisualizer";
import { CollaborationProvider, useCollaboration, type User as CollaborationUser } from "./CollaborationProvider";

// User cursor component
function UserCursor({ user }: { user: CollaborationUser }) {
  if (!user.cursor) return null;

  return (
    <div
      className="absolute pointer-events-none z-50 flex flex-col items-center"
      style={{
        left: user.cursor.x,
        top: user.cursor.y,
        transform: 'translate(-10px, -10px)',
        opacity: user.active ? 1 : 0.3
      }}
    >
      {/* <PERSON>ursor */}
      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M5 2.5L16 10L5 17.5V2.5Z" fill={user.color} stroke={user.color} strokeWidth="1.5" />
      </svg>

      {/* User label */}
      <div
        className="px-2 py-0.5 rounded text-xs text-white leading-none whitespace-nowrap"
        style={{ backgroundColor: user.color, marginTop: '2px' }}
      >
        {user.name}
      </div>
    </div>
  );
}

// Users list component
function UsersList() {
  const { users, currentUser } = useCollaboration();
  const [expanded, setExpanded] = useState(false);

  // Filter out current user and get active users first
  const sortedUsers = [...users]
    .filter(user => user.id !== currentUser?.id)
    .sort((a, b) => {
      // Active users first
      if (a.active && !b.active) return -1;
      if (!a.active && b.active) return 1;

      // Then sort by last active time
      return new Date(b.lastActive).getTime() - new Date(a.lastActive).getTime();
    });

  const activeCount = sortedUsers.filter(user => user.active).length;

  return (
    <div className="absolute top-4 right-4 z-40">
      <Button
        variant="outline"
        size="sm"
        className="mb-2 bg-background/80 backdrop-blur-sm"
        onClick={() => setExpanded(!expanded)}
      >
        <Users className="h-4 w-4 mr-1" />
        {activeCount} {activeCount === 1 ? 'user' : 'users'}
      </Button>

      {expanded && (
        <div className="bg-background/80 backdrop-blur-sm border border-border rounded-md p-2 w-48">
          <div className="text-xs font-medium mb-2">Collaborators</div>
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {sortedUsers.map(user => (
              <div key={user.id} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div
                    className="w-2 h-2 rounded-full mr-2"
                    style={{ backgroundColor: user.color }}
                  />
                  <span className="text-xs truncate max-w-[100px]">{user.name}</span>
                </div>
                <div
                  className={`w-2 h-2 rounded-full ${user.active ? 'bg-green-500' : 'bg-gray-400'}`}
                  title={user.active ? 'Online' : 'Offline'}
                />
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

// Collaboration wrapper for OntologyVisualizer
function CollaborativeVisualizerInner({ data, height, width }: { data: OntologyGraph, height?: number, width?: number }) {
  const { users, currentUser, sendMessage, isConnected } = useCollaboration();
  const containerRef = useRef<HTMLDivElement>(null);
  const [zoom, setZoom] = useState(1);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);

  // Track mouse movement
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!containerRef.current) return;

      const rect = containerRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      setMousePosition({ x, y });

      // Send cursor position update
      if (isConnected && !isDragging) {
        sendMessage({
          type: "cursor_move",
          data: { cursor: { x, y } } as any
        });
      }
    };

    const handleMouseDown = () => {
      setIsDragging(true);
    };

    const handleMouseUp = () => {
      setIsDragging(false);
    };

    if (containerRef.current) {
      containerRef.current.addEventListener("mousemove", handleMouseMove);
      containerRef.current.addEventListener("mousedown", handleMouseDown);
      window.addEventListener("mouseup", handleMouseUp);
    }

    return () => {
      if (containerRef.current) {
        containerRef.current.removeEventListener("mousemove", handleMouseMove);
        containerRef.current.removeEventListener("mousedown", handleMouseDown);
      }
      window.removeEventListener("mouseup", handleMouseUp);
    };
  }, [isConnected, sendMessage, isDragging]);

  // Handle zoom change
  const handleZoomChange = (newZoom: number) => {
    setZoom(newZoom);

    // Send zoom update
    sendMessage({
      type: "zoom",
      data: { level: newZoom } as any
    });
  };

  // Handle node movement
  const handleNodeMove = (nodeId: string, position: { x: number, y: number }) => {
    // Send node move update
    sendMessage({
      type: "node_move",
      data: { nodeId, position }
    });
  };

  return (
    <div
      ref={containerRef}
      className="relative"
      style={{ height: `${height || 600}px`, width: width ? `${width}px` : '100%' }}
    >
      {/* Visualizer component */}
      <OntologyVisualizer
        data={data}
        height={height}
        width={width}
      />

      {/* User cursors */}
      {users
        .filter(user => user.id !== currentUser?.id && user.cursor && user.active)
        .map(user => (
          <UserCursor key={user.id} user={user} />
      ))}

      {/* User list */}
      <UsersList />

      {/* Connection status */}
      {!isConnected && (
        <div className="absolute bottom-4 left-4 bg-yellow-500/80 text-black px-3 py-1 rounded-full text-xs flex items-center">
          <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
          Reconnecting...
        </div>
      )}
    </div>
  );
}

// Main component with CollaborationProvider
export function CollaborativeOntologyVisualizer({
  projectId = "demo-project",
  data,
  height,
  width
}: {
  projectId?: string,
  data: OntologyGraph,
  height?: number,
  width?: number
}) {
  return (
    <Card className="bg-card border-border w-full overflow-hidden">
      <CardHeader className="pb-2 flex flex-row items-center justify-between">
        <CardTitle className="text-base font-normal">Ontology Visualization</CardTitle>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
          >
            Semantic
          </Button>
          <Button
            variant="outline"
            size="sm"
          >
            Kinetic
          </Button>
          <Button
            variant="outline"
            size="sm"
          >
            Dynamic
          </Button>
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-0 relative">
        <CollaborationProvider projectId={projectId}>
          <CollaborativeVisualizerInner
            data={data}
            height={height}
            width={width}
          />
        </CollaborationProvider>
      </CardContent>
    </Card>
  );
}
