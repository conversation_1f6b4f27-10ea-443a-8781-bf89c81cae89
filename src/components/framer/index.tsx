"use client";

import { cn } from "@/lib/utils";
import type { ReactNode, CSSProperties } from "react";

// Re-export all Framer components with TypeScript support
// These are dynamically imported to reduce bundle size

export interface FramerComponentProps {
  children?: ReactNode;
  className?: string;
  style?: CSSProperties;
  id?: string;
  width?: any;
  height?: any;
  layoutId?: string;
  locale?: string;
}

// Component wrapper to handle styling conflicts
export const FramerWrapper = ({ 
  children, 
  className,
  isolateStyles = true,
  ...props 
}: FramerComponentProps & { isolateStyles?: boolean }) => {
  return (
    <div 
      className={cn(
        "framer-component",
        isolateStyles && "isolate",
        className
      )} 
      {...props}
    >
      {children}
    </div>
  );
};

// Lazy load Framer components to optimize bundle size
import dynamic from 'next/dynamic';

// Layout Components
export const FramerNavbar = dynamic(() => import('./components/navbar.js'), {
  loading: () => <div>Loading navbar...</div>,
  ssr: false
});

export const FramerHero = dynamic(() => import('./components/hero.js'), {
  loading: () => <div>Loading hero...</div>,
  ssr: false
});

export const FramerBanner = dynamic(() => import('./components/banner.js'), {
  loading: () => <div>Loading banner...</div>,
  ssr: false
});

export const FramerGradientBackground = dynamic(() => import('./components/gradient-background.js'), {
  loading: () => <div>Loading...</div>,
  ssr: false
});

// Button Components
export const FramerLargeButton = dynamic(() => import('./components/large-button.js'), {
  loading: () => <div>Loading...</div>,
  ssr: false
});

export const FramerSmallButton = dynamic(() => import('./components/small-button.js'), {
  loading: () => <div>Loading...</div>,
  ssr: false
});

// Card Components
export const FramerCard = dynamic(() => import('./components/card.js'), {
  loading: () => <div>Loading...</div>,
  ssr: false
});

export const FramerArticleCard = dynamic(() => import('./components/article-card.js'), {
  loading: () => <div>Loading...</div>,
  ssr: false
});

export const FramerProjectCard = dynamic(() => import('./components/project-card.js'), {
  loading: () => <div>Loading...</div>,
  ssr: false
});

export const FramerContactCard = dynamic(() => import('./components/contact-card.js'), {
  loading: () => <div>Loading...</div>,
  ssr: false
});

// Content Sections
export const FramerTestimonial = dynamic(() => import('./components/testimonial.js'), {
  loading: () => <div>Loading...</div>,
  ssr: false
});

export const FramerFAQ = dynamic(() => import('./components/faq.js'), {
  loading: () => <div>Loading...</div>,
  ssr: false
});

export const FramerServices = dynamic(() => import('./components/services.js'), {
  loading: () => <div>Loading...</div>,
  ssr: false
});

export const FramerProjects = dynamic(() => import('./components/projects.js'), {
  loading: () => <div>Loading...</div>,
  ssr: false
});

// Interactive Elements
export const FramerRating = dynamic(() => import('./components/rating.js'), {
  loading: () => <div>Loading...</div>,
  ssr: false
});

export const FramerRatingStars = dynamic(() => import('./components/rating-stars.js'), {
  loading: () => <div>Loading...</div>,
  ssr: false
});

export const FramerVideo = dynamic(() => import('./components/video.js'), {
  loading: () => <div>Loading...</div>,
  ssr: false
});

export const FramerTicker = dynamic(() => import('./components/ticker.js'), {
  loading: () => <div>Loading...</div>,
  ssr: false
});

// Form Components
export const FramerInput = dynamic(() => import('./components/input.js'), {
  loading: () => <div>Loading...</div>,
  ssr: false
});

export const FramerEmail = dynamic(() => import('./components/email.js'), {
  loading: () => <div>Loading...</div>,
  ssr: false
});

// Utility function to bridge Tailwind theme to Framer CSS variables
export const setupFramerTheme = () => {
  if (typeof window === 'undefined') return;
  
  const root = document.documentElement;
  const computedStyle = getComputedStyle(root);
  
  // Map Tailwind CSS variables to Framer equivalents
  const themeMapping = {
    '--unframer-black': computedStyle.getPropertyValue('--foreground'),
    '--unframer-white': computedStyle.getPropertyValue('--background'),
    '--unframer-primary': computedStyle.getPropertyValue('--primary'),
    '--unframer-secondary': computedStyle.getPropertyValue('--secondary'),
    '--unframer-accent': computedStyle.getPropertyValue('--accent'),
    '--unframer-muted': computedStyle.getPropertyValue('--muted'),
    '--unframer-border': computedStyle.getPropertyValue('--border'),
  };
  
  Object.entries(themeMapping).forEach(([framerVar, tailwindValue]) => {
    if (tailwindValue) {
      root.style.setProperty(framerVar, tailwindValue);
    }
  });
};