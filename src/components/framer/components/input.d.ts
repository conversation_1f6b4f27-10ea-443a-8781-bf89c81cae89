/* This file was generated by Unframer, do not edit manually */
import type * as React from "react"

import type { UnframerBreakpoint } from "unframer"

type Locale = string
export interface Props {
    children?: React.ReactNode
    locale?: Locale
    style?: React.CSSProperties
    className?: string
    id?: string
    width?: any
    height?: any
    layoutId?: string
    "variant"?: 'Desktop' | 'Phone'
    "type"?: 'Text' | 'Text Area' | 'Email' | 'Number' | 'Phone Number' | 'URL' | 'Date' | 'Time'
    "name"?: string
    "placeholder"?: string
    "required"?: boolean
}

const InputFramerComponent = (props: Props) => any

type VariantsMap = Partial<Record<UnframerBreakpoint, Props['variant']>> & { base: Props['variant'] }

InputFramerComponent.Responsive = (props: Omit<Props, 'variant'> & {variants?: VariantsMap}) => any

export default InputFramerComponent

