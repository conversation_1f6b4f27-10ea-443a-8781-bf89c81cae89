/* This file was generated by Unframer, do not edit manually */
import type * as React from "react"

import type { UnframerBreakpoint } from "unframer"

type Locale = string
export interface Props {
    children?: React.ReactNode
    locale?: Locale
    style?: React.CSSProperties
    className?: string
    id?: string
    width?: any
    height?: any
    layoutId?: string
    "variant"?: 'Previous Desktop' | 'Next Desktop' | 'Next Phone' | 'Previous Phone'
    "link"?: string
    "image"?: {src: string, srcSet?: string, alt?: string}
    "title"?: string
}

const MoreArticlesFramerComponent = (props: Props) => any

type VariantsMap = Partial<Record<UnframerBreakpoint, Props['variant']>> & { base: Props['variant'] }

MoreArticlesFramerComponent.Responsive = (props: Omit<Props, 'variant'> & {variants?: VariantsMap}) => any

export default MoreArticlesFramerComponent

