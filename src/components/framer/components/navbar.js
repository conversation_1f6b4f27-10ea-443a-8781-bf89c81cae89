// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";
import {
  stdin_default as stdin_default5
} from "./chunks/chunk-CUJMJG57.js";
import {
  stdin_default as stdin_default4
} from "./chunks/chunk-NZSA2AZS.js";
import {
  stdin_default as stdin_default2
} from "./chunks/chunk-63EFHLNF.js";
import {
  stdin_default as stdin_default3
} from "./chunks/chunk-QM54KZT3.js";
import {
  stdin_default
} from "./chunks/chunk-3IUEC7EM.js";
import {
  className,
  css,
  fonts
} from "./chunks/chunk-D6DQ2HQ2.js";
import "./chunks/chunk-3LMAB7ZO.js";

// virtual:navbar
import { Fragment as Fragment2 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/5aH8sTCuBXkEF28bXDlY/6zW6pjqXlUWAuM8CvRvT/MH9m4cXzT.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ComponentViewportProvider, ControlType, cx, getFonts, getFontsFromSharedStyle, Link, ResolveLinks, RichText, SmartComponentScopedContainer, useActiveVariantCallback, useComponentViewport, useLocaleInfo, useRouter, useVariantState, withCSS, withFX } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";

// /:https://framerusercontent.com/modules/t4d3RN8YpjGEeIkhpaWX/8KBE3N3fhG5ieFPwOZtk/nCQNaN8LD.js
import { fontStore } from "unframer";
fontStore.loadFonts([]);
var fonts2 = [{ explicitInter: true, fonts: [] }];
var css2 = [".framer-m9VkI .framer-styles-preset-1wi7vce:not(.rich-text-wrapper), .framer-m9VkI .framer-styles-preset-1wi7vce.rich-text-wrapper a { --framer-link-current-text-color: #111111; --framer-link-current-text-decoration: none; --framer-link-hover-text-color: rgba(10, 10, 10, 0.8); --framer-link-hover-text-decoration: none; --framer-link-text-color: var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, #0a0a0a); --framer-link-text-decoration: none; transition: color 0.2s cubic-bezier(0.44, 0, 0.56, 1) 0s; }"];
var className2 = "framer-m9VkI";

// /:https://framerusercontent.com/modules/kYJ0hJFhdtm6G3e3QyO6/g4rpjyJSfNJrlQAnvv8a/ypR5VEWEl.js
import { fontStore as fontStore2 } from "unframer";
fontStore2.loadFonts(["Inter-Medium", "Inter-Bold", "Inter-BoldItalic", "Inter-MediumItalic"]);
var fonts3 = [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5A3Ce6C9YYmCjpQx9M4inSaKU.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/Qx95Xyt0Ka3SGhinnbXIGpEIyP4.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/6mJuEAguuIuMog10gGvH5d3cl8.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/xYYWaj7wCU5zSQH0eXvSaS19wo.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/otTaNuNpVK4RbdlT7zDDdKvQBA.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/d3tHnaQIAeqiE5hGcRw4mmgWYU.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/DolVirEGb34pEXEp8t8FQBSK4.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/DpPBYI0sL4fYLgAkX8KXOPVt7c.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/4RAEQdEOrcnDkhHiiCbJOw92Lk.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/1K3W8DizY3v4emK8Mb08YHxTbs.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/tUSCtfYVM1I1IchuyCwz9gDdQ.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/VgYFWiwsAC5OYxAycRXXvhze58.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/DXD0Q7LSl7HEvDzucnyLnGBHM.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/GIryZETIX4IFypco5pYZONKhJIo.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/H89BbHkbHDzlxZzxi8uPzTsp90.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/u6gJwDuwB143kpNK1T1MDKDWkMc.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/43sJ6MfOPh1LCJt46OvyDuSbA6o.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/wccHG0r4gBDAIRhfHiOlq6oEkqw.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/WZ367JPwf9bRW6LdTHN8rXgSjw.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/QxmhnWTzLtyjIiZcfaLIJ8EFBXU.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/2A4Xx7CngadFGlVV4xrO06OBHY.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/khkJkwSL66WFg8SX6Wa726c.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/0E7IMbDzcGABpBwwqNEt60wU0w.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/NTJ0nQgIF0gcDelS14zQ9NR9Q.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/QrcNhgEPfRl0LS8qz5Ln8olanl8.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/JEXmejW8mXOYMtt0hyRg811kHac.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/ksvR4VsLksjpSwnC2fPgHRNMw.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/uy9s0iWuxiNnVt8EpTI3gzohpwo.woff2", weight: "500" }] }];
var css3 = ['.framer-0CPYn .framer-styles-preset-1mf8d9g:not(.rich-text-wrapper), .framer-0CPYn .framer-styles-preset-1mf8d9g.rich-text-wrapper p { --framer-font-family: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-open-type-features: normal; --framer-font-size: 15px; --framer-font-style: normal; --framer-font-style-bold: normal; --framer-font-style-bold-italic: italic; --framer-font-style-italic: italic; --framer-font-variation-axes: normal; --framer-font-weight: 500; --framer-font-weight-bold: 700; --framer-font-weight-bold-italic: 700; --framer-font-weight-italic: 500; --framer-letter-spacing: -0.04em; --framer-line-height: 140%; --framer-paragraph-spacing: 20px; --framer-text-alignment: start; --framer-text-color: var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, #0a0a0a); --framer-text-decoration: none; --framer-text-stroke-color: initial; --framer-text-stroke-width: initial; --framer-text-transform: none; }', '@media (max-width: 1199px) and (min-width: 810px) { .framer-0CPYn .framer-styles-preset-1mf8d9g:not(.rich-text-wrapper), .framer-0CPYn .framer-styles-preset-1mf8d9g.rich-text-wrapper p { --framer-font-family: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-open-type-features: normal; --framer-font-size: 14px; --framer-font-style: normal; --framer-font-style-bold: normal; --framer-font-style-bold-italic: italic; --framer-font-style-italic: italic; --framer-font-variation-axes: normal; --framer-font-weight: 500; --framer-font-weight-bold: 700; --framer-font-weight-bold-italic: 700; --framer-font-weight-italic: 500; --framer-letter-spacing: -0.04em; --framer-line-height: 140%; --framer-paragraph-spacing: 20px; --framer-text-alignment: start; --framer-text-color: var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, #0a0a0a); --framer-text-decoration: none; --framer-text-stroke-color: initial; --framer-text-stroke-width: initial; --framer-text-transform: none; } }', '@media (max-width: 809px) and (min-width: 0px) { .framer-0CPYn .framer-styles-preset-1mf8d9g:not(.rich-text-wrapper), .framer-0CPYn .framer-styles-preset-1mf8d9g.rich-text-wrapper p { --framer-font-family: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-open-type-features: normal; --framer-font-size: 13px; --framer-font-style: normal; --framer-font-style-bold: normal; --framer-font-style-bold-italic: italic; --framer-font-style-italic: italic; --framer-font-variation-axes: normal; --framer-font-weight: 500; --framer-font-weight-bold: 700; --framer-font-weight-bold-italic: 700; --framer-font-weight-italic: 500; --framer-letter-spacing: -0.04em; --framer-line-height: 140%; --framer-paragraph-spacing: 20px; --framer-text-alignment: start; --framer-text-color: var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, #0a0a0a); --framer-text-decoration: none; --framer-text-stroke-color: initial; --framer-text-stroke-width: initial; --framer-text-transform: none; } }'];
var className3 = "framer-0CPYn";

// /:https://framerusercontent.com/modules/5aH8sTCuBXkEF28bXDlY/6zW6pjqXlUWAuM8CvRvT/MH9m4cXzT.js
var LogoFonts = getFonts(stdin_default4);
var MenuItemFonts = getFonts(stdin_default2);
var HamburgerFonts = getFonts(stdin_default);
var MenuItemLargeFonts = getFonts(stdin_default3);
var MotionDivWithFX = withFX(motion.div);
var EmailFonts = getFonts(stdin_default5);
var cycleOrder = ["nQMAqLr57", "dBl3NGC5f", "f0ZG9nWNx", "gNqyhurpQ", "koDxrtEXx", "gKutwTn3I"];
var serializationHash = "framer-hVgn6";
var variantClassNames = { dBl3NGC5f: "framer-v-v89dtt", f0ZG9nWNx: "framer-v-1sabfjt", gKutwTn3I: "framer-v-12w42s8", gNqyhurpQ: "framer-v-14j3axz", koDxrtEXx: "framer-v-1dcusg4", nQMAqLr57: "framer-v-bzu5mb" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { delay: 0, duration: 0.5, ease: [0.96, -0.02, 0.38, 1.01], type: "tween" };
var animation = { opacity: 0, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, x: 0, y: -40 };
var transition2 = { damping: 30, delay: 0, mass: 1, stiffness: 210, type: "spring" };
var animation1 = { opacity: 0, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, transition: transition2, x: 0, y: -40 };
var transition3 = { damping: 30, delay: 0.06, mass: 1, stiffness: 210, type: "spring" };
var animation2 = { opacity: 0, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, transition: transition3, x: 0, y: -40 };
var transition4 = { damping: 30, delay: 0.12, mass: 1, stiffness: 210, type: "spring" };
var animation3 = { opacity: 0, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, transition: transition4, x: 0, y: -40 };
var transition5 = { damping: 30, delay: 0.18, mass: 1, stiffness: 210, type: "spring" };
var animation4 = { opacity: 0, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, transition: transition5, x: 0, y: -40 };
var transition6 = { damping: 30, delay: 0.24, mass: 1, stiffness: 210, type: "spring" };
var animation5 = { opacity: 0, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, transition: transition6, x: 0, y: -40 };
var transformTemplate1 = (_, t) => `translateX(-50%) ${t}`;
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { "Desktop open": "nQMAqLr57", "Phone open": "koDxrtEXx", "Tablet open": "f0ZG9nWNx", Desktop: "dBl3NGC5f", Phone: "gKutwTn3I", Tablet: "gNqyhurpQ" };
var getProps = ({ height, id, width, ...props }) => {
  return { ...props, variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "nQMAqLr57" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className: className4, layoutId, variant, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "nQMAqLr57", ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const { activeVariantCallback, delay } = useActiveVariantCallback(baseVariant);
  const onTap1m6xaby = activeVariantCallback(async (...args) => {
    setVariant("dBl3NGC5f");
  });
  const onTap8gqdx5 = activeVariantCallback(async (...args) => {
    setVariant("gNqyhurpQ");
  });
  const onTap1vkj13f = activeVariantCallback(async (...args) => {
    setVariant("gKutwTn3I");
  });
  const onTapyjrdku = activeVariantCallback(async (...args) => {
    setVariant("nQMAqLr57");
  });
  const onTap6wu28k = activeVariantCallback(async (...args) => {
    setVariant("f0ZG9nWNx");
  });
  const onTapsjv12q = activeVariantCallback(async (...args) => {
    setVariant("koDxrtEXx");
  });
  const LIy6SD5oa1m6xaby = activeVariantCallback(async (...args) => {
    setVariant("dBl3NGC5f");
  });
  const LIy6SD5oa8gqdx5 = activeVariantCallback(async (...args) => {
    setVariant("gNqyhurpQ");
  });
  const LIy6SD5oa1vkj13f = activeVariantCallback(async (...args) => {
    setVariant("gKutwTn3I");
  });
  const sharedStyleClassNames = [className2, className3, className];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  const isDisplayed = () => {
    if (["gNqyhurpQ", "gKutwTn3I"].includes(baseVariant)) return false;
    return true;
  };
  const router = useRouter();
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsxs(motion.header, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-bzu5mb", className4, classNames), "data-framer-name": "Desktop open", layoutDependency, layoutId: "nQMAqLr57", ref: refBinding, style: { backdropFilter: "none", backgroundColor: "rgb(255, 255, 255)", WebkitBackdropFilter: "none", ...style }, variants: { dBl3NGC5f: { backdropFilter: "blur(7px)", backgroundColor: "var(--token-eea70a16-506d-4b3b-87b7-e85e653a6e7c, rgb(245, 245, 245))", WebkitBackdropFilter: "blur(7px)" }, f0ZG9nWNx: { backdropFilter: "none", WebkitBackdropFilter: "none" }, gKutwTn3I: { backdropFilter: "none", backgroundColor: "var(--token-eea70a16-506d-4b3b-87b7-e85e653a6e7c, rgb(245, 245, 245))", WebkitBackdropFilter: "none" }, gNqyhurpQ: { backdropFilter: "none", backgroundColor: "var(--token-eea70a16-506d-4b3b-87b7-e85e653a6e7c, rgb(245, 245, 245))", WebkitBackdropFilter: "none" }, koDxrtEXx: { backdropFilter: "none", WebkitBackdropFilter: "none" } }, ...addPropertyOverrides({ dBl3NGC5f: { "data-framer-name": "Desktop" }, f0ZG9nWNx: { "data-framer-name": "Tablet open" }, gKutwTn3I: { "data-framer-name": "Phone" }, gNqyhurpQ: { "data-framer-name": "Tablet" }, koDxrtEXx: { "data-framer-name": "Phone open" } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsxs(motion.nav, { className: "framer-120hnkq", "data-framer-name": "Top", layoutDependency, layoutId: "AB7kPYq7F", children: [/* @__PURE__ */ _jsx(Link, { href: { webPageId: "augiA20Il" }, motionChild: true, nodeId: "XeBkzUx83", scopeId: "MH9m4cXzT", children: /* @__PURE__ */ _jsx(motion.a, { className: "framer-iogiyt framer-1a90gm8", "data-framer-name": "Link", "data-highlight": true, layoutDependency, layoutId: "XeBkzUx83", onTap: onTap1m6xaby, ...addPropertyOverrides({ dBl3NGC5f: { "data-highlight": void 0, onTap: void 0 }, f0ZG9nWNx: { onTap: onTap8gqdx5 }, gKutwTn3I: { "data-highlight": void 0, onTap: void 0 }, gNqyhurpQ: { "data-highlight": void 0, onTap: void 0 }, koDxrtEXx: { onTap: onTap1vkj13f } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 22, y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 55 - 690.4) / 1 * 0) + 0 + 0, ...addPropertyOverrides({ dBl3NGC5f: { y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 38 - 22) / 1 * 0) + 0 + 0 }, f0ZG9nWNx: { y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 55 - 820.6) / 1 * 0) + 0 + 0 }, gKutwTn3I: { y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 38 - 22) / 1 * 0) + 0 + 0 }, gNqyhurpQ: { y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 38 - 22) / 1 * 0) + 0 + 0 }, koDxrtEXx: { y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 59 - 750.6) / 1 * 0) + 0 + 0 } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-kxb2zt-container", layoutDependency, layoutId: "LEnasTN5x-container", nodeId: "LEnasTN5x", rendersWithMotion: true, scopeId: "MH9m4cXzT", children: /* @__PURE__ */ _jsx(stdin_default4, { height: "100%", HZSElEzAS: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", id: "LEnasTN5x", layoutId: "LEnasTN5x", variant: "evMN1mTNM", width: "100%" }) }) }) }) }), isDisplayed() && /* @__PURE__ */ _jsx(ResolveLinks, { links: [{ href: { webPageId: "ZLJzEk6Fj" }, implicitPathVariables: void 0 }, { href: { webPageId: "ZLJzEk6Fj" }, implicitPathVariables: void 0 }, { href: { webPageId: "ZLJzEk6Fj" }, implicitPathVariables: void 0 }, { href: { webPageId: "ZLJzEk6Fj" }, implicitPathVariables: void 0 }], children: (resolvedLinks) => /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 18, y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 55 - 690.4) / 1 * 0) + 2, ...addPropertyOverrides({ dBl3NGC5f: { y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 38 - 22) / 1 * 0) + 2 }, f0ZG9nWNx: { y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 55 - 820.6) / 1 * 0) + 2 }, koDxrtEXx: { y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 59 - 750.6) / 1 * 0) + 2 } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-1ubnatg-container", layoutDependency, layoutId: "d3f60GtmK-container", nodeId: "d3f60GtmK", rendersWithMotion: true, scopeId: "MH9m4cXzT", style: { opacity: 0 }, variants: { dBl3NGC5f: { opacity: 1 } }, children: /* @__PURE__ */ _jsx(stdin_default2, { height: "100%", i83LM0fHv: "", id: "d3f60GtmK", kSA3wuEjn: true, layoutId: "d3f60GtmK", OKr5AJd5k: resolvedLinks[0], width: "100%", wSDtEcD_o: "Studio", ...addPropertyOverrides({ dBl3NGC5f: { OKr5AJd5k: resolvedLinks[1] }, f0ZG9nWNx: { OKr5AJd5k: resolvedLinks[2] }, koDxrtEXx: { OKr5AJd5k: resolvedLinks[3] } }, baseVariant, gestureVariant) }) }) }) }), isDisplayed() && /* @__PURE__ */ _jsx(ResolveLinks, { links: [{ href: { webPageId: "EHD2n_rsP" }, implicitPathVariables: void 0 }, { href: { webPageId: "EHD2n_rsP" }, implicitPathVariables: void 0 }, { href: { webPageId: "EHD2n_rsP" }, implicitPathVariables: void 0 }, { href: { webPageId: "EHD2n_rsP" }, implicitPathVariables: void 0 }], children: (resolvedLinks1) => /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 18, y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 55 - 690.4) / 1 * 0) + 2, ...addPropertyOverrides({ dBl3NGC5f: { y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 38 - 22) / 1 * 0) + 2 }, f0ZG9nWNx: { y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 55 - 820.6) / 1 * 0) + 2 }, koDxrtEXx: { y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 59 - 750.6) / 1 * 0) + 2 } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-1phpovz-container", layoutDependency, layoutId: "d8gbtMIsi-container", nodeId: "d8gbtMIsi", rendersWithMotion: true, scopeId: "MH9m4cXzT", style: { opacity: 0 }, variants: { dBl3NGC5f: { opacity: 1 } }, children: /* @__PURE__ */ _jsx(stdin_default2, { height: "100%", i83LM0fHv: "27", id: "d8gbtMIsi", kSA3wuEjn: true, layoutId: "d8gbtMIsi", OKr5AJd5k: resolvedLinks1[0], width: "100%", wSDtEcD_o: "Projects", ...addPropertyOverrides({ dBl3NGC5f: { OKr5AJd5k: resolvedLinks1[1] }, f0ZG9nWNx: { OKr5AJd5k: resolvedLinks1[2] }, koDxrtEXx: { OKr5AJd5k: resolvedLinks1[3] } }, baseVariant, gestureVariant) }) }) }) }), isDisplayed() && /* @__PURE__ */ _jsx(ResolveLinks, { links: [{ href: { webPageId: "p3zwtzFek" }, implicitPathVariables: void 0 }, { href: { webPageId: "p3zwtzFek" }, implicitPathVariables: void 0 }, { href: { webPageId: "p3zwtzFek" }, implicitPathVariables: void 0 }, { href: { webPageId: "p3zwtzFek" }, implicitPathVariables: void 0 }], children: (resolvedLinks2) => /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 18, y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 55 - 690.4) / 1 * 0) + 2, ...addPropertyOverrides({ dBl3NGC5f: { y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 38 - 22) / 1 * 0) + 2 }, f0ZG9nWNx: { y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 55 - 820.6) / 1 * 0) + 2 }, koDxrtEXx: { y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 59 - 750.6) / 1 * 0) + 2 } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-1q78hcd-container", layoutDependency, layoutId: "t0QYUCRQM-container", nodeId: "t0QYUCRQM", rendersWithMotion: true, scopeId: "MH9m4cXzT", style: { opacity: 0 }, variants: { dBl3NGC5f: { opacity: 1 } }, children: /* @__PURE__ */ _jsx(stdin_default2, { height: "100%", i83LM0fHv: "", id: "t0QYUCRQM", kSA3wuEjn: false, layoutId: "t0QYUCRQM", OKr5AJd5k: resolvedLinks2[0], width: "100%", wSDtEcD_o: "Blog", ...addPropertyOverrides({ dBl3NGC5f: { OKr5AJd5k: resolvedLinks2[1] }, f0ZG9nWNx: { OKr5AJd5k: resolvedLinks2[2] }, koDxrtEXx: { OKr5AJd5k: resolvedLinks2[3] } }, baseVariant, gestureVariant) }) }) }) }), isDisplayed() && /* @__PURE__ */ _jsx(ResolveLinks, { links: [{ href: { webPageId: "hAlvrhFMj" }, implicitPathVariables: void 0 }, { href: { webPageId: "hAlvrhFMj" }, implicitPathVariables: void 0 }, { href: { webPageId: "hAlvrhFMj" }, implicitPathVariables: void 0 }, { href: { webPageId: "hAlvrhFMj" }, implicitPathVariables: void 0 }], children: (resolvedLinks3) => /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 18, y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 55 - 690.4) / 1 * 0) + 2, ...addPropertyOverrides({ dBl3NGC5f: { y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 38 - 22) / 1 * 0) + 2 }, f0ZG9nWNx: { y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 55 - 820.6) / 1 * 0) + 2 }, koDxrtEXx: { y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 59 - 750.6) / 1 * 0) + 2 } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-jspa9w-container", layoutDependency, layoutId: "LbuvcewGj-container", nodeId: "LbuvcewGj", rendersWithMotion: true, scopeId: "MH9m4cXzT", style: { opacity: 0 }, variants: { dBl3NGC5f: { opacity: 1 } }, children: /* @__PURE__ */ _jsx(stdin_default2, { height: "100%", i83LM0fHv: "", id: "LbuvcewGj", kSA3wuEjn: false, layoutId: "LbuvcewGj", OKr5AJd5k: resolvedLinks3[0], width: "100%", wSDtEcD_o: "Contact", ...addPropertyOverrides({ dBl3NGC5f: { OKr5AJd5k: resolvedLinks3[1] }, f0ZG9nWNx: { OKr5AJd5k: resolvedLinks3[2] }, koDxrtEXx: { OKr5AJd5k: resolvedLinks3[3] } }, baseVariant, gestureVariant) }) }) }) }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-ednsow", "data-framer-name": "Button container", "data-highlight": true, layoutDependency, layoutId: "NgnQirJGf", onTap: onTap1m6xaby, ...addPropertyOverrides({ dBl3NGC5f: { onTap: onTapyjrdku }, f0ZG9nWNx: { onTap: onTap8gqdx5 }, gKutwTn3I: { onTap: onTapsjv12q }, gNqyhurpQ: { onTap: onTap6wu28k }, koDxrtEXx: { onTap: onTap1vkj13f } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 12, y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 55 - 690.4) / 1 * 0) + 0 + 5, ...addPropertyOverrides({ dBl3NGC5f: { y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 38 - 22) / 1 * 0) + 0 + 5 }, f0ZG9nWNx: { y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 55 - 820.6) / 1 * 0) + 0 + 5 }, gKutwTn3I: { y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 38 - 22) / 1 * 0) + 0 + 5 }, gNqyhurpQ: { y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 38 - 22) / 1 * 0) + 0 + 5 }, koDxrtEXx: { y: (componentViewport?.y || 0) + 19 + (0 + 0 + ((componentViewport?.height || 200) - 59 - 750.6) / 1 * 0) + 0 + 5 } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-eda3z9-container", layoutDependency, layoutId: "zA8NPGRkM-container", nodeId: "zA8NPGRkM", rendersWithMotion: true, scopeId: "MH9m4cXzT", children: /* @__PURE__ */ _jsx(stdin_default, { height: "100%", id: "zA8NPGRkM", layoutId: "zA8NPGRkM", variant: "ZgplB38uO", width: "100%", ...addPropertyOverrides({ dBl3NGC5f: { variant: "qlBMBxhJm" }, gKutwTn3I: { variant: "qlBMBxhJm" }, gNqyhurpQ: { variant: "qlBMBxhJm" } }, baseVariant, gestureVariant) }) }) }) })] }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-1rfamfy", "data-framer-name": "Body", layoutDependency, layoutId: "LQHh6xDgU", style: { opacity: 1 }, variants: { dBl3NGC5f: { opacity: 0 }, f0ZG9nWNx: { opacity: 1 }, gKutwTn3I: { opacity: 0 }, gNqyhurpQ: { opacity: 0 }, koDxrtEXx: { opacity: 1 } }, children: [/* @__PURE__ */ _jsxs(motion.nav, { className: "framer-ugu5i9", "data-framer-name": "Navigation", layoutDependency, layoutId: "HB8tCGdyD", children: [/* @__PURE__ */ _jsx(MotionDivWithFX, { __framer__animate: { transition: transition2 }, __framer__animateOnce: false, __framer__enter: animation, __framer__exit: animation1, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0.5, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-6z5t2m", "data-framer-name": "Item container", layoutDependency, layoutId: "kOHhaRgyd", children: /* @__PURE__ */ _jsx(ResolveLinks, { links: [{ href: { webPageId: "augiA20Il" }, implicitPathVariables: void 0 }, { href: { webPageId: "augiA20Il" }, implicitPathVariables: void 0 }, { href: { webPageId: "augiA20Il" }, implicitPathVariables: void 0 }, { href: { webPageId: "augiA20Il" }, implicitPathVariables: void 0 }, { href: { webPageId: "augiA20Il" }, implicitPathVariables: void 0 }, { href: { webPageId: "augiA20Il" }, implicitPathVariables: void 0 }], children: (resolvedLinks4) => /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 72, y: (componentViewport?.y || 0) + 19 + (0 + 22 + ((componentViewport?.height || 200) - 55 - 690.4) / 1 * 1) + 120 + 0 + 0 + 0 + 0, ...addPropertyOverrides({ dBl3NGC5f: { y: (componentViewport?.y || 0) + -750 + 120 + 0 + 0 + 0 + 0 }, f0ZG9nWNx: { y: (componentViewport?.y || 0) + 19 + (0 + 22 + ((componentViewport?.height || 200) - 55 - 820.6) / 1 * 1) + 110 + 0 + 0 + 0 + 0 }, gKutwTn3I: { y: (componentViewport?.y || 0) + -604 + 80 + 0 + 0 + 0 + 0 }, gNqyhurpQ: { y: (componentViewport?.y || 0) + -750 + 130 + 0 + 0 + 0 + 0 }, koDxrtEXx: { y: (componentViewport?.y || 0) + 19 + (0 + 22 + ((componentViewport?.height || 200) - 59 - 750.6) / 1 * 1) + 70 + 0 + 0 + 0 + 0 } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-1l0v3f0-container", layoutDependency, layoutId: "M5EhK1MnW-container", nodeId: "M5EhK1MnW", rendersWithMotion: true, scopeId: "MH9m4cXzT", children: /* @__PURE__ */ _jsx(stdin_default3, { height: "100%", id: "M5EhK1MnW", layoutId: "M5EhK1MnW", LIy6SD5oa: LIy6SD5oa1m6xaby, RLreA9jcP: "Home", sEL51lU82: resolvedLinks4[0], variant: "Ejf5a8hFM", width: "100%", ...addPropertyOverrides({ dBl3NGC5f: { sEL51lU82: resolvedLinks4[1] }, f0ZG9nWNx: { LIy6SD5oa: LIy6SD5oa8gqdx5, sEL51lU82: resolvedLinks4[2], variant: "a6v_JP5sV" }, gKutwTn3I: { LIy6SD5oa: LIy6SD5oa8gqdx5, sEL51lU82: resolvedLinks4[5], variant: "sUHgiwMqj" }, gNqyhurpQ: { sEL51lU82: resolvedLinks4[3], variant: "a6v_JP5sV" }, koDxrtEXx: { LIy6SD5oa: LIy6SD5oa1vkj13f, sEL51lU82: resolvedLinks4[4], variant: "sUHgiwMqj" } }, baseVariant, gestureVariant) }) }) }) }) }), /* @__PURE__ */ _jsx(MotionDivWithFX, { __framer__animate: { transition: transition3 }, __framer__animateOnce: false, __framer__enter: animation, __framer__exit: animation2, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0.5, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-f7i6o0", "data-framer-name": "Item container", layoutDependency, layoutId: "bCxRE40yu", children: /* @__PURE__ */ _jsx(ResolveLinks, { links: [{ href: { webPageId: "ZLJzEk6Fj" }, implicitPathVariables: void 0 }, { href: { webPageId: "ZLJzEk6Fj" }, implicitPathVariables: void 0 }, { href: { webPageId: "ZLJzEk6Fj" }, implicitPathVariables: void 0 }, { href: { webPageId: "ZLJzEk6Fj" }, implicitPathVariables: void 0 }, { href: { webPageId: "ZLJzEk6Fj" }, implicitPathVariables: void 0 }, { href: { webPageId: "ZLJzEk6Fj" }, implicitPathVariables: void 0 }], children: (resolvedLinks5) => /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 72, y: (componentViewport?.y || 0) + 19 + (0 + 22 + ((componentViewport?.height || 200) - 55 - 690.4) / 1 * 1) + 120 + 0 + 0 + 72 + 0, ...addPropertyOverrides({ dBl3NGC5f: { y: (componentViewport?.y || 0) + -750 + 120 + 0 + 0 + 72 + 0 }, f0ZG9nWNx: { y: (componentViewport?.y || 0) + 19 + (0 + 22 + ((componentViewport?.height || 200) - 55 - 820.6) / 1 * 1) + 110 + 0 + 0 + 72 + 0 }, gKutwTn3I: { y: (componentViewport?.y || 0) + -604 + 80 + 0 + 0 + 76 + 0 }, gNqyhurpQ: { y: (componentViewport?.y || 0) + -750 + 130 + 0 + 0 + 72 + 0 }, koDxrtEXx: { y: (componentViewport?.y || 0) + 19 + (0 + 22 + ((componentViewport?.height || 200) - 59 - 750.6) / 1 * 1) + 70 + 0 + 0 + 76 + 0 } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-qy8ut1-container", layoutDependency, layoutId: "L8mkiprKd-container", nodeId: "L8mkiprKd", rendersWithMotion: true, scopeId: "MH9m4cXzT", children: /* @__PURE__ */ _jsx(stdin_default3, { height: "100%", id: "L8mkiprKd", layoutId: "L8mkiprKd", LIy6SD5oa: LIy6SD5oa1m6xaby, RLreA9jcP: "Studio", sEL51lU82: resolvedLinks5[0], variant: "Ejf5a8hFM", width: "100%", ...addPropertyOverrides({ dBl3NGC5f: { sEL51lU82: resolvedLinks5[1] }, f0ZG9nWNx: { LIy6SD5oa: LIy6SD5oa8gqdx5, sEL51lU82: resolvedLinks5[2], variant: "a6v_JP5sV" }, gKutwTn3I: { LIy6SD5oa: LIy6SD5oa8gqdx5, sEL51lU82: resolvedLinks5[5], variant: "sUHgiwMqj" }, gNqyhurpQ: { sEL51lU82: resolvedLinks5[3], variant: "a6v_JP5sV" }, koDxrtEXx: { LIy6SD5oa: LIy6SD5oa1vkj13f, sEL51lU82: resolvedLinks5[4], variant: "sUHgiwMqj" } }, baseVariant, gestureVariant) }) }) }) }) }), /* @__PURE__ */ _jsx(MotionDivWithFX, { __framer__animate: { transition: transition4 }, __framer__animateOnce: false, __framer__enter: animation, __framer__exit: animation3, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0.5, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-9p1fl7", "data-framer-name": "Item container", layoutDependency, layoutId: "nEwoNmAc2", children: /* @__PURE__ */ _jsx(ResolveLinks, { links: [{ href: { webPageId: "EHD2n_rsP" }, implicitPathVariables: void 0 }, { href: { webPageId: "EHD2n_rsP" }, implicitPathVariables: void 0 }, { href: { webPageId: "EHD2n_rsP" }, implicitPathVariables: void 0 }, { href: { webPageId: "EHD2n_rsP" }, implicitPathVariables: void 0 }, { href: { webPageId: "EHD2n_rsP" }, implicitPathVariables: void 0 }, { href: { webPageId: "EHD2n_rsP" }, implicitPathVariables: void 0 }], children: (resolvedLinks6) => /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 72, y: (componentViewport?.y || 0) + 19 + (0 + 22 + ((componentViewport?.height || 200) - 55 - 690.4) / 1 * 1) + 120 + 0 + 0 + 144 + 0, ...addPropertyOverrides({ dBl3NGC5f: { y: (componentViewport?.y || 0) + -750 + 120 + 0 + 0 + 144 + 0 }, f0ZG9nWNx: { y: (componentViewport?.y || 0) + 19 + (0 + 22 + ((componentViewport?.height || 200) - 55 - 820.6) / 1 * 1) + 110 + 0 + 0 + 144 + 0 }, gKutwTn3I: { y: (componentViewport?.y || 0) + -604 + 80 + 0 + 0 + 152 + 0 }, gNqyhurpQ: { y: (componentViewport?.y || 0) + -750 + 130 + 0 + 0 + 144 + 0 }, koDxrtEXx: { y: (componentViewport?.y || 0) + 19 + (0 + 22 + ((componentViewport?.height || 200) - 59 - 750.6) / 1 * 1) + 70 + 0 + 0 + 152 + 0 } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-1cb9u8q-container", layoutDependency, layoutId: "pQ8K83k7J-container", nodeId: "pQ8K83k7J", rendersWithMotion: true, scopeId: "MH9m4cXzT", children: /* @__PURE__ */ _jsx(stdin_default3, { height: "100%", id: "pQ8K83k7J", layoutId: "pQ8K83k7J", LIy6SD5oa: LIy6SD5oa1m6xaby, RLreA9jcP: "Projects", sEL51lU82: resolvedLinks6[0], variant: "Ejf5a8hFM", width: "100%", ...addPropertyOverrides({ dBl3NGC5f: { sEL51lU82: resolvedLinks6[1] }, f0ZG9nWNx: { LIy6SD5oa: LIy6SD5oa8gqdx5, sEL51lU82: resolvedLinks6[2], variant: "a6v_JP5sV" }, gKutwTn3I: { LIy6SD5oa: LIy6SD5oa8gqdx5, sEL51lU82: resolvedLinks6[5], variant: "sUHgiwMqj" }, gNqyhurpQ: { sEL51lU82: resolvedLinks6[3], variant: "a6v_JP5sV" }, koDxrtEXx: { LIy6SD5oa: LIy6SD5oa1vkj13f, sEL51lU82: resolvedLinks6[4], variant: "sUHgiwMqj" } }, baseVariant, gestureVariant) }) }) }) }) }), /* @__PURE__ */ _jsx(MotionDivWithFX, { __framer__animate: { transition: transition5 }, __framer__animateOnce: false, __framer__enter: animation, __framer__exit: animation4, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0.5, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-15pwy5", "data-framer-name": "Item container", layoutDependency, layoutId: "ggKdkY3Dp", children: /* @__PURE__ */ _jsx(ResolveLinks, { links: [{ href: { webPageId: "p3zwtzFek" }, implicitPathVariables: void 0 }, { href: { webPageId: "p3zwtzFek" }, implicitPathVariables: void 0 }, { href: { webPageId: "p3zwtzFek" }, implicitPathVariables: void 0 }, { href: { webPageId: "p3zwtzFek" }, implicitPathVariables: void 0 }, { href: { webPageId: "p3zwtzFek" }, implicitPathVariables: void 0 }, { href: { webPageId: "p3zwtzFek" }, implicitPathVariables: void 0 }], children: (resolvedLinks7) => /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 72, y: (componentViewport?.y || 0) + 19 + (0 + 22 + ((componentViewport?.height || 200) - 55 - 690.4) / 1 * 1) + 120 + 0 + 0 + 216 + 0, ...addPropertyOverrides({ dBl3NGC5f: { y: (componentViewport?.y || 0) + -750 + 120 + 0 + 0 + 216 + 0 }, f0ZG9nWNx: { y: (componentViewport?.y || 0) + 19 + (0 + 22 + ((componentViewport?.height || 200) - 55 - 820.6) / 1 * 1) + 110 + 0 + 0 + 216 + 0 }, gKutwTn3I: { y: (componentViewport?.y || 0) + -604 + 80 + 0 + 0 + 228 + 0 }, gNqyhurpQ: { y: (componentViewport?.y || 0) + -750 + 130 + 0 + 0 + 216 + 0 }, koDxrtEXx: { y: (componentViewport?.y || 0) + 19 + (0 + 22 + ((componentViewport?.height || 200) - 59 - 750.6) / 1 * 1) + 70 + 0 + 0 + 228 + 0 } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-dn12q8-container", layoutDependency, layoutId: "YFVAighm9-container", nodeId: "YFVAighm9", rendersWithMotion: true, scopeId: "MH9m4cXzT", children: /* @__PURE__ */ _jsx(stdin_default3, { height: "100%", id: "YFVAighm9", layoutId: "YFVAighm9", LIy6SD5oa: LIy6SD5oa1m6xaby, RLreA9jcP: "Blog", sEL51lU82: resolvedLinks7[0], variant: "Ejf5a8hFM", width: "100%", ...addPropertyOverrides({ dBl3NGC5f: { sEL51lU82: resolvedLinks7[1] }, f0ZG9nWNx: { LIy6SD5oa: LIy6SD5oa8gqdx5, sEL51lU82: resolvedLinks7[2], variant: "a6v_JP5sV" }, gKutwTn3I: { LIy6SD5oa: LIy6SD5oa8gqdx5, sEL51lU82: resolvedLinks7[5], variant: "sUHgiwMqj" }, gNqyhurpQ: { sEL51lU82: resolvedLinks7[3], variant: "a6v_JP5sV" }, koDxrtEXx: { LIy6SD5oa: LIy6SD5oa1vkj13f, sEL51lU82: resolvedLinks7[4], variant: "sUHgiwMqj" } }, baseVariant, gestureVariant) }) }) }) }) }), /* @__PURE__ */ _jsx(MotionDivWithFX, { __framer__animate: { transition: transition6 }, __framer__animateOnce: false, __framer__enter: animation, __framer__exit: animation5, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0.5, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-pem6zj", "data-framer-name": "Item container", layoutDependency, layoutId: "Vg4hCmwbq", children: /* @__PURE__ */ _jsx(ResolveLinks, { links: [{ href: { webPageId: "hAlvrhFMj" }, implicitPathVariables: void 0 }, { href: { webPageId: "hAlvrhFMj" }, implicitPathVariables: void 0 }, { href: { webPageId: "hAlvrhFMj" }, implicitPathVariables: void 0 }, { href: { webPageId: "hAlvrhFMj" }, implicitPathVariables: void 0 }, { href: { webPageId: "hAlvrhFMj" }, implicitPathVariables: void 0 }, { href: { webPageId: "hAlvrhFMj" }, implicitPathVariables: void 0 }], children: (resolvedLinks8) => /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 72, y: (componentViewport?.y || 0) + 19 + (0 + 22 + ((componentViewport?.height || 200) - 55 - 690.4) / 1 * 1) + 120 + 0 + 0 + 288 + 0, ...addPropertyOverrides({ dBl3NGC5f: { y: (componentViewport?.y || 0) + -750 + 120 + 0 + 0 + 288 + 0 }, f0ZG9nWNx: { y: (componentViewport?.y || 0) + 19 + (0 + 22 + ((componentViewport?.height || 200) - 55 - 820.6) / 1 * 1) + 110 + 0 + 0 + 288 + 0 }, gKutwTn3I: { y: (componentViewport?.y || 0) + -604 + 80 + 0 + 0 + 304 + 0 }, gNqyhurpQ: { y: (componentViewport?.y || 0) + -750 + 130 + 0 + 0 + 288 + 0 }, koDxrtEXx: { y: (componentViewport?.y || 0) + 19 + (0 + 22 + ((componentViewport?.height || 200) - 59 - 750.6) / 1 * 1) + 70 + 0 + 0 + 304 + 0 } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-i0l900-container", layoutDependency, layoutId: "bIPYo48U0-container", nodeId: "bIPYo48U0", rendersWithMotion: true, scopeId: "MH9m4cXzT", children: /* @__PURE__ */ _jsx(stdin_default3, { height: "100%", id: "bIPYo48U0", layoutId: "bIPYo48U0", LIy6SD5oa: LIy6SD5oa1m6xaby, RLreA9jcP: "Contact", sEL51lU82: resolvedLinks8[0], variant: "Ejf5a8hFM", width: "100%", ...addPropertyOverrides({ dBl3NGC5f: { sEL51lU82: resolvedLinks8[1] }, f0ZG9nWNx: { LIy6SD5oa: LIy6SD5oa8gqdx5, sEL51lU82: resolvedLinks8[2], variant: "a6v_JP5sV" }, gKutwTn3I: { LIy6SD5oa: LIy6SD5oa8gqdx5, sEL51lU82: resolvedLinks8[5], variant: "sUHgiwMqj" }, gNqyhurpQ: { sEL51lU82: resolvedLinks8[3], variant: "a6v_JP5sV" }, koDxrtEXx: { LIy6SD5oa: LIy6SD5oa1vkj13f, sEL51lU82: resolvedLinks8[4], variant: "sUHgiwMqj" } }, baseVariant, gestureVariant) }) }) }) }) })] }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-17uvimj", "data-framer-name": "Bottom", layoutDependency, layoutId: "D_GoueKQm", children: [/* @__PURE__ */ _jsxs(motion.div, { className: "framer-1cfglw0", "data-framer-name": "Contact", layoutDependency, layoutId: "aQ7ct8iPp", children: [/* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItTWVkaXVt", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "18px", "--framer-font-weight": "500", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "130%", "--framer-text-color": "var(--extracted-r6o4lv, rgb(9, 9, 9))" }, children: /* @__PURE__ */ _jsx(Link, { href: "tel:************", motionChild: true, nodeId: "AUaZ1xBTq", openInNewTab: true, scopeId: "MH9m4cXzT", smoothScroll: false, children: /* @__PURE__ */ _jsx(motion.a, { className: "framer-styles-preset-1wi7vce", "data-styles-preset": "nCQNaN8LD", children: "(*************" }) }) }) }), className: "framer-zypsr7", fonts: ["Inter-Medium"], layoutDependency, layoutId: "AUaZ1xBTq", style: { "--extracted-r6o4lv": "rgb(9, 9, 9)", "--framer-paragraph-spacing": "0px" }, verticalAlignment: "top", withExternalLayout: true }), /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 37, y: (componentViewport?.y || 0) + 19 + (0 + 22 + ((componentViewport?.height || 200) - 55 - 690.4) / 1 * 1) + 120 + 480 + 0 + 0 + 0 + 31.4, ...addPropertyOverrides({ dBl3NGC5f: { y: (componentViewport?.y || 0) + -750 + 120 + 480 + 0 + 0 + 0 + 31.4 }, f0ZG9nWNx: { y: (componentViewport?.y || 0) + 19 + (0 + 22 + ((componentViewport?.height || 200) - 55 - 820.6) / 1 * 1) + 110 + 470 + 0 + 0 + 0 + 31.4 }, gKutwTn3I: { y: (componentViewport?.y || 0) + -604 + 80 + 456 + 0 + 0 + 0 + 31.4 }, gNqyhurpQ: { y: (componentViewport?.y || 0) + -750 + 130 + 480 + 0 + 0 + 0 + 31.4 }, koDxrtEXx: { y: (componentViewport?.y || 0) + 19 + (0 + 22 + ((componentViewport?.height || 200) - 59 - 750.6) / 1 * 1) + 70 + 446 + 0 + 0 + 0 + 31.4 } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-1cprrbp-container", layoutDependency, layoutId: "MvMuqmvE3-container", nodeId: "MvMuqmvE3", rendersWithMotion: true, scopeId: "MH9m4cXzT", children: /* @__PURE__ */ _jsx(stdin_default5, { dgRMvez_W: "<EMAIL>", height: "100%", id: "MvMuqmvE3", layoutId: "MvMuqmvE3", variant: "oIIY33Dha", width: "100%", WNmHg1EX3: "mailto:<EMAIL>", ...addPropertyOverrides({ gKutwTn3I: { variant: "Zo8_CcwZu" }, koDxrtEXx: { variant: "Zo8_CcwZu" } }, baseVariant, gestureVariant) }) }) })] }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-zauiq7", "data-framer-name": "Legal", layoutDependency, layoutId: "bIPOBNddb", transformTemplate: transformTemplate1, ...addPropertyOverrides({ f0ZG9nWNx: { transformTemplate: void 0 }, gKutwTn3I: { transformTemplate: void 0 }, gNqyhurpQ: { transformTemplate: void 0 }, koDxrtEXx: { transformTemplate: void 0 } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-1mf8d9g", "data-styles-preset": "ypR5VEWEl", children: /* @__PURE__ */ _jsx(Link, { href: { pathVariables: { TMEoQtt6b: "privacy-policy" }, unresolvedPathSlugs: { TMEoQtt6b: { collectionId: "op7RHXBeI", collectionItemId: "EnwiwcuRN" } }, webPageId: "CmPsU4Vle" }, motionChild: true, nodeId: "r14yfj4Zp", openInNewTab: false, scopeId: "MH9m4cXzT", smoothScroll: false, children: /* @__PURE__ */ _jsx(motion.a, { className: "framer-styles-preset-1wi7vce", "data-styles-preset": "nCQNaN8LD", children: "Privacy Policy" }) }) }) }), className: "framer-cn0hy8", "data-highlight": true, fonts: ["Inter"], layoutDependency, layoutId: "r14yfj4Zp", onTap: onTap1m6xaby, style: { "--framer-paragraph-spacing": "0px" }, verticalAlignment: "top", withExternalLayout: true, ...addPropertyOverrides({ f0ZG9nWNx: { onTap: onTap8gqdx5 }, koDxrtEXx: { onTap: onTap1vkj13f } }, baseVariant, gestureVariant) }), /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-1mf8d9g", "data-styles-preset": "ypR5VEWEl", children: /* @__PURE__ */ _jsx(Link, { href: { pathVariables: { TMEoQtt6b: "terms-of-service" }, unresolvedPathSlugs: { TMEoQtt6b: { collectionId: "op7RHXBeI", collectionItemId: "SYN_Cviyw" } }, webPageId: "CmPsU4Vle" }, motionChild: true, nodeId: "FuvfJAvEi", openInNewTab: false, scopeId: "MH9m4cXzT", smoothScroll: false, children: /* @__PURE__ */ _jsx(motion.a, { className: "framer-styles-preset-1wi7vce", "data-styles-preset": "nCQNaN8LD", children: "Terms of Service" }) }) }) }), className: "framer-1dkccqr", "data-highlight": true, fonts: ["Inter"], layoutDependency, layoutId: "FuvfJAvEi", onTap: onTap1m6xaby, style: { "--framer-paragraph-spacing": "0px" }, verticalAlignment: "top", withExternalLayout: true, ...addPropertyOverrides({ f0ZG9nWNx: { onTap: onTap8gqdx5 }, koDxrtEXx: { onTap: onTap1vkj13f } }, baseVariant, gestureVariant) })] }), /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-txwsq6", "data-styles-preset": "fDRzSjw63", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10)))" }, children: "\xA9 2025 fabrica\xAE Studio" }) }), className: "framer-1gif4l1", "data-framer-name": "Copyright Text", fonts: ["Inter"], layoutDependency, layoutId: "keQ0NTShk", style: { "--extracted-r6o4lv": "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", "--framer-paragraph-spacing": "0px", opacity: 0.6 }, verticalAlignment: "top", withExternalLayout: true })] })] })] }) }) }) });
});
var css4 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-hVgn6.framer-1a90gm8, .framer-hVgn6 .framer-1a90gm8 { display: block; }", ".framer-hVgn6.framer-bzu5mb { align-content: center; align-items: center; display: flex; flex-direction: column; flex-wrap: nowrap; height: min-content; justify-content: space-between; overflow: hidden; padding: 19px 36px 36px 36px; position: relative; width: 1200px; }", ".framer-hVgn6 .framer-120hnkq { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; height: min-content; justify-content: space-between; overflow: visible; padding: 0px; position: relative; width: 100%; z-index: 2; }", ".framer-hVgn6 .framer-iogiyt { align-content: center; align-items: center; cursor: pointer; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: center; overflow: visible; padding: 0px; position: relative; text-decoration: none; width: min-content; }", ".framer-hVgn6 .framer-kxb2zt-container, .framer-hVgn6 .framer-1ubnatg-container, .framer-hVgn6 .framer-1phpovz-container, .framer-hVgn6 .framer-1q78hcd-container, .framer-hVgn6 .framer-jspa9w-container, .framer-hVgn6 .framer-eda3z9-container, .framer-hVgn6 .framer-1l0v3f0-container, .framer-hVgn6 .framer-qy8ut1-container, .framer-hVgn6 .framer-1cb9u8q-container, .framer-hVgn6 .framer-dn12q8-container, .framer-hVgn6 .framer-i0l900-container, .framer-hVgn6 .framer-1cprrbp-container { flex: none; height: auto; position: relative; width: auto; }", ".framer-hVgn6 .framer-ednsow { align-content: center; align-items: center; align-self: stretch; cursor: pointer; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: auto; justify-content: center; overflow: visible; padding: 0px; position: relative; width: min-content; }", ".framer-hVgn6 .framer-1rfamfy { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 120px; height: min-content; justify-content: center; overflow: visible; padding: 120px 0px 0px 0px; position: relative; width: 100%; z-index: 1; }", ".framer-hVgn6 .framer-ugu5i9 { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: center; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-hVgn6 .framer-6z5t2m, .framer-hVgn6 .framer-f7i6o0, .framer-hVgn6 .framer-9p1fl7, .framer-hVgn6 .framer-15pwy5, .framer-hVgn6 .framer-pem6zj { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: center; overflow: visible; padding: 0px; position: relative; width: min-content; }", ".framer-hVgn6 .framer-17uvimj { align-content: flex-end; align-items: flex-end; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; height: min-content; justify-content: space-between; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-hVgn6 .framer-1cfglw0 { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 8px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: min-content; }", ".framer-hVgn6 .framer-zypsr7, .framer-hVgn6 .framer-1gif4l1 { flex: none; height: auto; position: relative; white-space: pre; width: auto; }", ".framer-hVgn6 .framer-zauiq7 { align-content: center; align-items: center; bottom: 0px; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 42px; height: min-content; justify-content: flex-start; left: 50%; overflow: visible; padding: 0px; position: absolute; width: min-content; z-index: 1; }", ".framer-hVgn6 .framer-cn0hy8, .framer-hVgn6 .framer-1dkccqr { cursor: pointer; flex: none; height: auto; position: relative; white-space: pre; width: auto; }", ".framer-hVgn6.framer-v-v89dtt.framer-bzu5mb { padding: 19px 36px 19px 36px; }", ".framer-hVgn6.framer-v-v89dtt .framer-1rfamfy { left: 36px; position: absolute; right: 36px; top: -750px; width: unset; }", ".framer-hVgn6.framer-v-1sabfjt.framer-bzu5mb { padding: 19px 32px 36px 32px; width: 810px; }", ".framer-hVgn6.framer-v-1sabfjt .framer-1rfamfy { gap: 110px; padding: 110px 0px 0px 0px; }", ".framer-hVgn6.framer-v-1sabfjt .framer-17uvimj, .framer-hVgn6.framer-v-14j3axz .framer-17uvimj, .framer-hVgn6.framer-v-12w42s8 .framer-17uvimj { align-content: center; align-items: center; flex-direction: column; gap: 40px; justify-content: flex-start; }", ".framer-hVgn6.framer-v-1sabfjt .framer-1cfglw0, .framer-hVgn6.framer-v-14j3axz .framer-1cfglw0, .framer-hVgn6.framer-v-1dcusg4 .framer-1cfglw0, .framer-hVgn6.framer-v-12w42s8 .framer-1cfglw0 { align-content: center; align-items: center; }", ".framer-hVgn6.framer-v-1sabfjt .framer-zauiq7, .framer-hVgn6.framer-v-14j3axz .framer-zauiq7 { bottom: unset; flex-direction: column; gap: 10px; left: unset; position: relative; }", ".framer-hVgn6.framer-v-14j3axz.framer-bzu5mb { padding: 19px 32px 19px 32px; width: 810px; }", ".framer-hVgn6.framer-v-14j3axz .framer-1rfamfy { left: 32px; padding: 130px 0px 0px 0px; position: absolute; right: 32px; top: -750px; width: unset; }", ".framer-hVgn6.framer-v-1dcusg4.framer-bzu5mb { padding: 19px 20px 40px 20px; width: 390px; }", ".framer-hVgn6.framer-v-1dcusg4 .framer-1rfamfy { gap: 70px; padding: 70px 0px 0px 0px; }", ".framer-hVgn6.framer-v-1dcusg4 .framer-ugu5i9, .framer-hVgn6.framer-v-12w42s8 .framer-ugu5i9 { gap: 4px; }", ".framer-hVgn6.framer-v-1dcusg4 .framer-17uvimj { align-content: center; align-items: center; flex-direction: column; gap: 36px; justify-content: flex-start; }", ".framer-hVgn6.framer-v-1dcusg4 .framer-zauiq7, .framer-hVgn6.framer-v-12w42s8 .framer-zauiq7 { bottom: unset; flex-direction: column; gap: 12px; left: unset; position: relative; }", ".framer-hVgn6.framer-v-12w42s8.framer-bzu5mb { padding: 19px 20px 19px 20px; width: 390px; }", ".framer-hVgn6.framer-v-12w42s8 .framer-1rfamfy { gap: 80px; left: 24px; padding: 80px 0px 0px 0px; position: absolute; right: 24px; top: -604px; width: unset; }", ...css2, ...css3, ...css];
var FramerMH9m4cXzT = withCSS(Component, css4, "framer-hVgn6");
var stdin_default6 = FramerMH9m4cXzT;
FramerMH9m4cXzT.displayName = "Navbar";
FramerMH9m4cXzT.defaultProps = { height: 746, width: 1200 };
addPropertyControls(FramerMH9m4cXzT, { variant: { options: ["nQMAqLr57", "dBl3NGC5f", "f0ZG9nWNx", "gNqyhurpQ", "koDxrtEXx", "gKutwTn3I"], optionTitles: ["Desktop open", "Desktop", "Tablet open", "Tablet", "Phone open", "Phone"], title: "Variant", type: ControlType.Enum } });
addFonts(FramerMH9m4cXzT, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5A3Ce6C9YYmCjpQx9M4inSaKU.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/Qx95Xyt0Ka3SGhinnbXIGpEIyP4.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/6mJuEAguuIuMog10gGvH5d3cl8.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/xYYWaj7wCU5zSQH0eXvSaS19wo.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/otTaNuNpVK4RbdlT7zDDdKvQBA.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/d3tHnaQIAeqiE5hGcRw4mmgWYU.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/DolVirEGb34pEXEp8t8FQBSK4.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5vvr9Vy74if2I6bQbJvbw7SY1pQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/EOr0mi4hNtlgWNn9if640EZzXCo.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/Y9k9QrlZAqio88Klkmbd8VoMQc.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/OYrD2tBIBPvoJXiIHnLoOXnY9M.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/JeYwfuaPfZHQhEG8U5gtPDZ7WQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/vQyevYAyHtARFwPqUzQGpnDs.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/b6Y37FthZeALduNqHicBT6FutY.woff2", weight: "400" }] }, ...LogoFonts, ...MenuItemFonts, ...HamburgerFonts, ...MenuItemLargeFonts, ...EmailFonts, ...getFontsFromSharedStyle(fonts2), ...getFontsFromSharedStyle(fonts3), ...getFontsFromSharedStyle(fonts)], { supportsExplicitInterCodegen: true });

// virtual:navbar
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {};
stdin_default6.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default6,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default6, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default6);
export {
  ComponentWithRoot as default
};
