{"inputs": {"/:https://framerusercontent.com/modules/A8glwj2nHygG7Ntk0hyl/B6kwjp0eqCTnq3NK2QmC/AgkVqOuxm.js": {"bytes": 15678, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "virtual:get-template-button": {"bytes": 3011, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/A8glwj2nHygG7Ntk0hyl/B6kwjp0eqCTnq3NK2QmC/AgkVqOuxm.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/A8glwj2nHygG7Ntk0hyl/B6kwjp0eqCTnq3NK2QmC/AgkVqOuxm.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/g2cnji7Jaa30imfz7vPz/Yt1e07acnMaFp9CZYfKH/BvyOYDKED.js": {"bytes": 5139, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "virtual:plus-icon": {"bytes": 3011, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/g2cnji7Jaa30imfz7vPz/Yt1e07acnMaFp9CZYfKH/BvyOYDKED.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/g2cnji7Jaa30imfz7vPz/Yt1e07acnMaFp9CZYfKH/BvyOYDKED.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/fFLayKOd66X1viGh3w4y/R1pR6B4IpAhcuocyXjHX/GKtOymhXV.js": {"bytes": 10924, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/OTR8OYlvGuG53em7HPkd/XFT7UZEK3zn5KT7uQssI/gd6AWaps9.js": {"bytes": 10913, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/7583VVgwoRouErNZ0wXE/CiSJOJayosxmBn1Tinmm/ovCH22Xmz.js": {"bytes": 6109, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/a0Wb5TOIxxIAvu7q5B8D/q3saJqiiDbtZZYfeFyuz/c_ITIcTit.js": {"bytes": 10295, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/OTR8OYlvGuG53em7HPkd/XFT7UZEK3zn5KT7uQssI/gd6AWaps9.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/OTR8OYlvGuG53em7HPkd/XFT7UZEK3zn5KT7uQssI/gd6AWaps9.js"}, {"path": "/:https://framerusercontent.com/modules/7583VVgwoRouErNZ0wXE/CiSJOJayosxmBn1Tinmm/ovCH22Xmz.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/7583VVgwoRouErNZ0wXE/CiSJOJayosxmBn1Tinmm/ovCH22Xmz.js"}], "format": "esm"}, "/:https://framerusercontent.com/modules/mnjgJFRPj416r8pcvyDl/xwOi5nzd2tsTvl7sVclV/eDUVCTTXq.js": {"bytes": 13058, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/h8ioHyt4BUxuvVCE1Q10/aTM6mEMGlM19JjX8exDe/AvoidLayoutJumping_Prod.js": {"bytes": 2427, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/7LhVHX9lqXURUWhQBzJn/wzTnBCThGjoX9XSgG5QG/HLpRTFhim.js": {"bytes": 10763, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/rL0tRCNiDNbTgXcw7l8h/MQ0zM3QkM5MEl876oJCh/pAxoS1kOX.js": {"bytes": 10913, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/vaUDP9yUvXDLDorewcXH/TU27PKlUxf7UEubboCQ4/svYtzYwMA.js": {"bytes": 10751, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/4AfGmdzrVIJOiclhVWW5/wDcr6iY79T92uICesIJd/BvyOYDKED.js": {"bytes": 5139, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/NSkmAJg2RVe3fXUYvYRL/m3HXPaue5Xu13dGsibFw/d4er_GPVG.js": {"bytes": 7590, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/e47Mqi2vDwcso9yvGLGl/A5uhMJoa81U51rSVS7ew/zKohnBGrW.js": {"bytes": 50254, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/OTR8OYlvGuG53em7HPkd/XFT7UZEK3zn5KT7uQssI/gd6AWaps9.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/OTR8OYlvGuG53em7HPkd/XFT7UZEK3zn5KT7uQssI/gd6AWaps9.js"}, {"path": "/:https://framerusercontent.com/modules/7LhVHX9lqXURUWhQBzJn/wzTnBCThGjoX9XSgG5QG/HLpRTFhim.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/7LhVHX9lqXURUWhQBzJn/wzTnBCThGjoX9XSgG5QG/HLpRTFhim.js"}, {"path": "/:https://framerusercontent.com/modules/rL0tRCNiDNbTgXcw7l8h/MQ0zM3QkM5MEl876oJCh/pAxoS1kOX.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/rL0tRCNiDNbTgXcw7l8h/MQ0zM3QkM5MEl876oJCh/pAxoS1kOX.js"}, {"path": "/:https://framerusercontent.com/modules/vaUDP9yUvXDLDorewcXH/TU27PKlUxf7UEubboCQ4/svYtzYwMA.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/vaUDP9yUvXDLDorewcXH/TU27PKlUxf7UEubboCQ4/svYtzYwMA.js"}, {"path": "/:https://framerusercontent.com/modules/4AfGmdzrVIJOiclhVWW5/wDcr6iY79T92uICesIJd/BvyOYDKED.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/4AfGmdzrVIJOiclhVWW5/wDcr6iY79T92uICesIJd/BvyOYDKED.js"}, {"path": "/:https://framerusercontent.com/modules/NSkmAJg2RVe3fXUYvYRL/m3HXPaue5Xu13dGsibFw/d4er_GPVG.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/NSkmAJg2RVe3fXUYvYRL/m3HXPaue5Xu13dGsibFw/d4er_GPVG.js"}], "format": "esm"}, "/:https://framerusercontent.com/modules/8xf9SmDnMz8gG4sF4hpH/tLrkAll785TqtzKVhiuu/fkNrtm8z2.js": {"bytes": 14125, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/h8ioHyt4BUxuvVCE1Q10/aTM6mEMGlM19JjX8exDe/AvoidLayoutJumping_Prod.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/h8ioHyt4BUxuvVCE1Q10/aTM6mEMGlM19JjX8exDe/AvoidLayoutJumping_Prod.js"}, {"path": "/:https://framerusercontent.com/modules/e47Mqi2vDwcso9yvGLGl/A5uhMJoa81U51rSVS7ew/zKohnBGrW.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/e47Mqi2vDwcso9yvGLGl/A5uhMJoa81U51rSVS7ew/zKohnBGrW.js"}], "format": "esm"}, "/:https://esm.sh/*@motionone/utils@10.18.0/node/utils.mjs": {"bytes": 1852, "imports": [], "format": "esm"}, "/:https://esm.sh/*@motionone/utils": {"bytes": 140, "imports": [{"path": "/:https://esm.sh/*@motionone/utils@10.18.0/node/utils.mjs", "kind": "import-statement", "original": "/*@motionone/utils@10.18.0/node/utils.mjs"}], "format": "esm"}, "/:https://esm.sh/*@motionone/easing@10.18.0/node/easing.mjs": {"bytes": 841, "imports": [{"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}, {"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}], "format": "esm"}, "/:https://esm.sh/*@motionone/easing": {"bytes": 142, "imports": [{"path": "/:https://esm.sh/*@motionone/easing@10.18.0/node/easing.mjs", "kind": "import-statement", "original": "/*@motionone/easing@10.18.0/node/easing.mjs"}], "format": "esm"}, "/:https://framerusercontent.com/modules/cKGD16u2MGB7MfqfVXFp/wiztTCbXokZrMicHAmZc/Grain.js": {"bytes": 1855, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://esm.sh/*@motionone/easing", "kind": "import-statement", "original": "@motionone/easing"}], "format": "esm"}, "/:https://framerusercontent.com/modules/VTUDdizacRHpwbkOamr7/AykinQJbgwl92LvMGZwu/constants.js": {"bytes": 7142, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/D4TWeLfcxT6Tysr2BlYg/iZjmqdxVx1EOiM3k1FaW/useOnNavigationTargetChange.js": {"bytes": 880, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/ExNgrA7EJTKUPpH6vIlN/eiOrSJ2Ab5M9jPCvVwUz/useConstant.js": {"bytes": 436, "imports": [{"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/D2Lz5CmnNVPZFFiZXalt/QaCzPbriZBfXWZIIycFI/colorFromToken.js": {"bytes": 651, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/3mKFSGQqKHV82uOV1eBc/5fbRLvOpxZC0JOXugvwm/isMotionValue.js": {"bytes": 360, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/xDiQsqBGXzmMsv7AlEVy/uhunpMiNsbXxzjlXsg1y/useUniqueClassName.js": {"bytes": 958, "imports": [{"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/ETACN5BJyFTSo0VVDJfu/NHRqowOiXkF9UwOzczF7/variantUtils.js": {"bytes": 1572, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/eMBrwoqQK7h6mEeGQUH8/GuplvPJVjmxpk9zqOTcb/isBrowser.js": {"bytes": 867, "imports": [{"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/v9AWX2URmiYsHf7GbctE/XxKAZ9KlhWqf5x1JMyyF/useOnChange.js": {"bytes": 1070, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/3mKFSGQqKHV82uOV1eBc/5fbRLvOpxZC0JOXugvwm/isMotionValue.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/3mKFSGQqKHV82uOV1eBc/5fbRLvOpxZC0JOXugvwm/isMotionValue.js"}], "format": "esm"}, "/:https://framerusercontent.com/modules/4zHZnO5JojN1PrIbu2jm/revv9QCWpkh8lPzi2jje/time.js": {"bytes": 753, "imports": [], "format": "esm"}, "/:https://framerusercontent.com/modules/kNDwabfjDEb3vUxkQlZS/fSIr3AOAYbGlfSPgXpYu/useAutoMotionValue.js": {"bytes": 2171, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/3mKFSGQqKHV82uOV1eBc/5fbRLvOpxZC0JOXugvwm/isMotionValue.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/3mKFSGQqKHV82uOV1eBc/5fbRLvOpxZC0JOXugvwm/isMotionValue.js"}, {"path": "/:https://framerusercontent.com/modules/ExNgrA7EJTKUPpH6vIlN/eiOrSJ2Ab5M9jPCvVwUz/useConstant.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/ExNgrA7EJTKUPpH6vIlN/eiOrSJ2Ab5M9jPCvVwUz/useConstant.js"}], "format": "esm"}, "/:https://framerusercontent.com/modules/cuQH4dmpDnV8YK1mSgQX/KqRXqunFjE6ufhpc7ZRu/useFontControls.js": {"bytes": 1382, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/VTUDdizacRHpwbkOamr7/AykinQJbgwl92LvMGZwu/constants.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/VTUDdizacRHpwbkOamr7/AykinQJbgwl92LvMGZwu/constants.js"}], "format": "esm"}, "/:https://framerusercontent.com/modules/afBE9Yx1W6bY5q32qPxe/m3q7puE2tbo1S2C0s0CT/useRenderTarget.js": {"bytes": 1001, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/zGkoP8tPDCkoBzMdt5uq/0zFSjxIYliHxrQQnryFX/useControlledState.js": {"bytes": 529, "imports": [{"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/5SM58HxZHxjjv7aLMOgQ/WXz9i6mVki0bBCrKdqB3/propUtils.js": {"bytes": 2578, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/8CkHAZatUz1UR8jNTcfD/HwbnIAZlUmQ2oTpcLkaH/detectAutoSizingAxis.js": {"bytes": 545, "imports": [], "format": "esm"}, "/:https://framerusercontent.com/modules/G4IfyjvwmaeSBpdb4TWu/OIjZRBmWDcIE2B6qgG1j/index.js": {"bytes": 6018, "imports": [{"path": "/:https://framerusercontent.com/modules/VTUDdizacRHpwbkOamr7/AykinQJbgwl92LvMGZwu/constants.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/VTUDdizacRHpwbkOamr7/AykinQJbgwl92LvMGZwu/constants.js"}, {"path": "/:https://framerusercontent.com/modules/D4TWeLfcxT6Tysr2BlYg/iZjmqdxVx1EOiM3k1FaW/useOnNavigationTargetChange.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/D4TWeLfcxT6Tysr2BlYg/iZjmqdxVx1EOiM3k1FaW/useOnNavigationTargetChange.js"}, {"path": "/:https://framerusercontent.com/modules/ExNgrA7EJTKUPpH6vIlN/eiOrSJ2Ab5M9jPCvVwUz/useConstant.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/ExNgrA7EJTKUPpH6vIlN/eiOrSJ2Ab5M9jPCvVwUz/useConstant.js"}, {"path": "/:https://framerusercontent.com/modules/D2Lz5CmnNVPZFFiZXalt/QaCzPbriZBfXWZIIycFI/colorFromToken.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/D2Lz5CmnNVPZFFiZXalt/QaCzPbriZBfXWZIIycFI/colorFromToken.js"}, {"path": "/:https://framerusercontent.com/modules/3mKFSGQqKHV82uOV1eBc/5fbRLvOpxZC0JOXugvwm/isMotionValue.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/3mKFSGQqKHV82uOV1eBc/5fbRLvOpxZC0JOXugvwm/isMotionValue.js"}, {"path": "/:https://framerusercontent.com/modules/xDiQsqBGXzmMsv7AlEVy/uhunpMiNsbXxzjlXsg1y/useUniqueClassName.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/xDiQsqBGXzmMsv7AlEVy/uhunpMiNsbXxzjlXsg1y/useUniqueClassName.js"}, {"path": "/:https://framerusercontent.com/modules/ETACN5BJyFTSo0VVDJfu/NHRqowOiXkF9UwOzczF7/variantUtils.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/ETACN5BJyFTSo0VVDJfu/NHRqowOiXkF9UwOzczF7/variantUtils.js"}, {"path": "/:https://framerusercontent.com/modules/eMBrwoqQK7h6mEeGQUH8/GuplvPJVjmxpk9zqOTcb/isBrowser.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/eMBrwoqQK7h6mEeGQUH8/GuplvPJVjmxpk9zqOTcb/isBrowser.js"}, {"path": "/:https://framerusercontent.com/modules/v9AWX2URmiYsHf7GbctE/XxKAZ9KlhWqf5x1JMyyF/useOnChange.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/v9AWX2URmiYsHf7GbctE/XxKAZ9KlhWqf5x1JMyyF/useOnChange.js"}, {"path": "/:https://framerusercontent.com/modules/4zHZnO5JojN1PrIbu2jm/revv9QCWpkh8lPzi2jje/time.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/4zHZnO5JojN1PrIbu2jm/revv9QCWpkh8lPzi2jje/time.js"}, {"path": "/:https://framerusercontent.com/modules/kNDwabfjDEb3vUxkQlZS/fSIr3AOAYbGlfSPgXpYu/useAutoMotionValue.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/kNDwabfjDEb3vUxkQlZS/fSIr3AOAYbGlfSPgXpYu/useAutoMotionValue.js"}, {"path": "/:https://framerusercontent.com/modules/cuQH4dmpDnV8YK1mSgQX/KqRXqunFjE6ufhpc7ZRu/useFontControls.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/cuQH4dmpDnV8YK1mSgQX/KqRXqunFjE6ufhpc7ZRu/useFontControls.js"}, {"path": "/:https://framerusercontent.com/modules/afBE9Yx1W6bY5q32qPxe/m3q7puE2tbo1S2C0s0CT/useRenderTarget.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/afBE9Yx1W6bY5q32qPxe/m3q7puE2tbo1S2C0s0CT/useRenderTarget.js"}, {"path": "/:https://framerusercontent.com/modules/zGkoP8tPDCkoBzMdt5uq/0zFSjxIYliHxrQQnryFX/useControlledState.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/zGkoP8tPDCkoBzMdt5uq/0zFSjxIYliHxrQQnryFX/useControlledState.js"}, {"path": "/:https://framerusercontent.com/modules/5SM58HxZHxjjv7aLMOgQ/WXz9i6mVki0bBCrKdqB3/propUtils.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/5SM58HxZHxjjv7aLMOgQ/WXz9i6mVki0bBCrKdqB3/propUtils.js"}, {"path": "/:https://framerusercontent.com/modules/8CkHAZatUz1UR8jNTcfD/HwbnIAZlUmQ2oTpcLkaH/detectAutoSizingAxis.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/8CkHAZatUz1UR8jNTcfD/HwbnIAZlUmQ2oTpcLkaH/detectAutoSizingAxis.js"}], "format": "esm"}, "/:https://framerusercontent.com/modules/lRDHiNWNVWmE0lqtoVHP/7qT0r3So12155VV5Jq5x/Video.js": {"bytes": 12858, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/G4IfyjvwmaeSBpdb4TWu/OIjZRBmWDcIE2B6qgG1j/index.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/G4IfyjvwmaeSBpdb4TWu/OIjZRBmWDcIE2B6qgG1j/index.js"}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/dhWVSMV5UF1XiHz4xBG6/MYBJX3CHugamesc7Y6hI/l0AhafeLr.js": {"bytes": 9990, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/cKGD16u2MGB7MfqfVXFp/wiztTCbXokZrMicHAmZc/Grain.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/cKGD16u2MGB7MfqfVXFp/wiztTCbXokZrMicHAmZc/Grain.js"}, {"path": "/:https://framerusercontent.com/modules/lRDHiNWNVWmE0lqtoVHP/7qT0r3So12155VV5Jq5x/Video.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/lRDHiNWNVWmE0lqtoVHP/7qT0r3So12155VV5Jq5x/Video.js"}], "format": "esm"}, "/:https://framerusercontent.com/modules/hURpBmt4DoL1ekI04Bxb/9v8HRjwLCA1gvmbMoIE2/CiB8L1GbI.js": {"bytes": 17350, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/fFLayKOd66X1viGh3w4y/R1pR6B4IpAhcuocyXjHX/GKtOymhXV.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/fFLayKOd66X1viGh3w4y/R1pR6B4IpAhcuocyXjHX/GKtOymhXV.js"}, {"path": "/:https://framerusercontent.com/modules/a0Wb5TOIxxIAvu7q5B8D/q3saJqiiDbtZZYfeFyuz/c_ITIcTit.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/a0Wb5TOIxxIAvu7q5B8D/q3saJqiiDbtZZYfeFyuz/c_ITIcTit.js"}, {"path": "/:https://framerusercontent.com/modules/mnjgJFRPj416r8pcvyDl/xwOi5nzd2tsTvl7sVclV/eDUVCTTXq.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/mnjgJFRPj416r8pcvyDl/xwOi5nzd2tsTvl7sVclV/eDUVCTTXq.js"}, {"path": "/:https://framerusercontent.com/modules/8xf9SmDnMz8gG4sF4hpH/tLrkAll785TqtzKVhiuu/fkNrtm8z2.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/8xf9SmDnMz8gG4sF4hpH/tLrkAll785TqtzKVhiuu/fkNrtm8z2.js"}, {"path": "/:https://framerusercontent.com/modules/dhWVSMV5UF1XiHz4xBG6/MYBJX3CHugamesc7Y6hI/l0AhafeLr.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/dhWVSMV5UF1XiHz4xBG6/MYBJX3CHugamesc7Y6hI/l0AhafeLr.js"}], "format": "esm"}, "virtual:services": {"bytes": 3011, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/hURpBmt4DoL1ekI04Bxb/9v8HRjwLCA1gvmbMoIE2/CiB8L1GbI.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/hURpBmt4DoL1ekI04Bxb/9v8HRjwLCA1gvmbMoIE2/CiB8L1GbI.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/p0IcM10gZPVBYgU6grNy/w0miSuPITV0VNXwc0nHX/IwCB7tJrN.js": {"bytes": 11748, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "virtual:small-button": {"bytes": 3055, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/p0IcM10gZPVBYgU6grNy/w0miSuPITV0VNXwc0nHX/IwCB7tJrN.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/p0IcM10gZPVBYgU6grNy/w0miSuPITV0VNXwc0nHX/IwCB7tJrN.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/yjPkTLrAsiIaTyOt74Cv/i0zaQnzM5vQtCm9tYMcj/KEzuQXrOe.js": {"bytes": 12356, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/vaUDP9yUvXDLDorewcXH/TU27PKlUxf7UEubboCQ4/svYtzYwMA.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/vaUDP9yUvXDLDorewcXH/TU27PKlUxf7UEubboCQ4/svYtzYwMA.js"}], "format": "esm"}, "virtual:graph-item": {"bytes": 3011, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/yjPkTLrAsiIaTyOt74Cv/i0zaQnzM5vQtCm9tYMcj/KEzuQXrOe.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/yjPkTLrAsiIaTyOt74Cv/i0zaQnzM5vQtCm9tYMcj/KEzuQXrOe.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/xawm4QzHYhcBfaeZ6rpd/DYMAxX2W9P5owRrmIIYS/zKohnBGrW.js": {"bytes": 50254, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/OTR8OYlvGuG53em7HPkd/XFT7UZEK3zn5KT7uQssI/gd6AWaps9.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/OTR8OYlvGuG53em7HPkd/XFT7UZEK3zn5KT7uQssI/gd6AWaps9.js"}, {"path": "/:https://framerusercontent.com/modules/7LhVHX9lqXURUWhQBzJn/wzTnBCThGjoX9XSgG5QG/HLpRTFhim.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/7LhVHX9lqXURUWhQBzJn/wzTnBCThGjoX9XSgG5QG/HLpRTFhim.js"}, {"path": "/:https://framerusercontent.com/modules/rL0tRCNiDNbTgXcw7l8h/MQ0zM3QkM5MEl876oJCh/pAxoS1kOX.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/rL0tRCNiDNbTgXcw7l8h/MQ0zM3QkM5MEl876oJCh/pAxoS1kOX.js"}, {"path": "/:https://framerusercontent.com/modules/vaUDP9yUvXDLDorewcXH/TU27PKlUxf7UEubboCQ4/svYtzYwMA.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/vaUDP9yUvXDLDorewcXH/TU27PKlUxf7UEubboCQ4/svYtzYwMA.js"}, {"path": "/:https://framerusercontent.com/modules/4AfGmdzrVIJOiclhVWW5/wDcr6iY79T92uICesIJd/BvyOYDKED.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/4AfGmdzrVIJOiclhVWW5/wDcr6iY79T92uICesIJd/BvyOYDKED.js"}, {"path": "/:https://framerusercontent.com/modules/NSkmAJg2RVe3fXUYvYRL/m3HXPaue5Xu13dGsibFw/d4er_GPVG.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/NSkmAJg2RVe3fXUYvYRL/m3HXPaue5Xu13dGsibFw/d4er_GPVG.js"}], "format": "esm"}, "virtual:table-item": {"bytes": 3097, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/xawm4QzHYhcBfaeZ6rpd/DYMAxX2W9P5owRrmIIYS/zKohnBGrW.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/xawm4QzHYhcBfaeZ6rpd/DYMAxX2W9P5owRrmIIYS/zKohnBGrW.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/jhmpyPFWXY5SZFHr5WJm/PVrSP0XenIHZSOxuHFWG/fDRzSjw63.js": {"bytes": 10757, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/t4d3RN8YpjGEeIkhpaWX/8KBE3N3fhG5ieFPwOZtk/nCQNaN8LD.js": {"bytes": 1151, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/kYJ0hJFhdtm6G3e3QyO6/g4rpjyJSfNJrlQAnvv8a/ypR5VEWEl.js": {"bytes": 10913, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/7YAWvv4TdhRLOwJydNGi/YTwBBct98kJIILL0ZhAq/bE3WztOaD.js": {"bytes": 6514, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/soXlZNhf4UQKHBU3NPW9/d5bayGKVEMjVWciYdk33/ebExugia5.js": {"bytes": 8898, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/hvfOCrJ4J6tWoXLRSXJB/7zjMumcOuXWqD8earGn2/k3VRpAwZb.js": {"bytes": 11752, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/ikVcV6kvZDj6uMF5EU26/KDHrdhjIRTgeMUBLQTNL/qBz_H6jdv.js": {"bytes": 9179, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/zYxxjr8GDqJairdZcDtp/v3Db4YTIvyVZ2ZxABpqF/xbeyrFlxc.js": {"bytes": 12162, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/7583VVgwoRouErNZ0wXE/CiSJOJayosxmBn1Tinmm/ovCH22Xmz.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/7583VVgwoRouErNZ0wXE/CiSJOJayosxmBn1Tinmm/ovCH22Xmz.js"}], "format": "esm"}, "/:https://framerusercontent.com/modules/5aH8sTCuBXkEF28bXDlY/6zW6pjqXlUWAuM8CvRvT/MH9m4cXzT.js": {"bytes": 49845, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/jhmpyPFWXY5SZFHr5WJm/PVrSP0XenIHZSOxuHFWG/fDRzSjw63.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/jhmpyPFWXY5SZFHr5WJm/PVrSP0XenIHZSOxuHFWG/fDRzSjw63.js"}, {"path": "/:https://framerusercontent.com/modules/t4d3RN8YpjGEeIkhpaWX/8KBE3N3fhG5ieFPwOZtk/nCQNaN8LD.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/t4d3RN8YpjGEeIkhpaWX/8KBE3N3fhG5ieFPwOZtk/nCQNaN8LD.js"}, {"path": "/:https://framerusercontent.com/modules/kYJ0hJFhdtm6G3e3QyO6/g4rpjyJSfNJrlQAnvv8a/ypR5VEWEl.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/kYJ0hJFhdtm6G3e3QyO6/g4rpjyJSfNJrlQAnvv8a/ypR5VEWEl.js"}, {"path": "/:https://framerusercontent.com/modules/7YAWvv4TdhRLOwJydNGi/YTwBBct98kJIILL0ZhAq/bE3WztOaD.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/7YAWvv4TdhRLOwJydNGi/YTwBBct98kJIILL0ZhAq/bE3WztOaD.js"}, {"path": "/:https://framerusercontent.com/modules/soXlZNhf4UQKHBU3NPW9/d5bayGKVEMjVWciYdk33/ebExugia5.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/soXlZNhf4UQKHBU3NPW9/d5bayGKVEMjVWciYdk33/ebExugia5.js"}, {"path": "/:https://framerusercontent.com/modules/hvfOCrJ4J6tWoXLRSXJB/7zjMumcOuXWqD8earGn2/k3VRpAwZb.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/hvfOCrJ4J6tWoXLRSXJB/7zjMumcOuXWqD8earGn2/k3VRpAwZb.js"}, {"path": "/:https://framerusercontent.com/modules/ikVcV6kvZDj6uMF5EU26/KDHrdhjIRTgeMUBLQTNL/qBz_H6jdv.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/ikVcV6kvZDj6uMF5EU26/KDHrdhjIRTgeMUBLQTNL/qBz_H6jdv.js"}, {"path": "/:https://framerusercontent.com/modules/zYxxjr8GDqJairdZcDtp/v3Db4YTIvyVZ2ZxABpqF/xbeyrFlxc.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/zYxxjr8GDqJairdZcDtp/v3Db4YTIvyVZ2ZxABpqF/xbeyrFlxc.js"}], "format": "esm"}, "virtual:navbar": {"bytes": 3011, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/5aH8sTCuBXkEF28bXDlY/6zW6pjqXlUWAuM8CvRvT/MH9m4cXzT.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/5aH8sTCuBXkEF28bXDlY/6zW6pjqXlUWAuM8CvRvT/MH9m4cXzT.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/bheqKqCqY9DyDgUEkC03/yG9czLqwXSpPzSLagGre/Counter.js": {"bytes": 4449, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/RemXsFW03uzBjJnHK5HN/3c99VWAyjHD8wcIfLR3l/wf_7zBsvo.js": {"bytes": 10760, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/sheAA3QRTXz9bLwA9r03/FcIieYwsDxO6UpSjeEKG/vqRYjrJj7.js": {"bytes": 27410, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/bheqKqCqY9DyDgUEkC03/yG9czLqwXSpPzSLagGre/Counter.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/bheqKqCqY9DyDgUEkC03/yG9czLqwXSpPzSLagGre/Counter.js"}, {"path": "/:https://framerusercontent.com/modules/OTR8OYlvGuG53em7HPkd/XFT7UZEK3zn5KT7uQssI/gd6AWaps9.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/OTR8OYlvGuG53em7HPkd/XFT7UZEK3zn5KT7uQssI/gd6AWaps9.js"}, {"path": "/:https://framerusercontent.com/modules/RemXsFW03uzBjJnHK5HN/3c99VWAyjHD8wcIfLR3l/wf_7zBsvo.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/RemXsFW03uzBjJnHK5HN/3c99VWAyjHD8wcIfLR3l/wf_7zBsvo.js"}], "format": "esm"}, "virtual:card": {"bytes": 3097, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/sheAA3QRTXz9bLwA9r03/FcIieYwsDxO6UpSjeEKG/vqRYjrJj7.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/sheAA3QRTXz9bLwA9r03/FcIieYwsDxO6UpSjeEKG/vqRYjrJj7.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/WO8qAR7NTyqVujiY1SK9/NoZGyMMtboxvkFUZ0EMH/LyKOtaXoC.js": {"bytes": 10908, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/3yFyjk27PFRLgex6ZBBN/YmlGWK0e2pR5xkpCKQ4I/Nr9N73Jiy.js": {"bytes": 13955, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/OTR8OYlvGuG53em7HPkd/XFT7UZEK3zn5KT7uQssI/gd6AWaps9.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/OTR8OYlvGuG53em7HPkd/XFT7UZEK3zn5KT7uQssI/gd6AWaps9.js"}, {"path": "/:https://framerusercontent.com/modules/WO8qAR7NTyqVujiY1SK9/NoZGyMMtboxvkFUZ0EMH/LyKOtaXoC.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/WO8qAR7NTyqVujiY1SK9/NoZGyMMtboxvkFUZ0EMH/LyKOtaXoC.js"}], "format": "esm"}, "virtual:play": {"bytes": 3055, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/3yFyjk27PFRLgex6ZBBN/YmlGWK0e2pR5xkpCKQ4I/Nr9N73Jiy.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/3yFyjk27PFRLgex6ZBBN/YmlGWK0e2pR5xkpCKQ4I/Nr9N73Jiy.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/BeoOkMASwMSx2anCpheJ/BfQd4QDD6UWpgHx1ZG0v/CustomAccordion.js": {"bytes": 16341, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "styled-components", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/jS7H9bgOZwv6l2bEyd11/5zqVDvjTY2RPXtEbQSj5/OBpl5Xksq.js": {"bytes": 11365, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/BeoOkMASwMSx2anCpheJ/BfQd4QDD6UWpgHx1ZG0v/CustomAccordion.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/BeoOkMASwMSx2anCpheJ/BfQd4QDD6UWpgHx1ZG0v/CustomAccordion.js"}], "format": "esm"}, "virtual:faq": {"bytes": 3055, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/jS7H9bgOZwv6l2bEyd11/5zqVDvjTY2RPXtEbQSj5/OBpl5Xksq.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/jS7H9bgOZwv6l2bEyd11/5zqVDvjTY2RPXtEbQSj5/OBpl5Xksq.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/3oGNt9rERhKQntdKF0qc/4dTqq4IM1tz2qInyxUiv/P34Gbmd7R.js": {"bytes": 17626, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "virtual:small-button-submit": {"bytes": 3034, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/3oGNt9rERhKQntdKF0qc/4dTqq4IM1tz2qInyxUiv/P34Gbmd7R.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/3oGNt9rERhKQntdKF0qc/4dTqq4IM1tz2qInyxUiv/P34Gbmd7R.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/N10U22jxXY23ZYp8w0mb/0FQySw5R2wgCyRX1rvuq/PJOVhWHcj.js": {"bytes": 14372, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "virtual:created-by": {"bytes": 3011, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/N10U22jxXY23ZYp8w0mb/0FQySw5R2wgCyRX1rvuq/PJOVhWHcj.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/N10U22jxXY23ZYp8w0mb/0FQySw5R2wgCyRX1rvuq/PJOVhWHcj.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/pa1lStjkdNyFVnLJM7LW/M4yFLBqPxO3ZhHYTWaZz/PSo3NS0KW.js": {"bytes": 8984, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "virtual:steps": {"bytes": 3011, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/pa1lStjkdNyFVnLJM7LW/M4yFLBqPxO3ZhHYTWaZz/PSo3NS0KW.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/pa1lStjkdNyFVnLJM7LW/M4yFLBqPxO3ZhHYTWaZz/PSo3NS0KW.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "virtual:table": {"bytes": 3076, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/8xf9SmDnMz8gG4sF4hpH/tLrkAll785TqtzKVhiuu/fkNrtm8z2.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/8xf9SmDnMz8gG4sF4hpH/tLrkAll785TqtzKVhiuu/fkNrtm8z2.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/cRPBb24hNrEYtkpZDWdW/i0M2yCuTPdw4utNeEQfN/QDgEnH9Gf.js": {"bytes": 14828, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/bheqKqCqY9DyDgUEkC03/yG9czLqwXSpPzSLagGre/Counter.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/bheqKqCqY9DyDgUEkC03/yG9czLqwXSpPzSLagGre/Counter.js"}, {"path": "/:https://framerusercontent.com/modules/OTR8OYlvGuG53em7HPkd/XFT7UZEK3zn5KT7uQssI/gd6AWaps9.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/OTR8OYlvGuG53em7HPkd/XFT7UZEK3zn5KT7uQssI/gd6AWaps9.js"}], "format": "esm"}, "virtual:number-item": {"bytes": 3076, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/cRPBb24hNrEYtkpZDWdW/i0M2yCuTPdw4utNeEQfN/QDgEnH9Gf.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/cRPBb24hNrEYtkpZDWdW/i0M2yCuTPdw4utNeEQfN/QDgEnH9Gf.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/bdNZipKw8szZq7vrsxYX/RZ6NlpCWyRw8Z1RL7y6y/RfhQK9Aec.js": {"bytes": 10105, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/7LhVHX9lqXURUWhQBzJn/wzTnBCThGjoX9XSgG5QG/HLpRTFhim.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/7LhVHX9lqXURUWhQBzJn/wzTnBCThGjoX9XSgG5QG/HLpRTFhim.js"}], "format": "esm"}, "virtual:link": {"bytes": 3011, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/bdNZipKw8szZq7vrsxYX/RZ6NlpCWyRw8Z1RL7y6y/RfhQK9Aec.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/bdNZipKw8szZq7vrsxYX/RZ6NlpCWyRw8Z1RL7y6y/RfhQK9Aec.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/rih294o185a0kfkOE6g1/FBAfj3bDaX74m9ohqNmk/uAsrSsx9N.js": {"bytes": 5369, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/tqZ3GHXWs78LlBwshtxr/85NY0AbZlKayuQGPTus9/TWOB0A5WT.js": {"bytes": 10298, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/rih294o185a0kfkOE6g1/FBAfj3bDaX74m9ohqNmk/uAsrSsx9N.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/rih294o185a0kfkOE6g1/FBAfj3bDaX74m9ohqNmk/uAsrSsx9N.js"}], "format": "esm"}, "virtual:input": {"bytes": 3034, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/tqZ3GHXWs78LlBwshtxr/85NY0AbZlKayuQGPTus9/TWOB0A5WT.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/tqZ3GHXWs78LlBwshtxr/85NY0AbZlKayuQGPTus9/TWOB0A5WT.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/s05IbLhj72TZhduOAuNj/weAk3ePS4spt3ofDeb8O/Uqu9MXhEM.js": {"bytes": 15226, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/7LhVHX9lqXURUWhQBzJn/wzTnBCThGjoX9XSgG5QG/HLpRTFhim.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/7LhVHX9lqXURUWhQBzJn/wzTnBCThGjoX9XSgG5QG/HLpRTFhim.js"}, {"path": "/:https://framerusercontent.com/modules/vaUDP9yUvXDLDorewcXH/TU27PKlUxf7UEubboCQ4/svYtzYwMA.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/vaUDP9yUvXDLDorewcXH/TU27PKlUxf7UEubboCQ4/svYtzYwMA.js"}], "format": "esm"}, "virtual:more-articles": {"bytes": 3055, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/s05IbLhj72TZhduOAuNj/weAk3ePS4spt3ofDeb8O/Uqu9MXhEM.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/s05IbLhj72TZhduOAuNj/weAk3ePS4spt3ofDeb8O/Uqu9MXhEM.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/4LbD9wvqiBYHJaqgRQoc/IQ2UCrsHU3U8aO1BfNuX/gd6AWaps9.js": {"bytes": 10913, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/nAMdoVC0qVfhdkrWextH/DrBJh5fm1Ck4aywSZe9y/HLpRTFhim.js": {"bytes": 10763, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/2mMO1EX5xNxIoJZ10MHC/mseA4evZ2eHB8ECkN8B1/oFAZmwcVJ.js": {"bytes": 11061, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/4F8nL6jCU5uHNUN0NA7K/2nXsZbgoEaoTaYPV76xl/a_JFqsPzN.js": {"bytes": 6963, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/DmTAdEsxZYh2zCIyVcT8/81LNstvbVr8IlmhdenW7/IwCB7tJrN.js": {"bytes": 11748, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/kEfpoFmhRRcEXNcnsS3Z/sAaVlfOVnJwAGzz5oQ3a/pCa0ZyMgp.js": {"bytes": 16621, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/DmTAdEsxZYh2zCIyVcT8/81LNstvbVr8IlmhdenW7/IwCB7tJrN.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/DmTAdEsxZYh2zCIyVcT8/81LNstvbVr8IlmhdenW7/IwCB7tJrN.js"}], "format": "esm"}, "/:https://framerusercontent.com/modules/YlnZT9T2TSVRxOqTj1Ks/l0iC658Wkj8ymMCsLgoA/W6mI3CngM.js": {"bytes": 49160, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/cKGD16u2MGB7MfqfVXFp/wiztTCbXokZrMicHAmZc/Grain.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/cKGD16u2MGB7MfqfVXFp/wiztTCbXokZrMicHAmZc/Grain.js"}, {"path": "/:https://framerusercontent.com/modules/lRDHiNWNVWmE0lqtoVHP/7qT0r3So12155VV5Jq5x/Video.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/lRDHiNWNVWmE0lqtoVHP/7qT0r3So12155VV5Jq5x/Video.js"}, {"path": "/:https://framerusercontent.com/modules/jhmpyPFWXY5SZFHr5WJm/PVrSP0XenIHZSOxuHFWG/fDRzSjw63.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/jhmpyPFWXY5SZFHr5WJm/PVrSP0XenIHZSOxuHFWG/fDRzSjw63.js"}, {"path": "/:https://framerusercontent.com/modules/4LbD9wvqiBYHJaqgRQoc/IQ2UCrsHU3U8aO1BfNuX/gd6AWaps9.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/4LbD9wvqiBYHJaqgRQoc/IQ2UCrsHU3U8aO1BfNuX/gd6AWaps9.js"}, {"path": "/:https://framerusercontent.com/modules/nAMdoVC0qVfhdkrWextH/DrBJh5fm1Ck4aywSZe9y/HLpRTFhim.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/nAMdoVC0qVfhdkrWextH/DrBJh5fm1Ck4aywSZe9y/HLpRTFhim.js"}, {"path": "/:https://framerusercontent.com/modules/2mMO1EX5xNxIoJZ10MHC/mseA4evZ2eHB8ECkN8B1/oFAZmwcVJ.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/2mMO1EX5xNxIoJZ10MHC/mseA4evZ2eHB8ECkN8B1/oFAZmwcVJ.js"}, {"path": "/:https://framerusercontent.com/modules/4F8nL6jCU5uHNUN0NA7K/2nXsZbgoEaoTaYPV76xl/a_JFqsPzN.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/4F8nL6jCU5uHNUN0NA7K/2nXsZbgoEaoTaYPV76xl/a_JFqsPzN.js"}, {"path": "/:https://framerusercontent.com/modules/g2cnji7Jaa30imfz7vPz/Yt1e07acnMaFp9CZYfKH/BvyOYDKED.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/g2cnji7Jaa30imfz7vPz/Yt1e07acnMaFp9CZYfKH/BvyOYDKED.js"}, {"path": "/:https://framerusercontent.com/modules/a0Wb5TOIxxIAvu7q5B8D/q3saJqiiDbtZZYfeFyuz/c_ITIcTit.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/a0Wb5TOIxxIAvu7q5B8D/q3saJqiiDbtZZYfeFyuz/c_ITIcTit.js"}, {"path": "/:https://framerusercontent.com/modules/kEfpoFmhRRcEXNcnsS3Z/sAaVlfOVnJwAGzz5oQ3a/pCa0ZyMgp.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/kEfpoFmhRRcEXNcnsS3Z/sAaVlfOVnJwAGzz5oQ3a/pCa0ZyMgp.js"}], "format": "esm"}, "virtual:hero": {"bytes": 3011, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/YlnZT9T2TSVRxOqTj1Ks/l0iC658Wkj8ymMCsLgoA/W6mI3CngM.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/YlnZT9T2TSVRxOqTj1Ks/l0iC658Wkj8ymMCsLgoA/W6mI3CngM.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/EqoRljgeGyBgybgK0SIZ/vaxBUeUO9TAoYQif6Rpa/fDRzSjw63.js": {"bytes": 10757, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/3akaxm8QHD9JaLKaBxgT/VqNqXMEUsuM4OfhwOFSY/WhaJzx6A3.js": {"bytes": 9370, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/EqoRljgeGyBgybgK0SIZ/vaxBUeUO9TAoYQif6Rpa/fDRzSjw63.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/EqoRljgeGyBgybgK0SIZ/vaxBUeUO9TAoYQif6Rpa/fDRzSjw63.js"}], "format": "esm"}, "virtual:option": {"bytes": 3034, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/3akaxm8QHD9JaLKaBxgT/VqNqXMEUsuM4OfhwOFSY/WhaJzx6A3.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/3akaxm8QHD9JaLKaBxgT/VqNqXMEUsuM4OfhwOFSY/WhaJzx6A3.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/p1bBWLZwVrvHXuWlH4xx/n9OidViwumXepK7AzYdF/nCOD2Sdie.js": {"bytes": 10767, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/mywbaSp9mF6R83DCrxI4/0MJDIxKkII1tq8lANCiM/dHpxp5ikb.js": {"bytes": 16325, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/OTR8OYlvGuG53em7HPkd/XFT7UZEK3zn5KT7uQssI/gd6AWaps9.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/OTR8OYlvGuG53em7HPkd/XFT7UZEK3zn5KT7uQssI/gd6AWaps9.js"}, {"path": "/:https://framerusercontent.com/modules/DmTAdEsxZYh2zCIyVcT8/81LNstvbVr8IlmhdenW7/IwCB7tJrN.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/DmTAdEsxZYh2zCIyVcT8/81LNstvbVr8IlmhdenW7/IwCB7tJrN.js"}, {"path": "/:https://framerusercontent.com/modules/7583VVgwoRouErNZ0wXE/CiSJOJayosxmBn1Tinmm/ovCH22Xmz.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/7583VVgwoRouErNZ0wXE/CiSJOJayosxmBn1Tinmm/ovCH22Xmz.js"}], "format": "esm"}, "/:https://framerusercontent.com/modules/Evvpdb11LprA8hF4wwQ2/YUjEKblsUHoIv1sTKJxL/XMbv3HI5C.js": {"bytes": 18118, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/nAMdoVC0qVfhdkrWextH/DrBJh5fm1Ck4aywSZe9y/HLpRTFhim.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/nAMdoVC0qVfhdkrWextH/DrBJh5fm1Ck4aywSZe9y/HLpRTFhim.js"}, {"path": "/:https://framerusercontent.com/modules/p1bBWLZwVrvHXuWlH4xx/n9OidViwumXepK7AzYdF/nCOD2Sdie.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/p1bBWLZwVrvHXuWlH4xx/n9OidViwumXepK7AzYdF/nCOD2Sdie.js"}, {"path": "/:https://framerusercontent.com/modules/a0Wb5TOIxxIAvu7q5B8D/q3saJqiiDbtZZYfeFyuz/c_ITIcTit.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/a0Wb5TOIxxIAvu7q5B8D/q3saJqiiDbtZZYfeFyuz/c_ITIcTit.js"}, {"path": "/:https://framerusercontent.com/modules/mywbaSp9mF6R83DCrxI4/0MJDIxKkII1tq8lANCiM/dHpxp5ikb.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/mywbaSp9mF6R83DCrxI4/0MJDIxKkII1tq8lANCiM/dHpxp5ikb.js"}, {"path": "/:https://framerusercontent.com/modules/sheAA3QRTXz9bLwA9r03/FcIieYwsDxO6UpSjeEKG/vqRYjrJj7.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/sheAA3QRTXz9bLwA9r03/FcIieYwsDxO6UpSjeEKG/vqRYjrJj7.js"}], "format": "esm"}, "virtual:advantages": {"bytes": 3011, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/Evvpdb11LprA8hF4wwQ2/YUjEKblsUHoIv1sTKJxL/XMbv3HI5C.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/Evvpdb11LprA8hF4wwQ2/YUjEKblsUHoIv1sTKJxL/XMbv3HI5C.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://esm.sh/*@motionone/animation@10.18.0/node/animation.mjs": {"bytes": 3671, "imports": [{"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}, {"path": "/:https://esm.sh/*@motionone/easing", "kind": "import-statement", "original": "@motionone/easing"}, {"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}], "format": "esm"}, "/:https://esm.sh/*@motionone/animation": {"bytes": 148, "imports": [{"path": "/:https://esm.sh/*@motionone/animation@10.18.0/node/animation.mjs", "kind": "import-statement", "original": "/*@motionone/animation@10.18.0/node/animation.mjs"}], "format": "esm"}, "/:https://esm.sh/*hey-listen@1.0.8/node/hey-listen.mjs": {"bytes": 170, "imports": [], "format": "esm"}, "/:https://esm.sh/*hey-listen": {"bytes": 137, "imports": [{"path": "/:https://esm.sh/*hey-listen@1.0.8/node/hey-listen.mjs", "kind": "import-statement", "original": "/*hey-listen@1.0.8/node/hey-listen.mjs"}], "format": "esm"}, "/:https://esm.sh/*@motionone/types@10.17.1/node/types.mjs": {"bytes": 318, "imports": [], "format": "esm"}, "/:https://esm.sh/*@motionone/types": {"bytes": 140, "imports": [{"path": "/:https://esm.sh/*@motionone/types@10.17.1/node/types.mjs", "kind": "import-statement", "original": "/*@motionone/types@10.17.1/node/types.mjs"}], "format": "esm"}, "/:https://esm.sh/*tslib@2.8.1/node/tslib.mjs": {"bytes": 14748, "imports": [], "format": "esm"}, "/:https://esm.sh/*tslib": {"bytes": 229, "imports": [{"path": "/:https://esm.sh/*tslib@2.8.1/node/tslib.mjs", "kind": "import-statement", "original": "/*tslib@2.8.1/node/tslib.mjs"}, {"path": "/:https://esm.sh/*tslib@2.8.1/node/tslib.mjs", "kind": "import-statement", "original": "/*tslib@2.8.1/node/tslib.mjs"}], "format": "esm"}, "/:https://esm.sh/*@motionone/generators@10.18.0/node/generators.mjs": {"bytes": 2980, "imports": [{"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}, {"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}, {"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}, {"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}], "format": "esm"}, "/:https://esm.sh/*@motionone/generators": {"bytes": 150, "imports": [{"path": "/:https://esm.sh/*@motionone/generators@10.18.0/node/generators.mjs", "kind": "import-statement", "original": "/*@motionone/generators@10.18.0/node/generators.mjs"}], "format": "esm"}, "/:https://esm.sh/*@motionone/dom@10.18.0/node/dom.mjs": {"bytes": 26392, "imports": [{"path": "/:https://esm.sh/*@motionone/animation", "kind": "import-statement", "original": "@motionone/animation"}, {"path": "/:https://esm.sh/*hey-listen", "kind": "import-statement", "original": "hey-listen"}, {"path": "/:https://esm.sh/*@motionone/types", "kind": "import-statement", "original": "@motionone/types"}, {"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}, {"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}, {"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}, {"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}, {"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}, {"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}, {"path": "/:https://esm.sh/*@motionone/animation", "kind": "import-statement", "original": "@motionone/animation"}, {"path": "/:https://esm.sh/*tslib", "kind": "import-statement", "original": "tslib"}, {"path": "/:https://esm.sh/*hey-listen", "kind": "import-statement", "original": "hey-listen"}, {"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}, {"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}, {"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}, {"path": "/:https://esm.sh/*@motionone/animation", "kind": "import-statement", "original": "@motionone/animation"}, {"path": "/:https://esm.sh/*@motionone/generators", "kind": "import-statement", "original": "@motionone/generators"}, {"path": "/:https://esm.sh/*@motionone/generators", "kind": "import-statement", "original": "@motionone/generators"}, {"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}, {"path": "/:https://esm.sh/*@motionone/generators", "kind": "import-statement", "original": "@motionone/generators"}, {"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}, {"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}, {"path": "/:https://esm.sh/*tslib", "kind": "import-statement", "original": "tslib"}, {"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}, {"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}, {"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}, {"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}, {"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}, {"path": "/:https://esm.sh/*tslib", "kind": "import-statement", "original": "tslib"}, {"path": "/:https://esm.sh/*hey-listen", "kind": "import-statement", "original": "hey-listen"}, {"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}, {"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}, {"path": "/:https://esm.sh/*tslib", "kind": "import-statement", "original": "tslib"}, {"path": "/:https://esm.sh/*@motionone/animation", "kind": "import-statement", "original": "@motionone/animation"}, {"path": "/:https://esm.sh/*@motionone/utils", "kind": "import-statement", "original": "@motionone/utils"}], "format": "esm"}, "/:https://esm.sh/*@motionone/dom": {"bytes": 136, "imports": [{"path": "/:https://esm.sh/*@motionone/dom@10.18.0/node/dom.mjs", "kind": "import-statement", "original": "/*@motionone/dom@10.18.0/node/dom.mjs"}], "format": "esm"}, "/:https://framerusercontent.com/modules/B2xAlJLcN0gOnt11mSPw/jyRNgY7vYWXe6t31T0wo/Ticker.js": {"bytes": 12778, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://esm.sh/*@motionone/dom", "kind": "import-statement", "original": "@motionone/dom"}], "format": "esm"}, "/:https://framerusercontent.com/modules/JXipYeVE7fx0Gx9lfC1o/eEWwYAGd9qCIC36YeNjR/a_JFqsPzN.js": {"bytes": 6963, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/pRdtH3H3SzHXlVcu6fJq/gtdA974yag36O5Cqwsfp/YYl7PDyYl.js": {"bytes": 9896, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/B2xAlJLcN0gOnt11mSPw/jyRNgY7vYWXe6t31T0wo/Ticker.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/B2xAlJLcN0gOnt11mSPw/jyRNgY7vYWXe6t31T0wo/Ticker.js"}, {"path": "/:https://framerusercontent.com/modules/JXipYeVE7fx0Gx9lfC1o/eEWwYAGd9qCIC36YeNjR/a_JFqsPzN.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/JXipYeVE7fx0Gx9lfC1o/eEWwYAGd9qCIC36YeNjR/a_JFqsPzN.js"}], "format": "esm"}, "virtual:ticker": {"bytes": 3011, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/pRdtH3H3SzHXlVcu6fJq/gtdA974yag36O5Cqwsfp/YYl7PDyYl.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/pRdtH3H3SzHXlVcu6fJq/gtdA974yag36O5Cqwsfp/YYl7PDyYl.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/jwmEWI6XYLAo8iFW1jdT/yq8KXx0rmwm7kswAk0oY/ZsPpcvZL7.js": {"bytes": 18286, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "virtual:large-button-submit": {"bytes": 3034, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/jwmEWI6XYLAo8iFW1jdT/yq8KXx0rmwm7kswAk0oY/ZsPpcvZL7.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/jwmEWI6XYLAo8iFW1jdT/yq8KXx0rmwm7kswAk0oY/ZsPpcvZL7.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/wRGRkNPIgZM4wuze3hOY/TARN61VU0EmPqQfPlH4Z/ypR5VEWEl.js": {"bytes": 10913, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/AoMRsMghfrPsFoStNO5j/P8G2i4Dy8PwIpLcRzeNX/ZwY2WTzsN.js": {"bytes": 18858, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/wRGRkNPIgZM4wuze3hOY/TARN61VU0EmPqQfPlH4Z/ypR5VEWEl.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/wRGRkNPIgZM4wuze3hOY/TARN61VU0EmPqQfPlH4Z/ypR5VEWEl.js"}, {"path": "/:https://framerusercontent.com/modules/7583VVgwoRouErNZ0wXE/CiSJOJayosxmBn1Tinmm/ovCH22Xmz.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/7583VVgwoRouErNZ0wXE/CiSJOJayosxmBn1Tinmm/ovCH22Xmz.js"}], "format": "esm"}, "virtual:article-card": {"bytes": 3076, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/AoMRsMghfrPsFoStNO5j/P8G2i4Dy8PwIpLcRzeNX/ZwY2WTzsN.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/AoMRsMghfrPsFoStNO5j/P8G2i4Dy8PwIpLcRzeNX/ZwY2WTzsN.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "virtual:logo-card-small": {"bytes": 3034, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/4F8nL6jCU5uHNUN0NA7K/2nXsZbgoEaoTaYPV76xl/a_JFqsPzN.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/4F8nL6jCU5uHNUN0NA7K/2nXsZbgoEaoTaYPV76xl/a_JFqsPzN.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "virtual:hamburger": {"bytes": 3034, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/7YAWvv4TdhRLOwJydNGi/YTwBBct98kJIILL0ZhAq/bE3WztOaD.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/7YAWvv4TdhRLOwJydNGi/YTwBBct98kJIILL0ZhAq/bE3WztOaD.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/Ik27nyqmPbQ4RLC0V1Hw/09nrParnXLeO68YEmFcO/Nr9N73Jiy.js": {"bytes": 13955, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/OTR8OYlvGuG53em7HPkd/XFT7UZEK3zn5KT7uQssI/gd6AWaps9.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/OTR8OYlvGuG53em7HPkd/XFT7UZEK3zn5KT7uQssI/gd6AWaps9.js"}, {"path": "/:https://framerusercontent.com/modules/WO8qAR7NTyqVujiY1SK9/NoZGyMMtboxvkFUZ0EMH/LyKOtaXoC.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/WO8qAR7NTyqVujiY1SK9/NoZGyMMtboxvkFUZ0EMH/LyKOtaXoC.js"}], "format": "esm"}, "/:https://framerusercontent.com/modules/qjgWXMzvxaNU7gU0HfTs/67qbZo3yAQzhksxoJAxc/c3jVHY4cP.js": {"bytes": 12396, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/cKGD16u2MGB7MfqfVXFp/wiztTCbXokZrMicHAmZc/Grain.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/cKGD16u2MGB7MfqfVXFp/wiztTCbXokZrMicHAmZc/Grain.js"}, {"path": "/:https://framerusercontent.com/modules/Ik27nyqmPbQ4RLC0V1Hw/09nrParnXLeO68YEmFcO/Nr9N73Jiy.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/Ik27nyqmPbQ4RLC0V1Hw/09nrParnXLeO68YEmFcO/Nr9N73Jiy.js"}], "format": "esm"}, "virtual:video": {"bytes": 3055, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/qjgWXMzvxaNU7gU0HfTs/67qbZo3yAQzhksxoJAxc/c3jVHY4cP.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/qjgWXMzvxaNU7gU0HfTs/67qbZo3yAQzhksxoJAxc/c3jVHY4cP.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "virtual:intro": {"bytes": 3055, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/a0Wb5TOIxxIAvu7q5B8D/q3saJqiiDbtZZYfeFyuz/c_ITIcTit.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/a0Wb5TOIxxIAvu7q5B8D/q3saJqiiDbtZZYfeFyuz/c_ITIcTit.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/oDdmbFQXleEahNoGjmdo/WFPfNC3kP0LlLTNM21nD/d4er_GPVG.js": {"bytes": 7590, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "virtual:category": {"bytes": 3011, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/oDdmbFQXleEahNoGjmdo/WFPfNC3kP0LlLTNM21nD/d4er_GPVG.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/oDdmbFQXleEahNoGjmdo/WFPfNC3kP0LlLTNM21nD/d4er_GPVG.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/t1iJuWnZpK9UlGn9LTZR/MmGYJmvrUwcTK5x20ctC/d5lvgh6Gd.js": {"bytes": 7392, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "virtual:rating-stars": {"bytes": 3011, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/t1iJuWnZpK9UlGn9LTZR/MmGYJmvrUwcTK5x20ctC/d5lvgh6Gd.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/t1iJuWnZpK9UlGn9LTZR/MmGYJmvrUwcTK5x20ctC/d5lvgh6Gd.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "virtual:banner": {"bytes": 3034, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/mywbaSp9mF6R83DCrxI4/0MJDIxKkII1tq8lANCiM/dHpxp5ikb.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/mywbaSp9mF6R83DCrxI4/0MJDIxKkII1tq8lANCiM/dHpxp5ikb.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/sKy2U5CCL7dNlNvddsNT/jduSarGkVeMtg4GIVij9/do0Th5ePX.js": {"bytes": 10490, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/wRGRkNPIgZM4wuze3hOY/TARN61VU0EmPqQfPlH4Z/ypR5VEWEl.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/wRGRkNPIgZM4wuze3hOY/TARN61VU0EmPqQfPlH4Z/ypR5VEWEl.js"}], "format": "esm"}, "virtual:built-in": {"bytes": 3011, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/sKy2U5CCL7dNlNvddsNT/jduSarGkVeMtg4GIVij9/do0Th5ePX.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/sKy2U5CCL7dNlNvddsNT/jduSarGkVeMtg4GIVij9/do0Th5ePX.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/bS1D3ZEoaKmqk9Wy5pSI/DwfWvtC2vUITQYbUeZZI/dtX3pmnQt.js": {"bytes": 19439, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/7LhVHX9lqXURUWhQBzJn/wzTnBCThGjoX9XSgG5QG/HLpRTFhim.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/7LhVHX9lqXURUWhQBzJn/wzTnBCThGjoX9XSgG5QG/HLpRTFhim.js"}, {"path": "/:https://framerusercontent.com/modules/vaUDP9yUvXDLDorewcXH/TU27PKlUxf7UEubboCQ4/svYtzYwMA.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/vaUDP9yUvXDLDorewcXH/TU27PKlUxf7UEubboCQ4/svYtzYwMA.js"}], "format": "esm"}, "virtual:small-link": {"bytes": 3055, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/bS1D3ZEoaKmqk9Wy5pSI/DwfWvtC2vUITQYbUeZZI/dtX3pmnQt.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/bS1D3ZEoaKmqk9Wy5pSI/DwfWvtC2vUITQYbUeZZI/dtX3pmnQt.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "virtual:large-button": {"bytes": 3055, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/mnjgJFRPj416r8pcvyDl/xwOi5nzd2tsTvl7sVclV/eDUVCTTXq.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/mnjgJFRPj416r8pcvyDl/xwOi5nzd2tsTvl7sVclV/eDUVCTTXq.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/CBN86zQXoOXpcgKhNtfI/h1KCmCnIrIg9HM8rHLCH/qNTzVcuJS-0.js": {"bytes": 35251, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/CBN86zQXoOXpcgKhNtfI/h1KCmCnIrIg9HM8rHLCH/qNTzVcuJS-1.js": {"bytes": 410, "imports": [], "format": "esm"}, "/:https://framerusercontent.com/modules/CBN86zQXoOXpcgKhNtfI/h1KCmCnIrIg9HM8rHLCH/qNTzVcuJS.js": {"bytes": 2813, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/CBN86zQXoOXpcgKhNtfI/h1KCmCnIrIg9HM8rHLCH/qNTzVcuJS-0.js", "kind": "import-statement", "original": "./qNTzVcuJS-0.js"}, {"path": "/:https://framerusercontent.com/modules/CBN86zQXoOXpcgKhNtfI/h1KCmCnIrIg9HM8rHLCH/qNTzVcuJS-1.js", "kind": "import-statement", "original": "./qNTzVcuJS-1.js"}], "format": "esm"}, "/:https://framerusercontent.com/modules/94zGMcemTO1UzYqvT5pJ/FkQLg7r0T9A6zWOCSuGc/oFAZmwcVJ.js": {"bytes": 11061, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/hWxVS3apPj1PnFxNHjIQ/nspOabGpGjvj61gMbFbA/eKMoUoN9m.js": {"bytes": 28330, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/CBN86zQXoOXpcgKhNtfI/h1KCmCnIrIg9HM8rHLCH/qNTzVcuJS.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/CBN86zQXoOXpcgKhNtfI/h1KCmCnIrIg9HM8rHLCH/qNTzVcuJS.js"}, {"path": "/:https://framerusercontent.com/modules/94zGMcemTO1UzYqvT5pJ/FkQLg7r0T9A6zWOCSuGc/oFAZmwcVJ.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/94zGMcemTO1UzYqvT5pJ/FkQLg7r0T9A6zWOCSuGc/oFAZmwcVJ.js"}], "format": "esm"}, "virtual:project-card": {"bytes": 3055, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/hWxVS3apPj1PnFxNHjIQ/nspOabGpGjvj61gMbFbA/eKMoUoN9m.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/hWxVS3apPj1PnFxNHjIQ/nspOabGpGjvj61gMbFbA/eKMoUoN9m.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "virtual:menu-item": {"bytes": 3011, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/soXlZNhf4UQKHBU3NPW9/d5bayGKVEMjVWciYdk33/ebExugia5.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/soXlZNhf4UQKHBU3NPW9/d5bayGKVEMjVWciYdk33/ebExugia5.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/spPGp13l4XWgAIZFyxLC/emaf7oXZn50b4jG3PICH/zgy6bak25.js": {"bytes": 10764, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/wb4wdricYk6hX5QoFCY0/EzLNKPFRUFRrupUhGwbG/fOV3xrRss.js": {"bytes": 15627, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/vaUDP9yUvXDLDorewcXH/TU27PKlUxf7UEubboCQ4/svYtzYwMA.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/vaUDP9yUvXDLDorewcXH/TU27PKlUxf7UEubboCQ4/svYtzYwMA.js"}, {"path": "/:https://framerusercontent.com/modules/wRGRkNPIgZM4wuze3hOY/TARN61VU0EmPqQfPlH4Z/ypR5VEWEl.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/wRGRkNPIgZM4wuze3hOY/TARN61VU0EmPqQfPlH4Z/ypR5VEWEl.js"}, {"path": "/:https://framerusercontent.com/modules/spPGp13l4XWgAIZFyxLC/emaf7oXZn50b4jG3PICH/zgy6bak25.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/spPGp13l4XWgAIZFyxLC/emaf7oXZn50b4jG3PICH/zgy6bak25.js"}], "format": "esm"}, "virtual:article-card-l": {"bytes": 3055, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/wb4wdricYk6hX5QoFCY0/EzLNKPFRUFRrupUhGwbG/fOV3xrRss.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/wb4wdricYk6hX5QoFCY0/EzLNKPFRUFRrupUhGwbG/fOV3xrRss.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/9SnJWnLLsQu1WYKv4TtR/hhvSb1XFv2UspFIa9bFx/fN1_sGlJp.js": {"bytes": 10760, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/7LbtHc4qbG6Lt0rW66b4/qFIZAwipssfkc7WYZt6g/d5lvgh6Gd.js": {"bytes": 7392, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/t9kmVKgwV1DLW0GG8nPN/piyVQ3r6E2NxsJQlQ3Pt/fTmrIoq5f.js": {"bytes": 23569, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/9SnJWnLLsQu1WYKv4TtR/hhvSb1XFv2UspFIa9bFx/fN1_sGlJp.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/9SnJWnLLsQu1WYKv4TtR/hhvSb1XFv2UspFIa9bFx/fN1_sGlJp.js"}, {"path": "/:https://framerusercontent.com/modules/94zGMcemTO1UzYqvT5pJ/FkQLg7r0T9A6zWOCSuGc/oFAZmwcVJ.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/94zGMcemTO1UzYqvT5pJ/FkQLg7r0T9A6zWOCSuGc/oFAZmwcVJ.js"}, {"path": "/:https://framerusercontent.com/modules/vaUDP9yUvXDLDorewcXH/TU27PKlUxf7UEubboCQ4/svYtzYwMA.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/vaUDP9yUvXDLDorewcXH/TU27PKlUxf7UEubboCQ4/svYtzYwMA.js"}, {"path": "/:https://framerusercontent.com/modules/4AfGmdzrVIJOiclhVWW5/wDcr6iY79T92uICesIJd/BvyOYDKED.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/4AfGmdzrVIJOiclhVWW5/wDcr6iY79T92uICesIJd/BvyOYDKED.js"}, {"path": "/:https://framerusercontent.com/modules/7LbtHc4qbG6Lt0rW66b4/qFIZAwipssfkc7WYZt6g/d5lvgh6Gd.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/7LbtHc4qbG6Lt0rW66b4/qFIZAwipssfkc7WYZt6g/d5lvgh6Gd.js"}], "format": "esm"}, "virtual:testimonial": {"bytes": 3055, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/t9kmVKgwV1DLW0GG8nPN/piyVQ3r6E2NxsJQlQ3Pt/fTmrIoq5f.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/t9kmVKgwV1DLW0GG8nPN/piyVQ3r6E2NxsJQlQ3Pt/fTmrIoq5f.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/yRDItg6CGqm47W2hBzp5/IzT1L6BlcymqiRv450Eg/h_AuDmjef.js": {"bytes": 11799, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "virtual:logo-card": {"bytes": 3076, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/yRDItg6CGqm47W2hBzp5/IzT1L6BlcymqiRv450Eg/h_AuDmjef.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/yRDItg6CGqm47W2hBzp5/IzT1L6BlcymqiRv450Eg/h_AuDmjef.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "virtual:menu-item-large": {"bytes": 3076, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/hvfOCrJ4J6tWoXLRSXJB/7zjMumcOuXWqD8earGn2/k3VRpAwZb.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/hvfOCrJ4J6tWoXLRSXJB/7zjMumcOuXWqD8earGn2/k3VRpAwZb.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/zvBvxOn4yypid4okdhJh/B64S9zpOfU1k6qA6wRNR/kO8bO08Ia.js": {"bytes": 8547, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/OTR8OYlvGuG53em7HPkd/XFT7UZEK3zn5KT7uQssI/gd6AWaps9.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/OTR8OYlvGuG53em7HPkd/XFT7UZEK3zn5KT7uQssI/gd6AWaps9.js"}, {"path": "/:https://framerusercontent.com/modules/7583VVgwoRouErNZ0wXE/CiSJOJayosxmBn1Tinmm/ovCH22Xmz.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/7583VVgwoRouErNZ0wXE/CiSJOJayosxmBn1Tinmm/ovCH22Xmz.js"}], "format": "esm"}, "virtual:feature-item": {"bytes": 3011, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/zvBvxOn4yypid4okdhJh/B64S9zpOfU1k6qA6wRNR/kO8bO08Ia.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/zvBvxOn4yypid4okdhJh/B64S9zpOfU1k6qA6wRNR/kO8bO08Ia.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/rtWkvH5fSSlZDlo2o6ww/HnHVN87wcIU64TRg16LZ/kiJu4DQjn.js": {"bytes": 9772, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "virtual:plan-price-toggle": {"bytes": 3034, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/rtWkvH5fSSlZDlo2o6ww/HnHVN87wcIU64TRg16LZ/kiJu4DQjn.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/rtWkvH5fSSlZDlo2o6ww/HnHVN87wcIU64TRg16LZ/kiJu4DQjn.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "virtual:gradient-background": {"bytes": 3055, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/dhWVSMV5UF1XiHz4xBG6/MYBJX3CHugamesc7Y6hI/l0AhafeLr.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/dhWVSMV5UF1XiHz4xBG6/MYBJX3CHugamesc7Y6hI/l0AhafeLr.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub-0.js": {"bytes": 64962, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub-1.js": {"bytes": 410, "imports": [], "format": "esm"}, "/:https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js": {"bytes": 14608, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub-0.js", "kind": "import-statement", "original": "./B3xOiqtub-0.js"}, {"path": "/:https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub-1.js", "kind": "import-statement", "original": "./B3xOiqtub-1.js"}], "format": "esm"}, "/:https://framerusercontent.com/modules/xqMlz63qKzLJKZQseE5v/5e05xMkHRisZbX7kOPJG/qNTzVcuJS-0.js": {"bytes": 35251, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/xqMlz63qKzLJKZQseE5v/5e05xMkHRisZbX7kOPJG/qNTzVcuJS-1.js": {"bytes": 410, "imports": [], "format": "esm"}, "/:https://framerusercontent.com/modules/xqMlz63qKzLJKZQseE5v/5e05xMkHRisZbX7kOPJG/qNTzVcuJS.js": {"bytes": 2813, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/xqMlz63qKzLJKZQseE5v/5e05xMkHRisZbX7kOPJG/qNTzVcuJS-0.js", "kind": "import-statement", "original": "./qNTzVcuJS-0.js"}, {"path": "/:https://framerusercontent.com/modules/xqMlz63qKzLJKZQseE5v/5e05xMkHRisZbX7kOPJG/qNTzVcuJS-1.js", "kind": "import-statement", "original": "./qNTzVcuJS-1.js"}], "format": "esm"}, "/:https://framerusercontent.com/modules/lj6xtW3z0CEecnsPKstp/CzL6AGzcEG7H6XHoIDLI/ntVcqJNYe.js": {"bytes": 20133, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js"}, {"path": "/:https://framerusercontent.com/modules/xqMlz63qKzLJKZQseE5v/5e05xMkHRisZbX7kOPJG/qNTzVcuJS.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/xqMlz63qKzLJKZQseE5v/5e05xMkHRisZbX7kOPJG/qNTzVcuJS.js"}, {"path": "/:https://framerusercontent.com/modules/4LbD9wvqiBYHJaqgRQoc/IQ2UCrsHU3U8aO1BfNuX/gd6AWaps9.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/4LbD9wvqiBYHJaqgRQoc/IQ2UCrsHU3U8aO1BfNuX/gd6AWaps9.js"}, {"path": "/:https://framerusercontent.com/modules/fFLayKOd66X1viGh3w4y/R1pR6B4IpAhcuocyXjHX/GKtOymhXV.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/fFLayKOd66X1viGh3w4y/R1pR6B4IpAhcuocyXjHX/GKtOymhXV.js"}, {"path": "/:https://framerusercontent.com/modules/hWxVS3apPj1PnFxNHjIQ/nspOabGpGjvj61gMbFbA/eKMoUoN9m.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/hWxVS3apPj1PnFxNHjIQ/nspOabGpGjvj61gMbFbA/eKMoUoN9m.js"}], "format": "esm"}, "virtual:projects": {"bytes": 3011, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/lj6xtW3z0CEecnsPKstp/CzL6AGzcEG7H6XHoIDLI/ntVcqJNYe.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/lj6xtW3z0CEecnsPKstp/CzL6AGzcEG7H6XHoIDLI/ntVcqJNYe.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/a84bwyo0Mp6bAp2PNIRK/eU9bAv3251aSbskqIHQw/ovCH22Xmz.js": {"bytes": 6109, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "virtual:plus-icon-small": {"bytes": 3011, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/a84bwyo0Mp6bAp2PNIRK/eU9bAv3251aSbskqIHQw/ovCH22Xmz.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/a84bwyo0Mp6bAp2PNIRK/eU9bAv3251aSbskqIHQw/ovCH22Xmz.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "virtual:contact-card": {"bytes": 3055, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/kEfpoFmhRRcEXNcnsS3Z/sAaVlfOVnJwAGzz5oQ3a/pCa0ZyMgp.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/kEfpoFmhRRcEXNcnsS3Z/sAaVlfOVnJwAGzz5oQ3a/pCa0ZyMgp.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/KSWOGapdzc7mxJ6AkjqX/thTJaJ25WfWPZmEPEJeT/GKtOymhXV.js": {"bytes": 10924, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/1aeta7xc6zx0W5JNyFOV/r3RNaJt8KTXvhORSuRQp/nCQNaN8LD.js": {"bytes": 1151, "imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/oQDCvavtkx6jLdMjb7pO/6nTJTjO0zVyQDSbr1HPm/l0AhafeLr.js": {"bytes": 9990, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/cKGD16u2MGB7MfqfVXFp/wiztTCbXokZrMicHAmZc/Grain.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/cKGD16u2MGB7MfqfVXFp/wiztTCbXokZrMicHAmZc/Grain.js"}, {"path": "/:https://framerusercontent.com/modules/lRDHiNWNVWmE0lqtoVHP/7qT0r3So12155VV5Jq5x/Video.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/lRDHiNWNVWmE0lqtoVHP/7qT0r3So12155VV5Jq5x/Video.js"}], "format": "esm"}, "/:https://framerusercontent.com/modules/6bfz2672fNDwFlMNmD6D/Rcw6MtrajjM2drLODo28/pCa0ZyMgp.js": {"bytes": 16621, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/DmTAdEsxZYh2zCIyVcT8/81LNstvbVr8IlmhdenW7/IwCB7tJrN.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/DmTAdEsxZYh2zCIyVcT8/81LNstvbVr8IlmhdenW7/IwCB7tJrN.js"}], "format": "esm"}, "/:https://framerusercontent.com/modules/zw9OR6U2ucGcBK3TzK0e/LSxltdvoTFLfIzApY5HK/qBz_H6jdv.js": {"bytes": 9179, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framer.com/m/framer/icon-nullstate.js@0.7.0": {"bytes": 1084, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framer.com/m/phosphor-icons/House.js@0.0.57": {"bytes": 3435, "imports": [], "format": "esm"}, "/:https://framerusercontent.com/modules/Ma20hU0GGRxLxZphbywl/OSpwWF91FHPVFyQJjMHt/utils.js": {"bytes": 2469, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/tYScH7LTqUtz5KUaUAYP/p8dptk4UIND8hbFWz9V7/Phosphor.js": {"bytes": 25520, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framer.com/m/framer/icon-nullstate.js@0.7.0", "kind": "import-statement", "original": "https://framer.com/m/framer/icon-nullstate.js@0.7.0"}, {"path": "/:https://framer.com/m/phosphor-icons/House.js@0.0.57", "kind": "import-statement", "original": "https://framer.com/m/phosphor-icons/House.js@0.0.57"}, {"path": "/:https://framerusercontent.com/modules/Ma20hU0GGRxLxZphbywl/OSpwWF91FHPVFyQJjMHt/utils.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/Ma20hU0GGRxLxZphbywl/OSpwWF91FHPVFyQJjMHt/utils.js"}], "format": "esm"}, "/:https://framerusercontent.com/modules/jmz8JicKobTrI3XpEl8M/Ojn5Qm9Xr4RxhrDVvxcw/vTiEQWJOl.js": {"bytes": 12493, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/tYScH7LTqUtz5KUaUAYP/p8dptk4UIND8hbFWz9V7/Phosphor.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/tYScH7LTqUtz5KUaUAYP/p8dptk4UIND8hbFWz9V7/Phosphor.js"}, {"path": "/:https://framerusercontent.com/modules/94zGMcemTO1UzYqvT5pJ/FkQLg7r0T9A6zWOCSuGc/oFAZmwcVJ.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/94zGMcemTO1UzYqvT5pJ/FkQLg7r0T9A6zWOCSuGc/oFAZmwcVJ.js"}, {"path": "/:https://framerusercontent.com/modules/wRGRkNPIgZM4wuze3hOY/TARN61VU0EmPqQfPlH4Z/ypR5VEWEl.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/wRGRkNPIgZM4wuze3hOY/TARN61VU0EmPqQfPlH4Z/ypR5VEWEl.js"}], "format": "esm"}, "/:https://framerusercontent.com/modules/kFH0BlvE0NUW5ULtike1/JxLwaSV0InF8NIeGsDIf/ZsPpcvZL7.js": {"bytes": 18286, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/44urrVjRtyCNsT713Qdf/lVfX2LlSRSXVPq0OXNzK/q5ePSxRPK.js": {"bytes": 51797, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/EqoRljgeGyBgybgK0SIZ/vaxBUeUO9TAoYQif6Rpa/fDRzSjw63.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/EqoRljgeGyBgybgK0SIZ/vaxBUeUO9TAoYQif6Rpa/fDRzSjw63.js"}, {"path": "/:https://framerusercontent.com/modules/KSWOGapdzc7mxJ6AkjqX/thTJaJ25WfWPZmEPEJeT/GKtOymhXV.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/KSWOGapdzc7mxJ6AkjqX/thTJaJ25WfWPZmEPEJeT/GKtOymhXV.js"}, {"path": "/:https://framerusercontent.com/modules/1aeta7xc6zx0W5JNyFOV/r3RNaJt8KTXvhORSuRQp/nCQNaN8LD.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/1aeta7xc6zx0W5JNyFOV/r3RNaJt8KTXvhORSuRQp/nCQNaN8LD.js"}, {"path": "/:https://framerusercontent.com/modules/rL0tRCNiDNbTgXcw7l8h/MQ0zM3QkM5MEl876oJCh/pAxoS1kOX.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/rL0tRCNiDNbTgXcw7l8h/MQ0zM3QkM5MEl876oJCh/pAxoS1kOX.js"}, {"path": "/:https://framerusercontent.com/modules/vaUDP9yUvXDLDorewcXH/TU27PKlUxf7UEubboCQ4/svYtzYwMA.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/vaUDP9yUvXDLDorewcXH/TU27PKlUxf7UEubboCQ4/svYtzYwMA.js"}, {"path": "/:https://framerusercontent.com/modules/spPGp13l4XWgAIZFyxLC/emaf7oXZn50b4jG3PICH/zgy6bak25.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/spPGp13l4XWgAIZFyxLC/emaf7oXZn50b4jG3PICH/zgy6bak25.js"}, {"path": "/:https://framerusercontent.com/modules/oQDCvavtkx6jLdMjb7pO/6nTJTjO0zVyQDSbr1HPm/l0AhafeLr.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/oQDCvavtkx6jLdMjb7pO/6nTJTjO0zVyQDSbr1HPm/l0AhafeLr.js"}, {"path": "/:https://framerusercontent.com/modules/6bfz2672fNDwFlMNmD6D/Rcw6MtrajjM2drLODo28/pCa0ZyMgp.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/6bfz2672fNDwFlMNmD6D/Rcw6MtrajjM2drLODo28/pCa0ZyMgp.js"}, {"path": "/:https://framerusercontent.com/modules/zw9OR6U2ucGcBK3TzK0e/LSxltdvoTFLfIzApY5HK/qBz_H6jdv.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/zw9OR6U2ucGcBK3TzK0e/LSxltdvoTFLfIzApY5HK/qBz_H6jdv.js"}, {"path": "/:https://framerusercontent.com/modules/rih294o185a0kfkOE6g1/FBAfj3bDaX74m9ohqNmk/uAsrSsx9N.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/rih294o185a0kfkOE6g1/FBAfj3bDaX74m9ohqNmk/uAsrSsx9N.js"}, {"path": "/:https://framerusercontent.com/modules/jmz8JicKobTrI3XpEl8M/Ojn5Qm9Xr4RxhrDVvxcw/vTiEQWJOl.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/jmz8JicKobTrI3XpEl8M/Ojn5Qm9Xr4RxhrDVvxcw/vTiEQWJOl.js"}, {"path": "/:https://framerusercontent.com/modules/kFH0BlvE0NUW5ULtike1/JxLwaSV0InF8NIeGsDIf/ZsPpcvZL7.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/kFH0BlvE0NUW5ULtike1/JxLwaSV0InF8NIeGsDIf/ZsPpcvZL7.js"}], "format": "esm"}, "virtual:let-s-talk": {"bytes": 3076, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/44urrVjRtyCNsT713Qdf/lVfX2LlSRSXVPq0OXNzK/q5ePSxRPK.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/44urrVjRtyCNsT713Qdf/lVfX2LlSRSXVPq0OXNzK/q5ePSxRPK.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "virtual:logo": {"bytes": 3076, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/ikVcV6kvZDj6uMF5EU26/KDHrdhjIRTgeMUBLQTNL/qBz_H6jdv.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/ikVcV6kvZDj6uMF5EU26/KDHrdhjIRTgeMUBLQTNL/qBz_H6jdv.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/ggiAzj5S78TrxIvWWoMw/8KwDBIYbpseXDz20Cmq5/uAsrSsx9N.js": {"bytes": 5369, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "virtual:animated-line": {"bytes": 3055, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/ggiAzj5S78TrxIvWWoMw/8KwDBIYbpseXDz20Cmq5/uAsrSsx9N.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/ggiAzj5S78TrxIvWWoMw/8KwDBIYbpseXDz20Cmq5/uAsrSsx9N.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/zEo4GdvWJC14Pp4F1FlC/MBRPj4XNBFtQ3JDonsi9/uTEGFhvJJ.js": {"bytes": 21096, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/7LbtHc4qbG6Lt0rW66b4/qFIZAwipssfkc7WYZt6g/d5lvgh6Gd.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/7LbtHc4qbG6Lt0rW66b4/qFIZAwipssfkc7WYZt6g/d5lvgh6Gd.js"}], "format": "esm"}, "virtual:rating": {"bytes": 3011, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/zEo4GdvWJC14Pp4F1FlC/MBRPj4XNBFtQ3JDonsi9/uTEGFhvJJ.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/zEo4GdvWJC14Pp4F1FlC/MBRPj4XNBFtQ3JDonsi9/uTEGFhvJJ.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/qYAToHIuf9k41NVgtpE7/Nw8dm4Wk4ng8kkdcRuF6/vTiEQWJOl.js": {"bytes": 12493, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/tYScH7LTqUtz5KUaUAYP/p8dptk4UIND8hbFWz9V7/Phosphor.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/tYScH7LTqUtz5KUaUAYP/p8dptk4UIND8hbFWz9V7/Phosphor.js"}, {"path": "/:https://framerusercontent.com/modules/94zGMcemTO1UzYqvT5pJ/FkQLg7r0T9A6zWOCSuGc/oFAZmwcVJ.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/94zGMcemTO1UzYqvT5pJ/FkQLg7r0T9A6zWOCSuGc/oFAZmwcVJ.js"}, {"path": "/:https://framerusercontent.com/modules/wRGRkNPIgZM4wuze3hOY/TARN61VU0EmPqQfPlH4Z/ypR5VEWEl.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/wRGRkNPIgZM4wuze3hOY/TARN61VU0EmPqQfPlH4Z/ypR5VEWEl.js"}], "format": "esm"}, "virtual:contact-section-item": {"bytes": 3055, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/qYAToHIuf9k41NVgtpE7/Nw8dm4Wk4ng8kkdcRuF6/vTiEQWJOl.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/qYAToHIuf9k41NVgtpE7/Nw8dm4Wk4ng8kkdcRuF6/vTiEQWJOl.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/i5Hm4CBhDTKcuXKITWLU/ztxMjkl0ioCLIqrFvH74/w0aJmgNIP.js": {"bytes": 7064, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "virtual:social-icon-with-link": {"bytes": 3011, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/i5Hm4CBhDTKcuXKITWLU/ztxMjkl0ioCLIqrFvH74/w0aJmgNIP.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/i5Hm4CBhDTKcuXKITWLU/ztxMjkl0ioCLIqrFvH74/w0aJmgNIP.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/hIy76KeQ1GGfbMKr8D9B/yvtQunteujxxyITievV0/wmZEqp0yx.js": {"bytes": 10452, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "virtual:pre-loader": {"bytes": 3011, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/hIy76KeQ1GGfbMKr8D9B/yvtQunteujxxyITievV0/wmZEqp0yx.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/hIy76KeQ1GGfbMKr8D9B/yvtQunteujxxyITievV0/wmZEqp0yx.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "virtual:email": {"bytes": 3076, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/zYxxjr8GDqJairdZcDtp/v3Db4YTIvyVZ2ZxABpqF/xbeyrFlxc.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/zYxxjr8GDqJairdZcDtp/v3Db4YTIvyVZ2ZxABpqF/xbeyrFlxc.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "/:https://framerusercontent.com/modules/73s2XRS4okTTkcP9NVhE/uPO87sMavFRSA5KNmPsg/ysOIIzMbt.js": {"bytes": 12117, "imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/94zGMcemTO1UzYqvT5pJ/FkQLg7r0T9A6zWOCSuGc/oFAZmwcVJ.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/94zGMcemTO1UzYqvT5pJ/FkQLg7r0T9A6zWOCSuGc/oFAZmwcVJ.js"}, {"path": "/:https://framerusercontent.com/modules/vaUDP9yUvXDLDorewcXH/TU27PKlUxf7UEubboCQ4/svYtzYwMA.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/vaUDP9yUvXDLDorewcXH/TU27PKlUxf7UEubboCQ4/svYtzYwMA.js"}], "format": "esm"}, "virtual:photo-item": {"bytes": 3055, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "/:https://framerusercontent.com/modules/73s2XRS4okTTkcP9NVhE/uPO87sMavFRSA5KNmPsg/ysOIIzMbt.js", "kind": "import-statement", "original": "https://framerusercontent.com/modules/73s2XRS4okTTkcP9NVhE/uPO87sMavFRSA5KNmPsg/ysOIIzMbt.js"}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "format": "esm"}}, "outputs": {"animated-line.js": {"imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:animated-line", "inputs": {"virtual:animated-line": {"bytesInOutput": 2014}, "/:https://framerusercontent.com/modules/ggiAzj5S78TrxIvWWoMw/8KwDBIYbpseXDz20Cmq5/uAsrSsx9N.js": {"bytesInOutput": 4500}}, "bytes": 6722}, "rating.js": {"imports": [{"path": "chunks/chunk-XKETCZDK.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:rating", "inputs": {"virtual:rating": {"bytesInOutput": 1974}, "/:https://framerusercontent.com/modules/zEo4GdvWJC14Pp4F1FlC/MBRPj4XNBFtQ3JDonsi9/uTEGFhvJJ.js": {"bytesInOutput": 19843}}, "bytes": 22073}, "contact-section-item.js": {"imports": [{"path": "chunks/chunk-BTSBQWPZ.js", "kind": "import-statement"}, {"path": "chunks/chunk-5L2L6TVR.js", "kind": "import-statement"}, {"path": "chunks/chunk-SATCLWQE.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:contact-section-item", "inputs": {"virtual:contact-section-item": {"bytesInOutput": 2014}, "/:https://framerusercontent.com/modules/qYAToHIuf9k41NVgtpE7/Nw8dm4Wk4ng8kkdcRuF6/vTiEQWJOl.js": {"bytesInOutput": 11150}}, "bytes": 13619}, "social-icon-with-link.js": {"imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:social-icon-with-link", "inputs": {"virtual:social-icon-with-link": {"bytesInOutput": 1970}, "/:https://framerusercontent.com/modules/i5Hm4CBhDTKcuXKITWLU/ztxMjkl0ioCLIqrFvH74/w0aJmgNIP.js": {"bytesInOutput": 6127}}, "bytes": 8321}, "pre-loader.js": {"imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:pre-loader", "inputs": {"virtual:pre-loader": {"bytesInOutput": 1970}, "/:https://framerusercontent.com/modules/hIy76KeQ1GGfbMKr8D9B/yvtQunteujxxyITievV0/wmZEqp0yx.js": {"bytesInOutput": 9560}}, "bytes": 11732}, "email.js": {"imports": [{"path": "chunks/chunk-CUJMJG57.js", "kind": "import-statement"}, {"path": "chunks/chunk-3LMAB7ZO.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:email", "inputs": {"virtual:email": {"bytesInOutput": 2022}}, "bytes": 2196}, "photo-item.js": {"imports": [{"path": "chunks/chunk-5L2L6TVR.js", "kind": "import-statement"}, {"path": "chunks/chunk-VY5WWL2S.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:photo-item", "inputs": {"virtual:photo-item": {"bytesInOutput": 2014}, "/:https://framerusercontent.com/modules/73s2XRS4okTTkcP9NVhE/uPO87sMavFRSA5KNmPsg/ysOIIzMbt.js": {"bytesInOutput": 10920}}, "bytes": 13316}, "feature-item.js": {"imports": [{"path": "chunks/chunk-3LMAB7ZO.js", "kind": "import-statement"}, {"path": "chunks/chunk-YWUWNR35.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:feature-item", "inputs": {"virtual:feature-item": {"bytesInOutput": 1974}, "/:https://framerusercontent.com/modules/zvBvxOn4yypid4okdhJh/B64S9zpOfU1k6qA6wRNR/kO8bO08Ia.js": {"bytesInOutput": 7271}}, "bytes": 9587}, "plan-price-toggle.js": {"imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:plan-price-toggle", "inputs": {"virtual:plan-price-toggle": {"bytesInOutput": 1993}, "/:https://framerusercontent.com/modules/rtWkvH5fSSlZDlo2o6ww/HnHVN87wcIU64TRg16LZ/kiJu4DQjn.js": {"bytesInOutput": 8926}}, "bytes": 11135}, "gradient-background.js": {"imports": [{"path": "chunks/chunk-ZT4GUZK4.js", "kind": "import-statement"}, {"path": "chunks/chunk-G73PZF75.js", "kind": "import-statement"}, {"path": "chunks/chunk-MO4ELLBD.js", "kind": "import-statement"}, {"path": "chunks/chunk-MKEJQGYO.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:gradient-background", "inputs": {"virtual:gradient-background": {"bytesInOutput": 2001}}, "bytes": 2263}, "projects.js": {"imports": [{"path": "chunks/chunk-NEDE7LSE.js", "kind": "import-statement"}, {"path": "chunks/chunk-5L2L6TVR.js", "kind": "import-statement"}, {"path": "chunks/chunk-QOI7VI3Y.js", "kind": "import-statement"}, {"path": "chunks/chunk-F5ZOKVUG.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:projects", "inputs": {"virtual:projects": {"bytesInOutput": 1974}, "/:https://framerusercontent.com/modules/lj6xtW3z0CEecnsPKstp/CzL6AGzcEG7H6XHoIDLI/ntVcqJNYe.js": {"bytesInOutput": 17240}, "/:https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js": {"bytesInOutput": 13259}, "/:https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub-0.js": {"bytesInOutput": 68394}, "/:https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub-1.js": {"bytesInOutput": 102}, "/:https://framerusercontent.com/modules/xqMlz63qKzLJKZQseE5v/5e05xMkHRisZbX7kOPJG/qNTzVcuJS.js": {"bytesInOutput": 1162}, "/:https://framerusercontent.com/modules/xqMlz63qKzLJKZQseE5v/5e05xMkHRisZbX7kOPJG/qNTzVcuJS-0.js": {"bytesInOutput": 25028}, "/:https://framerusercontent.com/modules/xqMlz63qKzLJKZQseE5v/5e05xMkHRisZbX7kOPJG/qNTzVcuJS-1.js": {"bytesInOutput": 103}}, "bytes": 128638}, "plus-icon-small.js": {"imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:plus-icon-small", "inputs": {"virtual:plus-icon-small": {"bytesInOutput": 1970}, "/:https://framerusercontent.com/modules/a84bwyo0Mp6bAp2PNIRK/eU9bAv3251aSbskqIHQw/ovCH22Xmz.js": {"bytesInOutput": 5050}}, "bytes": 7232}, "contact-card.js": {"imports": [{"path": "chunks/chunk-25SFVEMK.js", "kind": "import-statement"}, {"path": "chunks/chunk-CVJIPDTS.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:contact-card", "inputs": {"virtual:contact-card": {"bytesInOutput": 2001}}, "bytes": 2182}, "let-s-talk.js": {"imports": [{"path": "chunks/chunk-BTSBQWPZ.js", "kind": "import-statement"}, {"path": "chunks/chunk-6MRKUUEA.js", "kind": "import-statement"}, {"path": "chunks/chunk-5L2L6TVR.js", "kind": "import-statement"}, {"path": "chunks/chunk-BPUMIZRX.js", "kind": "import-statement"}, {"path": "chunks/chunk-FGPXEFVT.js", "kind": "import-statement"}, {"path": "chunks/chunk-CVJIPDTS.js", "kind": "import-statement"}, {"path": "chunks/chunk-SATCLWQE.js", "kind": "import-statement"}, {"path": "chunks/chunk-G73PZF75.js", "kind": "import-statement"}, {"path": "chunks/chunk-MO4ELLBD.js", "kind": "import-statement"}, {"path": "chunks/chunk-MKEJQGYO.js", "kind": "import-statement"}, {"path": "chunks/chunk-45SLURIC.js", "kind": "import-statement"}, {"path": "chunks/chunk-VY5WWL2S.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:let-s-talk", "inputs": {"virtual:let-s-talk": {"bytesInOutput": 2039}, "/:https://framerusercontent.com/modules/44urrVjRtyCNsT713Qdf/lVfX2LlSRSXVPq0OXNzK/q5ePSxRPK.js": {"bytesInOutput": 49803}, "/:https://framerusercontent.com/modules/KSWOGapdzc7mxJ6AkjqX/thTJaJ25WfWPZmEPEJeT/GKtOymhXV.js": {"bytesInOutput": 10440}, "/:https://framerusercontent.com/modules/1aeta7xc6zx0W5JNyFOV/r3RNaJt8KTXvhORSuRQp/nCQNaN8LD.js": {"bytesInOutput": 682}, "/:https://framerusercontent.com/modules/oQDCvavtkx6jLdMjb7pO/6nTJTjO0zVyQDSbr1HPm/l0AhafeLr.js": {"bytesInOutput": 8854}, "/:https://framerusercontent.com/modules/6bfz2672fNDwFlMNmD6D/Rcw6MtrajjM2drLODo28/pCa0ZyMgp.js": {"bytesInOutput": 15845}, "/:https://framerusercontent.com/modules/zw9OR6U2ucGcBK3TzK0e/LSxltdvoTFLfIzApY5HK/qBz_H6jdv.js": {"bytesInOutput": 8610}, "/:https://framerusercontent.com/modules/jmz8JicKobTrI3XpEl8M/Ojn5Qm9Xr4RxhrDVvxcw/vTiEQWJOl.js": {"bytesInOutput": 11571}, "/:https://framerusercontent.com/modules/kFH0BlvE0NUW5ULtike1/JxLwaSV0InF8NIeGsDIf/ZsPpcvZL7.js": {"bytesInOutput": 17557}}, "bytes": 127339}, "chunks/chunk-BTSBQWPZ.js": {"imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}], "exports": ["Icon"], "inputs": {"/:https://framerusercontent.com/modules/tYScH7LTqUtz5KUaUAYP/p8dptk4UIND8hbFWz9V7/Phosphor.js": {"bytesInOutput": 24694}, "/:https://framer.com/m/framer/icon-nullstate.js@0.7.0": {"bytesInOutput": 567}, "/:https://framer.com/m/phosphor-icons/House.js@0.0.57": {"bytesInOutput": 3102}, "/:https://framerusercontent.com/modules/Ma20hU0GGRxLxZphbywl/OSpwWF91FHPVFyQJjMHt/utils.js": {"bytesInOutput": 1125}}, "bytes": 29923}, "logo.js": {"imports": [{"path": "chunks/chunk-NZSA2AZS.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:logo", "inputs": {"virtual:logo": {"bytesInOutput": 2022}}, "bytes": 2158}, "small-link.js": {"imports": [{"path": "chunks/chunk-SIWCYXHC.js", "kind": "import-statement"}, {"path": "chunks/chunk-VY5WWL2S.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:small-link", "inputs": {"virtual:small-link": {"bytesInOutput": 2014}, "/:https://framerusercontent.com/modules/bS1D3ZEoaKmqk9Wy5pSI/DwfWvtC2vUITQYbUeZZI/dtX3pmnQt.js": {"bytesInOutput": 18111}}, "bytes": 20507}, "large-button.js": {"imports": [{"path": "chunks/chunk-32JJPPLR.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:large-button", "inputs": {"virtual:large-button": {"bytesInOutput": 2001}}, "bytes": 2145}, "project-card.js": {"imports": [{"path": "chunks/chunk-NEDE7LSE.js", "kind": "import-statement"}, {"path": "chunks/chunk-5L2L6TVR.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:project-card", "inputs": {"virtual:project-card": {"bytesInOutput": 2001}}, "bytes": 2182}, "chunks/chunk-NEDE7LSE.js": {"imports": [{"path": "chunks/chunk-5L2L6TVR.js", "kind": "import-statement"}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}], "exports": ["stdin_default"], "inputs": {"/:https://framerusercontent.com/modules/hWxVS3apPj1PnFxNHjIQ/nspOabGpGjvj61gMbFbA/eKMoUoN9m.js": {"bytesInOutput": 26329}, "/:https://framerusercontent.com/modules/CBN86zQXoOXpcgKhNtfI/h1KCmCnIrIg9HM8rHLCH/qNTzVcuJS.js": {"bytesInOutput": 1132}, "/:https://framerusercontent.com/modules/CBN86zQXoOXpcgKhNtfI/h1KCmCnIrIg9HM8rHLCH/qNTzVcuJS-0.js": {"bytesInOutput": 24293}, "/:https://framerusercontent.com/modules/CBN86zQXoOXpcgKhNtfI/h1KCmCnIrIg9HM8rHLCH/qNTzVcuJS-1.js": {"bytesInOutput": 100}}, "bytes": 52566}, "menu-item.js": {"imports": [{"path": "chunks/chunk-63EFHLNF.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:menu-item", "inputs": {"virtual:menu-item": {"bytesInOutput": 1957}}, "bytes": 2098}, "article-card-l.js": {"imports": [{"path": "chunks/chunk-6MRKUUEA.js", "kind": "import-statement"}, {"path": "chunks/chunk-SATCLWQE.js", "kind": "import-statement"}, {"path": "chunks/chunk-VY5WWL2S.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:article-card-l", "inputs": {"virtual:article-card-l": {"bytesInOutput": 2014}, "/:https://framerusercontent.com/modules/wb4wdricYk6hX5QoFCY0/EzLNKPFRUFRrupUhGwbG/fOV3xrRss.js": {"bytesInOutput": 14183}}, "bytes": 16693}, "chunks/chunk-6MRKUUEA.js": {"imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "exports": ["className", "css", "fonts"], "inputs": {"/:https://framerusercontent.com/modules/spPGp13l4XWgAIZFyxLC/emaf7oXZn50b4jG3PICH/zgy6bak25.js": {"bytesInOutput": 10277}}, "bytes": 10416}, "testimonial.js": {"imports": [{"path": "chunks/chunk-XKETCZDK.js", "kind": "import-statement"}, {"path": "chunks/chunk-5L2L6TVR.js", "kind": "import-statement"}, {"path": "chunks/chunk-YNN2WKUD.js", "kind": "import-statement"}, {"path": "chunks/chunk-VY5WWL2S.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:testimonial", "inputs": {"virtual:testimonial": {"bytesInOutput": 2018}, "/:https://framerusercontent.com/modules/t9kmVKgwV1DLW0GG8nPN/piyVQ3r6E2NxsJQlQ3Pt/fTmrIoq5f.js": {"bytesInOutput": 21829}, "/:https://framerusercontent.com/modules/9SnJWnLLsQu1WYKv4TtR/hhvSb1XFv2UspFIa9bFx/fN1_sGlJp.js": {"bytesInOutput": 10276}}, "bytes": 34847}, "chunks/chunk-XKETCZDK.js": {"imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "exports": ["stdin_default"], "inputs": {"/:https://framerusercontent.com/modules/7LbtHc4qbG6Lt0rW66b4/qFIZAwipssfkc7WYZt6g/d5lvgh6Gd.js": {"bytesInOutput": 6429}}, "bytes": 6556}, "chunks/chunk-5L2L6TVR.js": {"imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "exports": ["className", "css", "fonts"], "inputs": {"/:https://framerusercontent.com/modules/94zGMcemTO1UzYqvT5pJ/FkQLg7r0T9A6zWOCSuGc/oFAZmwcVJ.js": {"bytesInOutput": 10574}}, "bytes": 10713}, "logo-card.js": {"imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:logo-card", "inputs": {"virtual:logo-card": {"bytesInOutput": 2035}, "/:https://framerusercontent.com/modules/yRDItg6CGqm47W2hBzp5/IzT1L6BlcymqiRv450Eg/h_AuDmjef.js": {"bytesInOutput": 10888}}, "bytes": 13123}, "menu-item-large.js": {"imports": [{"path": "chunks/chunk-QM54KZT3.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:menu-item-large", "inputs": {"virtual:menu-item-large": {"bytesInOutput": 2022}}, "bytes": 2169}, "logo-card-small.js": {"imports": [{"path": "chunks/chunk-WUWH5KEE.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:logo-card-small", "inputs": {"virtual:logo-card-small": {"bytesInOutput": 1980}}, "bytes": 2127}, "hamburger.js": {"imports": [{"path": "chunks/chunk-3IUEC7EM.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:hamburger", "inputs": {"virtual:hamburger": {"bytesInOutput": 1980}}, "bytes": 2121}, "video.js": {"imports": [{"path": "chunks/chunk-B6WKPYK7.js", "kind": "import-statement"}, {"path": "chunks/chunk-MO4ELLBD.js", "kind": "import-statement"}, {"path": "chunks/chunk-MKEJQGYO.js", "kind": "import-statement"}, {"path": "chunks/chunk-YWUWNR35.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:video", "inputs": {"virtual:video": {"bytesInOutput": 2018}, "/:https://framerusercontent.com/modules/qjgWXMzvxaNU7gU0HfTs/67qbZo3yAQzhksxoJAxc/c3jVHY4cP.js": {"bytesInOutput": 11477}, "/:https://framerusercontent.com/modules/Ik27nyqmPbQ4RLC0V1Hw/09nrParnXLeO68YEmFcO/Nr9N73Jiy.js": {"bytesInOutput": 12739}}, "bytes": 26895}, "intro.js": {"imports": [{"path": "chunks/chunk-CHS3IKS5.js", "kind": "import-statement"}, {"path": "chunks/chunk-3LMAB7ZO.js", "kind": "import-statement"}, {"path": "chunks/chunk-YWUWNR35.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:intro", "inputs": {"virtual:intro": {"bytesInOutput": 2001}}, "bytes": 2212}, "category.js": {"imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:category", "inputs": {"virtual:category": {"bytesInOutput": 1970}, "/:https://framerusercontent.com/modules/oDdmbFQXleEahNoGjmdo/WFPfNC3kP0LlLTNM21nD/d4er_GPVG.js": {"bytesInOutput": 6582}}, "bytes": 8750}, "rating-stars.js": {"imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:rating-stars", "inputs": {"virtual:rating-stars": {"bytesInOutput": 1970}, "/:https://framerusercontent.com/modules/t1iJuWnZpK9UlGn9LTZR/MmGYJmvrUwcTK5x20ctC/d5lvgh6Gd.js": {"bytesInOutput": 6429}}, "bytes": 8605}, "banner.js": {"imports": [{"path": "chunks/chunk-BAOIGPCO.js", "kind": "import-statement"}, {"path": "chunks/chunk-CVJIPDTS.js", "kind": "import-statement"}, {"path": "chunks/chunk-3LMAB7ZO.js", "kind": "import-statement"}, {"path": "chunks/chunk-YWUWNR35.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:banner", "inputs": {"virtual:banner": {"bytesInOutput": 1980}}, "bytes": 2229}, "built-in.js": {"imports": [{"path": "chunks/chunk-SATCLWQE.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:built-in", "inputs": {"virtual:built-in": {"bytesInOutput": 1970}, "/:https://framerusercontent.com/modules/sKy2U5CCL7dNlNvddsNT/jduSarGkVeMtg4GIVij9/do0Th5ePX.js": {"bytesInOutput": 9501}}, "bytes": 11743}, "input.js": {"imports": [{"path": "chunks/chunk-BPUMIZRX.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:input", "inputs": {"virtual:input": {"bytesInOutput": 1997}, "/:https://framerusercontent.com/modules/tqZ3GHXWs78LlBwshtxr/85NY0AbZlKayuQGPTus9/TWOB0A5WT.js": {"bytesInOutput": 9258}}, "bytes": 11509}, "chunks/chunk-BPUMIZRX.js": {"imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "exports": ["stdin_default"], "inputs": {"/:https://framerusercontent.com/modules/rih294o185a0kfkOE6g1/FBAfj3bDaX74m9ohqNmk/uAsrSsx9N.js": {"bytesInOutput": 4500}}, "bytes": 4627}, "more-articles.js": {"imports": [{"path": "chunks/chunk-SIWCYXHC.js", "kind": "import-statement"}, {"path": "chunks/chunk-VY5WWL2S.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:more-articles", "inputs": {"virtual:more-articles": {"bytesInOutput": 2014}, "/:https://framerusercontent.com/modules/s05IbLhj72TZhduOAuNj/weAk3ePS4spt3ofDeb8O/Uqu9MXhEM.js": {"bytesInOutput": 13868}}, "bytes": 16270}, "hero.js": {"imports": [{"path": "chunks/chunk-QOI7VI3Y.js", "kind": "import-statement"}, {"path": "chunks/chunk-25SFVEMK.js", "kind": "import-statement"}, {"path": "chunks/chunk-WUWH5KEE.js", "kind": "import-statement"}, {"path": "chunks/chunk-IWIJOZKQ.js", "kind": "import-statement"}, {"path": "chunks/chunk-CVJIPDTS.js", "kind": "import-statement"}, {"path": "chunks/chunk-5GTX57J2.js", "kind": "import-statement"}, {"path": "chunks/chunk-G73PZF75.js", "kind": "import-statement"}, {"path": "chunks/chunk-MO4ELLBD.js", "kind": "import-statement"}, {"path": "chunks/chunk-CHS3IKS5.js", "kind": "import-statement"}, {"path": "chunks/chunk-MKEJQGYO.js", "kind": "import-statement"}, {"path": "chunks/chunk-D6DQ2HQ2.js", "kind": "import-statement"}, {"path": "chunks/chunk-3LMAB7ZO.js", "kind": "import-statement"}, {"path": "chunks/chunk-YWUWNR35.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:hero", "inputs": {"virtual:hero": {"bytesInOutput": 1974}, "/:https://framerusercontent.com/modules/YlnZT9T2TSVRxOqTj1Ks/l0iC658Wkj8ymMCsLgoA/W6mI3CngM.js": {"bytesInOutput": 46696}, "/:https://framerusercontent.com/modules/2mMO1EX5xNxIoJZ10MHC/mseA4evZ2eHB8ECkN8B1/oFAZmwcVJ.js": {"bytesInOutput": 10577}}, "bytes": 60479}, "chunks/chunk-QOI7VI3Y.js": {"imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "exports": ["className", "css", "fonts"], "inputs": {"/:https://framerusercontent.com/modules/4LbD9wvqiBYHJaqgRQoc/IQ2UCrsHU3U8aO1BfNuX/gd6AWaps9.js": {"bytesInOutput": 10426}}, "bytes": 10565}, "chunks/chunk-25SFVEMK.js": {"imports": [{"path": "chunks/chunk-CVJIPDTS.js", "kind": "import-statement"}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "exports": ["stdin_default"], "inputs": {"/:https://framerusercontent.com/modules/kEfpoFmhRRcEXNcnsS3Z/sAaVlfOVnJwAGzz5oQ3a/pCa0ZyMgp.js": {"bytesInOutput": 15392}}, "bytes": 15593}, "chunks/chunk-WUWH5KEE.js": {"imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "exports": ["stdin_default"], "inputs": {"/:https://framerusercontent.com/modules/4F8nL6jCU5uHNUN0NA7K/2nXsZbgoEaoTaYPV76xl/a_JFqsPzN.js": {"bytesInOutput": 6112}}, "bytes": 6239}, "option.js": {"imports": [{"path": "chunks/chunk-FGPXEFVT.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:option", "inputs": {"virtual:option": {"bytesInOutput": 1993}, "/:https://framerusercontent.com/modules/3akaxm8QHD9JaLKaBxgT/VqNqXMEUsuM4OfhwOFSY/WhaJzx6A3.js": {"bytesInOutput": 8360}}, "bytes": 10621}, "chunks/chunk-FGPXEFVT.js": {"imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "exports": ["className", "css", "fonts"], "inputs": {"/:https://framerusercontent.com/modules/EqoRljgeGyBgybgK0SIZ/vaxBUeUO9TAoYQif6Rpa/fDRzSjw63.js": {"bytesInOutput": 10270}}, "bytes": 10409}, "advantages.js": {"imports": [{"path": "chunks/chunk-BAOIGPCO.js", "kind": "import-statement"}, {"path": "chunks/chunk-IWIJOZKQ.js", "kind": "import-statement"}, {"path": "chunks/chunk-CVJIPDTS.js", "kind": "import-statement"}, {"path": "chunks/chunk-CHS3IKS5.js", "kind": "import-statement"}, {"path": "chunks/chunk-3LMAB7ZO.js", "kind": "import-statement"}, {"path": "chunks/chunk-77A4MMPH.js", "kind": "import-statement"}, {"path": "chunks/chunk-PWMJ5SHX.js", "kind": "import-statement"}, {"path": "chunks/chunk-YWUWNR35.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:advantages", "inputs": {"virtual:advantages": {"bytesInOutput": 1974}, "/:https://framerusercontent.com/modules/Evvpdb11LprA8hF4wwQ2/YUjEKblsUHoIv1sTKJxL/XMbv3HI5C.js": {"bytesInOutput": 16481}, "/:https://framerusercontent.com/modules/p1bBWLZwVrvHXuWlH4xx/n9OidViwumXepK7AzYdF/nCOD2Sdie.js": {"bytesInOutput": 10283}}, "bytes": 29582}, "chunks/chunk-BAOIGPCO.js": {"imports": [{"path": "chunks/chunk-CVJIPDTS.js", "kind": "import-statement"}, {"path": "chunks/chunk-3LMAB7ZO.js", "kind": "import-statement"}, {"path": "chunks/chunk-YWUWNR35.js", "kind": "import-statement"}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "exports": ["stdin_default"], "inputs": {"/:https://framerusercontent.com/modules/mywbaSp9mF6R83DCrxI4/0MJDIxKkII1tq8lANCiM/dHpxp5ikb.js": {"bytesInOutput": 14965}}, "bytes": 15306}, "chunks/chunk-IWIJOZKQ.js": {"imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "exports": ["className", "css", "fonts"], "inputs": {"/:https://framerusercontent.com/modules/nAMdoVC0qVfhdkrWextH/DrBJh5fm1Ck4aywSZe9y/HLpRTFhim.js": {"bytesInOutput": 10276}}, "bytes": 10415}, "chunks/chunk-CVJIPDTS.js": {"imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "exports": ["stdin_default"], "inputs": {"/:https://framerusercontent.com/modules/DmTAdEsxZYh2zCIyVcT8/81LNstvbVr8IlmhdenW7/IwCB7tJrN.js": {"bytesInOutput": 10843}}, "bytes": 10970}, "ticker.js": {"imports": [{"path": "chunks/chunk-MKEJQGYO.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:ticker", "inputs": {"virtual:ticker": {"bytesInOutput": 1974}, "/:https://framerusercontent.com/modules/pRdtH3H3SzHXlVcu6fJq/gtdA974yag36O5Cqwsfp/YYl7PDyYl.js": {"bytesInOutput": 8932}, "/:https://framerusercontent.com/modules/B2xAlJLcN0gOnt11mSPw/jyRNgY7vYWXe6t31T0wo/Ticker.js": {"bytesInOutput": 12201}, "/:https://esm.sh/*@motionone/animation@10.18.0/node/animation.mjs": {"bytesInOutput": 3349}, "/:https://esm.sh/*@motionone/animation": {"bytesInOutput": 0}, "/:https://esm.sh/*hey-listen@1.0.8/node/hey-listen.mjs": {"bytesInOutput": 24}, "/:https://esm.sh/*hey-listen": {"bytesInOutput": 0}, "/:https://esm.sh/*@motionone/types@10.17.1/node/types.mjs": {"bytesInOutput": 209}, "/:https://esm.sh/*@motionone/types": {"bytesInOutput": 0}, "/:https://esm.sh/*tslib@2.8.1/node/tslib.mjs": {"bytesInOutput": 407}, "/:https://esm.sh/*tslib": {"bytesInOutput": 0}, "/:https://esm.sh/*@motionone/generators@10.18.0/node/generators.mjs": {"bytesInOutput": 2794}, "/:https://esm.sh/*@motionone/generators": {"bytesInOutput": 0}, "/:https://esm.sh/*@motionone/dom@10.18.0/node/dom.mjs": {"bytesInOutput": 12967}, "/:https://esm.sh/*@motionone/dom": {"bytesInOutput": 0}, "/:https://framerusercontent.com/modules/JXipYeVE7fx0Gx9lfC1o/eEWwYAGd9qCIC36YeNjR/a_JFqsPzN.js": {"bytesInOutput": 6192}}, "bytes": 50129}, "large-button-submit.js": {"imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:large-button-submit", "inputs": {"virtual:large-button-submit": {"bytesInOutput": 1993}, "/:https://framerusercontent.com/modules/jwmEWI6XYLAo8iFW1jdT/yq8KXx0rmwm7kswAk0oY/ZsPpcvZL7.js": {"bytesInOutput": 17180}}, "bytes": 19393}, "article-card.js": {"imports": [{"path": "chunks/chunk-SATCLWQE.js", "kind": "import-statement"}, {"path": "chunks/chunk-3LMAB7ZO.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:article-card", "inputs": {"virtual:article-card": {"bytesInOutput": 2039}, "/:https://framerusercontent.com/modules/AoMRsMghfrPsFoStNO5j/P8G2i4Dy8PwIpLcRzeNX/ZwY2WTzsN.js": {"bytesInOutput": 17576}}, "bytes": 19957}, "chunks/chunk-SATCLWQE.js": {"imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "exports": ["className", "css", "fonts"], "inputs": {"/:https://framerusercontent.com/modules/wRGRkNPIgZM4wuze3hOY/TARN61VU0EmPqQfPlH4Z/ypR5VEWEl.js": {"bytesInOutput": 10426}}, "bytes": 10565}, "play.js": {"imports": [{"path": "chunks/chunk-B6WKPYK7.js", "kind": "import-statement"}, {"path": "chunks/chunk-YWUWNR35.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:play", "inputs": {"virtual:play": {"bytesInOutput": 2014}, "/:https://framerusercontent.com/modules/3yFyjk27PFRLgex6ZBBN/YmlGWK0e2pR5xkpCKQ4I/Nr9N73Jiy.js": {"bytesInOutput": 12739}}, "bytes": 15123}, "chunks/chunk-B6WKPYK7.js": {"imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "exports": ["className", "css", "fonts"], "inputs": {"/:https://framerusercontent.com/modules/WO8qAR7NTyqVujiY1SK9/NoZGyMMtboxvkFUZ0EMH/LyKOtaXoC.js": {"bytesInOutput": 10421}}, "bytes": 10560}, "faq.js": {"imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "styled-components", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:faq", "inputs": {"virtual:faq": {"bytesInOutput": 2014}, "/:https://framerusercontent.com/modules/jS7H9bgOZwv6l2bEyd11/5zqVDvjTY2RPXtEbQSj5/OBpl5Xksq.js": {"bytesInOutput": 10451}, "/:https://framerusercontent.com/modules/BeoOkMASwMSx2anCpheJ/BfQd4QDD6UWpgHx1ZG0v/CustomAccordion.js": {"bytesInOutput": 15939}}, "bytes": 28796}, "small-button-submit.js": {"imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:small-button-submit", "inputs": {"virtual:small-button-submit": {"bytesInOutput": 1993}, "/:https://framerusercontent.com/modules/3oGNt9rERhKQntdKF0qc/4dTqq4IM1tz2qInyxUiv/P34Gbmd7R.js": {"bytesInOutput": 16518}}, "bytes": 18731}, "created-by.js": {"imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:created-by", "inputs": {"virtual:created-by": {"bytesInOutput": 1970}, "/:https://framerusercontent.com/modules/N10U22jxXY23ZYp8w0mb/0FQySw5R2wgCyRX1rvuq/PJOVhWHcj.js": {"bytesInOutput": 13445}}, "bytes": 15617}, "steps.js": {"imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:steps", "inputs": {"virtual:steps": {"bytesInOutput": 1970}, "/:https://framerusercontent.com/modules/pa1lStjkdNyFVnLJM7LW/M4yFLBqPxO3ZhHYTWaZz/PSo3NS0KW.js": {"bytesInOutput": 8096}}, "bytes": 10258}, "table.js": {"imports": [{"path": "chunks/chunk-X65FQ74X.js", "kind": "import-statement"}, {"path": "chunks/chunk-NFATBK6V.js", "kind": "import-statement"}, {"path": "chunks/chunk-45SLURIC.js", "kind": "import-statement"}, {"path": "chunks/chunk-YNN2WKUD.js", "kind": "import-statement"}, {"path": "chunks/chunk-SIWCYXHC.js", "kind": "import-statement"}, {"path": "chunks/chunk-VY5WWL2S.js", "kind": "import-statement"}, {"path": "chunks/chunk-YWUWNR35.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:table", "inputs": {"virtual:table": {"bytesInOutput": 2022}}, "bytes": 2381}, "number-item.js": {"imports": [{"path": "chunks/chunk-PWMJ5SHX.js", "kind": "import-statement"}, {"path": "chunks/chunk-YWUWNR35.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:number-item", "inputs": {"virtual:number-item": {"bytesInOutput": 2035}, "/:https://framerusercontent.com/modules/cRPBb24hNrEYtkpZDWdW/i0M2yCuTPdw4utNeEQfN/QDgEnH9Gf.js": {"bytesInOutput": 13582}}, "bytes": 15951}, "link.js": {"imports": [{"path": "chunks/chunk-SIWCYXHC.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:link", "inputs": {"virtual:link": {"bytesInOutput": 1970}, "/:https://framerusercontent.com/modules/bdNZipKw8szZq7vrsxYX/RZ6NlpCWyRw8Z1RL7y6y/RfhQK9Aec.js": {"bytesInOutput": 9054}}, "bytes": 11288}, "get-template-button.js": {"imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:get-template-button", "inputs": {"virtual:get-template-button": {"bytesInOutput": 1970}, "/:https://framerusercontent.com/modules/A8glwj2nHygG7Ntk0hyl/B6kwjp0eqCTnq3NK2QmC/AgkVqOuxm.js": {"bytesInOutput": 14738}}, "bytes": 16928}, "plus-icon.js": {"imports": [{"path": "chunks/chunk-5GTX57J2.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:plus-icon", "inputs": {"virtual:plus-icon": {"bytesInOutput": 1957}}, "bytes": 2098}, "chunks/chunk-5GTX57J2.js": {"imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "exports": ["stdin_default"], "inputs": {"/:https://framerusercontent.com/modules/g2cnji7Jaa30imfz7vPz/Yt1e07acnMaFp9CZYfKH/BvyOYDKED.js": {"bytesInOutput": 4105}}, "bytes": 4232}, "services.js": {"imports": [{"path": "chunks/chunk-ZT4GUZK4.js", "kind": "import-statement"}, {"path": "chunks/chunk-F5ZOKVUG.js", "kind": "import-statement"}, {"path": "chunks/chunk-32JJPPLR.js", "kind": "import-statement"}, {"path": "chunks/chunk-G73PZF75.js", "kind": "import-statement"}, {"path": "chunks/chunk-MO4ELLBD.js", "kind": "import-statement"}, {"path": "chunks/chunk-CHS3IKS5.js", "kind": "import-statement"}, {"path": "chunks/chunk-MKEJQGYO.js", "kind": "import-statement"}, {"path": "chunks/chunk-X65FQ74X.js", "kind": "import-statement"}, {"path": "chunks/chunk-NFATBK6V.js", "kind": "import-statement"}, {"path": "chunks/chunk-45SLURIC.js", "kind": "import-statement"}, {"path": "chunks/chunk-YNN2WKUD.js", "kind": "import-statement"}, {"path": "chunks/chunk-SIWCYXHC.js", "kind": "import-statement"}, {"path": "chunks/chunk-VY5WWL2S.js", "kind": "import-statement"}, {"path": "chunks/chunk-3LMAB7ZO.js", "kind": "import-statement"}, {"path": "chunks/chunk-YWUWNR35.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:services", "inputs": {"virtual:services": {"bytesInOutput": 1974}, "/:https://framerusercontent.com/modules/hURpBmt4DoL1ekI04Bxb/9v8HRjwLCA1gvmbMoIE2/CiB8L1GbI.js": {"bytesInOutput": 15721}}, "bytes": 18639}, "chunks/chunk-ZT4GUZK4.js": {"imports": [{"path": "chunks/chunk-G73PZF75.js", "kind": "import-statement"}, {"path": "chunks/chunk-MO4ELLBD.js", "kind": "import-statement"}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "exports": ["stdin_default"], "inputs": {"/:https://framerusercontent.com/modules/dhWVSMV5UF1XiHz4xBG6/MYBJX3CHugamesc7Y6hI/l0AhafeLr.js": {"bytesInOutput": 8838}}, "bytes": 9060}, "chunks/chunk-F5ZOKVUG.js": {"imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "exports": ["className", "css", "fonts"], "inputs": {"/:https://framerusercontent.com/modules/fFLayKOd66X1viGh3w4y/R1pR6B4IpAhcuocyXjHX/GKtOymhXV.js": {"bytesInOutput": 10437}}, "bytes": 10576}, "chunks/chunk-32JJPPLR.js": {"imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "exports": ["stdin_default"], "inputs": {"/:https://framerusercontent.com/modules/mnjgJFRPj416r8pcvyDl/xwOi5nzd2tsTvl7sVclV/eDUVCTTXq.js": {"bytesInOutput": 12065}}, "bytes": 12192}, "chunks/chunk-G73PZF75.js": {"imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "exports": ["Video"], "inputs": {"/:https://framerusercontent.com/modules/lRDHiNWNVWmE0lqtoVHP/7qT0r3So12155VV5Jq5x/Video.js": {"bytesInOutput": 12142}, "/:https://framerusercontent.com/modules/VTUDdizacRHpwbkOamr7/AykinQJbgwl92LvMGZwu/constants.js": {"bytesInOutput": 1402}, "/:https://framerusercontent.com/modules/D4TWeLfcxT6Tysr2BlYg/iZjmqdxVx1EOiM3k1FaW/useOnNavigationTargetChange.js": {"bytesInOutput": 522}, "/:https://framerusercontent.com/modules/ExNgrA7EJTKUPpH6vIlN/eiOrSJ2Ab5M9jPCvVwUz/useConstant.js": {"bytesInOutput": 32}, "/:https://framerusercontent.com/modules/D2Lz5CmnNVPZFFiZXalt/QaCzPbriZBfXWZIIycFI/colorFromToken.js": {"bytesInOutput": 34}, "/:https://framerusercontent.com/modules/3mKFSGQqKHV82uOV1eBc/5fbRLvOpxZC0JOXugvwm/isMotionValue.js": {"bytesInOutput": 40}, "/:https://framerusercontent.com/modules/xDiQsqBGXzmMsv7AlEVy/uhunpMiNsbXxzjlXsg1y/useUniqueClassName.js": {"bytesInOutput": 32}, "/:https://framerusercontent.com/modules/ETACN5BJyFTSo0VVDJfu/NHRqowOiXkF9UwOzczF7/variantUtils.js": {"bytesInOutput": 56}, "/:https://framerusercontent.com/modules/eMBrwoqQK7h6mEeGQUH8/GuplvPJVjmxpk9zqOTcb/isBrowser.js": {"bytesInOutput": 553}, "/:https://framerusercontent.com/modules/v9AWX2URmiYsHf7GbctE/XxKAZ9KlhWqf5x1JMyyF/useOnChange.js": {"bytesInOutput": 49}, "/:https://framerusercontent.com/modules/kNDwabfjDEb3vUxkQlZS/fSIr3AOAYbGlfSPgXpYu/useAutoMotionValue.js": {"bytesInOutput": 144}, "/:https://framerusercontent.com/modules/cuQH4dmpDnV8YK1mSgQX/KqRXqunFjE6ufhpc7ZRu/useFontControls.js": {"bytesInOutput": 87}, "/:https://framerusercontent.com/modules/afBE9Yx1W6bY5q32qPxe/m3q7puE2tbo1S2C0s0CT/useRenderTarget.js": {"bytesInOutput": 400}, "/:https://framerusercontent.com/modules/zGkoP8tPDCkoBzMdt5uq/0zFSjxIYliHxrQQnryFX/useControlledState.js": {"bytesInOutput": 33}, "/:https://framerusercontent.com/modules/5SM58HxZHxjjv7aLMOgQ/WXz9i6mVki0bBCrKdqB3/propUtils.js": {"bytesInOutput": 1420}}, "bytes": 18615}, "chunks/chunk-MO4ELLBD.js": {"imports": [{"path": "chunks/chunk-MKEJQGYO.js", "kind": "import-statement"}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}], "exports": ["Grain"], "inputs": {"/:https://framerusercontent.com/modules/cKGD16u2MGB7MfqfVXFp/wiztTCbXokZrMicHAmZc/Grain.js": {"bytesInOutput": 1285}}, "bytes": 1450}, "chunks/chunk-CHS3IKS5.js": {"imports": [{"path": "chunks/chunk-3LMAB7ZO.js", "kind": "import-statement"}, {"path": "chunks/chunk-YWUWNR35.js", "kind": "import-statement"}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "exports": ["stdin_default"], "inputs": {"/:https://framerusercontent.com/modules/a0Wb5TOIxxIAvu7q5B8D/q3saJqiiDbtZZYfeFyuz/c_ITIcTit.js": {"bytesInOutput": 9114}}, "bytes": 9382}, "chunks/chunk-MKEJQGYO.js": {"imports": [], "exports": ["A", "F", "I", "N", "O", "R", "S", "a", "b", "f", "h", "h2", "l", "s", "u", "v"], "inputs": {"/:https://esm.sh/*@motionone/utils@10.18.0/node/utils.mjs": {"bytesInOutput": 1295}, "/:https://esm.sh/*@motionone/utils": {"bytesInOutput": 0}, "/:https://esm.sh/*@motionone/easing@10.18.0/node/easing.mjs": {"bytesInOutput": 648}, "/:https://esm.sh/*@motionone/easing": {"bytesInOutput": 0}}, "bytes": 2167}, "chunks/chunk-X65FQ74X.js": {"imports": [{"path": "chunks/chunk-NFATBK6V.js", "kind": "import-statement"}, {"path": "chunks/chunk-45SLURIC.js", "kind": "import-statement"}, {"path": "chunks/chunk-YNN2WKUD.js", "kind": "import-statement"}, {"path": "chunks/chunk-SIWCYXHC.js", "kind": "import-statement"}, {"path": "chunks/chunk-VY5WWL2S.js", "kind": "import-statement"}, {"path": "chunks/chunk-YWUWNR35.js", "kind": "import-statement"}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "exports": ["stdin_default"], "inputs": {"/:https://framerusercontent.com/modules/8xf9SmDnMz8gG4sF4hpH/tLrkAll785TqtzKVhiuu/fkNrtm8z2.js": {"bytesInOutput": 13442}, "/:https://framerusercontent.com/modules/h8ioHyt4BUxuvVCE1Q10/aTM6mEMGlM19JjX8exDe/AvoidLayoutJumping_Prod.js": {"bytesInOutput": 1953}, "/:https://framerusercontent.com/modules/e47Mqi2vDwcso9yvGLGl/A5uhMJoa81U51rSVS7ew/zKohnBGrW.js": {"bytesInOutput": 48224}}, "bytes": 64568}, "small-button.js": {"imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:small-button", "inputs": {"virtual:small-button": {"bytesInOutput": 2014}, "/:https://framerusercontent.com/modules/p0IcM10gZPVBYgU6grNy/w0miSuPITV0VNXwc0nHX/IwCB7tJrN.js": {"bytesInOutput": 10843}}, "bytes": 13063}, "graph-item.js": {"imports": [{"path": "chunks/chunk-VY5WWL2S.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:graph-item", "inputs": {"virtual:graph-item": {"bytesInOutput": 1970}, "/:https://framerusercontent.com/modules/yjPkTLrAsiIaTyOt74Cv/i0zaQnzM5vQtCm9tYMcj/KEzuQXrOe.js": {"bytesInOutput": 11071}}, "bytes": 13317}, "table-item.js": {"imports": [{"path": "chunks/chunk-NFATBK6V.js", "kind": "import-statement"}, {"path": "chunks/chunk-45SLURIC.js", "kind": "import-statement"}, {"path": "chunks/chunk-YNN2WKUD.js", "kind": "import-statement"}, {"path": "chunks/chunk-SIWCYXHC.js", "kind": "import-statement"}, {"path": "chunks/chunk-VY5WWL2S.js", "kind": "import-statement"}, {"path": "chunks/chunk-YWUWNR35.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:table-item", "inputs": {"virtual:table-item": {"bytesInOutput": 2060}, "/:https://framerusercontent.com/modules/xawm4QzHYhcBfaeZ6rpd/DYMAxX2W9P5owRrmIIYS/zKohnBGrW.js": {"bytesInOutput": 48106}}, "bytes": 50902}, "chunks/chunk-NFATBK6V.js": {"imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "exports": ["stdin_default"], "inputs": {"/:https://framerusercontent.com/modules/NSkmAJg2RVe3fXUYvYRL/m3HXPaue5Xu13dGsibFw/d4er_GPVG.js": {"bytesInOutput": 6582}}, "bytes": 6709}, "chunks/chunk-45SLURIC.js": {"imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "exports": ["className", "css", "fonts"], "inputs": {"/:https://framerusercontent.com/modules/rL0tRCNiDNbTgXcw7l8h/MQ0zM3QkM5MEl876oJCh/pAxoS1kOX.js": {"bytesInOutput": 10426}}, "bytes": 10565}, "chunks/chunk-YNN2WKUD.js": {"imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "exports": ["stdin_default"], "inputs": {"/:https://framerusercontent.com/modules/4AfGmdzrVIJOiclhVWW5/wDcr6iY79T92uICesIJd/BvyOYDKED.js": {"bytesInOutput": 4105}}, "bytes": 4232}, "chunks/chunk-SIWCYXHC.js": {"imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "exports": ["className", "css", "fonts"], "inputs": {"/:https://framerusercontent.com/modules/7LhVHX9lqXURUWhQBzJn/wzTnBCThGjoX9XSgG5QG/HLpRTFhim.js": {"bytesInOutput": 10276}}, "bytes": 10415}, "chunks/chunk-VY5WWL2S.js": {"imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "exports": ["className", "css", "fonts"], "inputs": {"/:https://framerusercontent.com/modules/vaUDP9yUvXDLDorewcXH/TU27PKlUxf7UEubboCQ4/svYtzYwMA.js": {"bytesInOutput": 10264}}, "bytes": 10403}, "navbar.js": {"imports": [{"path": "chunks/chunk-CUJMJG57.js", "kind": "import-statement"}, {"path": "chunks/chunk-NZSA2AZS.js", "kind": "import-statement"}, {"path": "chunks/chunk-63EFHLNF.js", "kind": "import-statement"}, {"path": "chunks/chunk-QM54KZT3.js", "kind": "import-statement"}, {"path": "chunks/chunk-3IUEC7EM.js", "kind": "import-statement"}, {"path": "chunks/chunk-D6DQ2HQ2.js", "kind": "import-statement"}, {"path": "chunks/chunk-3LMAB7ZO.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:navbar", "inputs": {"virtual:navbar": {"bytesInOutput": 1974}, "/:https://framerusercontent.com/modules/5aH8sTCuBXkEF28bXDlY/6zW6pjqXlUWAuM8CvRvT/MH9m4cXzT.js": {"bytesInOutput": 47813}, "/:https://framerusercontent.com/modules/t4d3RN8YpjGEeIkhpaWX/8KBE3N3fhG5ieFPwOZtk/nCQNaN8LD.js": {"bytesInOutput": 667}, "/:https://framerusercontent.com/modules/kYJ0hJFhdtm6G3e3QyO6/g4rpjyJSfNJrlQAnvv8a/ypR5VEWEl.js": {"bytesInOutput": 10444}}, "bytes": 61882}, "chunks/chunk-CUJMJG57.js": {"imports": [{"path": "chunks/chunk-3LMAB7ZO.js", "kind": "import-statement"}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "exports": ["stdin_default"], "inputs": {"/:https://framerusercontent.com/modules/zYxxjr8GDqJairdZcDtp/v3Db4YTIvyVZ2ZxABpqF/xbeyrFlxc.js": {"bytesInOutput": 11091}}, "bytes": 11292}, "chunks/chunk-NZSA2AZS.js": {"imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "exports": ["stdin_default"], "inputs": {"/:https://framerusercontent.com/modules/ikVcV6kvZDj6uMF5EU26/KDHrdhjIRTgeMUBLQTNL/qBz_H6jdv.js": {"bytesInOutput": 8297}}, "bytes": 8424}, "chunks/chunk-63EFHLNF.js": {"imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "exports": ["stdin_default"], "inputs": {"/:https://framerusercontent.com/modules/soXlZNhf4UQKHBU3NPW9/d5bayGKVEMjVWciYdk33/ebExugia5.js": {"bytesInOutput": 7828}}, "bytes": 7955}, "chunks/chunk-QM54KZT3.js": {"imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "exports": ["stdin_default"], "inputs": {"/:https://framerusercontent.com/modules/hvfOCrJ4J6tWoXLRSXJB/7zjMumcOuXWqD8earGn2/k3VRpAwZb.js": {"bytesInOutput": 10789}}, "bytes": 10916}, "chunks/chunk-3IUEC7EM.js": {"imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "exports": ["stdin_default"], "inputs": {"/:https://framerusercontent.com/modules/7YAWvv4TdhRLOwJydNGi/YTwBBct98kJIILL0ZhAq/bE3WztOaD.js": {"bytesInOutput": 5671}}, "bytes": 5798}, "chunks/chunk-D6DQ2HQ2.js": {"imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "exports": ["className", "css", "fonts"], "inputs": {"/:https://framerusercontent.com/modules/jhmpyPFWXY5SZFHr5WJm/PVrSP0XenIHZSOxuHFWG/fDRzSjw63.js": {"bytesInOutput": 10270}}, "bytes": 10409}, "chunks/chunk-3LMAB7ZO.js": {"imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "exports": ["stdin_default"], "inputs": {"/:https://framerusercontent.com/modules/7583VVgwoRouErNZ0wXE/CiSJOJayosxmBn1Tinmm/ovCH22Xmz.js": {"bytesInOutput": 5050}}, "bytes": 5177}, "card.js": {"imports": [{"path": "chunks/chunk-77A4MMPH.js", "kind": "import-statement"}, {"path": "chunks/chunk-PWMJ5SHX.js", "kind": "import-statement"}, {"path": "chunks/chunk-YWUWNR35.js", "kind": "import-statement"}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "virtual:card", "inputs": {"virtual:card": {"bytesInOutput": 2043}}, "bytes": 2253}, "chunks/chunk-77A4MMPH.js": {"imports": [{"path": "chunks/chunk-PWMJ5SHX.js", "kind": "import-statement"}, {"path": "chunks/chunk-YWUWNR35.js", "kind": "import-statement"}, {"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}], "exports": ["stdin_default"], "inputs": {"/:https://framerusercontent.com/modules/sheAA3QRTXz9bLwA9r03/FcIieYwsDxO6UpSjeEKG/vqRYjrJj7.js": {"bytesInOutput": 25813}, "/:https://framerusercontent.com/modules/RemXsFW03uzBjJnHK5HN/3c99VWAyjHD8wcIfLR3l/wf_7zBsvo.js": {"bytesInOutput": 10276}}, "bytes": 36531}, "chunks/chunk-PWMJ5SHX.js": {"imports": [{"path": "react/jsx-runtime", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}, {"path": "unframer", "kind": "import-statement", "external": true}], "exports": ["Counter"], "inputs": {"/:https://framerusercontent.com/modules/bheqKqCqY9DyDgUEkC03/yG9czLqwXSpPzSLagGre/Counter.js": {"bytesInOutput": 4107}}, "bytes": 4226}, "chunks/chunk-YWUWNR35.js": {"imports": [{"path": "unframer", "kind": "import-statement", "external": true}], "exports": ["className", "css", "fonts"], "inputs": {"/:https://framerusercontent.com/modules/OTR8OYlvGuG53em7HPkd/XFT7UZEK3zn5KT7uQssI/gd6AWaps9.js": {"bytesInOutput": 10426}}, "bytes": 10565}}}