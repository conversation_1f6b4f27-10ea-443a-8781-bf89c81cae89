/* This file was generated by <PERSON>framer, do not edit manually */
import type * as React from "react"

import type { UnframerBreakpoint } from "unframer"

type Locale = string
export interface Props {
    children?: React.ReactNode
    locale?: Locale
    style?: React.CSSProperties
    className?: string
    id?: string
    width?: any
    height?: any
    layoutId?: string
    "variant"?: 'Desktop open' | 'Desktop closed' | 'Tablet open' | 'Tablet closed' | 'Phone open' | 'Phone closed'
    "number"?: string
    "name"?: string
    "description"?: string
    "_01Image"?: {src: string, srcSet?: string, alt?: string}
    "_02Image"?: {src: string, srcSet?: string, alt?: string}
    "_03Image"?: {src: string, srcSet?: string, alt?: string}
    "label"?: string
    "_01Category"?: string
    "_02Category"?: string
    "_03Category"?: string
    "_04Category"?: string
    "_05Category"?: string
    "_06Category"?: string
    "amount"?: string
}

const TableItemFramerComponent = (props: Props) => any

type VariantsMap = Partial<Record<UnframerBreakpoint, Props['variant']>> & { base: Props['variant'] }

TableItemFramerComponent.Responsive = (props: Omit<Props, 'variant'> & {variants?: VariantsMap}) => any

export default TableItemFramerComponent

