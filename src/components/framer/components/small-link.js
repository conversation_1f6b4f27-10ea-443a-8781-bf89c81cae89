// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";
import {
  className,
  css,
  fonts
} from "./chunks/chunk-SIWCYXHC.js";
import {
  className as className2,
  css as css2,
  fonts as fonts2
} from "./chunks/chunk-VY5WWL2S.js";

// virtual:small-link
import { Fragment as Fragment2 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/bS1D3ZEoaKmqk9Wy5pSI/DwfWvtC2vUITQYbUeZZI/dtX3pmnQt.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, getFontsFromSharedStyle, Link, RichText, SVG, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var enabledGestures = { mD1BhcCSE: { hover: true }, TB5NhxC0C: { hover: true } };
var cycleOrder = ["mD1BhcCSE", "IhnblAXlO", "TB5NhxC0C", "KZezpyNS4"];
var serializationHash = "framer-2efz1";
var variantClassNames = { IhnblAXlO: "framer-v-bv7vav", KZezpyNS4: "framer-v-az5pls", mD1BhcCSE: "framer-v-afx6zd", TB5NhxC0C: "framer-v-19pclpi" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { bounce: 0.35, delay: 0, duration: 0.39, type: "spring" };
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { "Desktop dark": "mD1BhcCSE", "Desktop light small": "TB5NhxC0C", "Phone dark": "IhnblAXlO", "Phone light small": "KZezpyNS4" };
var getProps = ({ height, id, link, title, width, ...props }) => {
  return { ...props, qzuYYjYOL: link ?? props.qzuYYjYOL, variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "mD1BhcCSE", x4f7TAELH: title ?? props.x4f7TAELH ?? "Title" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className: className3, layoutId, variant, x4f7TAELH, qzuYYjYOL, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "mD1BhcCSE", enabledGestures, ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [className, className2];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  const isDisplayed = () => {
    if (["IhnblAXlO", "KZezpyNS4"].includes(baseVariant)) return false;
    return true;
  };
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsx(Link, { href: qzuYYjYOL, motionChild: true, nodeId: "mD1BhcCSE", scopeId: "dtX3pmnQt", children: /* @__PURE__ */ _jsx(motion.a, { ...restProps, ...gestureHandlers, className: `${cx(scopingClassNames, "framer-afx6zd", className3, classNames)} framer-1fiqcwx`, "data-framer-name": "Desktop dark", layoutDependency, layoutId: "mD1BhcCSE", ref: refBinding, style: { ...style }, ...addPropertyOverrides({ "mD1BhcCSE-hover": { "data-framer-name": void 0 }, "TB5NhxC0C-hover": { "data-framer-name": void 0 }, IhnblAXlO: { "data-framer-name": "Phone dark" }, KZezpyNS4: { "data-framer-name": "Phone light small" }, TB5NhxC0C: { "data-framer-name": "Desktop light small" } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsxs(motion.div, { className: "framer-1r4klrj", "data-framer-name": "Container", layoutDependency, layoutId: "mOup_vaAX", children: [/* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-1oueo73", "data-styles-preset": "HLpRTFhim", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10)))" }, children: "Twitter" }) }), className: "framer-fxpu9w", fonts: ["Inter"], layoutDependency, layoutId: "ZxNEHdjLR", style: { "--extracted-r6o4lv": "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", "--framer-paragraph-spacing": "0px" }, text: x4f7TAELH, variants: { "mD1BhcCSE-hover": { "--extracted-r6o4lv": "rgb(0, 0, 0)" }, KZezpyNS4: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))" }, TB5NhxC0C: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))" } }, verticalAlignment: "top", withExternalLayout: true, ...addPropertyOverrides({ "mD1BhcCSE-hover": { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-1oueo73", "data-styles-preset": "HLpRTFhim", style: { "--framer-text-color": "var(--extracted-r6o4lv, rgb(0, 0, 0))" }, children: "Title" }) }) }, KZezpyNS4: { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-2s58fc", "data-styles-preset": "svYtzYwMA", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Title" }) }) }, TB5NhxC0C: { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-2s58fc", "data-styles-preset": "svYtzYwMA", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Title" }) }) } }, baseVariant, gestureVariant) }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-70k9fe", "data-framer-name": "Arrow", layoutDependency, layoutId: "OQbA9lMQy", children: [isDisplayed() && /* @__PURE__ */ _jsx(motion.div, { className: "framer-fp9yoz", "data-framer-name": "1", layoutDependency, layoutId: "giHfw2i5S", children: /* @__PURE__ */ _jsx(SVG, { className: "framer-hms4jt", "data-framer-name": "Vector", layout: "position", layoutDependency, layoutId: "CGWeMBvGt", opacity: 1, svg: '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 10 11"><path d="M 1.498 0.795 L 1.498 1.445 C 1.498 1.523 1.53 1.598 1.585 1.654 C 1.64 1.709 1.715 1.74 1.794 1.74 L 7.883 1.74 L 0.086 9.537 C 0.031 9.592 0 9.667 0 9.745 C 0 9.824 0.031 9.899 0.087 9.954 L 0.546 10.414 C 0.661 10.529 0.848 10.529 0.964 10.414 L 8.76 2.617 L 8.76 8.707 C 8.76 8.785 8.791 8.86 8.846 8.915 C 8.902 8.971 8.977 9.002 9.055 9.002 L 9.705 9.002 C 9.783 9.002 9.858 8.971 9.913 8.915 C 9.969 8.86 10 8.785 10 8.707 L 10 0.795 C 10 0.717 9.969 0.642 9.913 0.587 C 9.858 0.531 9.783 0.5 9.705 0.5 L 1.793 0.5 C 1.715 0.5 1.64 0.531 1.585 0.587 C 1.529 0.642 1.498 0.717 1.498 0.795 Z" fill="rgb(10,10,10)" opacity="0.3"></path></svg>', svgContentId: 12363694624, withExternalLayout: true, ...addPropertyOverrides({ "TB5NhxC0C-hover": { svg: '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 10 11"><path d="M 1.498 0.795 L 1.498 1.445 C 1.498 1.523 1.53 1.598 1.585 1.654 C 1.64 1.709 1.715 1.74 1.794 1.74 L 7.883 1.74 L 0.086 9.537 C 0.031 9.592 0 9.667 0 9.745 C 0 9.824 0.031 9.899 0.087 9.954 L 0.546 10.414 C 0.661 10.529 0.848 10.529 0.964 10.414 L 8.76 2.617 L 8.76 8.707 C 8.76 8.785 8.791 8.86 8.846 8.915 C 8.902 8.971 8.977 9.002 9.055 9.002 L 9.705 9.002 C 9.783 9.002 9.858 8.971 9.913 8.915 C 9.969 8.86 10 8.785 10 8.707 L 10 0.795 C 10 0.717 9.969 0.642 9.913 0.587 C 9.858 0.531 9.783 0.5 9.705 0.5 L 1.793 0.5 C 1.715 0.5 1.64 0.531 1.585 0.587 C 1.529 0.642 1.498 0.717 1.498 0.795 Z" fill="rgb(255, 255, 255)" opacity="0.3"></path></svg>', svgContentId: 9889774560 } }, baseVariant, gestureVariant) }) }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-hhjdwv", "data-framer-name": "2", layoutDependency, layoutId: "f0ErBX_we", children: /* @__PURE__ */ _jsx(SVG, { className: "framer-fpn24s", "data-framer-name": "Vector", layout: "position", layoutDependency, layoutId: "Don7J6j5O", opacity: 1, svg: '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 10 11"><path d="M 1.498 0.795 L 1.498 1.445 C 1.498 1.523 1.53 1.598 1.585 1.654 C 1.64 1.709 1.715 1.74 1.794 1.74 L 7.883 1.74 L 0.086 9.537 C 0.031 9.592 0 9.667 0 9.745 C 0 9.824 0.031 9.899 0.087 9.954 L 0.546 10.414 C 0.661 10.529 0.848 10.529 0.964 10.414 L 8.76 2.617 L 8.76 8.707 C 8.76 8.785 8.791 8.86 8.846 8.915 C 8.902 8.971 8.977 9.002 9.055 9.002 L 9.705 9.002 C 9.783 9.002 9.858 8.971 9.913 8.915 C 9.969 8.86 10 8.785 10 8.707 L 10 0.795 C 10 0.717 9.969 0.642 9.913 0.587 C 9.858 0.531 9.783 0.5 9.705 0.5 L 1.793 0.5 C 1.715 0.5 1.64 0.531 1.585 0.587 C 1.529 0.642 1.498 0.717 1.498 0.795 Z" fill="rgb(10,10,10)" opacity="0.3"></path></svg>', svgContentId: 12363694624, withExternalLayout: true, ...addPropertyOverrides({ "TB5NhxC0C-hover": { svg: '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 10 11"><path d="M 1.498 0.795 L 1.498 1.445 C 1.498 1.523 1.53 1.598 1.585 1.654 C 1.64 1.709 1.715 1.74 1.794 1.74 L 7.883 1.74 L 0.086 9.537 C 0.031 9.592 0 9.667 0 9.745 C 0 9.824 0.031 9.899 0.087 9.954 L 0.546 10.414 C 0.661 10.529 0.848 10.529 0.964 10.414 L 8.76 2.617 L 8.76 8.707 C 8.76 8.785 8.791 8.86 8.846 8.915 C 8.902 8.971 8.977 9.002 9.055 9.002 L 9.705 9.002 C 9.783 9.002 9.858 8.971 9.913 8.915 C 9.969 8.86 10 8.785 10 8.707 L 10 0.795 C 10 0.717 9.969 0.642 9.913 0.587 C 9.858 0.531 9.783 0.5 9.705 0.5 L 1.793 0.5 C 1.715 0.5 1.64 0.531 1.585 0.587 C 1.529 0.642 1.498 0.717 1.498 0.795 Z" fill="var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))" opacity="0.3"></path></svg>', svgContentId: 9352700347 }, KZezpyNS4: { svg: '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 10 11"><path d="M 1.498 0.795 L 1.498 1.445 C 1.498 1.523 1.53 1.598 1.585 1.654 C 1.64 1.709 1.715 1.74 1.794 1.74 L 7.883 1.74 L 0.086 9.537 C 0.031 9.592 0 9.667 0 9.745 C 0 9.824 0.031 9.899 0.087 9.954 L 0.546 10.414 C 0.661 10.529 0.848 10.529 0.964 10.414 L 8.76 2.617 L 8.76 8.707 C 8.76 8.785 8.791 8.86 8.846 8.915 C 8.902 8.971 8.977 9.002 9.055 9.002 L 9.705 9.002 C 9.783 9.002 9.858 8.971 9.913 8.915 C 9.969 8.86 10 8.785 10 8.707 L 10 0.795 C 10 0.717 9.969 0.642 9.913 0.587 C 9.858 0.531 9.783 0.5 9.705 0.5 L 1.793 0.5 C 1.715 0.5 1.64 0.531 1.585 0.587 C 1.529 0.642 1.498 0.717 1.498 0.795 Z" fill="var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)) /* {&quot;name&quot;:&quot;White&quot;} */" opacity="0.3"></path></svg>', svgContentId: 8926197795 }, TB5NhxC0C: { svg: '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 10 11"><path d="M 1.498 0.795 L 1.498 1.445 C 1.498 1.523 1.53 1.598 1.585 1.654 C 1.64 1.709 1.715 1.74 1.794 1.74 L 7.883 1.74 L 0.086 9.537 C 0.031 9.592 0 9.667 0 9.745 C 0 9.824 0.031 9.899 0.087 9.954 L 0.546 10.414 C 0.661 10.529 0.848 10.529 0.964 10.414 L 8.76 2.617 L 8.76 8.707 C 8.76 8.785 8.791 8.86 8.846 8.915 C 8.902 8.971 8.977 9.002 9.055 9.002 L 9.705 9.002 C 9.783 9.002 9.858 8.971 9.913 8.915 C 9.969 8.86 10 8.785 10 8.707 L 10 0.795 C 10 0.717 9.969 0.642 9.913 0.587 C 9.858 0.531 9.783 0.5 9.705 0.5 L 1.793 0.5 C 1.715 0.5 1.64 0.531 1.585 0.587 C 1.529 0.642 1.498 0.717 1.498 0.795 Z" fill="var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)) /* {&quot;name&quot;:&quot;White&quot;} */" opacity="0.3"></path></svg>', svgContentId: 8926197795 } }, baseVariant, gestureVariant) }) })] })] }) }) }) }) }) });
});
var css3 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-2efz1.framer-1fiqcwx, .framer-2efz1 .framer-1fiqcwx { display: block; }", ".framer-2efz1.framer-afx6zd { align-content: center; align-items: center; cursor: pointer; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: center; overflow: hidden; padding: 0px; position: relative; text-decoration: none; width: min-content; }", ".framer-2efz1 .framer-1r4klrj { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: center; overflow: visible; padding: 0px; position: relative; width: min-content; }", ".framer-2efz1 .framer-fxpu9w { flex: none; height: auto; position: relative; white-space: pre; width: auto; }", ".framer-2efz1 .framer-70k9fe { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 4px; height: min-content; justify-content: center; overflow: hidden; padding: 0px; position: relative; width: min-content; }", ".framer-2efz1 .framer-fp9yoz { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 4px; height: min-content; justify-content: center; left: 0px; overflow: visible; padding: 0px; position: absolute; top: -16px; width: min-content; z-index: 1; }", ".framer-2efz1 .framer-hms4jt, .framer-2efz1 .framer-fpn24s { flex: none; height: 11px; position: relative; width: 10px; }", ".framer-2efz1 .framer-hhjdwv { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 4px; height: min-content; justify-content: center; overflow: visible; padding: 0px; position: relative; width: min-content; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-2efz1.framer-afx6zd, .framer-2efz1 .framer-1r4klrj, .framer-2efz1 .framer-70k9fe, .framer-2efz1 .framer-fp9yoz, .framer-2efz1 .framer-hhjdwv { gap: 0px; } .framer-2efz1.framer-afx6zd > *, .framer-2efz1 .framer-1r4klrj > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } .framer-2efz1.framer-afx6zd > :first-child, .framer-2efz1 .framer-1r4klrj > :first-child, .framer-2efz1 .framer-fp9yoz > :first-child, .framer-2efz1 .framer-hhjdwv > :first-child { margin-left: 0px; } .framer-2efz1.framer-afx6zd > :last-child, .framer-2efz1 .framer-1r4klrj > :last-child, .framer-2efz1 .framer-fp9yoz > :last-child, .framer-2efz1 .framer-hhjdwv > :last-child { margin-right: 0px; } .framer-2efz1 .framer-70k9fe > * { margin: 0px; margin-bottom: calc(4px / 2); margin-top: calc(4px / 2); } .framer-2efz1 .framer-70k9fe > :first-child { margin-top: 0px; } .framer-2efz1 .framer-70k9fe > :last-child { margin-bottom: 0px; } .framer-2efz1 .framer-fp9yoz > *, .framer-2efz1 .framer-hhjdwv > * { margin: 0px; margin-left: calc(4px / 2); margin-right: calc(4px / 2); } }", ".framer-2efz1.framer-v-bv7vav.framer-afx6zd, .framer-2efz1.framer-v-az5pls.framer-afx6zd { cursor: unset; }", ".framer-2efz1.framer-v-afx6zd.hover .framer-fp9yoz, .framer-2efz1.framer-v-19pclpi.hover .framer-fp9yoz { left: unset; position: relative; top: unset; }", ".framer-2efz1.framer-v-afx6zd.hover .framer-hhjdwv, .framer-2efz1.framer-v-19pclpi.hover .framer-hhjdwv { bottom: -16px; left: 0px; position: absolute; z-index: 1; }", ...css, ...css2];
var FramerdtX3pmnQt = withCSS(Component, css3, "framer-2efz1");
var stdin_default = FramerdtX3pmnQt;
FramerdtX3pmnQt.displayName = "Small link";
FramerdtX3pmnQt.defaultProps = { height: 26, width: 61 };
addPropertyControls(FramerdtX3pmnQt, { variant: { options: ["mD1BhcCSE", "IhnblAXlO", "TB5NhxC0C", "KZezpyNS4"], optionTitles: ["Desktop dark", "Phone dark", "Desktop light small", "Phone light small"], title: "Variant", type: ControlType.Enum }, x4f7TAELH: { defaultValue: "Title", displayTextArea: false, title: "Title", type: ControlType.String }, qzuYYjYOL: { title: "Link", type: ControlType.Link } });
addFonts(FramerdtX3pmnQt, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5vvr9Vy74if2I6bQbJvbw7SY1pQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/EOr0mi4hNtlgWNn9if640EZzXCo.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/Y9k9QrlZAqio88Klkmbd8VoMQc.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/OYrD2tBIBPvoJXiIHnLoOXnY9M.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/JeYwfuaPfZHQhEG8U5gtPDZ7WQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/vQyevYAyHtARFwPqUzQGpnDs.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/b6Y37FthZeALduNqHicBT6FutY.woff2", weight: "400" }] }, ...getFontsFromSharedStyle(fonts), ...getFontsFromSharedStyle(fonts2)], { supportsExplicitInterCodegen: true });

// virtual:small-link
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {
  "base": "IhnblAXlO",
  "xl": "TB5NhxC0C"
};
stdin_default.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default);
export {
  ComponentWithRoot as default
};
