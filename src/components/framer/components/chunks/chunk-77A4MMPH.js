// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
import {
  Counter
} from "./chunk-PWMJ5SHX.js";
import {
  className,
  css,
  fonts
} from "./chunk-YWUWNR35.js";

// /:https://framerusercontent.com/modules/sheAA3QRTXz9bLwA9r03/FcIieYwsDxO6UpSjeEKG/vqRYjrJj7.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ComponentViewportProvider, ControlType, cx, getFonts, getFontsFromSharedStyle, getLoadingLazyAtYPosition, Image, RichText, SmartComponentScopedContainer, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";

// /:https://framerusercontent.com/modules/RemXsFW03uzBjJnHK5HN/3c99VWAyjHD8wcIfLR3l/wf_7zBsvo.js
import { fontStore } from "unframer";
fontStore.loadFonts(["Inter-Medium", "Inter-Bold", "Inter-BoldItalic", "Inter-MediumItalic"]);
var fonts2 = [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5A3Ce6C9YYmCjpQx9M4inSaKU.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/Qx95Xyt0Ka3SGhinnbXIGpEIyP4.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/6mJuEAguuIuMog10gGvH5d3cl8.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/xYYWaj7wCU5zSQH0eXvSaS19wo.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/otTaNuNpVK4RbdlT7zDDdKvQBA.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/d3tHnaQIAeqiE5hGcRw4mmgWYU.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/DolVirEGb34pEXEp8t8FQBSK4.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/DpPBYI0sL4fYLgAkX8KXOPVt7c.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/4RAEQdEOrcnDkhHiiCbJOw92Lk.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/1K3W8DizY3v4emK8Mb08YHxTbs.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/tUSCtfYVM1I1IchuyCwz9gDdQ.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/VgYFWiwsAC5OYxAycRXXvhze58.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/DXD0Q7LSl7HEvDzucnyLnGBHM.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/GIryZETIX4IFypco5pYZONKhJIo.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/H89BbHkbHDzlxZzxi8uPzTsp90.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/u6gJwDuwB143kpNK1T1MDKDWkMc.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/43sJ6MfOPh1LCJt46OvyDuSbA6o.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/wccHG0r4gBDAIRhfHiOlq6oEkqw.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/WZ367JPwf9bRW6LdTHN8rXgSjw.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/QxmhnWTzLtyjIiZcfaLIJ8EFBXU.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/2A4Xx7CngadFGlVV4xrO06OBHY.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/khkJkwSL66WFg8SX6Wa726c.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/0E7IMbDzcGABpBwwqNEt60wU0w.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/NTJ0nQgIF0gcDelS14zQ9NR9Q.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/QrcNhgEPfRl0LS8qz5Ln8olanl8.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/JEXmejW8mXOYMtt0hyRg811kHac.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/ksvR4VsLksjpSwnC2fPgHRNMw.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/uy9s0iWuxiNnVt8EpTI3gzohpwo.woff2", weight: "500" }] }];
var css2 = ['.framer-Nf4Gn .framer-styles-preset-1qnjizk:not(.rich-text-wrapper), .framer-Nf4Gn .framer-styles-preset-1qnjizk.rich-text-wrapper p { --framer-font-family: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-open-type-features: normal; --framer-font-size: 18px; --framer-font-style: normal; --framer-font-style-bold: normal; --framer-font-style-bold-italic: italic; --framer-font-style-italic: italic; --framer-font-variation-axes: normal; --framer-font-weight: 500; --framer-font-weight-bold: 700; --framer-font-weight-bold-italic: 700; --framer-font-weight-italic: 500; --framer-letter-spacing: -0.04em; --framer-line-height: 130%; --framer-paragraph-spacing: 20px; --framer-text-alignment: start; --framer-text-color: #090909; --framer-text-decoration: none; --framer-text-stroke-color: initial; --framer-text-stroke-width: initial; --framer-text-transform: none; }', '@media (max-width: 1199px) and (min-width: 810px) { .framer-Nf4Gn .framer-styles-preset-1qnjizk:not(.rich-text-wrapper), .framer-Nf4Gn .framer-styles-preset-1qnjizk.rich-text-wrapper p { --framer-font-family: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-open-type-features: normal; --framer-font-size: 17px; --framer-font-style: normal; --framer-font-style-bold: normal; --framer-font-style-bold-italic: italic; --framer-font-style-italic: italic; --framer-font-variation-axes: normal; --framer-font-weight: 500; --framer-font-weight-bold: 700; --framer-font-weight-bold-italic: 700; --framer-font-weight-italic: 500; --framer-letter-spacing: -0.04em; --framer-line-height: 130%; --framer-paragraph-spacing: 20px; --framer-text-alignment: start; --framer-text-color: #090909; --framer-text-decoration: none; --framer-text-stroke-color: initial; --framer-text-stroke-width: initial; --framer-text-transform: none; } }', '@media (max-width: 809px) and (min-width: 0px) { .framer-Nf4Gn .framer-styles-preset-1qnjizk:not(.rich-text-wrapper), .framer-Nf4Gn .framer-styles-preset-1qnjizk.rich-text-wrapper p { --framer-font-family: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-open-type-features: normal; --framer-font-size: 15px; --framer-font-style: normal; --framer-font-style-bold: normal; --framer-font-style-bold-italic: italic; --framer-font-style-italic: italic; --framer-font-variation-axes: normal; --framer-font-weight: 500; --framer-font-weight-bold: 700; --framer-font-weight-bold-italic: 700; --framer-font-weight-italic: 500; --framer-letter-spacing: -0.04em; --framer-line-height: 130%; --framer-paragraph-spacing: 20px; --framer-text-alignment: start; --framer-text-color: #090909; --framer-text-decoration: none; --framer-text-stroke-color: initial; --framer-text-stroke-width: initial; --framer-text-transform: none; } }'];
var className2 = "framer-Nf4Gn";

// /:https://framerusercontent.com/modules/sheAA3QRTXz9bLwA9r03/FcIieYwsDxO6UpSjeEKG/vqRYjrJj7.js
var CounterFonts = getFonts(Counter);
var enabledGestures = { oHqpGkbFc: { hover: true } };
var cycleOrder = ["oHqpGkbFc", "Y7OLqwEQ8", "NO0Dfc3UC"];
var serializationHash = "framer-AgEfc";
var variantClassNames = { NO0Dfc3UC: "framer-v-dzls3n", oHqpGkbFc: "framer-v-1j1zu9p", Y7OLqwEQ8: "framer-v-fkx8sy" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { delay: 0, duration: 0.45, ease: [0.82, 0.11, 0.37, 0.82], type: "tween" };
var toResponsiveImage = (value) => {
  if (typeof value === "object" && value !== null && typeof value.src === "string") {
    return value;
  }
  return typeof value === "string" ? { src: value } : void 0;
};
var negate = (value) => {
  return !value;
};
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { Desktop: "oHqpGkbFc", Phone: "NO0Dfc3UC", Tablet: "Y7OLqwEQ8" };
var getProps = ({ _01Logo, _02Logo, _03Logo, bottomText, counter, height, id, logoGrayscale, numberSmall, showLogos, speedMs, suffix, topText, width, ...props }) => {
  return { ...props, B2goKeVx1: _03Logo ?? props.B2goKeVx1, CEihHXtRL: logoGrayscale ?? props.CEihHXtRL ?? 100, eiMtdGMEG: counter ?? props.eiMtdGMEG ?? 50, HVRXp_Sg7: showLogos ?? props.HVRXp_Sg7 ?? true, JE5J2kZ5f: speedMs ?? props.JE5J2kZ5f ?? 60, noOZ4mEf6: bottomText ?? props.noOZ4mEf6 ?? "We\u2019ve delivered 50+ projects that help companies generate real results.", oi6jPflMU: _02Logo ?? props.oi6jPflMU, QsA9HyaU6: _01Logo ?? props.QsA9HyaU6, RE0VYGptP: numberSmall ?? props.RE0VYGptP ?? "01", variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "oHqpGkbFc", XxQcXuo02: topText ?? props.XxQcXuo02 ?? "Successful projects completed", ZSqaLyjGx: suffix ?? props.ZSqaLyjGx ?? "+" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className: className3, layoutId, variant, eiMtdGMEG, ZSqaLyjGx, JE5J2kZ5f, RE0VYGptP, XxQcXuo02, noOZ4mEf6, HVRXp_Sg7, QsA9HyaU6, oi6jPflMU, B2goKeVx1, CEihHXtRL, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "oHqpGkbFc", enabledGestures, ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [className2, className];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  const visible = negate(HVRXp_Sg7);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsxs(motion.div, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-1j1zu9p", className3, classNames), "data-framer-name": "Desktop", layoutDependency, layoutId: "oHqpGkbFc", ref: refBinding, style: { ...style }, ...addPropertyOverrides({ "oHqpGkbFc-hover": { "data-framer-name": void 0 }, NO0Dfc3UC: { "data-framer-name": "Phone" }, Y7OLqwEQ8: { "data-framer-name": "Tablet" } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsxs(motion.div, { className: "framer-lto9ai", "data-framer-name": "Top", layoutDependency, layoutId: "HuhWLQ1w1", style: { backgroundColor: "rgb(255, 255, 255)", borderBottomLeftRadius: 18, borderBottomRightRadius: 18, borderTopLeftRadius: 18, borderTopRightRadius: 18 }, variants: { "oHqpGkbFc-hover": { borderBottomLeftRadius: 0, borderBottomRightRadius: 0 } }, children: [/* @__PURE__ */ _jsx(ComponentViewportProvider, { children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-etnrh3-container", isAuthoredByUser: true, layoutDependency, layoutId: "v2hhixoGP-container", nodeId: "v2hhixoGP", rendersWithMotion: true, scopeId: "vqRYjrJj7", children: /* @__PURE__ */ _jsx(Counter, { decimalSeparatorType: "none", end: eiMtdGMEG, gapSize: 0, height: "100%", id: "v2hhixoGP", incrementType: "integer", layoutId: "v2hhixoGP", loop: false, prefixColor: "rgb(255, 255, 255)", prefixFont: { fontFamily: '"Inter", "Inter Placeholder", sans-serif', fontStyle: "normal", fontWeight: 700 }, prefixText: "", restartOnViewport: false, selectedFont: { fontFamily: '"Inter", "Inter Placeholder", sans-serif', fontStyle: "normal", fontWeight: 600 }, speed: JE5J2kZ5f, start: 0, startOnViewport: true, suffixColor: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", suffixFont: {}, suffixText: ZSqaLyjGx, textColor: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", textSize: 45, width: "100%", ...addPropertyOverrides({ NO0Dfc3UC: { textSize: 36 }, Y7OLqwEQ8: { textSize: 36 } }, baseVariant, gestureVariant) }) }) }), /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "10px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.06em", "--framer-line-height": "110%", "--framer-text-alignment": "right", "--framer-text-color": "var(--extracted-r6o4lv, rgb(9, 9, 9))" }, children: "01" }) }), className: "framer-1ksict5", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "KTRHEnLDR", style: { "--extracted-r6o4lv": "rgb(9, 9, 9)", "--framer-paragraph-spacing": "0px", opacity: 0.5 }, text: RE0VYGptP, verticalAlignment: "top", withExternalLayout: true })] }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-1co0dc5", "data-framer-name": "Bottom", layoutDependency, layoutId: "j32FZSvBx", style: { backgroundColor: "rgb(255, 255, 255)", borderBottomLeftRadius: 18, borderBottomRightRadius: 18, borderTopLeftRadius: 18, borderTopRightRadius: 18 }, variants: { "oHqpGkbFc-hover": { borderTopLeftRadius: 0, borderTopRightRadius: 0 } }, children: [/* @__PURE__ */ _jsx(motion.div, { className: "framer-1p9xpng", "data-framer-name": "Top", layoutDependency, layoutId: "GOh2T9aEC", children: /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-1qnjizk", "data-styles-preset": "wf_7zBsvo", style: { "--framer-text-alignment": "right" }, children: "Successful projects completed" }) }), className: "framer-14rc0hg", fonts: ["Inter"], layoutDependency, layoutId: "lGLLyusBM", style: { "--framer-paragraph-spacing": "0px" }, text: XxQcXuo02, verticalAlignment: "top", withExternalLayout: true }) }), HVRXp_Sg7 && /* @__PURE__ */ _jsxs(motion.div, { className: "framer-xlxqvt", "data-framer-name": "Bottom", layoutDependency, layoutId: "RwctIWIAU", style: { filter: `grayscale(${parseFloat(CEihHXtRL) / 100})`, WebkitFilter: `grayscale(${parseFloat(CEihHXtRL) / 100})` }, children: [/* @__PURE__ */ _jsx(Image, { background: { alt: "", fit: "fit", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0 + (((componentViewport?.height || 445) - 0 - 590) / 2 + 260 + 4) + 30 + 117.50000000000001 + 0), sizes: `calc((${componentViewport?.width || "100vw"} - 60px) * 0.27)`, ...toResponsiveImage(QsA9HyaU6), ...{ positionX: "center", positionY: "center" } }, className: "framer-6arpe1", "data-framer-name": "logo", layoutDependency, layoutId: "qsmu3n0aH", ...addPropertyOverrides({ "oHqpGkbFc-hover": { background: { alt: "", fit: "fit", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0 + (((componentViewport?.height || 445) - 0 - 590) / 2 + 260 + 0) + 30 + 119.50000000000001 + 0), sizes: `calc((${componentViewport?.width || "100vw"} - 60px) * 0.27)`, ...toResponsiveImage(QsA9HyaU6), ...{ positionX: "center", positionY: "center" } } }, NO0Dfc3UC: { background: { alt: "", fit: "fit", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0 + (((componentViewport?.height || 396) - 0 - 552) / 2 + 248 + 4) + 24 + 110.50000000000001 + 0), sizes: `calc((${componentViewport?.width || "100vw"} - 48px) * 0.27)`, ...toResponsiveImage(QsA9HyaU6), ...{ positionX: "center", positionY: "center" } } }, Y7OLqwEQ8: { background: { alt: "", fit: "fit", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0 + (((componentViewport?.height || 422) - 0 - 578) / 2 + 248 + 4) + 24 + 86.00000000000001 + 0 + 0), sizes: "69px", ...toResponsiveImage(QsA9HyaU6), ...{ positionX: "center", positionY: "center" } } } }, baseVariant, gestureVariant) }), /* @__PURE__ */ _jsx(Image, { background: { alt: "", fit: "fit", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0 + (((componentViewport?.height || 445) - 0 - 590) / 2 + 260 + 4) + 30 + 117.50000000000001 + 0), sizes: `calc((${componentViewport?.width || "100vw"} - 60px) * 0.27)`, ...toResponsiveImage(oi6jPflMU), ...{ positionX: "center", positionY: "center" } }, className: "framer-1nihjxj", "data-framer-name": "logo", layoutDependency, layoutId: "ABhsKlj7a", ...addPropertyOverrides({ "oHqpGkbFc-hover": { background: { alt: "", fit: "fit", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0 + (((componentViewport?.height || 445) - 0 - 590) / 2 + 260 + 0) + 30 + 119.50000000000001 + 0), sizes: `calc((${componentViewport?.width || "100vw"} - 60px) * 0.27)`, ...toResponsiveImage(oi6jPflMU), ...{ positionX: "center", positionY: "center" } } }, NO0Dfc3UC: { background: { alt: "", fit: "fit", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0 + (((componentViewport?.height || 396) - 0 - 552) / 2 + 248 + 4) + 24 + 110.50000000000001 + 0), sizes: `calc((${componentViewport?.width || "100vw"} - 48px) * 0.27)`, ...toResponsiveImage(oi6jPflMU), ...{ positionX: "center", positionY: "center" } } }, Y7OLqwEQ8: { background: { alt: "", fit: "fit", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0 + (((componentViewport?.height || 422) - 0 - 578) / 2 + 248 + 4) + 24 + 86.00000000000001 + 0 + 42), sizes: "69px", ...toResponsiveImage(oi6jPflMU), ...{ positionX: "center", positionY: "center" } } } }, baseVariant, gestureVariant) }), /* @__PURE__ */ _jsx(Image, { background: { alt: "", fit: "fit", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0 + (((componentViewport?.height || 445) - 0 - 590) / 2 + 260 + 4) + 30 + 117.50000000000001 + 0), sizes: `calc((${componentViewport?.width || "100vw"} - 60px) * 0.27)`, ...toResponsiveImage(B2goKeVx1), ...{ positionX: "center", positionY: "center" } }, className: "framer-1xqlcb6", "data-framer-name": "logo", layoutDependency, layoutId: "OQUllKnAV", ...addPropertyOverrides({ "oHqpGkbFc-hover": { background: { alt: "", fit: "fit", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0 + (((componentViewport?.height || 445) - 0 - 590) / 2 + 260 + 0) + 30 + 119.50000000000001 + 0), sizes: `calc((${componentViewport?.width || "100vw"} - 60px) * 0.27)`, ...toResponsiveImage(B2goKeVx1), ...{ positionX: "center", positionY: "center" } } }, NO0Dfc3UC: { background: { alt: "", fit: "fit", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0 + (((componentViewport?.height || 396) - 0 - 552) / 2 + 248 + 4) + 24 + 110.50000000000001 + 0), sizes: `calc((${componentViewport?.width || "100vw"} - 48px) * 0.27)`, ...toResponsiveImage(B2goKeVx1), ...{ positionX: "center", positionY: "center" } } }, Y7OLqwEQ8: { background: { alt: "", fit: "fit", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0 + (((componentViewport?.height || 422) - 0 - 578) / 2 + 248 + 4) + 24 + 86.00000000000001 + 0 + 82), sizes: "69px", ...toResponsiveImage(B2goKeVx1), ...{ positionX: "center", positionY: "center" } } } }, baseVariant, gestureVariant) })] }), visible && /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-1n1wh7h", "data-styles-preset": "gd6AWaps9", children: "We\u2019ve delivered 50+ projects that help companies generate real results." }) }), className: "framer-1fnepwh", fonts: ["Inter"], layoutDependency, layoutId: "Jsv71TPuV", style: { "--framer-paragraph-spacing": "0px", opacity: 0.6 }, text: noOZ4mEf6, verticalAlignment: "top", withExternalLayout: true })] })] }) }) }) });
});
var css3 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-AgEfc.framer-1icopbz, .framer-AgEfc .framer-1icopbz { display: block; }", ".framer-AgEfc.framer-1j1zu9p { align-content: center; align-items: center; cursor: default; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 4px; height: min-content; justify-content: center; overflow: hidden; padding: 0px; position: relative; width: 377px; }", ".framer-AgEfc .framer-lto9ai { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; height: min-content; justify-content: space-between; overflow: hidden; padding: 30px; position: relative; width: 100%; will-change: var(--framer-will-change-override, transform); }", ".framer-AgEfc .framer-etnrh3-container { flex: none; height: auto; position: relative; width: auto; }", ".framer-AgEfc .framer-1ksict5 { flex: none; height: auto; position: relative; white-space: pre; width: auto; }", ".framer-AgEfc .framer-1co0dc5 { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; height: 326px; justify-content: space-between; overflow: hidden; padding: 30px; position: relative; width: 100%; will-change: var(--framer-will-change-override, transform); }", ".framer-AgEfc .framer-1p9xpng { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 4px; height: min-content; justify-content: flex-end; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-AgEfc .framer-14rc0hg { flex: 1 0 0px; height: auto; max-width: 200px; position: relative; white-space: pre-wrap; width: 1px; word-break: break-word; word-wrap: break-word; }", ".framer-AgEfc .framer-xlxqvt { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; height: min-content; justify-content: space-between; overflow: hidden; padding: 0px; position: relative; width: 100%; }", ".framer-AgEfc .framer-6arpe1, .framer-AgEfc .framer-1xqlcb6 { aspect-ratio: 2.388888888888889 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 36px); overflow: visible; position: relative; width: 27%; }", ".framer-AgEfc .framer-1nihjxj { aspect-ratio: 2.361111111111111 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 36px); overflow: visible; position: relative; width: 27%; }", ".framer-AgEfc .framer-1fnepwh { --framer-text-wrap-override: balance; flex: none; height: auto; position: relative; width: 100%; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-AgEfc.framer-1j1zu9p, .framer-AgEfc .framer-1p9xpng { gap: 0px; } .framer-AgEfc.framer-1j1zu9p > * { margin: 0px; margin-bottom: calc(4px / 2); margin-top: calc(4px / 2); } .framer-AgEfc.framer-1j1zu9p > :first-child { margin-top: 0px; } .framer-AgEfc.framer-1j1zu9p > :last-child { margin-bottom: 0px; } .framer-AgEfc .framer-1p9xpng > * { margin: 0px; margin-left: calc(4px / 2); margin-right: calc(4px / 2); } .framer-AgEfc .framer-1p9xpng > :first-child { margin-left: 0px; } .framer-AgEfc .framer-1p9xpng > :last-child { margin-right: 0px; } }", ".framer-AgEfc.framer-v-fkx8sy .framer-lto9ai, .framer-AgEfc.framer-v-fkx8sy .framer-1co0dc5, .framer-AgEfc.framer-v-dzls3n .framer-lto9ai { padding: 24px; }", ".framer-AgEfc.framer-v-fkx8sy .framer-xlxqvt { align-content: flex-start; align-items: flex-start; flex-direction: column; gap: 11px; justify-content: flex-start; }", ".framer-AgEfc.framer-v-fkx8sy .framer-6arpe1 { aspect-ratio: unset; height: 31px; width: 69px; }", ".framer-AgEfc.framer-v-fkx8sy .framer-1nihjxj { aspect-ratio: 2.3870967741935485 / 1; height: var(--framer-aspect-ratio-supported, 29px); width: 69px; }", ".framer-AgEfc.framer-v-fkx8sy .framer-1xqlcb6 { height: var(--framer-aspect-ratio-supported, 29px); width: 69px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-AgEfc.framer-v-fkx8sy .framer-xlxqvt { gap: 0px; } .framer-AgEfc.framer-v-fkx8sy .framer-xlxqvt > * { margin: 0px; margin-bottom: calc(11px / 2); margin-top: calc(11px / 2); } .framer-AgEfc.framer-v-fkx8sy .framer-xlxqvt > :first-child { margin-top: 0px; } .framer-AgEfc.framer-v-fkx8sy .framer-xlxqvt > :last-child { margin-bottom: 0px; } }", ".framer-AgEfc.framer-v-dzls3n .framer-1co0dc5 { height: 300px; padding: 24px; }", ".framer-AgEfc.framer-v-dzls3n .framer-6arpe1 { aspect-ratio: unset; height: 36px; }", ".framer-AgEfc.framer-v-dzls3n .framer-1nihjxj { aspect-ratio: 2.3870967741935485 / 1; height: var(--framer-aspect-ratio-supported, 37px); }", ".framer-AgEfc.framer-v-dzls3n .framer-1xqlcb6 { height: var(--framer-aspect-ratio-supported, 37px); }", ".framer-AgEfc.framer-v-1j1zu9p.hover.framer-1j1zu9p { gap: 0px; }", ".framer-AgEfc.framer-v-1j1zu9p.hover .framer-1co0dc5 { height: 330px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-AgEfc.framer-v-1j1zu9p.hover.framer-1j1zu9p { gap: 0px; } .framer-AgEfc.framer-v-1j1zu9p.hover.framer-1j1zu9p > * { margin: 0px; margin-bottom: calc(0px / 2); margin-top: calc(0px / 2); } .framer-AgEfc.framer-v-1j1zu9p.hover.framer-1j1zu9p > :first-child { margin-top: 0px; } .framer-AgEfc.framer-v-1j1zu9p.hover.framer-1j1zu9p > :last-child { margin-bottom: 0px; } }", ...css2, ...css];
var FramervqRYjrJj7 = withCSS(Component, css3, "framer-AgEfc");
var stdin_default = FramervqRYjrJj7;
FramervqRYjrJj7.displayName = "Card";
FramervqRYjrJj7.defaultProps = { height: 445, width: 377 };
addPropertyControls(FramervqRYjrJj7, { variant: { options: ["oHqpGkbFc", "Y7OLqwEQ8", "NO0Dfc3UC"], optionTitles: ["Desktop", "Tablet", "Phone"], title: "Variant", type: ControlType.Enum }, eiMtdGMEG: { defaultValue: 50, displayStepper: true, title: "Counter", type: ControlType.Number }, ZSqaLyjGx: { defaultValue: "+", title: "Suffix", type: ControlType.String }, JE5J2kZ5f: { defaultValue: 60, max: 2e3, min: 0, step: 10, title: "Speed Ms", type: ControlType.Number }, RE0VYGptP: { defaultValue: "01", displayTextArea: false, title: "Number small", type: ControlType.String }, XxQcXuo02: { defaultValue: "Successful projects completed", displayTextArea: true, title: "Top text", type: ControlType.String }, noOZ4mEf6: { defaultValue: "We\u2019ve delivered 50+ projects that help companies generate real results.", displayTextArea: true, title: "Bottom text", type: ControlType.String }, HVRXp_Sg7: { defaultValue: true, title: "Show logos?", type: ControlType.Boolean }, QsA9HyaU6: { title: "01 Logo", type: ControlType.ResponsiveImage }, oi6jPflMU: { title: "02 Logo", type: ControlType.ResponsiveImage }, B2goKeVx1: { title: "03 Logo", type: ControlType.ResponsiveImage }, CEihHXtRL: { defaultValue: 100, title: "Logo grayscale", type: ControlType.Number } });
addFonts(FramervqRYjrJj7, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/hyOgCu0Xnghbimh0pE8QTvtt2AU.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/NeGmSOXrPBfEFIy5YZeHq17LEDA.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/oYaAX5himiTPYuN8vLWnqBbfD2s.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/lEJLP4R0yuCaMCjSXYHtJw72M.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/cRJyLNuTJR5jbyKzGi33wU9cqIQ.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/1ZFS7N918ojhhd0nQWdj3jz4w.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/A0Wcc7NgXMjUuFdquHDrIZpzZw0.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/DpPBYI0sL4fYLgAkX8KXOPVt7c.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/4RAEQdEOrcnDkhHiiCbJOw92Lk.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/1K3W8DizY3v4emK8Mb08YHxTbs.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/tUSCtfYVM1I1IchuyCwz9gDdQ.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/VgYFWiwsAC5OYxAycRXXvhze58.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/DXD0Q7LSl7HEvDzucnyLnGBHM.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/GIryZETIX4IFypco5pYZONKhJIo.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5vvr9Vy74if2I6bQbJvbw7SY1pQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/EOr0mi4hNtlgWNn9if640EZzXCo.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/Y9k9QrlZAqio88Klkmbd8VoMQc.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/OYrD2tBIBPvoJXiIHnLoOXnY9M.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/JeYwfuaPfZHQhEG8U5gtPDZ7WQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/vQyevYAyHtARFwPqUzQGpnDs.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/b6Y37FthZeALduNqHicBT6FutY.woff2", weight: "400" }] }, ...CounterFonts, ...getFontsFromSharedStyle(fonts2), ...getFontsFromSharedStyle(fonts)], { supportsExplicitInterCodegen: true });

export {
  stdin_default
};
