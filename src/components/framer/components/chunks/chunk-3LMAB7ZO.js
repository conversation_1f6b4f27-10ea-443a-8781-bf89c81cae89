// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
// /:https://framerusercontent.com/modules/7583VVgwoRouErNZ0wXE/CiSJOJayosxmBn1Tinmm/ovCH22Xmz.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var serializationHash = "framer-mF96D";
var variantClassNames = { U9IS5HbtX: "framer-v-ul5qem" };
var transition1 = { bounce: 0.2, delay: 0, duration: 0.4, type: "spring" };
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var getProps = ({ background, color, height, id, showVertical, width, ...props }) => {
  return { ...props, CgCxwDz_B: showVertical ?? props.CgCxwDz_B ?? true, CquvwTJCF: color ?? props.CquvwTJCF ?? "rgb(255, 255, 255)", L_MXIE6eA: background ?? props.L_MXIE6eA ?? "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className, layoutId, variant, CquvwTJCF, L_MXIE6eA, CgCxwDz_B, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ defaultVariant: "U9IS5HbtX", ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsxs(motion.div, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-ul5qem", className, classNames), "data-framer-name": "Variant 1", layoutDependency, layoutId: "U9IS5HbtX", ref: refBinding, style: { backgroundColor: L_MXIE6eA, borderBottomLeftRadius: 50, borderBottomRightRadius: 50, borderTopLeftRadius: 50, borderTopRightRadius: 50, ...style }, children: [CgCxwDz_B && /* @__PURE__ */ _jsx(motion.div, { className: "framer-1f2k6fv", "data-framer-name": "V", layoutDependency, layoutId: "QjeqJHeyd", style: { backgroundColor: CquvwTJCF } }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-19zm8hd", "data-framer-name": "H", layoutDependency, layoutId: "IUdgccIUF", style: { backgroundColor: CquvwTJCF, rotate: 90 } })] }) }) }) });
});
var css = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-mF96D.framer-5fz3i3, .framer-mF96D .framer-5fz3i3 { display: block; }", ".framer-mF96D.framer-ul5qem { align-content: center; align-items: center; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: 18px; justify-content: center; overflow: hidden; padding: 0px; position: relative; width: 18px; will-change: var(--framer-will-change-override, transform); }", ".framer-mF96D .framer-1f2k6fv, .framer-mF96D .framer-19zm8hd { flex: none; height: 8px; left: calc(50.00000000000002% - 2px / 2); overflow: visible; position: absolute; top: calc(50.00000000000002% - 8px / 2); width: 2px; z-index: 1; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-mF96D.framer-ul5qem { gap: 0px; } .framer-mF96D.framer-ul5qem > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } .framer-mF96D.framer-ul5qem > :first-child { margin-left: 0px; } .framer-mF96D.framer-ul5qem > :last-child { margin-right: 0px; } }"];
var FramerovCH22Xmz = withCSS(Component, css, "framer-mF96D");
var stdin_default = FramerovCH22Xmz;
FramerovCH22Xmz.displayName = "Plus icon small";
FramerovCH22Xmz.defaultProps = { height: 18, width: 18 };
addPropertyControls(FramerovCH22Xmz, { CquvwTJCF: { defaultValue: "rgb(255, 255, 255)", title: "Color", type: ControlType.Color }, L_MXIE6eA: { defaultValue: 'var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10)) /* {"name":"Black"} */', title: "Background", type: ControlType.Color }, CgCxwDz_B: { defaultValue: true, title: "Show vertical?", type: ControlType.Boolean } });
addFonts(FramerovCH22Xmz, [{ explicitInter: true, fonts: [] }], { supportsExplicitInterCodegen: true });

export {
  stdin_default
};
