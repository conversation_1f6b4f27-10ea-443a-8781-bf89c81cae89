// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
import {
  stdin_default
} from "./chunk-3LMAB7ZO.js";
import {
  className,
  css,
  fonts
} from "./chunk-YWUWNR35.js";

// /:https://framerusercontent.com/modules/a0Wb5TOIxxIAvu7q5B8D/q3saJqiiDbtZZYfeFyuz/c_ITIcTit.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ComponentViewportProvider, ControlType, cx, getFonts, getFontsFromSharedStyle, RichText, SmartComponentScopedContainer, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var PlusIconSmallFonts = getFonts(stdin_default);
var cycleOrder = ["VaDzezBN8", "Cmy0WdTav"];
var serializationHash = "framer-lvUtz";
var variantClassNames = { Cmy0WdTav: "framer-v-109u631", VaDzezBN8: "framer-v-1rxcnc" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { bounce: 0.2, delay: 0, duration: 0.4, type: "spring" };
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { Desktop: "VaDzezBN8", Phone: "Cmy0WdTav" };
var getProps = ({ height, iconBG, iconColor, id, textColor, title, width, ...props }) => {
  return { ...props, k3Z3ztoi4: title ?? props.k3Z3ztoi4 ?? "About us", RaZgbjWXH: textColor ?? props.RaZgbjWXH ?? "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", TmIm48vq7: iconColor ?? props.TmIm48vq7 ?? "rgb(255, 255, 255)", variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "VaDzezBN8", ws4vDxEZM: iconBG ?? props.ws4vDxEZM ?? "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className: className2, layoutId, variant, k3Z3ztoi4, RaZgbjWXH, TmIm48vq7, ws4vDxEZM, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "VaDzezBN8", ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [className];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsxs(motion.div, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-1rxcnc", className2, classNames), "data-framer-name": "Desktop", layoutDependency, layoutId: "VaDzezBN8", ref: refBinding, style: { ...style }, ...addPropertyOverrides({ Cmy0WdTav: { "data-framer-name": "Phone" } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 18, y: (componentViewport?.y || 0) + (0 + ((componentViewport?.height || 22) - 0 - 18) / 2), children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-84myrx-container", layoutDependency, layoutId: "TFdYWMptI-container", nodeId: "TFdYWMptI", rendersWithMotion: true, scopeId: "c_ITIcTit", children: /* @__PURE__ */ _jsx(stdin_default, { CquvwTJCF: TmIm48vq7, height: "100%", id: "TFdYWMptI", L_MXIE6eA: ws4vDxEZM, layoutId: "TFdYWMptI", width: "100%" }) }) }), /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-1n1wh7h", "data-styles-preset": "gd6AWaps9", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--variable-reference-RaZgbjWXH-c_ITIcTit))" }, children: "About us" }) }), className: "framer-158aegz", fonts: ["Inter"], layoutDependency, layoutId: "hw978sRYI", style: { "--extracted-r6o4lv": "var(--variable-reference-RaZgbjWXH-c_ITIcTit)", "--framer-paragraph-spacing": "0px", "--variable-reference-RaZgbjWXH-c_ITIcTit": RaZgbjWXH }, text: k3Z3ztoi4, verticalAlignment: "top", withExternalLayout: true })] }) }) }) });
});
var css2 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-lvUtz.framer-1vo9wvw, .framer-lvUtz .framer-1vo9wvw { display: block; }", ".framer-lvUtz.framer-1rxcnc { align-content: center; align-items: center; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 9px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: min-content; }", ".framer-lvUtz .framer-84myrx-container { flex: none; height: auto; position: relative; width: auto; }", ".framer-lvUtz .framer-158aegz { flex: none; height: auto; position: relative; white-space: pre; width: auto; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-lvUtz.framer-1rxcnc { gap: 0px; } .framer-lvUtz.framer-1rxcnc > * { margin: 0px; margin-left: calc(9px / 2); margin-right: calc(9px / 2); } .framer-lvUtz.framer-1rxcnc > :first-child { margin-left: 0px; } .framer-lvUtz.framer-1rxcnc > :last-child { margin-right: 0px; } }", ".framer-lvUtz.framer-v-109u631.framer-1rxcnc { gap: 6px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-lvUtz.framer-v-109u631.framer-1rxcnc { gap: 0px; } .framer-lvUtz.framer-v-109u631.framer-1rxcnc > * { margin: 0px; margin-left: calc(6px / 2); margin-right: calc(6px / 2); } .framer-lvUtz.framer-v-109u631.framer-1rxcnc > :first-child { margin-left: 0px; } .framer-lvUtz.framer-v-109u631.framer-1rxcnc > :last-child { margin-right: 0px; } }", ...css];
var Framerc_ITIcTit = withCSS(Component, css2, "framer-lvUtz");
var stdin_default2 = Framerc_ITIcTit;
Framerc_ITIcTit.displayName = "Intro";
Framerc_ITIcTit.defaultProps = { height: 22, width: 90 };
addPropertyControls(Framerc_ITIcTit, { variant: { options: ["VaDzezBN8", "Cmy0WdTav"], optionTitles: ["Desktop", "Phone"], title: "Variant", type: ControlType.Enum }, k3Z3ztoi4: { defaultValue: "About us", displayTextArea: false, title: "Title", type: ControlType.String }, RaZgbjWXH: { defaultValue: 'var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10)) /* {"name":"Black"} */', title: "Text color", type: ControlType.Color }, TmIm48vq7: { defaultValue: "rgb(255, 255, 255)", title: "Icon color", type: ControlType.Color }, ws4vDxEZM: { defaultValue: 'var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10)) /* {"name":"Black"} */', title: "Icon BG", type: ControlType.Color } });
addFonts(Framerc_ITIcTit, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5vvr9Vy74if2I6bQbJvbw7SY1pQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/EOr0mi4hNtlgWNn9if640EZzXCo.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/Y9k9QrlZAqio88Klkmbd8VoMQc.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/OYrD2tBIBPvoJXiIHnLoOXnY9M.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/JeYwfuaPfZHQhEG8U5gtPDZ7WQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/vQyevYAyHtARFwPqUzQGpnDs.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/b6Y37FthZeALduNqHicBT6FutY.woff2", weight: "400" }] }, ...PlusIconSmallFonts, ...getFontsFromSharedStyle(fonts)], { supportsExplicitInterCodegen: true });

export {
  stdin_default2 as stdin_default
};
