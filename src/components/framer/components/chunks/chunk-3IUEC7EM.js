// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
// /:https://framerusercontent.com/modules/7YAWvv4TdhRLOwJydNGi/YTwBBct98kJIILL0ZhAq/bE3WztOaD.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, useActiveVariantCallback, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
var cycleOrder = ["qlBMBxhJm", "ZgplB38uO"];
var serializationHash = "framer-UNjyH";
var variantClassNames = { qlBMBxhJm: "framer-v-1qd80pl", ZgplB38uO: "framer-v-4fzt3a" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { bounce: 0.2, delay: 0, duration: 0.4, type: "spring" };
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { Closed: "qlBMBxhJm", Open: "ZgplB38uO" };
var getProps = ({ click, height, id, width, ...props }) => {
  return { ...props, a5fwr9Mpw: click ?? props.a5fwr9Mpw, variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "qlBMBxhJm" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const { activeLocale, setLocale } = useLocaleInfo();
  const { style, className, layoutId, variant, a5fwr9Mpw, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "qlBMBxhJm", variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const { activeVariantCallback, delay } = useActiveVariantCallback(baseVariant);
  const onTap18zzu0r = activeVariantCallback(async (...args) => {
    setGestureState({ isPressed: false });
    if (a5fwr9Mpw) {
      const res = await a5fwr9Mpw(...args);
      if (res === false) return false;
    }
  });
  const sharedStyleClassNames = [];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  const ref1 = React.useRef(null);
  const defaultLayoutId = React.useId();
  const componentViewport = useComponentViewport();
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsxs(motion.div, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-1qd80pl", className, classNames), "data-framer-name": "Closed", "data-highlight": true, layoutDependency, layoutId: "qlBMBxhJm", onTap: onTap18zzu0r, ref: ref ?? ref1, style: { ...style }, ...addPropertyOverrides({ ZgplB38uO: { "data-framer-name": "Open" } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsx(motion.div, { className: "framer-1jxc6iw", layoutDependency, layoutId: "Rk56ZTEje", style: { backgroundColor: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", rotate: 0 }, variants: { ZgplB38uO: { rotate: 10 } } }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-qxejnu", layoutDependency, layoutId: "UJhjTMNAx", style: { backgroundColor: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", rotate: 0 }, variants: { ZgplB38uO: { rotate: -10 } } })] }) }) }) });
});
var css = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-UNjyH.framer-spcvwu, .framer-UNjyH .framer-spcvwu { display: block; }", ".framer-UNjyH.framer-1qd80pl { align-content: center; align-items: center; cursor: pointer; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 8px; height: min-content; justify-content: center; overflow: visible; padding: 0px; position: relative; width: min-content; }", ".framer-UNjyH .framer-1jxc6iw, .framer-UNjyH .framer-qxejnu { flex: none; height: 2px; overflow: hidden; position: relative; width: 59px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-UNjyH.framer-1qd80pl { gap: 0px; } .framer-UNjyH.framer-1qd80pl > * { margin: 0px; margin-bottom: calc(8px / 2); margin-top: calc(8px / 2); } .framer-UNjyH.framer-1qd80pl > :first-child { margin-top: 0px; } .framer-UNjyH.framer-1qd80pl > :last-child { margin-bottom: 0px; } }", ".framer-UNjyH.framer-v-4fzt3a.framer-1qd80pl { min-height: 12px; min-width: 59px; }", ".framer-UNjyH.framer-v-4fzt3a .framer-1jxc6iw, .framer-UNjyH.framer-v-4fzt3a .framer-qxejnu { left: 0px; position: absolute; right: 0px; top: calc(50.00000000000002% - 2px / 2); width: unset; z-index: 1; }"];
var FramerbE3WztOaD = withCSS(Component, css, "framer-UNjyH");
var stdin_default = FramerbE3WztOaD;
FramerbE3WztOaD.displayName = "Hamburger";
FramerbE3WztOaD.defaultProps = { height: 12, width: 59 };
addPropertyControls(FramerbE3WztOaD, { variant: { options: ["qlBMBxhJm", "ZgplB38uO"], optionTitles: ["Closed", "Open"], title: "Variant", type: ControlType.Enum }, a5fwr9Mpw: { title: "Click", type: ControlType.EventHandler } });
addFonts(FramerbE3WztOaD, [{ explicitInter: true, fonts: [] }], { supportsExplicitInterCodegen: true });

export {
  stdin_default
};
