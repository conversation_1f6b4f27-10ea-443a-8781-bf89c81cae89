// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
import {
  stdin_default
} from "./chunk-CVJIPDTS.js";

// /:https://framerusercontent.com/modules/kEfpoFmhRRcEXNcnsS3Z/sAaVlfOVnJwAGzz5oQ3a/pCa0ZyMgp.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ComponentViewportProvider, ControlType, cx, getFonts, getLoadingLazyAtYPosition, Image, RichText, SmartComponentScopedContainer, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var SmallButtonFonts = getFonts(stdin_default);
var enabledGestures = { L0JatAwCn: { hover: true } };
var cycleOrder = ["L0JatAwCn", "eEaIQeMBV"];
var serializationHash = "framer-UUXpR";
var variantClassNames = { eEaIQeMBV: "framer-v-1doshb7", L0JatAwCn: "framer-v-1wwdfxz" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { bounce: 0.2, delay: 0, duration: 0.4, type: "spring" };
var toResponsiveImage = (value) => {
  if (typeof value === "object" && value !== null && typeof value.src === "string") {
    return value;
  }
  return typeof value === "string" ? { src: value } : void 0;
};
var isSet = (value) => {
  if (Array.isArray(value)) return value.length > 0;
  return value !== void 0 && value !== null && value !== "";
};
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { Desktop: "L0JatAwCn", Phone: "eEaIQeMBV" };
var getProps = ({ button, company, height, id, image, link, name1, title, width, ...props }) => {
  return { ...props, FggqkLwNL: title ?? props.FggqkLwNL ?? "Team Lead", gBH5GgjXs: button ?? props.gBH5GgjXs ?? "Let\u2019s talk", i1j3qrwjd: company ?? props.i1j3qrwjd ?? "at fabrica\xAE ", NZwcZIstG: link ?? props.NZwcZIstG, rGlrlhWc1: name1 ?? props.rGlrlhWc1 ?? "Name", variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "L0JatAwCn", WHWOTj3TQ: image ?? props.WHWOTj3TQ ?? { src: "https://framerusercontent.com/images/XBirhPxPnqDiGkAtvDrRCiK4pS8.jpg" } };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className, layoutId, variant, FggqkLwNL, WHWOTj3TQ, i1j3qrwjd, rGlrlhWc1, gBH5GgjXs, NZwcZIstG, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "L0JatAwCn", enabledGestures, ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  const visible = isSet(FggqkLwNL);
  const visible1 = isSet(i1j3qrwjd);
  const visible2 = isSet(rGlrlhWc1);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsxs(motion.div, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-1wwdfxz", className, classNames), "data-framer-name": "Desktop", layoutDependency, layoutId: "L0JatAwCn", ref: refBinding, style: { ...style }, ...addPropertyOverrides({ "L0JatAwCn-hover": { "data-framer-name": void 0 }, eEaIQeMBV: { "data-framer-name": "Phone" } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsx(motion.div, { className: "framer-gcir4z", "data-framer-name": "Image", layoutDependency, layoutId: "AtZXGF9d_", style: { backgroundColor: "rgb(255, 255, 255)", borderBottomLeftRadius: 16, borderBottomRightRadius: 16, borderTopLeftRadius: 16, borderTopRightRadius: 16 }, variants: { "L0JatAwCn-hover": { borderBottomRightRadius: 0, borderTopRightRadius: 0 } }, children: /* @__PURE__ */ _jsx(Image, { background: { alt: "", fit: "fill", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + (0 + ((componentViewport?.height || 161) - 0 - ((componentViewport?.height || 161) - 0) * 1) / 2) + 6), pixelHeight: 288, pixelWidth: 226, sizes: "113px", ...toResponsiveImage(WHWOTj3TQ) }, className: "framer-bhhpli", "data-framer-name": "Team Member Image", layoutDependency, layoutId: "gVJmktcZu", style: { borderBottomLeftRadius: 12, borderBottomRightRadius: 12, borderTopLeftRadius: 12, borderTopRightRadius: 12 } }) }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-f4gtfg", "data-framer-name": "Card", layoutDependency, layoutId: "FR8PBmhFu", style: { backgroundColor: "rgb(255, 255, 255)", borderBottomLeftRadius: 16, borderBottomRightRadius: 16, borderTopLeftRadius: 16, borderTopRightRadius: 16 }, variants: { "L0JatAwCn-hover": { borderBottomLeftRadius: 0, borderTopLeftRadius: 0 } }, children: [/* @__PURE__ */ _jsxs(motion.div, { className: "framer-ilxhip", "data-framer-name": "Text", layoutDependency, layoutId: "MXygXDTR3", children: [/* @__PURE__ */ _jsxs(motion.div, { className: "framer-1o4mrbe", "data-framer-name": "Team Member Info", layoutDependency, layoutId: "dmORCFf8O", children: [visible && /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "14px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "100%", "--framer-text-color": "var(--extracted-r6o4lv, var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10)))" }, children: "Team Lead" }) }), className: "framer-7iz30i", "data-framer-name": "Team Member Role", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "j88Hr0CbV", style: { "--extracted-r6o4lv": "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", "--framer-paragraph-spacing": "0px" }, text: FggqkLwNL, verticalAlignment: "top", withExternalLayout: true }), visible1 && /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "12px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "110%", "--framer-text-color": "var(--extracted-r6o4lv, rgb(9, 9, 9))" }, children: "at fabrica\xAE " }) }), className: "framer-1dkoxx", "data-framer-name": "Team Member Company", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "lBriKx_di", style: { "--extracted-r6o4lv": "rgb(9, 9, 9)", "--framer-paragraph-spacing": "0px", opacity: 0.6 }, text: i1j3qrwjd, verticalAlignment: "top", withExternalLayout: true })] }), visible2 && /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "22px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "115%", "--framer-text-color": "var(--extracted-r6o4lv, rgb(9, 9, 9))" }, children: "Lauren Thompson" }) }), className: "framer-3w6c98", "data-framer-name": "Team Member Name", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "iH_QIizJO", style: { "--extracted-r6o4lv": "rgb(9, 9, 9)", "--framer-paragraph-spacing": "0px" }, text: rGlrlhWc1, verticalAlignment: "top", withExternalLayout: true })] }), /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 30, y: (componentViewport?.y || 0) + (0 + ((componentViewport?.height || 161) - 0 - 370.5) / 2) + 24 + 292.5, children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-1ulhjzu-container", layoutDependency, layoutId: "EEYsqtKv0-container", nodeId: "EEYsqtKv0", rendersWithMotion: true, scopeId: "pCa0ZyMgp", children: /* @__PURE__ */ _jsx(stdin_default, { GK_bavkEs: NZwcZIstG, height: "100%", id: "EEYsqtKv0", J1bovNTGx: gBH5GgjXs, layoutId: "EEYsqtKv0", variant: "oEh913J4W", width: "100%", ...addPropertyOverrides({ eEaIQeMBV: { variant: "efPssla96" } }, baseVariant, gestureVariant) }) }) })] })] }) }) }) });
});
var css = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-UUXpR.framer-1hc3jpa, .framer-UUXpR .framer-1hc3jpa { display: block; }", ".framer-UUXpR.framer-1wwdfxz { align-content: center; align-items: center; cursor: default; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: flex-start; max-width: 410px; overflow: visible; padding: 0px; position: relative; width: 280px; }", ".framer-UUXpR .framer-gcir4z { align-content: center; align-items: center; align-self: stretch; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: auto; justify-content: flex-start; overflow: visible; padding: 6px; position: relative; width: 125px; }", ".framer-UUXpR .framer-bhhpli { bottom: 6px; flex: none; left: 6px; position: absolute; right: 6px; top: 6px; z-index: 1; }", ".framer-UUXpR .framer-f4gtfg { align-content: flex-start; align-items: flex-start; display: flex; flex: 1 0 0px; flex-direction: column; flex-wrap: nowrap; gap: 20px; height: min-content; justify-content: flex-start; overflow: visible; padding: 24px; position: relative; width: 1px; }", ".framer-UUXpR .framer-ilxhip { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 6px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-UUXpR .framer-1o4mrbe { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: wrap; gap: 4px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-UUXpR .framer-7iz30i, .framer-UUXpR .framer-1dkoxx, .framer-UUXpR .framer-3w6c98 { flex: none; height: auto; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }", ".framer-UUXpR .framer-1ulhjzu-container { flex: none; height: auto; position: relative; width: auto; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-UUXpR.framer-1wwdfxz, .framer-UUXpR .framer-gcir4z, .framer-UUXpR .framer-f4gtfg, .framer-UUXpR .framer-ilxhip, .framer-UUXpR .framer-1o4mrbe { gap: 0px; } .framer-UUXpR.framer-1wwdfxz > * { margin: 0px; margin-left: calc(0px / 2); margin-right: calc(0px / 2); } .framer-UUXpR.framer-1wwdfxz > :first-child, .framer-UUXpR .framer-gcir4z > :first-child { margin-left: 0px; } .framer-UUXpR.framer-1wwdfxz > :last-child, .framer-UUXpR .framer-gcir4z > :last-child { margin-right: 0px; } .framer-UUXpR .framer-gcir4z > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } .framer-UUXpR .framer-f4gtfg > * { margin: 0px; margin-bottom: calc(20px / 2); margin-top: calc(20px / 2); } .framer-UUXpR .framer-f4gtfg > :first-child, .framer-UUXpR .framer-ilxhip > :first-child, .framer-UUXpR .framer-1o4mrbe > :first-child { margin-top: 0px; } .framer-UUXpR .framer-f4gtfg > :last-child, .framer-UUXpR .framer-ilxhip > :last-child, .framer-UUXpR .framer-1o4mrbe > :last-child { margin-bottom: 0px; } .framer-UUXpR .framer-ilxhip > * { margin: 0px; margin-bottom: calc(6px / 2); margin-top: calc(6px / 2); } .framer-UUXpR .framer-1o4mrbe > * { margin: 0px; margin-bottom: calc(4px / 2); margin-top: calc(4px / 2); } }"];
var FramerpCa0ZyMgp = withCSS(Component, css, "framer-UUXpR");
var stdin_default2 = FramerpCa0ZyMgp;
FramerpCa0ZyMgp.displayName = "Contact card";
FramerpCa0ZyMgp.defaultProps = { height: 161, width: 280 };
addPropertyControls(FramerpCa0ZyMgp, { variant: { options: ["L0JatAwCn", "eEaIQeMBV"], optionTitles: ["Desktop", "Phone"], title: "Variant", type: ControlType.Enum }, FggqkLwNL: { defaultValue: "Team Lead", displayTextArea: false, title: "Title", type: ControlType.String }, WHWOTj3TQ: { __defaultAssetReference: "data:framer/asset-reference,XBirhPxPnqDiGkAtvDrRCiK4pS8.jpg?originalFilename=Team+Member+Image.jpg&preferredSize=auto", title: "Image", type: ControlType.ResponsiveImage }, i1j3qrwjd: { defaultValue: "at fabrica\xAE ", displayTextArea: false, title: "Company", type: ControlType.String }, rGlrlhWc1: { defaultValue: "Name", displayTextArea: false, title: "Name", type: ControlType.String }, gBH5GgjXs: { defaultValue: "Let\u2019s talk", displayTextArea: false, title: "Button", type: ControlType.String }, NZwcZIstG: { title: "Link", type: ControlType.Link } });
addFonts(FramerpCa0ZyMgp, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/hyOgCu0Xnghbimh0pE8QTvtt2AU.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/NeGmSOXrPBfEFIy5YZeHq17LEDA.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/oYaAX5himiTPYuN8vLWnqBbfD2s.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/lEJLP4R0yuCaMCjSXYHtJw72M.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/cRJyLNuTJR5jbyKzGi33wU9cqIQ.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/1ZFS7N918ojhhd0nQWdj3jz4w.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/A0Wcc7NgXMjUuFdquHDrIZpzZw0.woff2", weight: "600" }] }, ...SmallButtonFonts], { supportsExplicitInterCodegen: true });

export {
  stdin_default2 as stdin_default
};
