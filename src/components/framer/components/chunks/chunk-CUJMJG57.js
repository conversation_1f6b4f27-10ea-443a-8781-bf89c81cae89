// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
import {
  stdin_default
} from "./chunk-3LMAB7ZO.js";

// /:https://framerusercontent.com/modules/zYxxjr8GDqJairdZcDtp/v3Db4YTIvyVZ2ZxABpqF/xbeyrFlxc.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ComponentViewportProvider, ControlType, cx, getFonts, Link, RichText, SmartComponentScopedContainer, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var PlusIconSmallFonts = getFonts(stdin_default);
var enabledGestures = { oIIY33Dha: { hover: true } };
var cycleOrder = ["oIIY33Dha", "Vq1FaZ_wm", "Zo8_CcwZu"];
var serializationHash = "framer-XRBqV";
var variantClassNames = { oIIY33Dha: "framer-v-1ryxeum", Vq1FaZ_wm: "framer-v-1wt46rb", Zo8_CcwZu: "framer-v-dacdgr" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { bounce: 0.2, delay: 0, duration: 0.4, type: "spring" };
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { Desktop: "oIIY33Dha", Phone: "Zo8_CcwZu", Tablet: "Vq1FaZ_wm" };
var getProps = ({ email, emailLink, height, id, width, ...props }) => {
  return { ...props, dgRMvez_W: email ?? props.dgRMvez_W ?? "Email", variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "oIIY33Dha", WNmHg1EX3: emailLink ?? props.WNmHg1EX3 };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className, layoutId, variant, dgRMvez_W, WNmHg1EX3, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "oIIY33Dha", enabledGestures, ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsx(Link, { href: WNmHg1EX3, motionChild: true, nodeId: "oIIY33Dha", scopeId: "xbeyrFlxc", children: /* @__PURE__ */ _jsxs(motion.a, { ...restProps, ...gestureHandlers, className: `${cx(scopingClassNames, "framer-1ryxeum", className, classNames)} framer-rk8vy9`, "data-framer-name": "Desktop", layoutDependency, layoutId: "oIIY33Dha", ref: refBinding, style: { ...style }, ...addPropertyOverrides({ "oIIY33Dha-hover": { "data-framer-name": void 0 }, Vq1FaZ_wm: { "data-framer-name": "Tablet" }, Zo8_CcwZu: { "data-framer-name": "Phone" } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 18, y: (componentViewport?.y || 0) + (0 + ((componentViewport?.height || 37) - 0 - 18) / 2), ...addPropertyOverrides({ Vq1FaZ_wm: { y: (componentViewport?.y || 0) + (0 + ((componentViewport?.height || 33) - 0 - 18) / 2) }, Zo8_CcwZu: { y: (componentViewport?.y || 0) + (0 + ((componentViewport?.height || 29) - 0 - 18) / 2) } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-149v3n5-container", layoutDependency, layoutId: "Ar1TOHe1i-container", nodeId: "Ar1TOHe1i", rendersWithMotion: true, scopeId: "xbeyrFlxc", style: { rotate: 0 }, variants: { "oIIY33Dha-hover": { rotate: 90 } }, children: /* @__PURE__ */ _jsx(stdin_default, { CgCxwDz_B: true, CquvwTJCF: "rgb(255, 255, 255)", height: "100%", id: "Ar1TOHe1i", L_MXIE6eA: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", layoutId: "Ar1TOHe1i", width: "100%" }) }) }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-17vqepl", "data-framer-name": "Container", layoutDependency, layoutId: "lHRml2nzK", children: [/* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "34px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.05em", "--framer-line-height": "110%", "--framer-text-color": "var(--extracted-r6o4lv, rgb(9, 9, 9))" }, children: "<EMAIL>" }) }), className: "framer-29jocm", "data-framer-name": "Email Address", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "Kef_rzVrM", style: { "--extracted-r6o4lv": "rgb(9, 9, 9)", "--framer-paragraph-spacing": "0px" }, text: dgRMvez_W, verticalAlignment: "top", withExternalLayout: true, ...addPropertyOverrides({ Vq1FaZ_wm: { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "30px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.05em", "--framer-line-height": "110%", "--framer-text-color": "var(--extracted-r6o4lv, rgb(9, 9, 9))" }, children: "Email" }) }) }, Zo8_CcwZu: { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "26px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.05em", "--framer-line-height": "110%", "--framer-text-color": "var(--extracted-r6o4lv, rgb(9, 9, 9))" }, children: "Title" }) }) } }, baseVariant, gestureVariant) }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-1wpm5k9", "data-framer-name": "Border", layoutDependency, layoutId: "lBjnM5_G0", style: { backgroundColor: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", opacity: 1 }, variants: { "oIIY33Dha-hover": { opacity: 0 } } })] })] }) }) }) }) });
});
var css = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-XRBqV.framer-rk8vy9, .framer-XRBqV .framer-rk8vy9 { display: block; }", ".framer-XRBqV.framer-1ryxeum { align-content: center; align-items: center; cursor: pointer; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 7px; height: min-content; justify-content: center; overflow: visible; padding: 0px; position: relative; text-decoration: none; width: min-content; }", ".framer-XRBqV .framer-149v3n5-container { flex: none; height: auto; position: relative; width: auto; z-index: 1; }", ".framer-XRBqV .framer-17vqepl { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: center; overflow: visible; padding: 0px; position: relative; width: min-content; }", ".framer-XRBqV .framer-29jocm { flex: none; height: auto; position: relative; white-space: pre; width: auto; }", ".framer-XRBqV .framer-1wpm5k9 { bottom: -6px; flex: none; height: 2px; left: 0px; overflow: hidden; position: absolute; width: 100%; z-index: 1; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-XRBqV.framer-1ryxeum, .framer-XRBqV .framer-17vqepl { gap: 0px; } .framer-XRBqV.framer-1ryxeum > * { margin: 0px; margin-left: calc(7px / 2); margin-right: calc(7px / 2); } .framer-XRBqV.framer-1ryxeum > :first-child, .framer-XRBqV .framer-17vqepl > :first-child { margin-left: 0px; } .framer-XRBqV.framer-1ryxeum > :last-child, .framer-XRBqV .framer-17vqepl > :last-child { margin-right: 0px; } .framer-XRBqV .framer-17vqepl > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } }", ".framer-XRBqV.framer-v-1wt46rb.framer-1ryxeum, .framer-XRBqV.framer-v-dacdgr.framer-1ryxeum { cursor: unset; }", ".framer-XRBqV.framer-v-dacdgr .framer-1wpm5k9 { bottom: -5px; }"];
var FramerxbeyrFlxc = withCSS(Component, css, "framer-XRBqV");
var stdin_default2 = FramerxbeyrFlxc;
FramerxbeyrFlxc.displayName = "Email";
FramerxbeyrFlxc.defaultProps = { height: 37, width: 105 };
addPropertyControls(FramerxbeyrFlxc, { variant: { options: ["oIIY33Dha", "Vq1FaZ_wm", "Zo8_CcwZu"], optionTitles: ["Desktop", "Tablet", "Phone"], title: "Variant", type: ControlType.Enum }, dgRMvez_W: { defaultValue: "Email", displayTextArea: false, title: "Email", type: ControlType.String }, WNmHg1EX3: { title: "Email link", type: ControlType.Link } });
addFonts(FramerxbeyrFlxc, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/hyOgCu0Xnghbimh0pE8QTvtt2AU.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/NeGmSOXrPBfEFIy5YZeHq17LEDA.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/oYaAX5himiTPYuN8vLWnqBbfD2s.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/lEJLP4R0yuCaMCjSXYHtJw72M.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/cRJyLNuTJR5jbyKzGi33wU9cqIQ.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/1ZFS7N918ojhhd0nQWdj3jz4w.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/A0Wcc7NgXMjUuFdquHDrIZpzZw0.woff2", weight: "600" }] }, ...PlusIconSmallFonts], { supportsExplicitInterCodegen: true });

export {
  stdin_default2 as stdin_default
};
