// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
import {
  className,
  css,
  fonts
} from "./chunk-5L2L6TVR.js";

// /:https://framerusercontent.com/modules/hWxVS3apPj1PnFxNHjIQ/nspOabGpGjvj61gMbFbA/eKMoUoN9m.js
import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ChildrenCanSuspend, ControlType, cx, getFontsFromSharedStyle, getLoadingLazyAtYPosition, Image, Link, PathVariablesContext, RichText, useComponentViewport, useLocaleInfo, useQueryData, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";

// /:https://framerusercontent.com/modules/CBN86zQXoOXpcgKhNtfI/h1KCmCnIrIg9HM8rHLCH/qNTzVcuJS.js
import { addPropertyControls as e4, ControlType as t3, QueryCache as l2, QueryEngine as i2 } from "unframer";

// /:https://framerusercontent.com/modules/CBN86zQXoOXpcgKhNtfI/h1KCmCnIrIg9HM8rHLCH/qNTzVcuJS-0.js
import { ControlType as p } from "unframer";
import { ControlType as A } from "unframer";
var t;
var e = Object.create;
var r = Object.defineProperty;
var n = Object.getOwnPropertyDescriptor;
var i = Object.getOwnPropertyNames;
var s = Object.getPrototypeOf;
var a = Object.prototype.hasOwnProperty;
var o = (t32, e42) => function() {
  return e42 || (0, t32[i(t32)[0]])((e42 = { exports: {} }).exports, e42), e42.exports;
};
var u = (t32, e42, s2, o22) => {
  if (e42 && "object" == typeof e42 || "function" == typeof e42) for (let u22 of i(e42)) a.call(t32, u22) || u22 === s2 || r(t32, u22, { get: () => e42[u22], enumerable: !(o22 = n(e42, u22)) || o22.enumerable });
  return t32;
};
var l = (t32, n22, i22) => (i22 = null != t32 ? e(s(t32)) : {}, u(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  !n22 && t32 && t32.__esModule ? i22 : r(i22, "default", { value: t32, enumerable: true }),
  t32
));
var h = o({ "../../../node_modules/dataloader/index.js"(t32, e42) {
  var r2, n22 = /* @__PURE__ */ function() {
    function t4(t5, e6) {
      if ("function" != typeof t5) throw TypeError("DataLoader must be constructed with a function which accepts Array<key> and returns Promise<Array<value>>, but got: " + t5 + ".");
      this._batchLoadFn = t5, this._maxBatchSize = function(t6) {
        if (!(!t6 || false !== t6.batch)) return 1;
        var e7 = t6 && t6.maxBatchSize;
        if (void 0 === e7) return 1 / 0;
        if ("number" != typeof e7 || e7 < 1) throw TypeError("maxBatchSize must be a positive number: " + e7);
        return e7;
      }(e6), this._batchScheduleFn = function(t6) {
        var e7 = t6 && t6.batchScheduleFn;
        if (void 0 === e7) return i22;
        if ("function" != typeof e7) throw TypeError("batchScheduleFn must be a function: " + e7);
        return e7;
      }(e6), this._cacheKeyFn = function(t6) {
        var e7 = t6 && t6.cacheKeyFn;
        if (void 0 === e7) return function(t7) {
          return t7;
        };
        if ("function" != typeof e7) throw TypeError("cacheKeyFn must be a function: " + e7);
        return e7;
      }(e6), this._cacheMap = function(t6) {
        if (!(!t6 || false !== t6.cache)) return null;
        var e7 = t6 && t6.cacheMap;
        if (void 0 === e7) return /* @__PURE__ */ new Map();
        if (null !== e7) {
          var r3 = ["get", "set", "delete", "clear"].filter(function(t7) {
            return e7 && "function" != typeof e7[t7];
          });
          if (0 !== r3.length) throw TypeError("Custom cacheMap missing methods: " + r3.join(", "));
        }
        return e7;
      }(e6), this._batch = null, this.name = e6 && e6.name ? e6.name : null;
    }
    var e5 = t4.prototype;
    return e5.load = function(t5) {
      if (null == t5) throw TypeError("The loader.load() function must be called with a value, but got: " + String(t5) + ".");
      var e6 = function(t6) {
        var e7 = t6._batch;
        if (null !== e7 && !e7.hasDispatched && e7.keys.length < t6._maxBatchSize) return e7;
        var r4 = { hasDispatched: false, keys: [], callbacks: [] };
        return t6._batch = r4, t6._batchScheduleFn(function() {
          (function(t7, e8) {
            var r5;
            if (e8.hasDispatched = true, 0 === e8.keys.length) {
              a2(e8);
              return;
            }
            try {
              r5 = t7._batchLoadFn(e8.keys);
            } catch (r6) {
              return s2(t7, e8, TypeError("DataLoader must be constructed with a function which accepts Array<key> and returns Promise<Array<value>>, but the function errored synchronously: " + String(r6) + "."));
            }
            if (!r5 || "function" != typeof r5.then) return s2(t7, e8, TypeError("DataLoader must be constructed with a function which accepts Array<key> and returns Promise<Array<value>>, but the function did not return a Promise: " + String(r5) + "."));
            r5.then(function(t8) {
              if (!o22(t8)) throw TypeError("DataLoader must be constructed with a function which accepts Array<key> and returns Promise<Array<value>>, but the function did not return a Promise of an Array: " + String(t8) + ".");
              if (t8.length !== e8.keys.length) throw TypeError("DataLoader must be constructed with a function which accepts Array<key> and returns Promise<Array<value>>, but the function did not return a Promise of an Array of the same length as the Array of keys.\n\nKeys:\n" + String(e8.keys) + "\n\nValues:\n" + String(t8));
              a2(e8);
              for (var r6 = 0; r6 < e8.callbacks.length; r6++) {
                var n4 = t8[r6];
                n4 instanceof Error ? e8.callbacks[r6].reject(n4) : e8.callbacks[r6].resolve(n4);
              }
            }).catch(function(r6) {
              s2(t7, e8, r6);
            });
          })(t6, r4);
        }), r4;
      }(this), r3 = this._cacheMap, n3 = this._cacheKeyFn(t5);
      if (r3) {
        var i3 = r3.get(n3);
        if (i3) {
          var u22 = e6.cacheHits || (e6.cacheHits = []);
          return new Promise(function(t6) {
            u22.push(function() {
              t6(i3);
            });
          });
        }
      }
      e6.keys.push(t5);
      var l22 = new Promise(function(t6, r4) {
        e6.callbacks.push({ resolve: t6, reject: r4 });
      });
      return r3 && r3.set(n3, l22), l22;
    }, e5.loadMany = function(t5) {
      if (!o22(t5)) throw TypeError("The loader.loadMany() function must be called with Array<key> but got: " + t5 + ".");
      for (var e6 = [], r3 = 0; r3 < t5.length; r3++) e6.push(this.load(t5[r3]).catch(function(t6) {
        return t6;
      }));
      return Promise.all(e6);
    }, e5.clear = function(t5) {
      var e6 = this._cacheMap;
      if (e6) {
        var r3 = this._cacheKeyFn(t5);
        e6.delete(r3);
      }
      return this;
    }, e5.clearAll = function() {
      var t5 = this._cacheMap;
      return t5 && t5.clear(), this;
    }, e5.prime = function(t5, e6) {
      var r3 = this._cacheMap;
      if (r3) {
        var n3, i3 = this._cacheKeyFn(t5);
        void 0 === r3.get(i3) && (e6 instanceof Error ? (n3 = Promise.reject(e6)).catch(function() {
        }) : n3 = Promise.resolve(e6), r3.set(i3, n3));
      }
      return this;
    }, t4;
  }(), i22 = "object" == typeof process && "function" == typeof process.nextTick ? function(t4) {
    r2 || (r2 = Promise.resolve()), r2.then(function() {
      process.nextTick(t4);
    });
  } : "function" == typeof setImmediate ? function(t4) {
    setImmediate(t4);
  } : function(t4) {
    setTimeout(t4);
  };
  function s2(t4, e5, r3) {
    a2(e5);
    for (var n3 = 0; n3 < e5.keys.length; n3++) t4.clear(e5.keys[n3]), e5.callbacks[n3].reject(r3);
  }
  function a2(t4) {
    if (t4.cacheHits) for (var e5 = 0; e5 < t4.cacheHits.length; e5++) t4.cacheHits[e5]();
  }
  function o22(t4) {
    return "object" == typeof t4 && null !== t4 && "number" == typeof t4.length && (0 === t4.length || t4.length > 0 && Object.prototype.hasOwnProperty.call(t4, t4.length - 1));
  }
  e42.exports = n22;
} });
var c = l(h());
var f = { Uint8: 1, Uint16: 2, Uint32: 4, BigUint64: 8, Int8: 1, Int16: 2, Int32: 4, BigInt64: 8, Float32: 4, Float64: 8 };
var g = class {
  getOffset() {
    return this.offset;
  }
  ensureLength(t32) {
    let e42 = this.bytes.length;
    if (!(this.offset + t32 <= e42)) throw Error("Reading out of bounds");
  }
  readUint8() {
    let t32 = f.Uint8;
    this.ensureLength(t32);
    let e42 = this.view.getUint8(this.offset);
    return this.offset += t32, e42;
  }
  readUint16() {
    let t32 = f.Uint16;
    this.ensureLength(t32);
    let e42 = this.view.getUint16(this.offset);
    return this.offset += t32, e42;
  }
  readUint32() {
    let t32 = f.Uint32;
    this.ensureLength(t32);
    let e42 = this.view.getUint32(this.offset);
    return this.offset += t32, e42;
  }
  readUint64() {
    let t32 = this.readBigUint64();
    return Number(t32);
  }
  readBigUint64() {
    let t32 = f.BigUint64;
    this.ensureLength(t32);
    let e42 = this.view.getBigUint64(this.offset);
    return this.offset += t32, e42;
  }
  readInt8() {
    let t32 = f.Int8;
    this.ensureLength(t32);
    let e42 = this.view.getInt8(this.offset);
    return this.offset += t32, e42;
  }
  readInt16() {
    let t32 = f.Int16;
    this.ensureLength(t32);
    let e42 = this.view.getInt16(this.offset);
    return this.offset += t32, e42;
  }
  readInt32() {
    let t32 = f.Int32;
    this.ensureLength(t32);
    let e42 = this.view.getInt32(this.offset);
    return this.offset += t32, e42;
  }
  readInt64() {
    let t32 = this.readBigInt64();
    return Number(t32);
  }
  readBigInt64() {
    let t32 = f.BigInt64;
    this.ensureLength(t32);
    let e42 = this.view.getBigInt64(this.offset);
    return this.offset += t32, e42;
  }
  readFloat32() {
    let t32 = f.Float32;
    this.ensureLength(t32);
    let e42 = this.view.getFloat32(this.offset);
    return this.offset += t32, e42;
  }
  readFloat64() {
    let t32 = f.Float64;
    this.ensureLength(t32);
    let e42 = this.view.getFloat64(this.offset);
    return this.offset += t32, e42;
  }
  readBytes(t32) {
    let e42 = this.offset, r2 = e42 + t32, n22 = this.bytes.subarray(e42, r2);
    return this.offset = r2, n22;
  }
  readString() {
    let t32 = this.readUint32(), e42 = this.readBytes(t32);
    return this.decoder.decode(e42);
  }
  readJson() {
    let t32 = this.readString();
    return JSON.parse(t32);
  }
  constructor(t32) {
    this.bytes = t32, this.offset = 0, this.view = d(this.bytes), this.decoder = new TextDecoder();
  }
};
function d(t32) {
  return new DataView(t32.buffer, t32.byteOffset, t32.byteLength);
}
var y = "undefined" != typeof window;
var v = y && "function" == typeof window.requestIdleCallback;
function w(t32, ...e42) {
  if (!t32) throw Error("Assertion Error" + (e42.length > 0 ? ": " + e42.join(" ") : ""));
}
function m(t32) {
  throw Error(`Unexpected value: ${t32}`);
}
var U = (t32) => 2 ** t32 - 1;
var S = (t32) => -(2 ** (t32 - 1));
var k = (t32) => 2 ** (t32 - 1) - 1;
var L = { Uint8: 0, Uint16: 0, Uint32: 0, Uint64: 0, BigUint64: 0, Int8: S(8), Int16: S(16), Int32: S(32), Int64: Number.MIN_SAFE_INTEGER, BigInt64: -(BigInt(2) ** BigInt(63)) };
var B = { Uint8: U(8), Uint16: U(16), Uint32: U(32), Uint64: Number.MAX_SAFE_INTEGER, BigUint64: BigInt(2) ** BigInt(64) - BigInt(1), Int8: k(8), Int16: k(16), Int32: k(32), Int64: Number.MAX_SAFE_INTEGER, BigInt64: BigInt(2) ** BigInt(63) - BigInt(1) };
function F(t32) {
  return Number.isFinite(t32);
}
function T(t32) {
  return null === t32;
}
var N = class t2 {
  static fromString(e42) {
    let [r2, n22, i22] = e42.split("/").map(Number);
    return w(F(r2), "Invalid chunkId"), w(F(n22), "Invalid offset"), w(F(i22), "Invalid length"), new t2(r2, n22, i22);
  }
  toString() {
    return `${this.chunkId}/${this.offset}/${this.length}`;
  }
  static read(e42) {
    let r2 = e42.readUint16(), n22 = e42.readUint32(), i22 = e42.readUint32();
    return new t2(r2, n22, i22);
  }
  write(t32) {
    t32.writeUint16(this.chunkId), t32.writeUint32(this.offset), t32.writeUint32(this.length);
  }
  compare(t32) {
    return this.chunkId < t32.chunkId ? -1 : this.chunkId > t32.chunkId ? 1 : this.offset < t32.offset ? -1 : this.offset > t32.offset ? 1 : (w(this.length === t32.length), 0);
  }
  constructor(t32, e42, r2) {
    this.chunkId = t32, this.offset = e42, this.length = r2;
  }
};
function O(t32) {
  if (T(t32)) return 0;
  switch (t32.type) {
    case A.Array:
      return 1;
    case A.Boolean:
      return 2;
    case A.Color:
      return 3;
    case A.Date:
      return 4;
    case A.Enum:
      return 5;
    case A.File:
      return 6;
    case A.ResponsiveImage:
      return 10;
    case A.Link:
      return 7;
    case A.Number:
      return 8;
    case A.Object:
      return 9;
    case A.RichText:
      return 11;
    case A.String:
      return 12;
    default:
      m(t32);
  }
}
function x(e42) {
  let r2 = e42.readUint16(), n22 = [];
  for (let i22 = 0; i22 < r2; i22++) {
    let r3 = t.read(e42);
    n22.push(r3);
  }
  return { type: A.Array, value: n22 };
}
function P(e42, r2) {
  for (let n22 of (e42.writeUint16(r2.value.length), r2.value)) t.write(e42, n22);
}
function q(e42, r2, n22) {
  let i22 = e42.value.length, s2 = r2.value.length;
  if (i22 < s2) return -1;
  if (i22 > s2) return 1;
  for (let s3 = 0; s3 < i22; s3++) {
    let i3 = e42.value[s3], a2 = r2.value[s3], o22 = t.compare(i3, a2, n22);
    if (0 !== o22) return o22;
  }
  return 0;
}
function R(t32) {
  return { type: A.Boolean, value: 0 !== t32.readUint8() };
}
function _(t32, e42) {
  t32.writeUint8(e42.value ? 1 : 0);
}
function D(t32, e42) {
  return t32.value < e42.value ? -1 : t32.value > e42.value ? 1 : 0;
}
function j(t32) {
  return { type: A.Color, value: t32.readString() };
}
function C(t32, e42) {
  t32.writeString(e42.value);
}
function J(t32, e42) {
  return t32.value < e42.value ? -1 : t32.value > e42.value ? 1 : 0;
}
function W(t32) {
  let e42 = t32.readInt64(), r2 = new Date(e42);
  return { type: A.Date, value: r2.toISOString() };
}
function $(t32, e42) {
  let r2 = new Date(e42.value), n22 = r2.getTime();
  t32.writeInt64(n22);
}
function z(t32, e42) {
  let r2 = new Date(t32.value), n22 = new Date(e42.value);
  return r2 < n22 ? -1 : r2 > n22 ? 1 : 0;
}
function G(t32) {
  return { type: A.Enum, value: t32.readString() };
}
function K(t32, e42) {
  t32.writeString(e42.value);
}
function H(t32, e42) {
  return t32.value < e42.value ? -1 : t32.value > e42.value ? 1 : 0;
}
function V(t32) {
  return { type: A.File, value: t32.readString() };
}
function X(t32, e42) {
  t32.writeString(e42.value);
}
function Q(t32, e42) {
  return t32.value < e42.value ? -1 : t32.value > e42.value ? 1 : 0;
}
function Y(t32) {
  return { type: A.Link, value: t32.readJson() };
}
function Z(t32, e42) {
  t32.writeJson(e42.value);
}
function tt(t32, e42) {
  let r2 = JSON.stringify(t32.value), n22 = JSON.stringify(e42.value);
  return r2 < n22 ? -1 : r2 > n22 ? 1 : 0;
}
function te(t32) {
  return { type: A.Number, value: t32.readFloat64() };
}
function tr(t32, e42) {
  t32.writeFloat64(e42.value);
}
function tn(t32, e42) {
  return t32.value < e42.value ? -1 : t32.value > e42.value ? 1 : 0;
}
function ti(e42) {
  let r2 = e42.readUint16(), n22 = {};
  for (let i22 = 0; i22 < r2; i22++) {
    let r3 = e42.readString();
    n22[r3] = t.read(e42);
  }
  return { type: A.Object, value: n22 };
}
function ts(e42, r2) {
  let n22 = Object.entries(r2.value);
  for (let [r3, i22] of (e42.writeUint16(n22.length), n22)) e42.writeString(r3), t.write(e42, i22);
}
function ta(e42, r2, n22) {
  let i22 = Object.keys(e42.value).sort(), s2 = Object.keys(r2.value).sort();
  if (i22.length < s2.length) return -1;
  if (i22.length > s2.length) return 1;
  for (let a2 = 0; a2 < i22.length; a2++) {
    let o22 = i22[a2], u22 = s2[a2];
    if (o22 < u22) return -1;
    if (o22 > u22) return 1;
    let l22 = e42.value[o22] ?? null, h2 = r2.value[u22] ?? null, c2 = t.compare(l22, h2, n22);
    if (0 !== c2) return c2;
  }
  return 0;
}
function to(t32) {
  return { type: A.ResponsiveImage, value: t32.readJson() };
}
function tu(t32, e42) {
  t32.writeJson(e42.value);
}
function tl(t32, e42) {
  let r2 = JSON.stringify(t32.value), n22 = JSON.stringify(e42.value);
  return r2 < n22 ? -1 : r2 > n22 ? 1 : 0;
}
function th(t32) {
  return { type: A.RichText, value: t32.readUint32() };
}
function tc(t32, e42) {
  t32.writeUint32(e42.value);
}
function tf(t32, e42) {
  let r2 = t32.value, n22 = e42.value;
  return r2 < n22 ? -1 : r2 > n22 ? 1 : 0;
}
function tg(t32) {
  return { type: A.String, value: t32.readString() };
}
function td(t32, e42) {
  t32.writeString(e42.value);
}
function tp(t32, e42, r2) {
  let n22 = t32.value, i22 = e42.value;
  return (0 === r2.type && (n22 = t32.value.toLowerCase(), i22 = e42.value.toLowerCase()), n22 < i22) ? -1 : n22 > i22 ? 1 : 0;
}
((t32) => {
  t32.read = function(t4) {
    let e42 = t4.readUint8();
    switch (e42) {
      case 0:
        return null;
      case 1:
        return x(t4);
      case 2:
        return R(t4);
      case 3:
        return j(t4);
      case 4:
        return W(t4);
      case 5:
        return G(t4);
      case 6:
        return V(t4);
      case 7:
        return Y(t4);
      case 8:
        return te(t4);
      case 9:
        return ti(t4);
      case 10:
        return to(t4);
      case 11:
        return th(t4);
      case 12:
        return tg(t4);
      default:
        m(e42);
    }
  }, t32.write = function(t4, e42) {
    let r2 = O(e42);
    if (t4.writeUint8(r2), !T(e42)) switch (e42.type) {
      case A.Array:
        return P(t4, e42);
      case A.Boolean:
        return _(t4, e42);
      case A.Color:
        return C(t4, e42);
      case A.Date:
        return $(t4, e42);
      case A.Enum:
        return K(t4, e42);
      case A.File:
        return X(t4, e42);
      case A.Link:
        return Z(t4, e42);
      case A.Number:
        return tr(t4, e42);
      case A.Object:
        return ts(t4, e42);
      case A.ResponsiveImage:
        return tu(t4, e42);
      case A.RichText:
        return tc(t4, e42);
      case A.String:
        return td(t4, e42);
      default:
        m(e42);
    }
  }, t32.compare = function(t4, e42, r2) {
    let n22 = O(t4), i22 = O(e42);
    if (n22 < i22) return -1;
    if (n22 > i22) return 1;
    if (T(t4) || T(e42)) return 0;
    switch (t4.type) {
      case A.Array:
        return w(e42.type === A.Array), q(t4, e42, r2);
      case A.Boolean:
        return w(e42.type === A.Boolean), D(t4, e42);
      case A.Color:
        return w(e42.type === A.Color), J(t4, e42);
      case A.Date:
        return w(e42.type === A.Date), z(t4, e42);
      case A.Enum:
        return w(e42.type === A.Enum), H(t4, e42);
      case A.File:
        return w(e42.type === A.File), Q(t4, e42);
      case A.Link:
        return w(e42.type === A.Link), tt(t4, e42);
      case A.Number:
        return w(e42.type === A.Number), tn(t4, e42);
      case A.Object:
        return w(e42.type === A.Object), ta(t4, e42, r2);
      case A.ResponsiveImage:
        return w(e42.type === A.ResponsiveImage), tl(t4, e42);
      case A.RichText:
        return w(e42.type === A.RichText), tf(t4, e42);
      case A.String:
        return w(e42.type === A.String), tp(t4, e42, r2);
      default:
        m(t4);
    }
  };
})(t || (t = {}));
var tv = 3;
var tw = 250;
var tm = [
  408,
  // Request Timeout
  429,
  // Too Many Requests
  500,
  // Internal Server Error
  502,
  // Bad Gateway
  503,
  // Service Unavailable
  504
];
var tI = async (t32, e42) => {
  let r2 = 0;
  for (; ; ) {
    try {
      let n22 = await fetch(t32, e42);
      if (!tm.includes(n22.status) || ++r2 > tv) return n22;
    } catch (t4) {
      if (e42?.signal?.aborted || ++r2 > tv) throw t4;
    }
    await tb(r2);
  }
};
async function tb(t32) {
  let e42 = Math.floor(tw * (Math.random() + 1) * 2 ** (t32 - 1));
  await new Promise((t4) => {
    setTimeout(t4, e42);
  });
}
async function tU(t32, e42) {
  let r2 = tL(e42), n22 = [], i22 = 0;
  for (let t4 of r2) n22.push(`${t4.from}-${t4.to - 1}`), i22 += t4.to - t4.from;
  let s2 = new URL(t32), a2 = n22.join(",");
  s2.searchParams.set("range", a2);
  let o22 = await tI(s2);
  if (200 !== o22.status) throw Error(`Request failed: ${o22.status} ${o22.statusText}`);
  let u22 = await o22.arrayBuffer(), l22 = new Uint8Array(u22);
  if (l22.length !== i22) throw Error("Request failed: Unexpected response length");
  let h2 = new tS(), c2 = 0;
  for (let t4 of r2) {
    let e5 = t4.to - t4.from, r3 = c2 + e5, n3 = l22.subarray(c2, r3);
    h2.write(t4.from, n3), c2 = r3;
  }
  return e42.map((t4) => h2.read(t4.from, t4.to - t4.from));
}
var tS = class {
  read(t32, e42) {
    for (let r2 of this.chunks) {
      if (t32 < r2.start) break;
      if (t32 > r2.end) continue;
      if (t32 + e42 > r2.end) break;
      let n22 = t32 - r2.start, i22 = n22 + e42;
      return r2.data.slice(n22, i22);
    }
    throw Error("Missing data");
  }
  write(t32, e42) {
    let r2 = t32, n22 = r2 + e42.length, i22 = 0, s2 = this.chunks.length;
    for (; i22 < s2; i22++) {
      let t4 = this.chunks[i22];
      if (w(t4, "Missing chunk"), !(r2 > t4.end)) {
        if (r2 > t4.start) {
          let n3 = r2 - t4.start, i3 = t4.data.subarray(0, n3);
          e42 = tk(i3, e42), r2 = t4.start;
        }
        break;
      }
    }
    for (; s2 > i22; s2--) {
      let t4 = this.chunks[s2 - 1];
      if (w(t4, "Missing chunk"), !(n22 < t4.start)) {
        if (n22 < t4.end) {
          let r3 = n22 - t4.start, i3 = t4.data.subarray(r3);
          e42 = tk(e42, i3), n22 = t4.end;
        }
        break;
      }
    }
    let a2 = { start: r2, end: n22, data: e42 }, o22 = s2 - i22;
    this.chunks.splice(i22, o22, a2);
  }
  constructor() {
    this.chunks = [];
  }
};
function tk(t32, e42) {
  let r2 = t32.length + e42.length, n22 = new Uint8Array(r2);
  return n22.set(t32, 0), n22.set(e42, t32.length), n22;
}
function tL(t32) {
  w(t32.length > 0, "Must have at least one range");
  let e42 = [...t32].sort((t4, e5) => t4.from - e5.from), r2 = [];
  for (let t4 of e42) {
    let e5 = r2.length - 1, n22 = r2[e5];
    n22 && t4.from <= n22.to ? r2[e5] = { from: n22.from, to: Math.max(n22.to, t4.to) } : r2.push(t4);
  }
  return r2;
}
var tE = class e3 {
  static read(r2) {
    let n22 = new e3(), i22 = r2.readUint16();
    for (let e42 = 0; e42 < i22; e42++) {
      let e5 = r2.readString(), i3 = t.read(r2);
      n22.setField(e5, i3);
    }
    return n22;
  }
  write(e42) {
    for (let [r2, n22] of (e42.writeUint16(this.fields.size), this.fields)) e42.writeString(r2), t.write(e42, n22);
  }
  getData() {
    let t32 = {};
    for (let [e42, r2] of this.fields) t32[e42] = r2;
    return t32;
  }
  setField(t32, e42) {
    this.fields.set(t32, e42);
  }
  getField(t32) {
    return this.fields.get(t32);
  }
  constructor() {
    this.fields = /* @__PURE__ */ new Map();
  }
};
var tM = class {
  scanItems() {
    return this.itemsPromise ??= tI(this.url).then(async (t32) => {
      if (!t32.ok) throw Error(`Request failed: ${t32.status} ${t32.statusText}`);
      let e42 = await t32.arrayBuffer(), r2 = new Uint8Array(e42), n22 = new g(r2), i22 = [], s2 = n22.readUint32();
      for (let t4 = 0; t4 < s2; t4++) {
        let t5 = n22.getOffset(), e5 = tE.read(n22), r3 = n22.getOffset() - t5, s3 = new N(this.id, t5, r3), a2 = s3.toString(), o22 = { pointer: a2, data: e5.getData() };
        this.itemLoader.prime(a2, o22), i22.push(o22);
      }
      return i22;
    }), this.itemsPromise;
  }
  resolveItem(t32) {
    return this.itemLoader.load(t32);
  }
  constructor(t32, e42) {
    this.id = t32, this.url = e42, this.itemLoader = new c.default(async (t4) => {
      let e5 = t4.map((t5) => {
        let e6 = N.fromString(t5);
        return { from: e6.offset, to: e6.offset + e6.length };
      }), r2 = await tU(this.url, e5);
      return r2.map((e6, r3) => {
        let n22 = new g(e6), i22 = tE.read(n22), s2 = t4[r3];
        return w(s2, "Missing pointer"), { pointer: s2, data: i22.getData() };
      });
    });
  }
};
var tF = class {
  async scanItems() {
    let t32 = await Promise.all(this.chunks.map(async (t4) => t4.scanItems()));
    return t32.flat();
  }
  async resolveItems(t32) {
    return Promise.all(t32.map((t4) => {
      let e42 = N.fromString(t4), r2 = this.chunks[e42.chunkId];
      return w(r2, "Missing chunk"), r2.resolveItem(t4);
    }));
  }
  compareItems(t32, e42) {
    let r2 = N.fromString(t32.pointer), n22 = N.fromString(e42.pointer);
    return r2.compare(n22);
  }
  compareValues(e42, r2, n22) {
    return t.compare(e42, r2, n22);
  }
  constructor(t32) {
    this.options = t32, this.schema = this.options.schema, this.indexes = this.options.indexes, this.resolveRichText = this.options.resolveRichText, this.chunks = this.options.chunks.map((t4, e42) => new tM(e42, t4));
  }
};

// /:https://framerusercontent.com/modules/CBN86zQXoOXpcgKhNtfI/h1KCmCnIrIg9HM8rHLCH/qNTzVcuJS-1.js
var e2 = [];
async function resolveRichText(t4) {
  let i3 = e2[t4];
  if (i3) return await i3();
}

// /:https://framerusercontent.com/modules/CBN86zQXoOXpcgKhNtfI/h1KCmCnIrIg9HM8rHLCH/qNTzVcuJS.js
var n2 = { id: { isNullable: false, type: t3.String }, nextItemId: { isNullable: true, type: t3.String }, previousItemId: { isNullable: true, type: t3.String }, ulw6Us1gG: { isNullable: true, type: t3.String }, ZTCDifSmg: { isNullable: true, type: t3.String } };
var o2 = new i2();
var u2 = new l2(o2);
var p2 = { collectionByLocaleId: { default: new tF({ chunks: [new URL("./qNTzVcuJS-chunk-default-0.framercms", "https://framerusercontent.com/modules/CBN86zQXoOXpcgKhNtfI/h1KCmCnIrIg9HM8rHLCH/qNTzVcuJS.js").href.replace("/modules/", "/cms/")], indexes: [], resolveRichText, schema: n2 }) }, displayName: "Category" };
var stdin_default = p2;
e4(p2, { ulw6Us1gG: { defaultValue: "", title: "Title", type: t3.String }, ZTCDifSmg: { title: "Slug", type: t3.String }, previousItemId: { dataIdentifier: "local-module:collection/qNTzVcuJS:default", title: "Previous", type: t3.CollectionReference }, nextItemId: { dataIdentifier: "local-module:collection/qNTzVcuJS:default", title: "Next", type: t3.CollectionReference } });

// /:https://framerusercontent.com/modules/hWxVS3apPj1PnFxNHjIQ/nspOabGpGjvj61gMbFbA/eKMoUoN9m.js
var enabledGestures = { RLnNDkK1c: { hover: true } };
var cycleOrder = ["RLnNDkK1c", "kYpj4oxpj"];
var serializationHash = "framer-Iu4Op";
var variantClassNames = { kYpj4oxpj: "framer-v-syuufd", RLnNDkK1c: "framer-v-1of3use" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { delay: 0, duration: 0.45, ease: [0.82, 0.11, 0.37, 0.82], type: "tween" };
var transformTemplate1 = (_2, t4) => `translateY(-50%) ${t4}`;
var QueryData = ({ query: query2, pageSize, children }) => {
  const data = useQueryData(query2);
  return children(data);
};
var isSet = (value) => {
  if (Array.isArray(value)) return value.length > 0;
  return value !== void 0 && value !== null && value !== "";
};
var toResponsiveImage = (value) => {
  if (typeof value === "object" && value !== null && typeof value.src === "string") {
    return value;
  }
  return typeof value === "string" ? { src: value } : void 0;
};
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { Desktop: "RLnNDkK1c", Phone: "kYpj4oxpj" };
var getProps = ({ category, height, id, link, logo, previewImage, title, width, year, ...props }) => {
  return { ...props, EOzevFWPq: link ?? props.EOzevFWPq, OuxDw9Wc4: title ?? props.OuxDw9Wc4 ?? "Ephemeral", qZAUb4V0E: previewImage ?? props.qZAUb4V0E, t8xTis15R: category ?? props.t8xTis15R, variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "RLnNDkK1c", WJHOOjgGx: logo ?? props.WJHOOjgGx, x3OlI7F0c: year ?? props.x3OlI7F0c ?? "2025" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className: className2, layoutId, variant, OuxDw9Wc4, t8xTis15R, x3OlI7F0c, qZAUb4V0E, WJHOOjgGx, EOzevFWPq, ulw6Us1gGiKSbMTziV, ZTCDifSmgiKSbMTziV, idiKSbMTziV, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "RLnNDkK1c", enabledGestures, ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [className];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  const visible = isSet(WJHOOjgGx);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsx(Link, { href: EOzevFWPq, motionChild: true, nodeId: "RLnNDkK1c", openInNewTab: false, scopeId: "eKMoUoN9m", children: /* @__PURE__ */ _jsxs(motion.a, { ...restProps, ...gestureHandlers, className: `${cx(scopingClassNames, "framer-1of3use", className2, classNames)} framer-1pxbyww`, "data-framer-name": "Desktop", layoutDependency, layoutId: "RLnNDkK1c", ref: refBinding, style: { borderBottomLeftRadius: 18, borderBottomRightRadius: 18, borderTopLeftRadius: 18, borderTopRightRadius: 18, ...style }, variants: { kYpj4oxpj: { borderBottomLeftRadius: 14, borderBottomRightRadius: 14, borderTopLeftRadius: 14, borderTopRightRadius: 14 } }, ...addPropertyOverrides({ "RLnNDkK1c-hover": { "data-framer-name": void 0 }, kYpj4oxpj: { "data-framer-name": "Phone" } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsxs(motion.div, { className: "framer-f2jpip", "data-framer-name": "Project header", layoutDependency, layoutId: "Q8py6SQe4", style: { backgroundColor: "rgb(255, 255, 255)", borderBottomLeftRadius: 18, borderBottomRightRadius: 18, borderTopLeftRadius: 18, borderTopRightRadius: 18 }, variants: { kYpj4oxpj: { borderBottomLeftRadius: 14, borderBottomRightRadius: 14, borderTopLeftRadius: 14, borderTopRightRadius: 14 } }, children: [/* @__PURE__ */ _jsxs(motion.div, { className: "framer-zgvrqz", "data-framer-name": "Project info", layoutDependency, layoutId: "jwivwShSA", children: [/* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-9v8dhs", "data-styles-preset": "oFAZmwcVJ", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10)))" }, children: "Ephemeral" }) }), className: "framer-1cjq3al", "data-framer-name": "Project name", fonts: ["Inter"], layoutDependency, layoutId: "uTaY9wVPQ", style: { "--extracted-r6o4lv": "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", "--framer-paragraph-spacing": "0px" }, text: OuxDw9Wc4, verticalAlignment: "top", withExternalLayout: true }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-12nyrxi", layoutDependency, layoutId: "iKSbMTziV", style: { opacity: 0 }, transformTemplate: transformTemplate1, children: /* @__PURE__ */ _jsx(ChildrenCanSuspend, { children: /* @__PURE__ */ _jsx(QueryData, { query: { from: { alias: "iKSbMTziV", data: stdin_default, type: "Collection" }, limit: { type: "LiteralValue", value: 10 }, orderBy: [{ arguments: [{ type: "LiteralValue", value: t8xTis15R }, { collection: "iKSbMTziV", name: "id", type: "Identifier" }], direction: "asc", functionName: "INDEX_OF", type: "FunctionCall" }], select: [{ collection: "iKSbMTziV", name: "ulw6Us1gG", type: "Identifier" }, { collection: "iKSbMTziV", name: "ZTCDifSmg", type: "Identifier" }, { collection: "iKSbMTziV", name: "id", type: "Identifier" }], where: { left: { collection: "iKSbMTziV", name: "id", type: "Identifier" }, operator: "in", right: { type: "LiteralValue", value: t8xTis15R }, type: "BinaryOperation" } }, children: (collection, paginationInfo, loadMore) => /* @__PURE__ */ _jsx(_Fragment, { children: collection?.map(({ id: idiKSbMTziV2, ulw6Us1gG: ulw6Us1gGiKSbMTziV2, ZTCDifSmg: ZTCDifSmgiKSbMTziV2 }, index) => {
    ulw6Us1gGiKSbMTziV2 ??= "";
    ZTCDifSmgiKSbMTziV2 ??= "";
    return /* @__PURE__ */ _jsx(LayoutGroup, { id: `iKSbMTziV-${idiKSbMTziV2}`, children: /* @__PURE__ */ _jsx(PathVariablesContext.Provider, { value: { ZTCDifSmg: ZTCDifSmgiKSbMTziV2 }, children: /* @__PURE__ */ _jsx(motion.div, { "aria-label": "filter-category", className: "framer-125cew3", layoutDependency, layoutId: "hLGSRQvCq", children: /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-9v8dhs", "data-styles-preset": "oFAZmwcVJ", children: "Title" }) }), className: "framer-zi7t4d", "data-framer-name": "Title", fonts: ["Inter"], layoutDependency, layoutId: "Z8GCRquPV", text: ulw6Us1gGiKSbMTziV2, verticalAlignment: "top", withExternalLayout: true }) }) }) }, idiKSbMTziV2);
  }) }) }) }) }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-10jxc9a", "data-framer-name": "Year", layoutDependency, layoutId: "q1mNqOsXU", children: [/* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItTWVkaXVt", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "12px", "--framer-font-weight": "500", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "130%", "--framer-text-alignment": "right", "--framer-text-color": "var(--extracted-r6o4lv, rgb(9, 9, 9))" }, children: "/" }) }), className: "framer-1wfwdp4", "data-framer-name": "/", fonts: ["Inter-Medium"], layoutDependency, layoutId: "XywQOMpKL", style: { "--extracted-r6o4lv": "rgb(9, 9, 9)", "--framer-paragraph-spacing": "0px", opacity: 0.6 }, verticalAlignment: "top", withExternalLayout: true }), /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItTWVkaXVt", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "12px", "--framer-font-weight": "500", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "130%", "--framer-text-alignment": "right", "--framer-text-color": "var(--extracted-r6o4lv, rgb(9, 9, 9))" }, children: "2025" }) }), className: "framer-9ocb8t", fonts: ["Inter-Medium"], layoutDependency, layoutId: "utomm3wBc", style: { "--extracted-r6o4lv": "rgb(9, 9, 9)", "--framer-paragraph-spacing": "0px", opacity: 0.6 }, text: x3OlI7F0c, verticalAlignment: "top", withExternalLayout: true })] })] }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-1xqu72a", "data-framer-name": "Dots", layoutDependency, layoutId: "xCMZU_65A", children: [/* @__PURE__ */ _jsx(motion.div, { className: "framer-g366ra", "data-framer-name": "Ellipse 5", layoutDependency, layoutId: "ePuhgem_B", style: { backgroundColor: "rgb(231, 231, 231)", borderBottomLeftRadius: "100%", borderBottomRightRadius: "100%", borderTopLeftRadius: "100%", borderTopRightRadius: "100%" }, variants: { "RLnNDkK1c-hover": { backgroundColor: "rgb(253, 95, 89)" } } }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-1wwmucy", "data-framer-name": "Ellipse 6", layoutDependency, layoutId: "Qp_K7ebf5", style: { backgroundColor: "rgb(231, 231, 231)", borderBottomLeftRadius: "100%", borderBottomRightRadius: "100%", borderTopLeftRadius: "100%", borderTopRightRadius: "100%" }, variants: { "RLnNDkK1c-hover": { backgroundColor: "rgb(255, 187, 44)" } } }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-ylz3x1", "data-framer-name": "Ellipse 7", layoutDependency, layoutId: "aHusmTDIh", style: { backgroundColor: "rgb(231, 231, 231)", borderBottomLeftRadius: "100%", borderBottomRightRadius: "100%", borderTopLeftRadius: "100%", borderTopRightRadius: "100%" }, variants: { "RLnNDkK1c-hover": { backgroundColor: "rgb(37, 201, 61)" } } })] })] }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-1nwqy7f", "data-framer-name": "Background", layoutDependency, layoutId: "PT4849OS5", style: { backgroundColor: "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", borderBottomLeftRadius: 18, borderBottomRightRadius: 18, borderTopLeftRadius: 18, borderTopRightRadius: 18 }, variants: { kYpj4oxpj: { borderBottomLeftRadius: 14, borderBottomRightRadius: 14, borderTopLeftRadius: 14, borderTopRightRadius: 14 } }, children: [visible && /* @__PURE__ */ _jsx(Image, { background: { alt: "", fit: "fit", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0 + (((componentViewport?.height || 473) - 0 - 472) / 2 + 58 + 4) + 0 + 165), sizes: `calc(${componentViewport?.width || "100vw"} * 0.2544)`, ...toResponsiveImage(WJHOOjgGx), ...{ positionX: "center", positionY: "center" } }, className: "framer-jem6dr", "data-framer-name": "Logo", layoutDependency, layoutId: "MMoJ144Nw", style: { scale: 1 }, variants: { "RLnNDkK1c-hover": { scale: 0.8 } }, ...addPropertyOverrides({ kYpj4oxpj: { background: { alt: "", fit: "fit", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0 + (((componentViewport?.height || 469) - 0 - 467.6) / 2 + 53.6 + 4) + 0 + 165), sizes: `calc(${componentViewport?.width || "100vw"} * 0.36)`, ...toResponsiveImage(WJHOOjgGx), ...{ positionX: "center", positionY: "center" } } } }, baseVariant, gestureVariant) }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-ybsvxe", "data-framer-name": "Image container", layoutDependency, layoutId: "f73f9V_uV", style: { borderBottomLeftRadius: 16, borderBottomRightRadius: 16, borderTopLeftRadius: 16, borderTopRightRadius: 16 }, variants: { kYpj4oxpj: { borderBottomLeftRadius: 11, borderBottomRightRadius: 11, borderTopLeftRadius: 11, borderTopRightRadius: 11 } }, children: [/* @__PURE__ */ _jsx(motion.div, { className: "framer-16bbbzt", "data-framer-name": "blackout", layoutDependency, layoutId: "Mp16oZ1Pp", style: { backgroundColor: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", opacity: 0.15 }, variants: { "RLnNDkK1c-hover": { opacity: 0.2 } } }), /* @__PURE__ */ _jsx(Image, { background: { alt: "", fit: "fill", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0 + (((componentViewport?.height || 473) - 0 - 472) / 2 + 58 + 4) + 4 + 0), sizes: `calc(${componentViewport?.width || "100vw"} - 8px)`, ...toResponsiveImage(qZAUb4V0E) }, className: "framer-jh1lcx", "data-framer-name": "Image", layoutDependency, layoutId: "NjLRJqWAN", style: { filter: "none", scale: 1, WebkitFilter: "none" }, variants: { "RLnNDkK1c-hover": { filter: "blur(7px)", scale: 1.13, WebkitFilter: "blur(7px)" } }, ...addPropertyOverrides({ "RLnNDkK1c-hover": { background: { alt: "", fit: "fill", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0 + (((componentViewport?.height || 473) - 0 - 472) / 2 + 58 + 4) + 0 + 0), sizes: componentViewport?.width || "100vw", ...toResponsiveImage(qZAUb4V0E) } }, kYpj4oxpj: { background: { alt: "", fit: "fill", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0 + (((componentViewport?.height || 469) - 0 - 467.6) / 2 + 53.6 + 4) + 4 + 0), sizes: `calc(${componentViewport?.width || "100vw"} - 8px)`, ...toResponsiveImage(qZAUb4V0E) } } }, baseVariant, gestureVariant) })] })] })] }) }) }) }) });
});
var css2 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-Iu4Op.framer-1pxbyww, .framer-Iu4Op .framer-1pxbyww { display: block; }", ".framer-Iu4Op.framer-1of3use { align-content: center; align-items: center; cursor: pointer; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 4px; height: min-content; justify-content: center; overflow: visible; padding: 0px; position: relative; text-decoration: none; width: 562px; }", ".framer-Iu4Op .framer-f2jpip { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 20px; height: 58px; justify-content: flex-start; overflow: visible; padding: 18px 24px 18px 24px; position: relative; width: 100%; }", ".framer-Iu4Op .framer-zgvrqz { align-content: flex-end; align-items: flex-end; display: flex; flex: 1 0 0px; flex-direction: row; flex-wrap: nowrap; gap: 18px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: 1px; }", ".framer-Iu4Op .framer-1cjq3al, .framer-Iu4Op .framer-zi7t4d, .framer-Iu4Op .framer-1wfwdp4, .framer-Iu4Op .framer-9ocb8t { flex: none; height: auto; position: relative; white-space: pre; width: auto; }", ".framer-Iu4Op .framer-12nyrxi { -webkit-user-select: none; align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 20px; height: min-content; justify-content: center; left: 0px; padding: 0px; pointer-events: none; position: absolute; top: 50%; user-select: none; width: min-content; z-index: -1; }", ".framer-Iu4Op .framer-125cew3 { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: flex-start; padding: 0px; position: relative; width: min-content; }", ".framer-Iu4Op .framer-10jxc9a { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: center; overflow: hidden; padding: 0px; position: relative; width: min-content; }", ".framer-Iu4Op .framer-1xqu72a { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 3px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: min-content; }", ".framer-Iu4Op .framer-g366ra, .framer-Iu4Op .framer-1wwmucy, .framer-Iu4Op .framer-ylz3x1 { flex: none; height: 8px; position: relative; width: 8px; }", ".framer-Iu4Op .framer-1nwqy7f { align-content: center; align-items: center; aspect-ratio: 1.3658536585365855 / 1; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 4px; height: var(--framer-aspect-ratio-supported, 411px); justify-content: center; overflow: hidden; padding: 0px; position: relative; width: 100%; will-change: var(--framer-will-change-override, transform); }", ".framer-Iu4Op .framer-jem6dr { aspect-ratio: 1.7875 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 112px); overflow: hidden; position: relative; width: 25%; z-index: 1; }", ".framer-Iu4Op .framer-ybsvxe { align-content: center; align-items: center; bottom: 4px; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 4px; justify-content: center; left: 4px; overflow: hidden; padding: 0px; position: absolute; right: 4px; top: 4px; will-change: var(--framer-will-change-override, transform); z-index: 0; }", ".framer-Iu4Op .framer-16bbbzt { bottom: 0px; flex: none; left: 0px; position: absolute; right: 0px; top: 0px; z-index: 2; }", ".framer-Iu4Op .framer-jh1lcx { bottom: 0px; flex: none; left: 0px; position: absolute; right: 0px; top: 0px; z-index: 1; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-Iu4Op.framer-1of3use, .framer-Iu4Op .framer-f2jpip, .framer-Iu4Op .framer-zgvrqz, .framer-Iu4Op .framer-12nyrxi, .framer-Iu4Op .framer-125cew3, .framer-Iu4Op .framer-10jxc9a, .framer-Iu4Op .framer-1xqu72a, .framer-Iu4Op .framer-1nwqy7f, .framer-Iu4Op .framer-ybsvxe { gap: 0px; } .framer-Iu4Op.framer-1of3use > *, .framer-Iu4Op .framer-1nwqy7f > * { margin: 0px; margin-bottom: calc(4px / 2); margin-top: calc(4px / 2); } .framer-Iu4Op.framer-1of3use > :first-child, .framer-Iu4Op .framer-12nyrxi > :first-child, .framer-Iu4Op .framer-1nwqy7f > :first-child { margin-top: 0px; } .framer-Iu4Op.framer-1of3use > :last-child, .framer-Iu4Op .framer-12nyrxi > :last-child, .framer-Iu4Op .framer-1nwqy7f > :last-child { margin-bottom: 0px; } .framer-Iu4Op .framer-f2jpip > * { margin: 0px; margin-left: calc(20px / 2); margin-right: calc(20px / 2); } .framer-Iu4Op .framer-f2jpip > :first-child, .framer-Iu4Op .framer-zgvrqz > :first-child, .framer-Iu4Op .framer-125cew3 > :first-child, .framer-Iu4Op .framer-10jxc9a > :first-child, .framer-Iu4Op .framer-1xqu72a > :first-child, .framer-Iu4Op .framer-ybsvxe > :first-child { margin-left: 0px; } .framer-Iu4Op .framer-f2jpip > :last-child, .framer-Iu4Op .framer-zgvrqz > :last-child, .framer-Iu4Op .framer-125cew3 > :last-child, .framer-Iu4Op .framer-10jxc9a > :last-child, .framer-Iu4Op .framer-1xqu72a > :last-child, .framer-Iu4Op .framer-ybsvxe > :last-child { margin-right: 0px; } .framer-Iu4Op .framer-zgvrqz > * { margin: 0px; margin-left: calc(18px / 2); margin-right: calc(18px / 2); } .framer-Iu4Op .framer-12nyrxi > * { margin: 0px; margin-bottom: calc(20px / 2); margin-top: calc(20px / 2); } .framer-Iu4Op .framer-125cew3 > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } .framer-Iu4Op .framer-10jxc9a > * { margin: 0px; margin-left: calc(0px / 2); margin-right: calc(0px / 2); } .framer-Iu4Op .framer-1xqu72a > * { margin: 0px; margin-left: calc(3px / 2); margin-right: calc(3px / 2); } .framer-Iu4Op .framer-ybsvxe > * { margin: 0px; margin-left: calc(4px / 2); margin-right: calc(4px / 2); } }", ".framer-Iu4Op.framer-v-syuufd.framer-1of3use { cursor: unset; }", ".framer-Iu4Op.framer-v-syuufd .framer-f2jpip { height: min-content; padding: 16px 20px 16px 20px; }", ".framer-Iu4Op.framer-v-syuufd .framer-zgvrqz { align-content: center; align-items: center; gap: 16px; }", ".framer-Iu4Op.framer-v-syuufd .framer-1xqu72a { gap: 2px; }", ".framer-Iu4Op.framer-v-syuufd .framer-g366ra, .framer-Iu4Op.framer-v-syuufd .framer-1wwmucy, .framer-Iu4Op.framer-v-syuufd .framer-ylz3x1 { aspect-ratio: 1 / 1; height: var(--framer-aspect-ratio-supported, 6px); width: 6px; }", ".framer-Iu4Op.framer-v-syuufd .framer-jem6dr { width: 36%; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-Iu4Op.framer-v-syuufd .framer-zgvrqz, .framer-Iu4Op.framer-v-syuufd .framer-1xqu72a { gap: 0px; } .framer-Iu4Op.framer-v-syuufd .framer-zgvrqz > * { margin: 0px; margin-left: calc(16px / 2); margin-right: calc(16px / 2); } .framer-Iu4Op.framer-v-syuufd .framer-zgvrqz > :first-child, .framer-Iu4Op.framer-v-syuufd .framer-1xqu72a > :first-child { margin-left: 0px; } .framer-Iu4Op.framer-v-syuufd .framer-zgvrqz > :last-child, .framer-Iu4Op.framer-v-syuufd .framer-1xqu72a > :last-child { margin-right: 0px; } .framer-Iu4Op.framer-v-syuufd .framer-1xqu72a > * { margin: 0px; margin-left: calc(2px / 2); margin-right: calc(2px / 2); } }", ".framer-Iu4Op.framer-v-1of3use.hover .framer-ybsvxe { bottom: 0px; left: 0px; right: 0px; top: 0px; }", ...css];
var FramereKMoUoN9m = withCSS(Component, css2, "framer-Iu4Op");
var stdin_default2 = FramereKMoUoN9m;
FramereKMoUoN9m.displayName = "Project card";
FramereKMoUoN9m.defaultProps = { height: 473, width: 562 };
addPropertyControls(FramereKMoUoN9m, { variant: { options: ["RLnNDkK1c", "kYpj4oxpj"], optionTitles: ["Desktop", "Phone"], title: "Variant", type: ControlType.Enum }, OuxDw9Wc4: { defaultValue: "Ephemeral", title: "Title", type: ControlType.String }, t8xTis15R: { dataIdentifier: "local-module:collection/qNTzVcuJS:default", title: "Category", type: ControlType.MultiCollectionReference }, x3OlI7F0c: { defaultValue: "2025", title: "Year", type: ControlType.String }, qZAUb4V0E: { title: "Preview image", type: ControlType.ResponsiveImage }, WJHOOjgGx: { title: "Logo", type: ControlType.ResponsiveImage }, EOzevFWPq: { title: "Link", type: ControlType.Link } });
addFonts(FramereKMoUoN9m, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5vvr9Vy74if2I6bQbJvbw7SY1pQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/EOr0mi4hNtlgWNn9if640EZzXCo.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/Y9k9QrlZAqio88Klkmbd8VoMQc.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/OYrD2tBIBPvoJXiIHnLoOXnY9M.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/JeYwfuaPfZHQhEG8U5gtPDZ7WQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/vQyevYAyHtARFwPqUzQGpnDs.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/b6Y37FthZeALduNqHicBT6FutY.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5A3Ce6C9YYmCjpQx9M4inSaKU.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/Qx95Xyt0Ka3SGhinnbXIGpEIyP4.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/6mJuEAguuIuMog10gGvH5d3cl8.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/xYYWaj7wCU5zSQH0eXvSaS19wo.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/otTaNuNpVK4RbdlT7zDDdKvQBA.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/d3tHnaQIAeqiE5hGcRw4mmgWYU.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/DolVirEGb34pEXEp8t8FQBSK4.woff2", weight: "500" }] }, ...getFontsFromSharedStyle(fonts)], { supportsExplicitInterCodegen: true });

export {
  stdin_default2 as stdin_default
};
