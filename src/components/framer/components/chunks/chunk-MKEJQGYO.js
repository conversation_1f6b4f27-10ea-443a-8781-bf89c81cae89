// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
// /:https://esm.sh/*@motionone/utils@10.18.0/node/utils.mjs
function h(o, r) {
  o.indexOf(r) === -1 && o.push(r);
}
var x = (o, r, e) => Math.min(Math.max(e, o), r);
var b = { duration: 0.3, delay: 0, endDelay: 0, repeat: 0, easing: "ease" };
var s = (o) => typeof o == "number";
var a = (o) => Array.isArray(o) && !s(o[0]);
var l = (o, r, e) => {
  let t = r - o;
  return ((e - o) % t + t) % t + o;
};
function g(o, r) {
  return a(o) ? o[l(0, o.length, r)] : o;
}
var i = (o, r, e) => -e * o + e * r + o;
var O = () => {
};
var u = (o) => o;
var f = (o, r, e) => r - o === 0 ? 1 : (e - o) / (r - o);
function m(o, r) {
  let e = o[o.length - 1];
  for (let t = 1; t <= r; t++) {
    let p = f(0, r, t);
    o.push(i(e, 1, p));
  }
}
function d(o) {
  let r = [0];
  return m(r, o - 1), r;
}
function S(o, r = d(o.length), e = u) {
  let t = o.length, p = t - r.length;
  return p > 0 && m(r, p), (y) => {
    let n = 0;
    for (; n < t - 2 && !(y < r[n + 1]); n++) ;
    let c = x(0, 1, f(r[n], r[n + 1], y));
    return c = g(e, n)(c), i(o[n], o[n + 1], c);
  };
}
var A = (o) => Array.isArray(o) && s(o[0]);
var F = (o) => typeof o == "object" && !!o.createAnimation;
var I = (o) => typeof o == "function";
var v = (o) => typeof o == "string";
var N = { ms: (o) => o * 1e3, s: (o) => o / 1e3 };
function R(o, r) {
  return r ? o * (1e3 / r) : 0;
}

// /:https://esm.sh/*@motionone/easing@10.18.0/node/easing.mjs
var s2 = (n, e, t) => (((1 - 3 * t + 3 * e) * n + (3 * t - 6 * e)) * n + 3 * e) * n;
var f2 = 1e-7;
var d2 = 12;
function b2(n, e, t, r, o) {
  let i2, c, u2 = 0;
  do
    c = e + (t - e) / 2, i2 = s2(c, r, o) - n, i2 > 0 ? t = c : e = c;
  while (Math.abs(i2) > f2 && ++u2 < d2);
  return c;
}
function l2(n, e, t, r) {
  if (n === e && t === r) return u;
  let o = (i2) => b2(i2, 0, 1, n, t);
  return (i2) => i2 === 0 || i2 === 1 ? i2 : s2(o(i2), e, r);
}
var h2 = (n, e = "end") => (t) => {
  t = e === "end" ? Math.min(t, 0.999) : Math.max(t, 1e-3);
  let r = t * n, o = e === "end" ? Math.floor(r) : Math.ceil(r);
  return x(0, 1, o / n);
};

export {
  h,
  b,
  s,
  a,
  O,
  u,
  f,
  S,
  A,
  F,
  I,
  v,
  N,
  R,
  l2 as l,
  h2
};
