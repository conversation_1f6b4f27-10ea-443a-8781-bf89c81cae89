// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
// /:https://framerusercontent.com/modules/ikVcV6kvZDj6uMF5EU26/KDHrdhjIRTgeMUBLQTNL/qBz_H6jdv.js
import { jsx as _jsx } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, RichText, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
var cycleOrder = ["evMN1mTNM", "ZPM_PrHzb", "wNhkD5cob"];
var serializationHash = "framer-uNobG";
var variantClassNames = { evMN1mTNM: "framer-v-1ppeyn8", wNhkD5cob: "framer-v-1igudlo", ZPM_PrHzb: "framer-v-ki7hw0" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { bounce: 0.2, delay: 0, duration: 0.4, type: "spring" };
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { Desktop: "evMN1mTNM", Phone: "wNhkD5cob", Tablet: "ZPM_PrHzb" };
var getProps = ({ color, height, id, width, ...props }) => {
  return { ...props, HZSElEzAS: color ?? props.HZSElEzAS ?? "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "evMN1mTNM" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const { activeLocale, setLocale } = useLocaleInfo();
  const { style, className, layoutId, variant, HZSElEzAS, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "evMN1mTNM", variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  const ref1 = React.useRef(null);
  const defaultLayoutId = React.useId();
  const componentViewport = useComponentViewport();
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsx(motion.div, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-1ppeyn8", className, classNames), "data-framer-name": "Desktop", layoutDependency, layoutId: "evMN1mTNM", ref: ref ?? ref1, style: { ...style }, ...addPropertyOverrides({ wNhkD5cob: { "data-framer-name": "Phone" }, ZPM_PrHzb: { "data-framer-name": "Tablet" } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "20px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "110%", "--framer-text-color": "var(--extracted-r6o4lv, var(--variable-reference-HZSElEzAS-qBz_H6jdv))" }, children: "fabrica\xAE " }) }), className: "framer-1lz6zv4", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "CKJr1zoZd", style: { "--extracted-r6o4lv": "var(--variable-reference-HZSElEzAS-qBz_H6jdv)", "--framer-paragraph-spacing": "0px", "--variable-reference-HZSElEzAS-qBz_H6jdv": HZSElEzAS }, verticalAlignment: "top", withExternalLayout: true, ...addPropertyOverrides({ wNhkD5cob: { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "18px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "110%", "--framer-text-color": "var(--extracted-r6o4lv, var(--variable-reference-HZSElEzAS-qBz_H6jdv))" }, children: "fabrica\xAE " }) }) }, ZPM_PrHzb: { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "19px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "110%", "--framer-text-color": "var(--extracted-r6o4lv, var(--variable-reference-HZSElEzAS-qBz_H6jdv))" }, children: "fabrica\xAE " }) }) } }, baseVariant, gestureVariant) }) }) }) }) });
});
var css = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-uNobG.framer-aa2ccv, .framer-uNobG .framer-aa2ccv { display: block; }", ".framer-uNobG.framer-1ppeyn8 { align-content: center; align-items: center; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: center; overflow: hidden; padding: 0px; position: relative; width: min-content; }", ".framer-uNobG .framer-1lz6zv4 { flex: none; height: auto; position: relative; white-space: pre; width: auto; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-uNobG.framer-1ppeyn8 { gap: 0px; } .framer-uNobG.framer-1ppeyn8 > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } .framer-uNobG.framer-1ppeyn8 > :first-child { margin-left: 0px; } .framer-uNobG.framer-1ppeyn8 > :last-child { margin-right: 0px; } }"];
var FramerqBz_H6jdv = withCSS(Component, css, "framer-uNobG");
var stdin_default = FramerqBz_H6jdv;
FramerqBz_H6jdv.displayName = "Logo";
FramerqBz_H6jdv.defaultProps = { height: 22, width: 79 };
addPropertyControls(FramerqBz_H6jdv, { variant: { options: ["evMN1mTNM", "ZPM_PrHzb", "wNhkD5cob"], optionTitles: ["Desktop", "Tablet", "Phone"], title: "Variant", type: ControlType.Enum }, HZSElEzAS: { defaultValue: 'var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10)) /* {"name":"Black"} */', title: "Color", type: ControlType.Color } });
addFonts(FramerqBz_H6jdv, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/hyOgCu0Xnghbimh0pE8QTvtt2AU.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/NeGmSOXrPBfEFIy5YZeHq17LEDA.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/oYaAX5himiTPYuN8vLWnqBbfD2s.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/lEJLP4R0yuCaMCjSXYHtJw72M.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/cRJyLNuTJR5jbyKzGi33wU9cqIQ.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/1ZFS7N918ojhhd0nQWdj3jz4w.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/A0Wcc7NgXMjUuFdquHDrIZpzZw0.woff2", weight: "600" }] }], { supportsExplicitInterCodegen: true });

export {
  stdin_default
};
