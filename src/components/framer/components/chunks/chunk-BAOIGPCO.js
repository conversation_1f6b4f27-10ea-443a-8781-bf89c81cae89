// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
import {
  stdin_default as stdin_default2
} from "./chunk-CVJIPDTS.js";
import {
  stdin_default
} from "./chunk-3LMAB7ZO.js";
import {
  className,
  css,
  fonts
} from "./chunk-YWUWNR35.js";

// /:https://framerusercontent.com/modules/mywbaSp9mF6R83DCrxI4/0MJDIxKkII1tq8lANCiM/dHpxp5ikb.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ComponentViewportProvider, ControlType, cx, getFonts, getFontsFromSharedStyle, getLoadingLazyAtYPosition, Image, ResolveLinks, RichText, SmartComponentScopedContainer, useActiveVariantCallback, useComponentViewport, useLocaleInfo, useRouter, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var SmallButtonFonts = getFonts(stdin_default2);
var PlusIconSmallFonts = getFonts(stdin_default);
var enabledGestures = { IjVHBd9Ch: { hover: true } };
var cycleOrder = ["IjVHBd9Ch", "af9TYEOm9", "RL7fKJ_oz"];
var serializationHash = "framer-FiNIy";
var variantClassNames = { af9TYEOm9: "framer-v-1p0w6q", IjVHBd9Ch: "framer-v-mjktsb", RL7fKJ_oz: "framer-v-npk9m3" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { delay: 0, duration: 0.5, ease: [0.82, 0.11, 0.37, 0.82], type: "tween" };
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { "Phone Click": "RL7fKJ_oz", Desktop: "IjVHBd9Ch", Phone: "af9TYEOm9" };
var getProps = ({ height, id, link, linkTitle, text, width, ...props }) => {
  return { ...props, hw6v_4Fbo: text ?? props.hw6v_4Fbo ?? "Text", u9qzj8Ajl: link ?? props.u9qzj8Ajl, variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "IjVHBd9Ch", XL_ELPYj5: linkTitle ?? props.XL_ELPYj5 ?? "Let's talk" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className: className2, layoutId, variant, hw6v_4Fbo, XL_ELPYj5, u9qzj8Ajl, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "IjVHBd9Ch", enabledGestures, ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const { activeVariantCallback, delay } = useActiveVariantCallback(baseVariant);
  const onTap1myd3wy = activeVariantCallback(async (...args) => {
    setGestureState({ isPressed: false });
    setVariant("RL7fKJ_oz");
  });
  const onTap1pn16g4 = activeVariantCallback(async (...args) => {
    setGestureState({ isPressed: false });
    setVariant("af9TYEOm9");
  });
  const sharedStyleClassNames = [className];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  const router = useRouter();
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsxs(motion.div, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-mjktsb", className2, classNames), "data-framer-name": "Desktop", layoutDependency, layoutId: "IjVHBd9Ch", ref: refBinding, style: { borderBottomLeftRadius: 18, borderBottomRightRadius: 18, borderTopLeftRadius: 18, borderTopRightRadius: 18, boxShadow: "none", ...style }, variants: { "IjVHBd9Ch-hover": { boxShadow: "0px 0.48174984141951427px 1.252549587690737px -1.0833333333333333px rgba(0, 0, 0, 0.13903), 0px 1.8308266425947657px 4.760149270746391px -2.1666666666666665px rgba(0, 0, 0, 0.1279), 0px 8px 20.8px -3.25px rgba(0, 0, 0, 0.077)" }, RL7fKJ_oz: { boxShadow: "0px 0.48174984141951427px 1.252549587690737px -1.0833333333333333px rgba(0, 0, 0, 0.13903), 0px 1.8308266425947657px 4.760149270746391px -2.1666666666666665px rgba(0, 0, 0, 0.1279), 0px 8px 20.8px -3.25px rgba(0, 0, 0, 0.077)" } }, ...addPropertyOverrides({ "IjVHBd9Ch-hover": { "data-framer-name": void 0 }, af9TYEOm9: { "data-framer-name": "Phone", "data-highlight": true, onTap: onTap1myd3wy }, RL7fKJ_oz: { "data-framer-name": "Phone Click", "data-highlight": true, onTap: onTap1pn16g4 } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsxs(motion.div, { className: "framer-mbu10z", "data-framer-name": "Text", layoutDependency, layoutId: "vLLXfkFDG", style: { opacity: 0 }, variants: { "IjVHBd9Ch-hover": { opacity: 1 }, RL7fKJ_oz: { opacity: 1 } }, children: [/* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-1n1wh7h", "data-styles-preset": "gd6AWaps9", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Your digital journey begins with a conversation. Let's talk today." }) }), className: "framer-j3uvcp", fonts: ["Inter"], layoutDependency, layoutId: "nFYqkJEze", style: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", "--framer-paragraph-spacing": "0px" }, text: hw6v_4Fbo, verticalAlignment: "top", withExternalLayout: true }), /* @__PURE__ */ _jsx(ResolveLinks, { links: [{ href: { webPageId: "hAlvrhFMj" }, implicitPathVariables: void 0 }, { href: { webPageId: "hAlvrhFMj" }, implicitPathVariables: void 0 }, { href: { webPageId: "hAlvrhFMj" }, implicitPathVariables: void 0 }], children: (resolvedLinks) => /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 30, y: (componentViewport?.y || 0) + (componentViewport?.height || 621) - 218 + 0 + 138, ...addPropertyOverrides({ "IjVHBd9Ch-hover": { y: (componentViewport?.y || 0) + 30 + ((componentViewport?.height || 621) - 60 - 168) + 0 + 138 }, RL7fKJ_oz: { y: (componentViewport?.y || 0) + 30 + ((componentViewport?.height || 621) - 60 - 168) + 0 + 138 } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-18x8z56-container", layoutDependency, layoutId: "XGYBY28Tw-container", nodeId: "XGYBY28Tw", rendersWithMotion: true, scopeId: "dHpxp5ikb", children: /* @__PURE__ */ _jsx(stdin_default2, { GK_bavkEs: resolvedLinks[0], height: "100%", id: "XGYBY28Tw", J1bovNTGx: "Let's talk", layoutId: "XGYBY28Tw", variant: "oEh913J4W", width: "100%", ...addPropertyOverrides({ "IjVHBd9Ch-hover": { GK_bavkEs: u9qzj8Ajl, J1bovNTGx: XL_ELPYj5 }, af9TYEOm9: { GK_bavkEs: resolvedLinks[1], variant: "efPssla96" }, RL7fKJ_oz: { GK_bavkEs: resolvedLinks[2], variant: "efPssla96" } }, baseVariant, gestureVariant) }) }) }) })] }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-dj4pjp", "data-framer-name": "BG", layoutDependency, layoutId: "dAQs8oCrk", style: { borderBottomLeftRadius: 18, borderBottomRightRadius: 18, borderTopLeftRadius: 18, borderTopRightRadius: 18 }, children: [/* @__PURE__ */ _jsx(motion.div, { className: "framer-d0lrfs", "data-framer-name": "Blackout", layoutDependency, layoutId: "YSWcqEd8Z", style: { backgroundColor: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", opacity: 0.2 } }), /* @__PURE__ */ _jsx(Image, { background: { alt: "Close-up portrait of a person wearing a gray insulated hooded jacket with the hood up, showing their face framed by the hood against a light background.", fit: "fill", intrinsicHeight: 932, intrinsicWidth: 566, loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0 + -1), pixelHeight: 932, pixelWidth: 566, sizes: `calc(${componentViewport?.width || "100vw"} + 2px)`, src: "https://framerusercontent.com/images/KL17tuoYHz5TzXCqskqaMY5Iw0.jpg", srcSet: "https://framerusercontent.com/images/KL17tuoYHz5TzXCqskqaMY5Iw0.jpg 566w" }, className: "framer-1lfeovs", "data-framer-name": "Image", layoutDependency, layoutId: "xJUjfNLFZ", style: { filter: "none", scale: 1, WebkitFilter: "none" }, variants: { "IjVHBd9Ch-hover": { filter: "blur(5px)", scale: 1.1, WebkitFilter: "blur(5px)" }, RL7fKJ_oz: { filter: "blur(5px)", scale: 1.1, WebkitFilter: "blur(5px)" } } }), /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 18, y: (componentViewport?.y || 0) + 0 + 20, children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-15e4rj6-container", layoutDependency, layoutId: "Se34jrNFr-container", nodeId: "Se34jrNFr", rendersWithMotion: true, scopeId: "dHpxp5ikb", style: { rotate: 0 }, variants: { "IjVHBd9Ch-hover": { rotate: 90 } }, children: /* @__PURE__ */ _jsx(stdin_default, { CgCxwDz_B: true, CquvwTJCF: "rgb(255, 255, 255)", height: "100%", id: "Se34jrNFr", L_MXIE6eA: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", layoutId: "Se34jrNFr", width: "100%" }) }) })] })] }) }) }) });
});
var css2 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-FiNIy.framer-i0xhmi, .framer-FiNIy .framer-i0xhmi { display: block; }", ".framer-FiNIy.framer-mjktsb { align-content: flex-end; align-items: flex-end; cursor: default; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 4px; height: 621px; justify-content: center; overflow: visible; padding: 30px; position: relative; width: 377px; }", ".framer-FiNIy .framer-mbu10z { align-content: flex-start; align-items: flex-start; bottom: 50px; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 26px; height: min-content; justify-content: center; left: 30px; overflow: visible; padding: 0px; position: absolute; width: min-content; z-index: 2; }", ".framer-FiNIy .framer-j3uvcp { flex: none; height: auto; position: relative; white-space: pre-wrap; width: 317px; word-break: break-word; word-wrap: break-word; }", ".framer-FiNIy .framer-18x8z56-container { flex: none; height: auto; position: relative; width: auto; }", ".framer-FiNIy .framer-dj4pjp { align-content: center; align-items: center; bottom: 0px; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 4px; justify-content: center; left: 0px; overflow: hidden; padding: 0px; position: absolute; right: 0px; top: 0px; will-change: var(--framer-will-change-override, transform); z-index: 1; }", ".framer-FiNIy .framer-d0lrfs { bottom: 0px; flex: none; left: 0px; overflow: visible; position: absolute; right: 0px; top: 0px; z-index: 1; }", ".framer-FiNIy .framer-1lfeovs { bottom: -1px; flex: none; left: -1px; overflow: visible; position: absolute; right: -1px; top: -1px; z-index: 0; }", ".framer-FiNIy .framer-15e4rj6-container { flex: none; height: auto; position: absolute; right: 20px; top: 20px; width: auto; z-index: 1; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-FiNIy.framer-mjktsb, .framer-FiNIy .framer-mbu10z, .framer-FiNIy .framer-dj4pjp { gap: 0px; } .framer-FiNIy.framer-mjktsb > * { margin: 0px; margin-left: calc(4px / 2); margin-right: calc(4px / 2); } .framer-FiNIy.framer-mjktsb > :first-child { margin-left: 0px; } .framer-FiNIy.framer-mjktsb > :last-child { margin-right: 0px; } .framer-FiNIy .framer-mbu10z > * { margin: 0px; margin-bottom: calc(26px / 2); margin-top: calc(26px / 2); } .framer-FiNIy .framer-mbu10z > :first-child, .framer-FiNIy .framer-dj4pjp > :first-child { margin-top: 0px; } .framer-FiNIy .framer-mbu10z > :last-child, .framer-FiNIy .framer-dj4pjp > :last-child { margin-bottom: 0px; } .framer-FiNIy .framer-dj4pjp > * { margin: 0px; margin-bottom: calc(4px / 2); margin-top: calc(4px / 2); } }", ".framer-FiNIy.framer-v-npk9m3 .framer-mbu10z, .framer-FiNIy.framer-v-mjktsb.hover .framer-mbu10z { bottom: unset; flex: 1 0 0px; left: unset; position: relative; width: 1px; }", ".framer-FiNIy.framer-v-npk9m3 .framer-j3uvcp, .framer-FiNIy.framer-v-mjktsb.hover .framer-j3uvcp { max-width: 320px; width: 100%; }", ...css];
var FramerdHpxp5ikb = withCSS(Component, css2, "framer-FiNIy");
var stdin_default3 = FramerdHpxp5ikb;
FramerdHpxp5ikb.displayName = "Banner";
FramerdHpxp5ikb.defaultProps = { height: 621, width: 377 };
addPropertyControls(FramerdHpxp5ikb, { variant: { options: ["IjVHBd9Ch", "af9TYEOm9", "RL7fKJ_oz"], optionTitles: ["Desktop", "Phone", "Phone Click"], title: "Variant", type: ControlType.Enum }, hw6v_4Fbo: { defaultValue: "Text", displayTextArea: true, title: "Text", type: ControlType.String }, XL_ELPYj5: { defaultValue: "Let's talk", displayTextArea: true, title: "Link title", type: ControlType.String }, u9qzj8Ajl: { title: "Link", type: ControlType.Link } });
addFonts(FramerdHpxp5ikb, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5vvr9Vy74if2I6bQbJvbw7SY1pQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/EOr0mi4hNtlgWNn9if640EZzXCo.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/Y9k9QrlZAqio88Klkmbd8VoMQc.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/OYrD2tBIBPvoJXiIHnLoOXnY9M.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/JeYwfuaPfZHQhEG8U5gtPDZ7WQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/vQyevYAyHtARFwPqUzQGpnDs.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/b6Y37FthZeALduNqHicBT6FutY.woff2", weight: "400" }] }, ...SmallButtonFonts, ...PlusIconSmallFonts, ...getFontsFromSharedStyle(fonts)], { supportsExplicitInterCodegen: true });

export {
  stdin_default3 as stdin_default
};
