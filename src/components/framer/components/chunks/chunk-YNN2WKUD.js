// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
// /:https://framerusercontent.com/modules/4AfGmdzrVIJOiclhVWW5/wDcr6iY79T92uICesIJd/BvyOYDKED.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var serializationHash = "framer-ZPULr";
var variantClassNames = { SGmNeATQX: "framer-v-1xcu7rj" };
var transition1 = { bounce: 0.2, delay: 0, duration: 0.4, type: "spring" };
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var getProps = ({ color, height, id, showVertical, width, ...props }) => {
  return { ...props, gUDzlhgLq: showVertical ?? props.gUDzlhgLq ?? true, Rl_qLe3MC: color ?? props.Rl_qLe3MC ?? "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className, layoutId, variant, Rl_qLe3MC, gUDzlhgLq, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ defaultVariant: "SGmNeATQX", ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsxs(motion.div, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-1xcu7rj", className, classNames), "data-framer-name": "Variant 1", layoutDependency, layoutId: "SGmNeATQX", ref: refBinding, style: { ...style }, children: [gUDzlhgLq && /* @__PURE__ */ _jsx(motion.div, { className: "framer-2rakpf", "data-framer-name": "V", layoutDependency, layoutId: "izBaNmQax", style: { backgroundColor: Rl_qLe3MC } }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-48ytin", "data-framer-name": "H", layoutDependency, layoutId: "sgcbBsV6E", style: { backgroundColor: Rl_qLe3MC, rotate: 90 } })] }) }) }) });
});
var css = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-ZPULr.framer-mnr6bg, .framer-ZPULr .framer-mnr6bg { display: block; }", ".framer-ZPULr.framer-1xcu7rj { height: 16px; overflow: visible; position: relative; width: 16px; }", ".framer-ZPULr .framer-2rakpf, .framer-ZPULr .framer-48ytin { bottom: 0px; flex: none; left: calc(50.00000000000002% - 2px / 2); overflow: visible; position: absolute; top: 0px; width: 2px; }"];
var FramerBvyOYDKED = withCSS(Component, css, "framer-ZPULr");
var stdin_default = FramerBvyOYDKED;
FramerBvyOYDKED.displayName = "Plus icon";
FramerBvyOYDKED.defaultProps = { height: 16, width: 16 };
addPropertyControls(FramerBvyOYDKED, { Rl_qLe3MC: { defaultValue: 'var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10)) /* {"name":"Black"} */', title: "Color", type: ControlType.Color }, gUDzlhgLq: { defaultValue: true, title: "Show vertical", type: ControlType.Boolean } });
addFonts(FramerBvyOYDKED, [{ explicitInter: true, fonts: [] }], { supportsExplicitInterCodegen: true });

export {
  stdin_default
};
