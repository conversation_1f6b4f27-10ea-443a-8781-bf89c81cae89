// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
import {
  stdin_default as stdin_default2
} from "./chunk-NFATBK6V.js";
import {
  className as className3,
  css as css3,
  fonts as fonts3
} from "./chunk-45SLURIC.js";
import {
  stdin_default
} from "./chunk-YNN2WKUD.js";
import {
  className as className2,
  css as css2,
  fonts as fonts2
} from "./chunk-SIWCYXHC.js";
import {
  className as className4,
  css as css4,
  fonts as fonts4
} from "./chunk-VY5WWL2S.js";
import {
  className,
  css,
  fonts
} from "./chunk-YWUWNR35.js";

// /:https://framerusercontent.com/modules/8xf9SmDnMz8gG4sF4hpH/tLrkAll785TqtzKVhiuu/fkNrtm8z2.js
import { jsx as _jsx3, jsxs as _jsxs2 } from "react/jsx-runtime";
import { addFonts as addFonts2, addPropertyControls as addPropertyControls3, ComponentViewportProvider as ComponentViewportProvider2, ControlType as ControlType3, cx as cx2, getFonts as getFonts2, SmartComponentScopedContainer as SmartComponentScopedContainer2, useComponentViewport as useComponentViewport2, useLocaleInfo as useLocaleInfo2, useVariantState as useVariantState2, withCSS as withCSS2 } from "unframer";
import { LayoutGroup as LayoutGroup2, motion as motion2, MotionConfigContext as MotionConfigContext2 } from "unframer";
import * as React2 from "react";
import { useRef as useRef3 } from "react";

// /:https://framerusercontent.com/modules/h8ioHyt4BUxuvVCE1Q10/aTM6mEMGlM19JjX8exDe/AvoidLayoutJumping_Prod.js
import { jsx as _jsx } from "react/jsx-runtime";
import { addPropertyControls, ControlType, RenderTarget } from "unframer";
import { useEffect, useRef } from "react";
function AvoidLayoutJumping({ direction, style }) {
  const isCanvas = RenderTarget.current() === RenderTarget.canvas;
  const ref = useRef(null);
  const rafId = useRef();
  const vertical = direction === "vertical" || direction === "both";
  const horizontal = direction === "horizontal" || direction === "both";
  useEffect(() => {
    if (isCanvas) return;
    const parent = ref.current?.parentElement?.parentElement;
    if (!parent) return;
    const container = parent.parentElement;
    if (!container) return;
    const updateSize = () => {
      const rect = parent.getBoundingClientRect();
      if (horizontal) {
        container.style.width = `${rect.width}px`;
      }
      if (vertical) {
        container.style.height = `${rect.height}px`;
      }
      rafId.current = requestAnimationFrame(updateSize);
    };
    rafId.current = requestAnimationFrame(updateSize);
    return () => {
      if (rafId.current) {
        cancelAnimationFrame(rafId.current);
      }
      if (container) {
        if (horizontal) {
          container.style.width = "";
        }
        if (vertical) {
          container.style.height = "";
        }
      }
    };
  }, [direction]);
  return /* @__PURE__ */ _jsx("div", { ref, style: { ...style } });
}
AvoidLayoutJumping.displayName = "Layout Jump Preventer";
addPropertyControls(AvoidLayoutJumping, { direction: {
  type: ControlType.Enum,
  defaultValue: "vertical",
  options: ["vertical", "horizontal", "both"],
  optionTitles: ["Vertical", "Horizontal", "Both"],
  displaySegmentedControl: true,
  segmentedControlDirection: "vertical",
  // @ts-ignore
  optionIcons: ["direction-vertical", "direction-horizontal", "direction-all"],
  description: "More components at [Framer University](https://frameruni.link/cc)."
} });

// /:https://framerusercontent.com/modules/e47Mqi2vDwcso9yvGLGl/A5uhMJoa81U51rSVS7ew/zKohnBGrW.js
import { jsx as _jsx2, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls as addPropertyControls2, ComponentViewportProvider, ControlType as ControlType2, cx, getFonts, getFontsFromSharedStyle, Image, RichText, SmartComponentScopedContainer, useActiveVariantCallback, useComponentViewport, useLocaleInfo, useVariantState, withCSS, withFX } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef as useRef2 } from "react";
var CategoryFonts = getFonts(stdin_default2);
var PlusIconFonts = getFonts(stdin_default);
var MotionDivWithFX = withFX(motion.div);
var cycleOrder = ["CgQ12EskR", "dhCQszsnX", "Ve10kfITC", "B2uVs0yE2", "Wi4tFM7FN", "st3rodc5M"];
var serializationHash = "framer-Rs4As";
var variantClassNames = { B2uVs0yE2: "framer-v-43amvy", CgQ12EskR: "framer-v-1nd063s", dhCQszsnX: "framer-v-1cek2le", st3rodc5M: "framer-v-1ok7lm2", Ve10kfITC: "framer-v-13mbndl", Wi4tFM7FN: "framer-v-dzpdh" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var animation = { opacity: 0, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, x: 0, y: 0 };
var transition1 = { bounce: 0.1, delay: 0.2, duration: 1.2, type: "spring" };
var transition2 = { delay: 0, duration: 0.4, ease: [0.82, 0.11, 0.37, 0.82], type: "tween" };
var isSet = (value) => {
  if (Array.isArray(value)) return value.length > 0;
  return value !== void 0 && value !== null && value !== "";
};
var toResponsiveImage = (value) => {
  if (typeof value === "object" && value !== null && typeof value.src === "string") {
    return value;
  }
  return typeof value === "string" ? { src: value } : void 0;
};
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx2(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { "Desktop closed": "dhCQszsnX", "Desktop open": "CgQ12EskR", "Phone closed": "st3rodc5M", "Phone open": "Wi4tFM7FN", "Tablet closed": "B2uVs0yE2", "Tablet open": "Ve10kfITC" };
var getProps = ({ _01Category, _01Image, _02Category, _02Image, _03Category, _03Image, _04Category, _05Category, _06Category, amount, description, height, id, label, name1, number, width, ...props }) => {
  return { ...props, A55R9swTB: _04Category ?? props.A55R9swTB ?? "Typography", Axz4_1jdS: amount ?? props.Axz4_1jdS ?? "7+", BM5PQSKQ4: _01Category ?? props.BM5PQSKQ4 ?? "Packaging design", CgqILTR_F: number ?? props.CgqILTR_F ?? "(001)", D5EIDF1hM: _02Category ?? props.D5EIDF1hM ?? "Logo design", h9KdJ0MhX: description ?? props.h9KdJ0MhX ?? "Modern, responsive, and user-friendly websites designed to engage visitors and drive conversions.", KAi5r409E: _06Category ?? props.KAi5r409E ?? "Visual identity", kraMjzSa3: _03Category ?? props.kraMjzSa3 ?? "Rebranding", rGrSa8vGT: _03Image ?? props.rGrSa8vGT ?? { alt: "", src: "https://framerusercontent.com/images/6girwIRKdg1doDEWAHr4oDIbroU.jpg" }, rQrZWdPlR: _01Image ?? props.rQrZWdPlR ?? { alt: "", src: "https://framerusercontent.com/images/vGSJoy0fkCYvuK5CETUzS64NNo.jpg" }, Sl057QITc: _05Category ?? props.Sl057QITc ?? "Guidelines", snuND8XMP: _02Image ?? props.snuND8XMP ?? { alt: "", src: "https://framerusercontent.com/images/6xxZ3D6rnu26P86nUVvj2eanCY.jpg" }, uXdq8Ygre: name1 ?? props.uXdq8Ygre ?? "Web design and development", variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "CgQ12EskR", wjU0kH4We: label ?? props.wjU0kH4We ?? "Categories" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef2(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className: className5, layoutId, variant, CgqILTR_F, uXdq8Ygre, h9KdJ0MhX, rQrZWdPlR, snuND8XMP, rGrSa8vGT, wjU0kH4We, BM5PQSKQ4, D5EIDF1hM, kraMjzSa3, A55R9swTB, Sl057QITc, KAi5r409E, Axz4_1jdS, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "CgQ12EskR", ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const { activeVariantCallback, delay } = useActiveVariantCallback(baseVariant);
  const onTapjsy8ok = activeVariantCallback(async (...args) => {
    setGestureState({ isPressed: false });
    setVariant("dhCQszsnX");
  });
  const onTap1qg85yv = activeVariantCallback(async (...args) => {
    setGestureState({ isPressed: false });
    setVariant("CgQ12EskR");
  });
  const onTapixx6c5 = activeVariantCallback(async (...args) => {
    setGestureState({ isPressed: false });
    setVariant("B2uVs0yE2");
  });
  const onTap170jwlb = activeVariantCallback(async (...args) => {
    setGestureState({ isPressed: false });
    setVariant("Ve10kfITC");
  });
  const onTapnhk6c2 = activeVariantCallback(async (...args) => {
    setGestureState({ isPressed: false });
    setVariant("st3rodc5M");
  });
  const onTap1focs21 = activeVariantCallback(async (...args) => {
    setGestureState({ isPressed: false });
    setVariant("Wi4tFM7FN");
  });
  const sharedStyleClassNames = [className, className3, className4, className2];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  const visible = isSet(rQrZWdPlR);
  const visible1 = isSet(snuND8XMP);
  const visible2 = isSet(rGrSa8vGT);
  const visible3 = isSet(BM5PQSKQ4);
  const visible4 = isSet(D5EIDF1hM);
  const visible5 = isSet(kraMjzSa3);
  const visible6 = isSet(A55R9swTB);
  const visible7 = isSet(Sl057QITc);
  const visible8 = isSet(KAi5r409E);
  const visible9 = isSet(Axz4_1jdS);
  return /* @__PURE__ */ _jsx2(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx2(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx2(Transition, { value: transition2, children: /* @__PURE__ */ _jsxs(MotionDivWithFX, { ...restProps, ...gestureHandlers, __framer__animate: { transition: transition1 }, __framer__animateOnce: true, __framer__enter: animation, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: cx(scopingClassNames, "framer-1nd063s", className5, classNames), "data-border": true, "data-framer-name": "Desktop open", "data-highlight": true, layoutDependency, layoutId: "CgQ12EskR", onTap: onTapjsy8ok, ref: refBinding, style: { "--border-bottom-width": "1px", "--border-color": "rgba(255, 255, 255, 0.1)", "--border-left-width": "0px", "--border-right-width": "0px", "--border-style": "solid", "--border-top-width": "0px", ...style }, variants: { dhCQszsnX: { "--border-bottom-width": "0px" } }, ...addPropertyOverrides({ B2uVs0yE2: { "data-framer-name": "Tablet closed", onTap: onTap170jwlb }, dhCQszsnX: { "data-framer-name": "Desktop closed", onTap: onTap1qg85yv }, st3rodc5M: { "data-framer-name": "Phone closed", onTap: onTap1focs21 }, Ve10kfITC: { "data-framer-name": "Tablet open", onTap: onTapixx6c5 }, Wi4tFM7FN: { "data-framer-name": "Phone open", onTap: onTapnhk6c2 } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsx2(motion.div, { className: "framer-wos98z", "data-framer-name": "1", layoutDependency, layoutId: "CfA5TgtWM", children: /* @__PURE__ */ _jsx2(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx2(React.Fragment, { children: /* @__PURE__ */ _jsx2(motion.p, { className: "framer-styles-preset-1n1wh7h", "data-styles-preset": "gd6AWaps9", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "(001)" }) }), className: "framer-vxijqi", fonts: ["Inter"], layoutDependency, layoutId: "g_S7LmJ4u", style: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", "--framer-paragraph-spacing": "0px", opacity: 1 }, text: CgqILTR_F, variants: { B2uVs0yE2: { opacity: 0.6 }, dhCQszsnX: { opacity: 0.6 }, st3rodc5M: { opacity: 0.6 }, Ve10kfITC: { opacity: 1 }, Wi4tFM7FN: { opacity: 1 } }, verticalAlignment: "top", withExternalLayout: true }) }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-1ox4qjh", "data-framer-name": "2", layoutDependency, layoutId: "yc1pxkbYd", style: { "--border-bottom-width": "0px", "--border-color": "rgba(0, 0, 0, 0)", "--border-left-width": "0px", "--border-right-width": "0px", "--border-style": "solid", "--border-top-width": "0px" }, variants: { B2uVs0yE2: { "--border-bottom-width": "0px", "--border-left-width": "0px", "--border-right-width": "0px", "--border-top-width": "0px" }, dhCQszsnX: { "--border-bottom-width": "1px", "--border-color": "rgba(255, 255, 255, 0.1)", "--border-left-width": "0px", "--border-right-width": "0px", "--border-style": "solid", "--border-top-width": "0px" }, st3rodc5M: { "--border-bottom-width": "0px", "--border-left-width": "0px", "--border-right-width": "0px", "--border-top-width": "0px" }, Ve10kfITC: { "--border-bottom-width": "0px", "--border-left-width": "0px", "--border-right-width": "0px", "--border-top-width": "0px" }, Wi4tFM7FN: { "--border-bottom-width": "0px", "--border-left-width": "0px", "--border-right-width": "0px", "--border-top-width": "0px" } }, ...addPropertyOverrides({ dhCQszsnX: { "data-border": true } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsxs(motion.div, { className: "framer-174h6l6", "data-framer-name": "Text", layoutDependency, layoutId: "bxbYrchtJ", style: { opacity: 1 }, variants: { B2uVs0yE2: { opacity: 0 }, dhCQszsnX: { opacity: 0 }, st3rodc5M: { opacity: 0 }, Ve10kfITC: { opacity: 1 }, Wi4tFM7FN: { opacity: 1 } }, children: [/* @__PURE__ */ _jsxs(motion.div, { className: "framer-1qdqpra", "data-framer-name": "1", layoutDependency, layoutId: "HoiXRxt9M", children: [/* @__PURE__ */ _jsxs(motion.div, { className: "framer-1t0yt9s", "data-framer-name": "Images", layoutDependency, layoutId: "rlb2goPWu", children: [visible && /* @__PURE__ */ _jsx2(Image, { background: { alt: "", fit: "fill", intrinsicHeight: 140, intrinsicWidth: 140, pixelHeight: 140, pixelWidth: 140, sizes: "80px", ...toResponsiveImage(rQrZWdPlR) }, className: "framer-knkn1", "data-framer-name": "Image", layoutDependency, layoutId: "glE2KICd7", style: { borderBottomLeftRadius: 12, borderBottomRightRadius: 12, borderTopLeftRadius: 12, borderTopRightRadius: 12 }, ...addPropertyOverrides({ st3rodc5M: { background: { alt: "", fit: "fill", intrinsicHeight: 140, intrinsicWidth: 140, pixelHeight: 140, pixelWidth: 140, sizes: "60px", ...toResponsiveImage(rQrZWdPlR) } }, Wi4tFM7FN: { background: { alt: "", fit: "fill", intrinsicHeight: 140, intrinsicWidth: 140, pixelHeight: 140, pixelWidth: 140, sizes: "60px", ...toResponsiveImage(rQrZWdPlR) } } }, baseVariant, gestureVariant) }), visible1 && /* @__PURE__ */ _jsx2(Image, { background: { alt: "", fit: "fill", intrinsicHeight: 140, intrinsicWidth: 140, pixelHeight: 140, pixelWidth: 140, sizes: "80px", ...toResponsiveImage(snuND8XMP) }, className: "framer-rbfec2", "data-framer-name": "Image-1", layoutDependency, layoutId: "Nmlg9N1EV", style: { borderBottomLeftRadius: 14, borderBottomRightRadius: 14, borderTopLeftRadius: 14, borderTopRightRadius: 14, boxShadow: "-0.8002510761181474px 0px 1.7605523674599244px -0.7000000000000001px rgba(0, 0, 0, 0.39912), -2.1792961931685566px 0px 4.794451624970825px -1.4000000000000001px rgba(0, 0, 0, 0.38727), -4.784945682594844px 0px 10.526880501708659px -2.1px rgba(0, 0, 0, 0.36488), -10.621486890826201px 0px 23.367271159817644px -2.8000000000000003px rgba(0, 0, 0, 0.31473), -27px 0px 59.400000000000006px -3.5px rgba(0, 0, 0, 0.174)" }, ...addPropertyOverrides({ st3rodc5M: { background: { alt: "", fit: "fill", intrinsicHeight: 140, intrinsicWidth: 140, pixelHeight: 140, pixelWidth: 140, sizes: "60px", ...toResponsiveImage(snuND8XMP) } }, Wi4tFM7FN: { background: { alt: "", fit: "fill", intrinsicHeight: 140, intrinsicWidth: 140, pixelHeight: 140, pixelWidth: 140, sizes: "60px", ...toResponsiveImage(snuND8XMP) } } }, baseVariant, gestureVariant) }), visible2 && /* @__PURE__ */ _jsx2(Image, { background: { alt: "", fit: "fill", intrinsicHeight: 140, intrinsicWidth: 140, pixelHeight: 140, pixelWidth: 140, sizes: "80px", ...toResponsiveImage(rGrSa8vGT) }, className: "framer-38v78q", "data-framer-name": "Image-2", layoutDependency, layoutId: "sv7tv0Qbb", style: { borderBottomLeftRadius: 14, borderBottomRightRadius: 14, borderTopLeftRadius: 14, borderTopRightRadius: 14, boxShadow: "-0.8002510761181474px 0px 1.7605523674599244px -0.7000000000000001px rgba(0, 0, 0, 0.39912), -2.1792961931685566px 0px 4.794451624970825px -1.4000000000000001px rgba(0, 0, 0, 0.38727), -4.784945682594844px 0px 10.526880501708659px -2.1px rgba(0, 0, 0, 0.36488), -10.621486890826201px 0px 23.367271159817644px -2.8000000000000003px rgba(0, 0, 0, 0.31473), -27px 0px 59.400000000000006px -3.5px rgba(0, 0, 0, 0.174)" }, ...addPropertyOverrides({ st3rodc5M: { background: { alt: "", fit: "fill", intrinsicHeight: 140, intrinsicWidth: 140, pixelHeight: 140, pixelWidth: 140, sizes: "60px", ...toResponsiveImage(rGrSa8vGT) } }, Wi4tFM7FN: { background: { alt: "", fit: "fill", intrinsicHeight: 140, intrinsicWidth: 140, pixelHeight: 140, pixelWidth: 140, sizes: "60px", ...toResponsiveImage(rGrSa8vGT) } } }, baseVariant, gestureVariant) })] }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-14ruax2", "data-framer-name": "Service Info", layoutDependency, layoutId: "O4w4apYOJ", children: [/* @__PURE__ */ _jsx2(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx2(React.Fragment, { children: /* @__PURE__ */ _jsx2(motion.p, { className: "framer-styles-preset-1rii1wr", "data-styles-preset": "pAxoS1kOX", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Web design and development" }) }), className: "framer-1xsxun7", fonts: ["Inter"], layoutDependency, layoutId: "YeUv35BWL", style: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", "--framer-paragraph-spacing": "0px" }, text: uXdq8Ygre, verticalAlignment: "top", withExternalLayout: true }), /* @__PURE__ */ _jsx2(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx2(React.Fragment, { children: /* @__PURE__ */ _jsx2(motion.p, { className: "framer-styles-preset-1n1wh7h", "data-styles-preset": "gd6AWaps9", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Modern, responsive, and user-friendly websites designed to engage visitors and drive conversions." }) }), className: "framer-vli817", fonts: ["Inter"], layoutDependency, layoutId: "sSkK4Utmh", style: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", "--framer-paragraph-spacing": "0px", opacity: 0.6 }, text: h9KdJ0MhX, verticalAlignment: "top", withExternalLayout: true })] })] }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-obmt7x", "data-framer-name": "Categories", layoutDependency, layoutId: "IMY0sElhj", children: [/* @__PURE__ */ _jsx2(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx2(React.Fragment, { children: /* @__PURE__ */ _jsx2(motion.p, { className: "framer-styles-preset-2s58fc", "data-styles-preset": "svYtzYwMA", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Categories" }) }), className: "framer-17jid8u", fonts: ["Inter"], layoutDependency, layoutId: "ne44yG0fq", style: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", "--framer-paragraph-spacing": "0px", opacity: 0.6 }, text: wjU0kH4We, verticalAlignment: "top", withExternalLayout: true }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-1yd713w", "data-framer-name": "Items", layoutDependency, layoutId: "vl7nUocU6", children: [visible3 && /* @__PURE__ */ _jsx2(ComponentViewportProvider, { height: 30, children: /* @__PURE__ */ _jsx2(SmartComponentScopedContainer, { className: "framer-wtbzr0-container", layoutDependency, layoutId: "BAv3e0azh-container", nodeId: "BAv3e0azh", rendersWithMotion: true, scopeId: "zKohnBGrW", children: /* @__PURE__ */ _jsx2(stdin_default2, { height: "100%", id: "BAv3e0azh", layoutId: "BAv3e0azh", width: "100%", X9u8fXHAT: BM5PQSKQ4 }) }) }), visible4 && /* @__PURE__ */ _jsx2(ComponentViewportProvider, { height: 30, children: /* @__PURE__ */ _jsx2(SmartComponentScopedContainer, { className: "framer-1pqxl5f-container", layoutDependency, layoutId: "YDaiRyTKS-container", nodeId: "YDaiRyTKS", rendersWithMotion: true, scopeId: "zKohnBGrW", children: /* @__PURE__ */ _jsx2(stdin_default2, { height: "100%", id: "YDaiRyTKS", layoutId: "YDaiRyTKS", width: "100%", X9u8fXHAT: D5EIDF1hM }) }) }), visible5 && /* @__PURE__ */ _jsx2(ComponentViewportProvider, { height: 30, children: /* @__PURE__ */ _jsx2(SmartComponentScopedContainer, { className: "framer-h4tz30-container", layoutDependency, layoutId: "PlN2LJiWT-container", nodeId: "PlN2LJiWT", rendersWithMotion: true, scopeId: "zKohnBGrW", children: /* @__PURE__ */ _jsx2(stdin_default2, { height: "100%", id: "PlN2LJiWT", layoutId: "PlN2LJiWT", width: "100%", X9u8fXHAT: kraMjzSa3 }) }) }), visible6 && /* @__PURE__ */ _jsx2(ComponentViewportProvider, { height: 30, children: /* @__PURE__ */ _jsx2(SmartComponentScopedContainer, { className: "framer-jeszxr-container", layoutDependency, layoutId: "lAPG02YVT-container", nodeId: "lAPG02YVT", rendersWithMotion: true, scopeId: "zKohnBGrW", children: /* @__PURE__ */ _jsx2(stdin_default2, { height: "100%", id: "lAPG02YVT", layoutId: "lAPG02YVT", width: "100%", X9u8fXHAT: A55R9swTB }) }) }), visible7 && /* @__PURE__ */ _jsx2(ComponentViewportProvider, { height: 30, children: /* @__PURE__ */ _jsx2(SmartComponentScopedContainer, { className: "framer-1g1rklt-container", layoutDependency, layoutId: "vG0ClFqJi-container", nodeId: "vG0ClFqJi", rendersWithMotion: true, scopeId: "zKohnBGrW", children: /* @__PURE__ */ _jsx2(stdin_default2, { height: "100%", id: "vG0ClFqJi", layoutId: "vG0ClFqJi", width: "100%", X9u8fXHAT: Sl057QITc }) }) }), visible8 && /* @__PURE__ */ _jsx2(ComponentViewportProvider, { height: 30, children: /* @__PURE__ */ _jsx2(SmartComponentScopedContainer, { className: "framer-3bxzad-container", layoutDependency, layoutId: "S1lVN7uCe-container", nodeId: "S1lVN7uCe", rendersWithMotion: true, scopeId: "zKohnBGrW", children: /* @__PURE__ */ _jsx2(stdin_default2, { height: "100%", id: "S1lVN7uCe", layoutId: "S1lVN7uCe", width: "100%", X9u8fXHAT: KAi5r409E }) }) }), visible9 && /* @__PURE__ */ _jsx2(motion.div, { className: "framer-k21hu0", "data-framer-name": "Number", layoutDependency, layoutId: "AbPoHDADR", style: { backgroundColor: "rgb(39, 39, 39)", borderBottomLeftRadius: 50, borderBottomRightRadius: 50, borderTopLeftRadius: 50, borderTopRightRadius: 50 }, children: /* @__PURE__ */ _jsx2(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx2(React.Fragment, { children: /* @__PURE__ */ _jsx2(motion.p, { className: "framer-styles-preset-2s58fc", "data-styles-preset": "svYtzYwMA", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "7+" }) }), className: "framer-a7z6k3", fonts: ["Inter"], layoutDependency, layoutId: "sqlD3lnXm", style: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", "--framer-paragraph-spacing": "0px" }, text: Axz4_1jdS, verticalAlignment: "top", withExternalLayout: true }) })] })] })] }), /* @__PURE__ */ _jsx2(motion.div, { className: "framer-koycop", "data-border": true, "data-framer-name": "Button", layoutDependency, layoutId: "irOcpV35e", style: { "--border-bottom-width": "1px", "--border-color": "rgb(49, 49, 49)", "--border-left-width": "1px", "--border-right-width": "1px", "--border-style": "solid", "--border-top-width": "1px", borderBottomLeftRadius: "100%", borderBottomRightRadius: "100%", borderTopLeftRadius: "100%", borderTopRightRadius: "100%", rotate: 0 }, variants: { B2uVs0yE2: { rotate: 270 }, dhCQszsnX: { rotate: 270 }, st3rodc5M: { rotate: 270 }, Ve10kfITC: { rotate: 0 }, Wi4tFM7FN: { rotate: 0 } }, children: /* @__PURE__ */ _jsx2(ComponentViewportProvider, { height: 16, width: "16px", ...addPropertyOverrides({ B2uVs0yE2: { y: (componentViewport?.y || 0) + 0 + (0 * (((componentViewport?.height || 62) - 0 - 0) / 1) + 0) + 45 + 13 }, dhCQszsnX: { y: (componentViewport?.y || 0) + 0 + (0 * (((componentViewport?.height || 66) - 0 - 0) / 1) + 0) + 43 + 15 }, st3rodc5M: { y: (componentViewport?.y || 0) + 0 + 64 + 10 }, Ve10kfITC: { y: (componentViewport?.y || 0) + 30 + (0 * (((componentViewport?.height || 402) - 30 - 0) / 1) + 0) + 0 + 13 }, Wi4tFM7FN: { y: (componentViewport?.y || 0) + 16 + 0 + 10 } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx2(SmartComponentScopedContainer, { className: "framer-1xewh8u-container", layoutDependency, layoutId: "ni1X41hiP-container", nodeId: "ni1X41hiP", rendersWithMotion: true, scopeId: "zKohnBGrW", style: { rotate: 0 }, variants: { B2uVs0yE2: { rotate: 90 }, dhCQszsnX: { rotate: 90 }, st3rodc5M: { rotate: 90 }, Ve10kfITC: { rotate: 0 }, Wi4tFM7FN: { rotate: 0 } }, children: /* @__PURE__ */ _jsx2(stdin_default, { gUDzlhgLq: false, height: "100%", id: "ni1X41hiP", layoutId: "ni1X41hiP", Rl_qLe3MC: "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", style: { height: "100%", width: "100%" }, width: "100%", ...addPropertyOverrides({ B2uVs0yE2: { gUDzlhgLq: true }, dhCQszsnX: { gUDzlhgLq: true }, st3rodc5M: { gUDzlhgLq: true } }, baseVariant, gestureVariant) }) }) }) }), /* @__PURE__ */ _jsx2(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx2(React.Fragment, { children: /* @__PURE__ */ _jsx2(motion.p, { className: "framer-styles-preset-1oueo73", "data-styles-preset": "HLpRTFhim", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Web design and development" }) }), className: "framer-1pna3mw", "data-framer-name": "Service Title", fonts: ["Inter"], layoutDependency, layoutId: "JBxt9cCf6", style: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", "--framer-paragraph-spacing": "0px", opacity: 0 }, text: uXdq8Ygre, variants: { B2uVs0yE2: { opacity: 1 }, dhCQszsnX: { opacity: 1 }, st3rodc5M: { opacity: 1 } }, verticalAlignment: "top", withExternalLayout: true })] })] }) }) }) });
});
var css5 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-Rs4As.framer-lfb39o, .framer-Rs4As .framer-lfb39o { display: block; }", ".framer-Rs4As.framer-1nd063s { cursor: pointer; display: grid; gap: 0px; grid-auto-rows: minmax(0, 1fr); grid-template-columns: repeat(4, minmax(50px, 1fr)); grid-template-rows: repeat(1, minmax(0, 1fr)); height: min-content; justify-content: center; overflow: visible; padding: 30px 0px 0px 0px; position: relative; width: 1128px; }", ".framer-Rs4As .framer-wos98z { align-content: flex-start; align-items: flex-start; align-self: start; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 4px; height: 100%; justify-content: flex-start; justify-self: start; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-Rs4As .framer-vxijqi, .framer-Rs4As .framer-a7z6k3 { flex: none; height: auto; position: relative; white-space: pre; width: auto; }", ".framer-Rs4As .framer-1ox4qjh { align-content: flex-start; align-items: flex-start; align-self: start; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 40px; grid-column: auto / span 3; height: min-content; justify-content: flex-start; justify-self: start; overflow: hidden; padding: 0px 0px 70px 0px; position: relative; width: 100%; }", ".framer-Rs4As .framer-174h6l6 { align-content: flex-start; align-items: flex-start; display: flex; flex: 1 0 0px; flex-direction: row; flex-wrap: nowrap; gap: 40px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: 1px; }", ".framer-Rs4As .framer-1qdqpra { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 44px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 0px; position: relative; width: 60%; }", ".framer-Rs4As .framer-1t0yt9s { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 0px; position: relative; width: 156px; }", ".framer-Rs4As .framer-knkn1 { aspect-ratio: 1 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 80px); overflow: visible; position: relative; width: 80px; }", ".framer-Rs4As .framer-rbfec2 { aspect-ratio: 1 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 80px); left: 38px; overflow: visible; position: absolute; top: 0px; width: 80px; z-index: 1; }", ".framer-Rs4As .framer-38v78q { aspect-ratio: 1 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 80px); left: 76px; overflow: visible; position: absolute; top: 0px; width: 80px; z-index: 1; }", ".framer-Rs4As .framer-14ruax2 { align-content: flex-start; align-items: flex-start; display: flex; flex: 1 0 0px; flex-direction: column; flex-wrap: nowrap; gap: 20px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: 1px; }", ".framer-Rs4As .framer-1xsxun7 { flex: none; height: auto; max-width: 300px; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }", ".framer-Rs4As .framer-vli817 { --framer-text-wrap-override: balance; flex: none; height: auto; position: relative; width: 100%; }", ".framer-Rs4As .framer-obmt7x { align-content: flex-start; align-items: flex-start; display: flex; flex: 1 0 0px; flex-direction: column; flex-wrap: nowrap; gap: 18px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: 1px; }", ".framer-Rs4As .framer-17jid8u { flex: none; height: auto; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }", ".framer-Rs4As .framer-1yd713w { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: row; flex-wrap: wrap; gap: 8px; height: min-content; justify-content: flex-start; max-width: 350px; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-Rs4As .framer-wtbzr0-container, .framer-Rs4As .framer-1pqxl5f-container, .framer-Rs4As .framer-h4tz30-container, .framer-Rs4As .framer-jeszxr-container, .framer-Rs4As .framer-1g1rklt-container, .framer-Rs4As .framer-3bxzad-container { flex: none; height: auto; position: relative; width: auto; }", ".framer-Rs4As .framer-k21hu0 { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 36px; height: 30px; justify-content: center; overflow: visible; padding: 9px 12px 9px 12px; position: relative; width: min-content; }", ".framer-Rs4As .framer-koycop { flex: none; height: 46px; position: relative; width: 46px; }", ".framer-Rs4As .framer-1xewh8u-container { flex: none; height: 16px; left: calc(50.00000000000002% - 16px / 2); position: absolute; top: calc(50.00000000000002% - 16px / 2); width: 16px; }", ".framer-Rs4As .framer-1pna3mw { bottom: -26px; flex: none; height: auto; left: 0px; position: absolute; white-space: pre; width: auto; z-index: 1; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-Rs4As .framer-wos98z, .framer-Rs4As .framer-1ox4qjh, .framer-Rs4As .framer-174h6l6, .framer-Rs4As .framer-1qdqpra, .framer-Rs4As .framer-1t0yt9s, .framer-Rs4As .framer-14ruax2, .framer-Rs4As .framer-obmt7x, .framer-Rs4As .framer-1yd713w, .framer-Rs4As .framer-k21hu0 { gap: 0px; } .framer-Rs4As .framer-wos98z > * { margin: 0px; margin-left: calc(4px / 2); margin-right: calc(4px / 2); } .framer-Rs4As .framer-wos98z > :first-child, .framer-Rs4As .framer-1ox4qjh > :first-child, .framer-Rs4As .framer-174h6l6 > :first-child, .framer-Rs4As .framer-1qdqpra > :first-child, .framer-Rs4As .framer-1t0yt9s > :first-child, .framer-Rs4As .framer-1yd713w > :first-child, .framer-Rs4As .framer-k21hu0 > :first-child { margin-left: 0px; } .framer-Rs4As .framer-wos98z > :last-child, .framer-Rs4As .framer-1ox4qjh > :last-child, .framer-Rs4As .framer-174h6l6 > :last-child, .framer-Rs4As .framer-1qdqpra > :last-child, .framer-Rs4As .framer-1t0yt9s > :last-child, .framer-Rs4As .framer-1yd713w > :last-child, .framer-Rs4As .framer-k21hu0 > :last-child { margin-right: 0px; } .framer-Rs4As .framer-1ox4qjh > *, .framer-Rs4As .framer-174h6l6 > * { margin: 0px; margin-left: calc(40px / 2); margin-right: calc(40px / 2); } .framer-Rs4As .framer-1qdqpra > * { margin: 0px; margin-left: calc(44px / 2); margin-right: calc(44px / 2); } .framer-Rs4As .framer-1t0yt9s > * { margin: 0px; margin-left: calc(0px / 2); margin-right: calc(0px / 2); } .framer-Rs4As .framer-14ruax2 > * { margin: 0px; margin-bottom: calc(20px / 2); margin-top: calc(20px / 2); } .framer-Rs4As .framer-14ruax2 > :first-child, .framer-Rs4As .framer-obmt7x > :first-child { margin-top: 0px; } .framer-Rs4As .framer-14ruax2 > :last-child, .framer-Rs4As .framer-obmt7x > :last-child { margin-bottom: 0px; } .framer-Rs4As .framer-obmt7x > * { margin: 0px; margin-bottom: calc(18px / 2); margin-top: calc(18px / 2); } .framer-Rs4As .framer-1yd713w > * { margin: 0px; margin-left: calc(8px / 2); margin-right: calc(8px / 2); } .framer-Rs4As .framer-k21hu0 > * { margin: 0px; margin-left: calc(36px / 2); margin-right: calc(36px / 2); } }", ".framer-Rs4As.framer-v-1cek2le.framer-1nd063s { padding: 0px; }", ".framer-Rs4As.framer-v-1cek2le .framer-1ox4qjh, .framer-Rs4As.framer-v-43amvy .framer-1ox4qjh { align-content: center; align-items: center; padding: 0px 0px 20px 0px; }", ".framer-Rs4As.framer-v-1cek2le .framer-174h6l6 { flex: none; left: 0px; order: 0; padding: 0px 86px 0px 0px; position: absolute; top: -171px; width: 100%; z-index: 1; }", ".framer-Rs4As.framer-v-1cek2le .framer-koycop { order: 2; }", ".framer-Rs4As.framer-v-1cek2le .framer-1pna3mw, .framer-Rs4As.framer-v-43amvy .framer-1pna3mw { bottom: unset; flex: 1 0 0px; left: unset; order: 1; position: relative; white-space: pre-wrap; width: 1px; word-break: break-word; word-wrap: break-word; }", ".framer-Rs4As.framer-v-13mbndl.framer-1nd063s { width: 810px; }", ".framer-Rs4As.framer-v-13mbndl .framer-1ox4qjh { gap: 30px; padding: 0px 0px 40px 0px; }", ".framer-Rs4As.framer-v-13mbndl .framer-174h6l6 { flex-direction: column; gap: 26px; }", ".framer-Rs4As.framer-v-13mbndl .framer-1qdqpra, .framer-Rs4As.framer-v-43amvy .framer-1qdqpra { flex-direction: column; gap: 30px; width: 100%; }", ".framer-Rs4As.framer-v-13mbndl .framer-14ruax2, .framer-Rs4As.framer-v-13mbndl .framer-obmt7x, .framer-Rs4As.framer-v-43amvy .framer-14ruax2, .framer-Rs4As.framer-v-43amvy .framer-obmt7x, .framer-Rs4As.framer-v-dzpdh .framer-obmt7x { flex: none; gap: 16px; width: 100%; }", ".framer-Rs4As.framer-v-13mbndl .framer-1xsxun7, .framer-Rs4As.framer-v-43amvy .framer-1xsxun7, .framer-Rs4As.framer-v-dzpdh .framer-1xsxun7, .framer-Rs4As.framer-v-1ok7lm2 .framer-1xsxun7 { max-width: unset; }", ".framer-Rs4As.framer-v-13mbndl .framer-1yd713w, .framer-Rs4As.framer-v-43amvy .framer-1yd713w { max-width: 400px; }", ".framer-Rs4As.framer-v-13mbndl .framer-koycop { height: 42px; position: absolute; right: 0px; top: 0px; width: 42px; z-index: 1; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-Rs4As.framer-v-13mbndl .framer-1ox4qjh, .framer-Rs4As.framer-v-13mbndl .framer-174h6l6, .framer-Rs4As.framer-v-13mbndl .framer-1qdqpra, .framer-Rs4As.framer-v-13mbndl .framer-14ruax2, .framer-Rs4As.framer-v-13mbndl .framer-obmt7x { gap: 0px; } .framer-Rs4As.framer-v-13mbndl .framer-1ox4qjh > * { margin: 0px; margin-left: calc(30px / 2); margin-right: calc(30px / 2); } .framer-Rs4As.framer-v-13mbndl .framer-1ox4qjh > :first-child { margin-left: 0px; } .framer-Rs4As.framer-v-13mbndl .framer-1ox4qjh > :last-child { margin-right: 0px; } .framer-Rs4As.framer-v-13mbndl .framer-174h6l6 > * { margin: 0px; margin-bottom: calc(26px / 2); margin-top: calc(26px / 2); } .framer-Rs4As.framer-v-13mbndl .framer-174h6l6 > :first-child, .framer-Rs4As.framer-v-13mbndl .framer-1qdqpra > :first-child, .framer-Rs4As.framer-v-13mbndl .framer-14ruax2 > :first-child, .framer-Rs4As.framer-v-13mbndl .framer-obmt7x > :first-child { margin-top: 0px; } .framer-Rs4As.framer-v-13mbndl .framer-174h6l6 > :last-child, .framer-Rs4As.framer-v-13mbndl .framer-1qdqpra > :last-child, .framer-Rs4As.framer-v-13mbndl .framer-14ruax2 > :last-child, .framer-Rs4As.framer-v-13mbndl .framer-obmt7x > :last-child { margin-bottom: 0px; } .framer-Rs4As.framer-v-13mbndl .framer-1qdqpra > * { margin: 0px; margin-bottom: calc(30px / 2); margin-top: calc(30px / 2); } .framer-Rs4As.framer-v-13mbndl .framer-14ruax2 > *, .framer-Rs4As.framer-v-13mbndl .framer-obmt7x > * { margin: 0px; margin-bottom: calc(16px / 2); margin-top: calc(16px / 2); } }", ".framer-Rs4As.framer-v-43amvy.framer-1nd063s { padding: 0px; width: 810px; }", ".framer-Rs4As.framer-v-43amvy .framer-174h6l6 { flex: none; flex-direction: column; gap: 26px; left: 0px; order: 0; position: absolute; top: -171px; width: 100%; z-index: 1; }", ".framer-Rs4As.framer-v-43amvy .framer-koycop { height: 42px; order: 2; width: 42px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-Rs4As.framer-v-43amvy .framer-174h6l6, .framer-Rs4As.framer-v-43amvy .framer-1qdqpra, .framer-Rs4As.framer-v-43amvy .framer-14ruax2, .framer-Rs4As.framer-v-43amvy .framer-obmt7x { gap: 0px; } .framer-Rs4As.framer-v-43amvy .framer-174h6l6 > * { margin: 0px; margin-bottom: calc(26px / 2); margin-top: calc(26px / 2); } .framer-Rs4As.framer-v-43amvy .framer-174h6l6 > :first-child, .framer-Rs4As.framer-v-43amvy .framer-1qdqpra > :first-child, .framer-Rs4As.framer-v-43amvy .framer-14ruax2 > :first-child, .framer-Rs4As.framer-v-43amvy .framer-obmt7x > :first-child { margin-top: 0px; } .framer-Rs4As.framer-v-43amvy .framer-174h6l6 > :last-child, .framer-Rs4As.framer-v-43amvy .framer-1qdqpra > :last-child, .framer-Rs4As.framer-v-43amvy .framer-14ruax2 > :last-child, .framer-Rs4As.framer-v-43amvy .framer-obmt7x > :last-child { margin-bottom: 0px; } .framer-Rs4As.framer-v-43amvy .framer-1qdqpra > * { margin: 0px; margin-bottom: calc(30px / 2); margin-top: calc(30px / 2); } .framer-Rs4As.framer-v-43amvy .framer-14ruax2 > *, .framer-Rs4As.framer-v-43amvy .framer-obmt7x > * { margin: 0px; margin-bottom: calc(16px / 2); margin-top: calc(16px / 2); } }", ".framer-Rs4As.framer-v-dzpdh.framer-1nd063s { align-content: flex-start; align-items: flex-start; display: flex; flex-direction: row; flex-wrap: nowrap; justify-content: flex-start; padding: 16px 0px 0px 0px; width: 390px; }", ".framer-Rs4As.framer-v-dzpdh .framer-wos98z { align-self: stretch; height: auto; padding: 11px 24px 0px 0px; width: min-content; }", ".framer-Rs4As.framer-v-dzpdh .framer-1ox4qjh { align-self: unset; flex: 1 0 0px; gap: 30px; padding: 0px 0px 26px 0px; width: 1px; }", ".framer-Rs4As.framer-v-dzpdh .framer-174h6l6 { flex-direction: column; gap: 18px; padding: 0px 36px 0px 0px; }", ".framer-Rs4As.framer-v-dzpdh .framer-1qdqpra, .framer-Rs4As.framer-v-1ok7lm2 .framer-1qdqpra { flex-direction: column; gap: 24px; width: 100%; }", ".framer-Rs4As.framer-v-dzpdh .framer-knkn1, .framer-Rs4As.framer-v-dzpdh .framer-rbfec2, .framer-Rs4As.framer-v-dzpdh .framer-38v78q, .framer-Rs4As.framer-v-1ok7lm2 .framer-knkn1, .framer-Rs4As.framer-v-1ok7lm2 .framer-rbfec2, .framer-Rs4As.framer-v-1ok7lm2 .framer-38v78q { height: var(--framer-aspect-ratio-supported, 60px); width: 60px; }", ".framer-Rs4As.framer-v-dzpdh .framer-14ruax2, .framer-Rs4As.framer-v-1ok7lm2 .framer-14ruax2 { flex: none; gap: 12px; width: 100%; }", ".framer-Rs4As.framer-v-dzpdh .framer-1yd713w, .framer-Rs4As.framer-v-1ok7lm2 .framer-1yd713w { gap: 6px; max-width: 410px; }", ".framer-Rs4As.framer-v-dzpdh .framer-koycop { height: 36px; position: absolute; right: 0px; top: 0px; width: 36px; z-index: 1; }", ".framer-Rs4As.framer-v-dzpdh .framer-1pna3mw { max-width: 220px; white-space: pre-wrap; word-break: break-word; word-wrap: break-word; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-Rs4As.framer-v-dzpdh.framer-1nd063s, .framer-Rs4As.framer-v-dzpdh .framer-1ox4qjh, .framer-Rs4As.framer-v-dzpdh .framer-174h6l6, .framer-Rs4As.framer-v-dzpdh .framer-1qdqpra, .framer-Rs4As.framer-v-dzpdh .framer-14ruax2, .framer-Rs4As.framer-v-dzpdh .framer-obmt7x, .framer-Rs4As.framer-v-dzpdh .framer-1yd713w { gap: 0px; } .framer-Rs4As.framer-v-dzpdh.framer-1nd063s > * { margin: 0px; margin-left: calc(0px / 2); margin-right: calc(0px / 2); } .framer-Rs4As.framer-v-dzpdh.framer-1nd063s > :first-child, .framer-Rs4As.framer-v-dzpdh .framer-1ox4qjh > :first-child, .framer-Rs4As.framer-v-dzpdh .framer-1yd713w > :first-child { margin-left: 0px; } .framer-Rs4As.framer-v-dzpdh.framer-1nd063s > :last-child, .framer-Rs4As.framer-v-dzpdh .framer-1ox4qjh > :last-child, .framer-Rs4As.framer-v-dzpdh .framer-1yd713w > :last-child { margin-right: 0px; } .framer-Rs4As.framer-v-dzpdh .framer-1ox4qjh > * { margin: 0px; margin-left: calc(30px / 2); margin-right: calc(30px / 2); } .framer-Rs4As.framer-v-dzpdh .framer-174h6l6 > * { margin: 0px; margin-bottom: calc(18px / 2); margin-top: calc(18px / 2); } .framer-Rs4As.framer-v-dzpdh .framer-174h6l6 > :first-child, .framer-Rs4As.framer-v-dzpdh .framer-1qdqpra > :first-child, .framer-Rs4As.framer-v-dzpdh .framer-14ruax2 > :first-child, .framer-Rs4As.framer-v-dzpdh .framer-obmt7x > :first-child { margin-top: 0px; } .framer-Rs4As.framer-v-dzpdh .framer-174h6l6 > :last-child, .framer-Rs4As.framer-v-dzpdh .framer-1qdqpra > :last-child, .framer-Rs4As.framer-v-dzpdh .framer-14ruax2 > :last-child, .framer-Rs4As.framer-v-dzpdh .framer-obmt7x > :last-child { margin-bottom: 0px; } .framer-Rs4As.framer-v-dzpdh .framer-1qdqpra > * { margin: 0px; margin-bottom: calc(24px / 2); margin-top: calc(24px / 2); } .framer-Rs4As.framer-v-dzpdh .framer-14ruax2 > * { margin: 0px; margin-bottom: calc(12px / 2); margin-top: calc(12px / 2); } .framer-Rs4As.framer-v-dzpdh .framer-obmt7x > * { margin: 0px; margin-bottom: calc(16px / 2); margin-top: calc(16px / 2); } .framer-Rs4As.framer-v-dzpdh .framer-1yd713w > * { margin: 0px; margin-left: calc(6px / 2); margin-right: calc(6px / 2); } }", ".framer-Rs4As.framer-v-1ok7lm2.framer-1nd063s { align-content: flex-start; align-items: flex-start; display: flex; flex-direction: row; flex-wrap: nowrap; justify-content: flex-start; padding: 0px; width: 390px; }", ".framer-Rs4As.framer-v-1ok7lm2 .framer-wos98z { align-content: center; align-items: center; align-self: stretch; height: auto; padding: 16px 24px 16px 0px; width: min-content; }", ".framer-Rs4As.framer-v-1ok7lm2 .framer-1ox4qjh { align-content: center; align-items: center; align-self: unset; flex: 1 0 0px; gap: unset; justify-content: space-between; padding: 16px 0px 16px 0px; width: 1px; }", ".framer-Rs4As.framer-v-1ok7lm2 .framer-174h6l6 { flex: none; flex-direction: column; gap: 18px; left: 0px; order: 0; padding: 0px 36px 0px 0px; position: absolute; top: -90px; width: 100%; z-index: 1; }", ".framer-Rs4As.framer-v-1ok7lm2 .framer-obmt7x { flex: none; width: 100%; }", ".framer-Rs4As.framer-v-1ok7lm2 .framer-koycop { height: 36px; order: 2; width: 36px; z-index: 1; }", ".framer-Rs4As.framer-v-1ok7lm2 .framer-1pna3mw { bottom: unset; flex: 1 0 0px; left: unset; max-width: 220px; order: 1; position: relative; white-space: pre-wrap; width: 1px; word-break: break-word; word-wrap: break-word; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-Rs4As.framer-v-1ok7lm2.framer-1nd063s, .framer-Rs4As.framer-v-1ok7lm2 .framer-1ox4qjh, .framer-Rs4As.framer-v-1ok7lm2 .framer-174h6l6, .framer-Rs4As.framer-v-1ok7lm2 .framer-1qdqpra, .framer-Rs4As.framer-v-1ok7lm2 .framer-14ruax2, .framer-Rs4As.framer-v-1ok7lm2 .framer-1yd713w { gap: 0px; } .framer-Rs4As.framer-v-1ok7lm2.framer-1nd063s > * { margin: 0px; margin-left: calc(0px / 2); margin-right: calc(0px / 2); } .framer-Rs4As.framer-v-1ok7lm2.framer-1nd063s > :first-child, .framer-Rs4As.framer-v-1ok7lm2 .framer-1yd713w > :first-child { margin-left: 0px; } .framer-Rs4As.framer-v-1ok7lm2.framer-1nd063s > :last-child, .framer-Rs4As.framer-v-1ok7lm2 .framer-1yd713w > :last-child { margin-right: 0px; } .framer-Rs4As.framer-v-1ok7lm2 .framer-1ox4qjh > *, .framer-Rs4As.framer-v-1ok7lm2 .framer-1ox4qjh > :first-child, .framer-Rs4As.framer-v-1ok7lm2 .framer-1ox4qjh > :last-child { margin: 0px; } .framer-Rs4As.framer-v-1ok7lm2 .framer-174h6l6 > * { margin: 0px; margin-bottom: calc(18px / 2); margin-top: calc(18px / 2); } .framer-Rs4As.framer-v-1ok7lm2 .framer-174h6l6 > :first-child, .framer-Rs4As.framer-v-1ok7lm2 .framer-1qdqpra > :first-child, .framer-Rs4As.framer-v-1ok7lm2 .framer-14ruax2 > :first-child { margin-top: 0px; } .framer-Rs4As.framer-v-1ok7lm2 .framer-174h6l6 > :last-child, .framer-Rs4As.framer-v-1ok7lm2 .framer-1qdqpra > :last-child, .framer-Rs4As.framer-v-1ok7lm2 .framer-14ruax2 > :last-child { margin-bottom: 0px; } .framer-Rs4As.framer-v-1ok7lm2 .framer-1qdqpra > * { margin: 0px; margin-bottom: calc(24px / 2); margin-top: calc(24px / 2); } .framer-Rs4As.framer-v-1ok7lm2 .framer-14ruax2 > * { margin: 0px; margin-bottom: calc(12px / 2); margin-top: calc(12px / 2); } .framer-Rs4As.framer-v-1ok7lm2 .framer-1yd713w > * { margin: 0px; margin-left: calc(6px / 2); margin-right: calc(6px / 2); } }", ...css, ...css3, ...css4, ...css2, '.framer-Rs4As[data-border="true"]::after, .framer-Rs4As [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none; }'];
var FramerzKohnBGrW = withCSS(Component, css5, "framer-Rs4As");
var stdin_default3 = FramerzKohnBGrW;
FramerzKohnBGrW.displayName = "Table item";
FramerzKohnBGrW.defaultProps = { height: 282, width: 1128 };
addPropertyControls2(FramerzKohnBGrW, { variant: { options: ["CgQ12EskR", "dhCQszsnX", "Ve10kfITC", "B2uVs0yE2", "Wi4tFM7FN", "st3rodc5M"], optionTitles: ["Desktop open", "Desktop closed", "Tablet open", "Tablet closed", "Phone open", "Phone closed"], title: "Variant", type: ControlType2.Enum }, CgqILTR_F: { defaultValue: "(001)", displayTextArea: false, title: "Number", type: ControlType2.String }, uXdq8Ygre: { defaultValue: "Web design and development", displayTextArea: true, title: "Name", type: ControlType2.String }, h9KdJ0MhX: { defaultValue: "Modern, responsive, and user-friendly websites designed to engage visitors and drive conversions.", displayTextArea: true, title: "Description", type: ControlType2.String }, rQrZWdPlR: { __defaultAssetReference: "data:framer/asset-reference,vGSJoy0fkCYvuK5CETUzS64NNo.jpg?originalFilename=image.jpg&preferredSize=auto", __vekterDefault: { alt: "", assetReference: "data:framer/asset-reference,vGSJoy0fkCYvuK5CETUzS64NNo.jpg?originalFilename=image.jpg&preferredSize=auto" }, title: "01 Image", type: ControlType2.ResponsiveImage }, snuND8XMP: { __defaultAssetReference: "data:framer/asset-reference,6xxZ3D6rnu26P86nUVvj2eanCY.jpg?originalFilename=image-1.jpg&preferredSize=auto", __vekterDefault: { alt: "", assetReference: "data:framer/asset-reference,6xxZ3D6rnu26P86nUVvj2eanCY.jpg?originalFilename=image-1.jpg&preferredSize=auto" }, title: "02 Image", type: ControlType2.ResponsiveImage }, rGrSa8vGT: { __defaultAssetReference: "data:framer/asset-reference,6girwIRKdg1doDEWAHr4oDIbroU.jpg?originalFilename=image-2.jpg&preferredSize=auto", __vekterDefault: { alt: "", assetReference: "data:framer/asset-reference,6girwIRKdg1doDEWAHr4oDIbroU.jpg?originalFilename=image-2.jpg&preferredSize=auto" }, title: "03 Image", type: ControlType2.ResponsiveImage }, wjU0kH4We: { defaultValue: "Categories", displayTextArea: false, title: "Label", type: ControlType2.String }, BM5PQSKQ4: { defaultValue: "Packaging design", displayTextArea: false, title: "01 Category", type: ControlType2.String }, D5EIDF1hM: { defaultValue: "Logo design", displayTextArea: false, title: "02 Category", type: ControlType2.String }, kraMjzSa3: { defaultValue: "Rebranding", displayTextArea: false, title: "03 Category", type: ControlType2.String }, A55R9swTB: { defaultValue: "Typography", displayTextArea: false, title: "04 Category", type: ControlType2.String }, Sl057QITc: { defaultValue: "Guidelines", displayTextArea: false, title: "05 Category", type: ControlType2.String }, KAi5r409E: { defaultValue: "Visual identity", displayTextArea: false, title: "06 Category", type: ControlType2.String }, Axz4_1jdS: { defaultValue: "7+", displayTextArea: false, title: "Amount", type: ControlType2.String } });
addFonts(FramerzKohnBGrW, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5vvr9Vy74if2I6bQbJvbw7SY1pQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/EOr0mi4hNtlgWNn9if640EZzXCo.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/Y9k9QrlZAqio88Klkmbd8VoMQc.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/OYrD2tBIBPvoJXiIHnLoOXnY9M.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/JeYwfuaPfZHQhEG8U5gtPDZ7WQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/vQyevYAyHtARFwPqUzQGpnDs.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/b6Y37FthZeALduNqHicBT6FutY.woff2", weight: "400" }] }, ...CategoryFonts, ...PlusIconFonts, ...getFontsFromSharedStyle(fonts), ...getFontsFromSharedStyle(fonts3), ...getFontsFromSharedStyle(fonts4), ...getFontsFromSharedStyle(fonts2)], { supportsExplicitInterCodegen: true });

// /:https://framerusercontent.com/modules/8xf9SmDnMz8gG4sF4hpH/tLrkAll785TqtzKVhiuu/fkNrtm8z2.js
var LayoutJumpPreventerFonts = getFonts2(AvoidLayoutJumping);
var TableItemFonts = getFonts2(stdin_default3);
var cycleOrder2 = ["ooQ9Z1hJi", "jnEpGTkzu", "WzbaFXYz_"];
var serializationHash2 = "framer-gxbXF";
var variantClassNames2 = { jnEpGTkzu: "framer-v-1yaccjd", ooQ9Z1hJi: "framer-v-x5wmst", WzbaFXYz_: "framer-v-cbr0hw" };
function addPropertyOverrides2(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition12 = { delay: 0, duration: 0.4, ease: [0.82, 0.11, 0.37, 0.82], type: "tween" };
var transformTemplate1 = (_, t) => `translateX(-50%) ${t}`;
var addImageAlt = (image, alt) => {
  if (!image || typeof image !== "object") {
    return;
  }
  return { ...image, alt };
};
var Transition2 = ({ value, children }) => {
  const config = React2.useContext(MotionConfigContext2);
  const transition = value ?? config.transition;
  const contextValue = React2.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx3(MotionConfigContext2.Provider, { value: contextValue, children });
};
var Variants2 = motion2.create(React2.Fragment);
var humanReadableVariantMap2 = { "Desktop ": "ooQ9Z1hJi", Phone: "WzbaFXYz_", Tablet: "jnEpGTkzu" };
var getProps2 = ({ height, id, width, ...props }) => {
  return { ...props, variant: humanReadableVariantMap2[props.variant] ?? props.variant ?? "ooQ9Z1hJi" };
};
var createLayoutDependency2 = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component2 = /* @__PURE__ */ React2.forwardRef(function(props, ref) {
  const fallbackRef = useRef3(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React2.useId();
  const { activeLocale, setLocale } = useLocaleInfo2();
  const componentViewport = useComponentViewport2();
  const { style, className: className5, layoutId, variant, ...restProps } = getProps2(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState2({ cycleOrder: cycleOrder2, defaultVariant: "ooQ9Z1hJi", ref: refBinding, variant, variantClassNames: variantClassNames2 });
  const layoutDependency = createLayoutDependency2(props, variants);
  const sharedStyleClassNames = [];
  const scopingClassNames = cx2(serializationHash2, ...sharedStyleClassNames);
  return /* @__PURE__ */ _jsx3(LayoutGroup2, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx3(Variants2, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx3(Transition2, { value: transition12, children: /* @__PURE__ */ _jsxs2(motion2.div, { ...restProps, ...gestureHandlers, className: cx2(scopingClassNames, "framer-x5wmst", className5, classNames), "data-framer-name": "Desktop ", layoutDependency, layoutId: "ooQ9Z1hJi", ref: refBinding, style: { ...style }, ...addPropertyOverrides2({ jnEpGTkzu: { "data-framer-name": "Tablet" }, WzbaFXYz_: { "data-framer-name": "Phone" } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsx3(ComponentViewportProvider2, { children: /* @__PURE__ */ _jsx3(SmartComponentScopedContainer2, { className: "framer-o8ohx9-container", isAuthoredByUser: true, isModuleExternal: true, layoutDependency, layoutId: "FUdXhFw__-container", nodeId: "FUdXhFw__", rendersWithMotion: true, scopeId: "fkNrtm8z2", transformTemplate: transformTemplate1, children: /* @__PURE__ */ _jsx3(AvoidLayoutJumping, { direction: "vertical", height: "100%", id: "FUdXhFw__", layoutId: "FUdXhFw__", width: "100%" }) }) }), /* @__PURE__ */ _jsx3(ComponentViewportProvider2, { height: 259, width: componentViewport?.width || "100vw", y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 517) - 0 - 1096) / 2 + 0 + 0), ...addPropertyOverrides2({ jnEpGTkzu: { y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 648) - 0 - 1096) / 2 + 0 + 0) }, WzbaFXYz_: { y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 200) - 0 - 1036) / 2 + 0 + 0) } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx3(SmartComponentScopedContainer2, { className: "framer-1rzj7xg-container", layoutDependency, layoutId: "YQuQ3H2Ay-container", nodeId: "YQuQ3H2Ay", rendersWithMotion: true, scopeId: "fkNrtm8z2", children: /* @__PURE__ */ _jsx3(stdin_default3, { A55R9swTB: "Typography", Axz4_1jdS: "6+", BM5PQSKQ4: "Packaging design", CgqILTR_F: "(001)", D5EIDF1hM: "Logo design", h9KdJ0MhX: "Modern, responsive, and user-friendly websites designed to engage visitors and drive conversions.", height: "100%", id: "YQuQ3H2Ay", KAi5r409E: "Visual identity", kraMjzSa3: "Rebranding", layoutId: "YQuQ3H2Ay", Sl057QITc: "Guidelines", style: { width: "100%" }, uXdq8Ygre: "Web design and development", variant: "CgQ12EskR", width: "100%", wjU0kH4We: "Categories", ...addPropertyOverrides2({ jnEpGTkzu: { variant: "Ve10kfITC" }, WzbaFXYz_: { variant: "Wi4tFM7FN" } }, baseVariant, gestureVariant) }) }) }), /* @__PURE__ */ _jsx3(ComponentViewportProvider2, { height: 259, width: componentViewport?.width || "100vw", y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 517) - 0 - 1096) / 2 + 259 + 20), ...addPropertyOverrides2({ jnEpGTkzu: { y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 648) - 0 - 1096) / 2 + 259 + 20) }, WzbaFXYz_: { y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 200) - 0 - 1036) / 2 + 259 + 0) } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx3(SmartComponentScopedContainer2, { className: "framer-1bfpaot-container", layoutDependency, layoutId: "kq5KO2lh5-container", nodeId: "kq5KO2lh5", rendersWithMotion: true, scopeId: "fkNrtm8z2", children: /* @__PURE__ */ _jsx3(stdin_default3, { A55R9swTB: "Analytics & reporting", Axz4_1jdS: "6+", BM5PQSKQ4: "Content strategy", CgqILTR_F: "(002)", D5EIDF1hM: "Community management", h9KdJ0MhX: "Strategic, data-driven social media campaigns designed to build brand awareness and connect with your target audience.", height: "100%", id: "kq5KO2lh5", KAi5r409E: "", kraMjzSa3: "Paid advertising", layoutId: "kq5KO2lh5", rGrSa8vGT: addImageAlt({ src: "https://framerusercontent.com/images/2BxeG0o2qWf8AOHmXP5mvB7fXo.jpg" }, ""), rQrZWdPlR: addImageAlt({ src: "https://framerusercontent.com/images/DsMKi7qE5JNWO5UQxmeqZGDSOI.jpg" }, ""), Sl057QITc: "Platform optimization", snuND8XMP: addImageAlt({ src: "https://framerusercontent.com/images/PTZo29JDyFUqhP5lmoOwf726M.jpg" }, ""), style: { width: "100%" }, uXdq8Ygre: "Social media marketing", variant: "dhCQszsnX", width: "100%", wjU0kH4We: "Categories", ...addPropertyOverrides2({ jnEpGTkzu: { variant: "B2uVs0yE2" }, WzbaFXYz_: { variant: "st3rodc5M" } }, baseVariant, gestureVariant) }) }) }), /* @__PURE__ */ _jsx3(ComponentViewportProvider2, { height: 259, width: componentViewport?.width || "100vw", y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 517) - 0 - 1096) / 2 + 518 + 40), ...addPropertyOverrides2({ jnEpGTkzu: { y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 648) - 0 - 1096) / 2 + 518 + 40) }, WzbaFXYz_: { y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 200) - 0 - 1036) / 2 + 518 + 0) } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx3(SmartComponentScopedContainer2, { className: "framer-1sy2cyp-container", layoutDependency, layoutId: "A4YTV6Fjt-container", nodeId: "A4YTV6Fjt", rendersWithMotion: true, scopeId: "fkNrtm8z2", children: /* @__PURE__ */ _jsx3(stdin_default3, { A55R9swTB: "Link building", Axz4_1jdS: "4+", BM5PQSKQ4: "Keyword research", CgqILTR_F: "(003)", D5EIDF1hM: "On-page optimization", h9KdJ0MhX: "Comprehensive search optimization and engaging content that increases visibility, drives organic traffic, and establishes thought leadership.", height: "100%", id: "A4YTV6Fjt", KAi5r409E: "", kraMjzSa3: "Content creation", layoutId: "A4YTV6Fjt", rGrSa8vGT: addImageAlt({ src: "https://framerusercontent.com/images/7HAgaIAjq6jlYJoi8ME87oXs6w.jpg" }, ""), rQrZWdPlR: addImageAlt({ src: "https://framerusercontent.com/images/qQlR5lTiRYzT2lPzSWLLVkcgH6Y.jpg" }, ""), Sl057QITc: "", snuND8XMP: addImageAlt({ src: "https://framerusercontent.com/images/PzUf5VcgXOfitprgtvScN6spik.jpg" }, ""), style: { width: "100%" }, uXdq8Ygre: "SEO and content marketing", variant: "dhCQszsnX", width: "100%", wjU0kH4We: "Categories", ...addPropertyOverrides2({ jnEpGTkzu: { variant: "B2uVs0yE2" }, WzbaFXYz_: { variant: "st3rodc5M" } }, baseVariant, gestureVariant) }) }) }), /* @__PURE__ */ _jsx3(ComponentViewportProvider2, { height: 259, width: componentViewport?.width || "100vw", y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 517) - 0 - 1096) / 2 + 777 + 60), ...addPropertyOverrides2({ jnEpGTkzu: { y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 648) - 0 - 1096) / 2 + 777 + 60) }, WzbaFXYz_: { y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 200) - 0 - 1036) / 2 + 777 + 0) } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx3(SmartComponentScopedContainer2, { className: "framer-8xpe05-container", layoutDependency, layoutId: "lHh2B2_ww-container", nodeId: "lHh2B2_ww", rendersWithMotion: true, scopeId: "fkNrtm8z2", children: /* @__PURE__ */ _jsx3(stdin_default3, { A55R9swTB: "Color systems", Axz4_1jdS: "6+", BM5PQSKQ4: "Brand strategy", CgqILTR_F: "(004)", D5EIDF1hM: "Logo design", h9KdJ0MhX: "Distinctive, memorable brand experiences that communicate your values and create emotional connections with your customers.", height: "100%", id: "lHh2B2_ww", KAi5r409E: "Identity applications", kraMjzSa3: "Brand voice", layoutId: "lHh2B2_ww", rGrSa8vGT: addImageAlt({ src: "https://framerusercontent.com/images/OvxlgM3dgsl1n9Hl1FAnutk3YQ.jpg" }, ""), rQrZWdPlR: addImageAlt({ src: "https://framerusercontent.com/images/9hTP0obDSaEcVCyC5kaHbx7FfI.jpg" }, ""), Sl057QITc: "Brand guidelines", snuND8XMP: addImageAlt({ src: "https://framerusercontent.com/images/zhgLgjCtsbVWTYRQuFeBf3XoW6c.jpg" }, ""), style: { width: "100%" }, uXdq8Ygre: "Branding and identity", variant: "dhCQszsnX", width: "100%", wjU0kH4We: "Categories", ...addPropertyOverrides2({ jnEpGTkzu: { variant: "B2uVs0yE2" }, WzbaFXYz_: { variant: "st3rodc5M" } }, baseVariant, gestureVariant) }) }) })] }) }) }) });
});
var css6 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-gxbXF.framer-15uodjv, .framer-gxbXF .framer-15uodjv { display: block; }", ".framer-gxbXF.framer-x5wmst { align-content: center; align-items: center; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 20px; height: min-content; justify-content: center; overflow: visible; padding: 0px; position: relative; width: 1128px; }", ".framer-gxbXF .framer-o8ohx9-container { flex: none; height: auto; left: 50%; position: absolute; top: 0px; width: auto; z-index: 1; }", ".framer-gxbXF .framer-1rzj7xg-container, .framer-gxbXF .framer-1bfpaot-container, .framer-gxbXF .framer-1sy2cyp-container, .framer-gxbXF .framer-8xpe05-container { flex: none; height: auto; position: relative; width: 100%; z-index: 1; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-gxbXF.framer-x5wmst { gap: 0px; } .framer-gxbXF.framer-x5wmst > * { margin: 0px; margin-bottom: calc(20px / 2); margin-top: calc(20px / 2); } .framer-gxbXF.framer-x5wmst > :first-child { margin-top: 0px; } .framer-gxbXF.framer-x5wmst > :last-child { margin-bottom: 0px; } }", ".framer-gxbXF.framer-v-1yaccjd.framer-x5wmst { width: 810px; }", ".framer-gxbXF.framer-v-cbr0hw.framer-x5wmst { gap: 0px; width: 390px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-gxbXF.framer-v-cbr0hw.framer-x5wmst { gap: 0px; } .framer-gxbXF.framer-v-cbr0hw.framer-x5wmst > * { margin: 0px; margin-bottom: calc(0px / 2); margin-top: calc(0px / 2); } .framer-gxbXF.framer-v-cbr0hw.framer-x5wmst > :first-child { margin-top: 0px; } .framer-gxbXF.framer-v-cbr0hw.framer-x5wmst > :last-child { margin-bottom: 0px; } }"];
var FramerfkNrtm8z2 = withCSS2(Component2, css6, "framer-gxbXF");
var stdin_default4 = FramerfkNrtm8z2;
FramerfkNrtm8z2.displayName = "Table";
FramerfkNrtm8z2.defaultProps = { height: 517, width: 1128 };
addPropertyControls3(FramerfkNrtm8z2, { variant: { options: ["ooQ9Z1hJi", "jnEpGTkzu", "WzbaFXYz_"], optionTitles: ["Desktop ", "Tablet", "Phone"], title: "Variant", type: ControlType3.Enum } });
addFonts2(FramerfkNrtm8z2, [{ explicitInter: true, fonts: [] }, ...LayoutJumpPreventerFonts, ...TableItemFonts], { supportsExplicitInterCodegen: true });

export {
  stdin_default4 as stdin_default
};
