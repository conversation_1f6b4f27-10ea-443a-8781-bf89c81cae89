// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
// /:https://framerusercontent.com/modules/hvfOCrJ4J6tWoXLRSXJB/7zjMumcOuXWqD8earGn2/k3VRpAwZb.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, Link, RichText, useActiveVariantCallback, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var enabledGestures = { Ejf5a8hFM: { hover: true } };
var cycleOrder = ["Ejf5a8hFM", "a6v_JP5sV", "sUHgiwMqj"];
var serializationHash = "framer-IFf4n";
var variantClassNames = { a6v_JP5sV: "framer-v-1cjh3ku", Ejf5a8hFM: "framer-v-14ac7s4", sUHgiwMqj: "framer-v-1h77cpm" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { bounce: 0.35, delay: 0, duration: 0.39, type: "spring" };
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { Desktop: "Ejf5a8hFM", Phone: "sUHgiwMqj", Tablet: "a6v_JP5sV" };
var getProps = ({ click, height, id, link, title, width, ...props }) => {
  return { ...props, LIy6SD5oa: click ?? props.LIy6SD5oa, RLreA9jcP: title ?? props.RLreA9jcP ?? "Title", sEL51lU82: link ?? props.sEL51lU82, variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "Ejf5a8hFM" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className, layoutId, variant, RLreA9jcP, sEL51lU82, LIy6SD5oa, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "Ejf5a8hFM", enabledGestures, ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const { activeVariantCallback, delay } = useActiveVariantCallback(baseVariant);
  const onTap15a3yuw = activeVariantCallback(async (...args) => {
    setGestureState({ isPressed: false });
    if (LIy6SD5oa) {
      const res = await LIy6SD5oa(...args);
      if (res === false) return false;
    }
  });
  const sharedStyleClassNames = [];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  const isDisplayed = () => {
    if (["a6v_JP5sV", "sUHgiwMqj"].includes(baseVariant)) return false;
    return true;
  };
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsx(Link, { href: sEL51lU82, motionChild: true, nodeId: "Ejf5a8hFM", scopeId: "k3VRpAwZb", smoothScroll: true, children: /* @__PURE__ */ _jsxs(motion.a, { ...restProps, ...gestureHandlers, className: `${cx(scopingClassNames, "framer-14ac7s4", className, classNames)} framer-1435c2t`, "data-framer-name": "Desktop", "data-highlight": true, layoutDependency, layoutId: "Ejf5a8hFM", onTap: onTap15a3yuw, ref: refBinding, style: { ...style }, ...addPropertyOverrides({ "Ejf5a8hFM-hover": { "data-framer-name": void 0 }, a6v_JP5sV: { "data-framer-name": "Tablet" }, sUHgiwMqj: { "data-framer-name": "Phone" } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "60px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.06em", "--framer-line-height": "120%", "--framer-text-alignment": "center", "--framer-text-color": "var(--extracted-r6o4lv, var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10)))" }, children: "Title" }) }), className: "framer-ggkluq", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "IxsOzOSNq", style: { "--extracted-r6o4lv": "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", "--framer-paragraph-spacing": "0px" }, text: RLreA9jcP, verticalAlignment: "top", withExternalLayout: true, ...addPropertyOverrides({ a6v_JP5sV: { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "42px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.06em", "--framer-line-height": "120%", "--framer-text-alignment": "center", "--framer-text-color": "var(--extracted-r6o4lv, var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10)))" }, children: "Title" }) }) }, sUHgiwMqj: { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "32px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.06em", "--framer-line-height": "120%", "--framer-text-alignment": "center", "--framer-text-color": "var(--extracted-r6o4lv, var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10)))" }, children: "Title" }) }) } }, baseVariant, gestureVariant) }), isDisplayed() && /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "60px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.06em", "--framer-line-height": "120%", "--framer-text-alignment": "center", "--framer-text-color": "var(--extracted-r6o4lv, var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10)))" }, children: "Title" }) }), className: "framer-8k52a7", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "z1SjKvY6D", style: { "--extracted-r6o4lv": "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", "--framer-paragraph-spacing": "0px" }, text: RLreA9jcP, verticalAlignment: "top", withExternalLayout: true })] }) }) }) }) });
});
var css = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-IFf4n.framer-1435c2t, .framer-IFf4n .framer-1435c2t { display: block; }", ".framer-IFf4n.framer-14ac7s4 { align-content: center; align-items: center; cursor: pointer; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: center; overflow: hidden; padding: 0px 3px 0px 0px; position: relative; text-decoration: none; width: min-content; }", ".framer-IFf4n .framer-ggkluq { flex: none; height: auto; position: relative; white-space: pre; width: auto; }", ".framer-IFf4n .framer-8k52a7 { bottom: -90px; flex: none; height: auto; left: 0px; position: absolute; white-space: pre; width: auto; z-index: 1; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-IFf4n.framer-14ac7s4 { gap: 0px; } .framer-IFf4n.framer-14ac7s4 > * { margin: 0px; margin-bottom: calc(10px / 2); margin-top: calc(10px / 2); } .framer-IFf4n.framer-14ac7s4 > :first-child { margin-top: 0px; } .framer-IFf4n.framer-14ac7s4 > :last-child { margin-bottom: 0px; } }", ".framer-IFf4n.framer-v-14ac7s4.hover .framer-ggkluq { left: 0px; position: absolute; top: -90px; z-index: 1; }", ".framer-IFf4n.framer-v-14ac7s4.hover .framer-8k52a7 { bottom: unset; left: unset; position: relative; }"];
var Framerk3VRpAwZb = withCSS(Component, css, "framer-IFf4n");
var stdin_default = Framerk3VRpAwZb;
Framerk3VRpAwZb.displayName = "Menu item large";
Framerk3VRpAwZb.defaultProps = { height: 72, width: 113 };
addPropertyControls(Framerk3VRpAwZb, { variant: { options: ["Ejf5a8hFM", "a6v_JP5sV", "sUHgiwMqj"], optionTitles: ["Desktop", "Tablet", "Phone"], title: "Variant", type: ControlType.Enum }, RLreA9jcP: { defaultValue: "Title", displayTextArea: false, title: "Title", type: ControlType.String }, sEL51lU82: { title: "Link", type: ControlType.Link }, LIy6SD5oa: { title: "Click", type: ControlType.EventHandler } });
addFonts(Framerk3VRpAwZb, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/hyOgCu0Xnghbimh0pE8QTvtt2AU.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/NeGmSOXrPBfEFIy5YZeHq17LEDA.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/oYaAX5himiTPYuN8vLWnqBbfD2s.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/lEJLP4R0yuCaMCjSXYHtJw72M.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/cRJyLNuTJR5jbyKzGi33wU9cqIQ.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/1ZFS7N918ojhhd0nQWdj3jz4w.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/A0Wcc7NgXMjUuFdquHDrIZpzZw0.woff2", weight: "600" }] }], { supportsExplicitInterCodegen: true });

export {
  stdin_default
};
