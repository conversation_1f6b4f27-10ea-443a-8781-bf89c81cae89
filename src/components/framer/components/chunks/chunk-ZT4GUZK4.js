// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
import {
  Video
} from "./chunk-G73PZF75.js";
import {
  Grain
} from "./chunk-MO4ELLBD.js";

// /:https://framerusercontent.com/modules/dhWVSMV5UF1XiHz4xBG6/MYBJX3CHugamesc7Y6hI/l0AhafeLr.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ComponentViewportProvider, ControlType, cx, getFonts, getLoadingLazyAtYPosition, getPropertyControls, Image, SmartComponentScopedContainer, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var VideoFonts = getFonts(Video);
var GrainFonts = getFonts(Grain);
var VideoControls = getPropertyControls(Video);
var cycleOrder = ["DQjSdvNWb", "t56mZZuyf"];
var serializationHash = "framer-eOQ89";
var variantClassNames = { DQjSdvNWb: "framer-v-lahi66", t56mZZuyf: "framer-v-1roe3i4" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { bounce: 0.2, delay: 0, duration: 0.4, type: "spring" };
var negate = (value) => {
  return !value;
};
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { Desktop: "DQjSdvNWb", Phone: "t56mZZuyf" };
var getProps = ({ file, hasVideo, height, id, source, videoOpacity, width, ...props }) => {
  return { ...props, EfstzZdWy: videoOpacity ?? props.EfstzZdWy ?? 0.5, fgfZL9eoW: source ?? props.fgfZL9eoW ?? "Upload", nHfCx4e4R: file ?? props.nHfCx4e4R ?? "https://framerusercontent.com/assets/G0NwzP4bivPvK55b3ubxNslUs.mp4", NXkb7129J: hasVideo ?? props.NXkb7129J, variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "DQjSdvNWb" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className, layoutId, variant, NXkb7129J, fgfZL9eoW, nHfCx4e4R, EfstzZdWy, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "DQjSdvNWb", ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  const visible = negate(NXkb7129J);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsxs(motion.div, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-lahi66", className, classNames), "data-framer-name": "Desktop", layoutDependency, layoutId: "DQjSdvNWb", ref: refBinding, style: { backgroundColor: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", borderBottomLeftRadius: 25, borderBottomRightRadius: 25, borderTopLeftRadius: 25, borderTopRightRadius: 25, ...style }, variants: { t56mZZuyf: { borderBottomLeftRadius: 0, borderBottomRightRadius: 0, borderTopLeftRadius: 0, borderTopRightRadius: 0 } }, ...addPropertyOverrides({ t56mZZuyf: { "data-framer-name": "Phone" } }, baseVariant, gestureVariant), children: [visible && /* @__PURE__ */ _jsx(Image, { background: { alt: "Dark gradiend background", fit: "stretch", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0), pixelHeight: 1402, pixelWidth: 1912, positionX: "center", positionY: "center", sizes: componentViewport?.width || "100vw", src: "https://framerusercontent.com/images/vrhxHFTuxnCduP4nljUulqZcuQ.jpg", srcSet: "https://framerusercontent.com/images/vrhxHFTuxnCduP4nljUulqZcuQ.jpg?scale-down-to=512 512w,https://framerusercontent.com/images/vrhxHFTuxnCduP4nljUulqZcuQ.jpg?scale-down-to=1024 1024w,https://framerusercontent.com/images/vrhxHFTuxnCduP4nljUulqZcuQ.jpg 1912w" }, className: "framer-1o5ljvc", "data-framer-name": "Image", layoutDependency, layoutId: "tLHnNKIht", style: { filter: "brightness(1.15)", WebkitFilter: "brightness(1.15)" } }), NXkb7129J && /* @__PURE__ */ _jsx(ComponentViewportProvider, { children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-143hdec-container", isAuthoredByUser: true, isModuleExternal: true, layoutDependency, layoutId: "AteaD_2hO-container", nodeId: "AteaD_2hO", rendersWithMotion: true, scopeId: "l0AhafeLr", style: { filter: "grayscale(1)", opacity: EfstzZdWy, WebkitFilter: "grayscale(1)" }, children: /* @__PURE__ */ _jsx(Video, { backgroundColor: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", borderRadius: 0, bottomLeftRadius: 0, bottomRightRadius: 0, controls: false, height: "100%", id: "AteaD_2hO", isMixedBorderRadius: false, layoutId: "AteaD_2hO", loop: true, muted: true, objectFit: "cover", playing: true, poster: "https://framerusercontent.com/images/vrhxHFTuxnCduP4nljUulqZcuQ.jpg", posterEnabled: true, srcFile: nHfCx4e4R, srcType: fgfZL9eoW, srcUrl: "https://assets.mixkit.co/videos/43192/43192-720.mp4", startTime: 10, style: { height: "100%", width: "100%" }, topLeftRadius: 0, topRightRadius: 0, volume: 25, width: "100%" }) }) }), /* @__PURE__ */ _jsx(ComponentViewportProvider, { children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-1on14o5-container", isAuthoredByUser: true, isModuleExternal: true, layoutDependency, layoutId: "vbq2CXiRx-container", nodeId: "vbq2CXiRx", rendersWithMotion: true, scopeId: "l0AhafeLr", children: /* @__PURE__ */ _jsx(Grain, { height: "100%", id: "vbq2CXiRx", layoutId: "vbq2CXiRx", opacity: 0.05, style: { height: "100%", width: "100%" }, width: "100%" }) }) })] }) }) }) });
});
var css = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-eOQ89.framer-14it2bz, .framer-eOQ89 .framer-14it2bz { display: block; }", ".framer-eOQ89.framer-lahi66 { align-content: center; align-items: center; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: 850px; justify-content: center; overflow: hidden; padding: 0px; position: relative; width: 1188px; will-change: var(--framer-will-change-override, transform); }", ".framer-eOQ89 .framer-1o5ljvc { bottom: 0px; flex: none; left: 0px; position: absolute; right: 0px; top: 0px; z-index: 0; }", ".framer-eOQ89 .framer-143hdec-container { bottom: 0px; flex: none; left: 0px; position: absolute; right: 0px; top: 0px; z-index: 2; }", ".framer-eOQ89 .framer-1on14o5-container { flex: none; height: 100%; position: relative; width: 100%; z-index: 0; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-eOQ89.framer-lahi66 { gap: 0px; } .framer-eOQ89.framer-lahi66 > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } .framer-eOQ89.framer-lahi66 > :first-child { margin-left: 0px; } .framer-eOQ89.framer-lahi66 > :last-child { margin-right: 0px; } }", ".framer-eOQ89.framer-v-1roe3i4.framer-lahi66 { width: 390px; will-change: unset; }"];
var Framerl0AhafeLr = withCSS(Component, css, "framer-eOQ89");
var stdin_default = Framerl0AhafeLr;
Framerl0AhafeLr.displayName = "Gradient background";
Framerl0AhafeLr.defaultProps = { height: 850, width: 1188 };
addPropertyControls(Framerl0AhafeLr, { variant: { options: ["DQjSdvNWb", "t56mZZuyf"], optionTitles: ["Desktop", "Phone"], title: "Variant", type: ControlType.Enum }, NXkb7129J: { defaultValue: false, title: "Has video?", type: ControlType.Boolean }, fgfZL9eoW: VideoControls?.["srcType"] && { ...VideoControls["srcType"], defaultValue: "Upload", description: "", hidden: void 0, title: "Source" }, nHfCx4e4R: VideoControls?.["srcFile"] && { ...VideoControls["srcFile"], __defaultAssetReference: "data:framer/asset-reference,G0NwzP4bivPvK55b3ubxNslUs.mp4?originalFilename=compressed3.mp4", description: void 0, hidden: void 0, title: "File" }, EfstzZdWy: { defaultValue: 0.5, max: 1, min: 0, step: 0.01, title: "Video opacity", type: ControlType.Number } });
addFonts(Framerl0AhafeLr, [{ explicitInter: true, fonts: [] }, ...VideoFonts, ...GrainFonts], { supportsExplicitInterCodegen: true });

export {
  stdin_default
};
