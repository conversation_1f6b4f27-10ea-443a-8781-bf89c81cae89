// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
// /:https://framerusercontent.com/modules/mnjgJFRPj416r8pcvyDl/xwOi5nzd2tsTvl7sVclV/eDUVCTTXq.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, Link, RichText, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var enabledGestures = { VMnT_oIqC: { hover: true } };
var cycleOrder = ["VMnT_oIqC", "KSgQQNr4r"];
var serializationHash = "framer-JO4Ez";
var variantClassNames = { KSgQQNr4r: "framer-v-1ijknvv", VMnT_oIqC: "framer-v-rbw179" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { bounce: 0.35, delay: 0, duration: 0.39, type: "spring" };
var transformTemplate1 = (_, t) => `translateX(-50%) ${t}`;
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { Default: "VMnT_oIqC", Phone: "KSgQQNr4r" };
var getProps = ({ bG, color, height, hoverBG, hoverColor, id, link, title, width, ...props }) => {
  return { ...props, iyuXB1N8q: color ?? props.iyuXB1N8q ?? "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", R8iwJ2h7U: hoverColor ?? props.R8iwJ2h7U ?? "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", SnWMJ5xn7: hoverBG ?? props.SnWMJ5xn7 ?? "rgb(0, 0, 0)", tulsc_W5A: bG ?? props.tulsc_W5A ?? "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "VMnT_oIqC", X9Xju9FBn: title ?? props.X9Xju9FBn ?? "Submit", XooFhyn6y: link ?? props.XooFhyn6y };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className, layoutId, variant, X9Xju9FBn, XooFhyn6y, iyuXB1N8q, tulsc_W5A, R8iwJ2h7U, SnWMJ5xn7, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "VMnT_oIqC", enabledGestures, ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsx(Link, { href: XooFhyn6y, motionChild: true, nodeId: "VMnT_oIqC", scopeId: "eDUVCTTXq", children: /* @__PURE__ */ _jsxs(motion.a, { ...restProps, ...gestureHandlers, className: `${cx(scopingClassNames, "framer-rbw179", className, classNames)} framer-1qniyoc`, "data-framer-name": "Default", "data-reset": "button", layoutDependency, layoutId: "VMnT_oIqC", ref: refBinding, style: { backgroundColor: tulsc_W5A, borderBottomLeftRadius: 50, borderBottomRightRadius: 50, borderTopLeftRadius: 50, borderTopRightRadius: 50, ...style }, variants: { "VMnT_oIqC-hover": { backgroundColor: SnWMJ5xn7 } }, ...addPropertyOverrides({ "VMnT_oIqC-hover": { "data-framer-name": void 0 }, KSgQQNr4r: { "data-framer-name": "Phone" } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "18px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-text-color": "var(--extracted-r6o4lv, var(--variable-reference-iyuXB1N8q-eDUVCTTXq))" }, children: "Submit" }) }), className: "framer-1mhfyus", "data-framer-name": "Submit 1", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "kyftlWjnv", style: { "--extracted-r6o4lv": "var(--variable-reference-iyuXB1N8q-eDUVCTTXq)", "--framer-link-text-color": "rgb(0, 153, 255)", "--framer-link-text-decoration": "underline", "--variable-reference-iyuXB1N8q-eDUVCTTXq": iyuXB1N8q, "--variable-reference-R8iwJ2h7U-eDUVCTTXq": R8iwJ2h7U, opacity: 0 }, text: X9Xju9FBn, transformTemplate: transformTemplate1, variants: { "VMnT_oIqC-hover": { "--extracted-r6o4lv": "var(--variable-reference-R8iwJ2h7U-eDUVCTTXq)", "--variable-reference-R8iwJ2h7U-eDUVCTTXq": R8iwJ2h7U, opacity: 1 } }, verticalAlignment: "top", withExternalLayout: true, ...addPropertyOverrides({ "VMnT_oIqC-hover": { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "18px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-text-color": "var(--extracted-r6o4lv, var(--variable-reference-R8iwJ2h7U-eDUVCTTXq))" }, children: "Submit" }) }), transformTemplate: void 0 } }, baseVariant, gestureVariant) }), /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "18px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-text-color": "var(--extracted-r6o4lv, var(--variable-reference-iyuXB1N8q-eDUVCTTXq))" }, children: "Submit" }) }), className: "framer-meoha7", "data-framer-name": "Submit 2", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "u0YJuuI3F", style: { "--extracted-r6o4lv": "var(--variable-reference-iyuXB1N8q-eDUVCTTXq)", "--framer-link-text-color": "rgb(0, 153, 255)", "--framer-link-text-decoration": "underline", "--variable-reference-iyuXB1N8q-eDUVCTTXq": iyuXB1N8q, "--variable-reference-R8iwJ2h7U-eDUVCTTXq": R8iwJ2h7U, opacity: 1 }, text: X9Xju9FBn, variants: { "VMnT_oIqC-hover": { "--extracted-r6o4lv": "var(--variable-reference-R8iwJ2h7U-eDUVCTTXq)", "--variable-reference-R8iwJ2h7U-eDUVCTTXq": R8iwJ2h7U, opacity: 0 } }, verticalAlignment: "top", withExternalLayout: true, ...addPropertyOverrides({ "VMnT_oIqC-hover": { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "18px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-text-color": "var(--extracted-r6o4lv, var(--variable-reference-R8iwJ2h7U-eDUVCTTXq))" }, children: "Submit" }) }), transformTemplate: transformTemplate1 } }, baseVariant, gestureVariant) })] }) }) }) }) });
});
var css = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-JO4Ez.framer-1qniyoc, .framer-JO4Ez .framer-1qniyoc { display: block; }", ".framer-JO4Ez.framer-rbw179 { align-content: center; align-items: center; cursor: pointer; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 0px; height: 58px; justify-content: center; overflow: hidden; padding: 18px 30px 18px 30px; position: relative; text-decoration: none; width: min-content; will-change: var(--framer-will-change-override, transform); }", ".framer-JO4Ez .framer-1mhfyus { -webkit-user-select: none; flex: none; height: auto; left: 50%; position: absolute; top: -22px; user-select: none; white-space: pre; width: auto; z-index: 1; }", ".framer-JO4Ez .framer-meoha7 { -webkit-user-select: none; flex: none; height: auto; position: relative; user-select: none; white-space: pre; width: auto; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-JO4Ez.framer-rbw179 { gap: 0px; } .framer-JO4Ez.framer-rbw179 > * { margin: 0px; margin-left: calc(0px / 2); margin-right: calc(0px / 2); } .framer-JO4Ez.framer-rbw179 > :first-child { margin-left: 0px; } .framer-JO4Ez.framer-rbw179 > :last-child { margin-right: 0px; } }", ".framer-JO4Ez.framer-v-1ijknvv.framer-rbw179 { cursor: unset; }", ".framer-JO4Ez.framer-v-rbw179.hover .framer-1mhfyus { left: unset; position: relative; top: unset; }", ".framer-JO4Ez.framer-v-rbw179.hover .framer-meoha7 { left: 50%; position: absolute; top: 58px; z-index: 1; }"];
var FramereDUVCTTXq = withCSS(Component, css, "framer-JO4Ez");
var stdin_default = FramereDUVCTTXq;
FramereDUVCTTXq.displayName = "Large button";
FramereDUVCTTXq.defaultProps = { height: 58, width: 117 };
addPropertyControls(FramereDUVCTTXq, { variant: { options: ["VMnT_oIqC", "KSgQQNr4r"], optionTitles: ["Default", "Phone"], title: "Variant", type: ControlType.Enum }, X9Xju9FBn: { defaultValue: "Submit", displayTextArea: false, title: "Title", type: ControlType.String }, XooFhyn6y: { title: "Link", type: ControlType.Link }, iyuXB1N8q: { defaultValue: 'var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)) /* {"name":"White"} */', title: "Color", type: ControlType.Color }, tulsc_W5A: { defaultValue: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", title: "BG", type: ControlType.Color }, R8iwJ2h7U: { defaultValue: 'var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)) /* {"name":"White"} */', title: "Hover color", type: ControlType.Color }, SnWMJ5xn7: { defaultValue: "rgb(0, 0, 0)", title: "Hover BG", type: ControlType.Color } });
addFonts(FramereDUVCTTXq, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/hyOgCu0Xnghbimh0pE8QTvtt2AU.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/NeGmSOXrPBfEFIy5YZeHq17LEDA.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/oYaAX5himiTPYuN8vLWnqBbfD2s.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/lEJLP4R0yuCaMCjSXYHtJw72M.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/cRJyLNuTJR5jbyKzGi33wU9cqIQ.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/1ZFS7N918ojhhd0nQWdj3jz4w.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/A0Wcc7NgXMjUuFdquHDrIZpzZw0.woff2", weight: "600" }] }], { supportsExplicitInterCodegen: true });

export {
  stdin_default
};
