// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";
import {
  className as className2,
  css as css2,
  fonts as fonts2
} from "./chunks/chunk-5L2L6TVR.js";
import {
  className,
  css,
  fonts
} from "./chunks/chunk-VY5WWL2S.js";

// virtual:photo-item
import { Fragment as Fragment2 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/73s2XRS4okTTkcP9NVhE/uPO87sMavFRSA5KNmPsg/ysOIIzMbt.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, getFontsFromSharedStyle, getLoadingLazyAtYPosition, Image, RichText, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var cycleOrder = ["w_y1fprRT", "tw0giMFjy"];
var serializationHash = "framer-E083R";
var variantClassNames = { tw0giMFjy: "framer-v-grfmoy", w_y1fprRT: "framer-v-c2zeop" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { bounce: 0.2, delay: 0, duration: 0.4, type: "spring" };
var toResponsiveImage = (value) => {
  if (typeof value === "object" && value !== null && typeof value.src === "string") {
    return value;
  }
  return typeof value === "string" ? { src: value } : void 0;
};
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { Desktop: "w_y1fprRT", Phone: "tw0giMFjy" };
var getProps = ({ height, id, image, title, width, year, ...props }) => {
  return { ...props, Jw4x6a9q6: year ?? props.Jw4x6a9q6 ?? "/2024", variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "w_y1fprRT", xegnZyZ_a: title ?? props.xegnZyZ_a ?? "Title", YJkDo5GsF: image ?? props.YJkDo5GsF };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className: className3, layoutId, variant, YJkDo5GsF, xegnZyZ_a, Jw4x6a9q6, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "w_y1fprRT", ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [className2, className];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsxs(motion.div, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-c2zeop", className3, classNames), "data-framer-name": "Desktop", layoutDependency, layoutId: "w_y1fprRT", ref: refBinding, style: { ...style }, ...addPropertyOverrides({ tw0giMFjy: { "data-framer-name": "Phone" } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsx(Image, { background: { alt: "", fit: "fill", intrinsicHeight: 810, intrinsicWidth: 1125, loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0 + (((componentViewport?.height || 868) - 0 - 868.6) / 2 + 0 + 0)), pixelHeight: 810, pixelWidth: 1125, sizes: componentViewport?.width || "100vw", ...toResponsiveImage(YJkDo5GsF) }, className: "framer-14pyp52", "data-framer-name": "Image-2", layoutDependency, layoutId: "lyHkkFBNz", style: { borderBottomLeftRadius: 18, borderBottomRightRadius: 18, borderTopLeftRadius: 18, borderTopRightRadius: 18 }, variants: { tw0giMFjy: { borderBottomLeftRadius: 14, borderBottomRightRadius: 14, borderTopLeftRadius: 14, borderTopRightRadius: 14 } }, ...addPropertyOverrides({ tw0giMFjy: { background: { alt: "", fit: "fill", intrinsicHeight: 810, intrinsicWidth: 1125, loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0 + (((componentViewport?.height || 320) - 0 - 852.6) / 2 + 0 + 0)), pixelHeight: 810, pixelWidth: 1125, sizes: componentViewport?.width || "100vw", ...toResponsiveImage(YJkDo5GsF) } } }, baseVariant, gestureVariant) }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-17ei0hq", "data-framer-name": "Project header", layoutDependency, layoutId: "KEcAGHgyh", children: [/* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-9v8dhs", "data-styles-preset": "oFAZmwcVJ", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10)))" }, children: "Meet the team" }) }), className: "framer-4w8wn3", "data-framer-name": "Project name", fonts: ["Inter"], layoutDependency, layoutId: "nzdanlGWQ", style: { "--extracted-r6o4lv": "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", "--framer-paragraph-spacing": "0px" }, text: xegnZyZ_a, verticalAlignment: "top", withExternalLayout: true }), /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-2s58fc", "data-styles-preset": "svYtzYwMA", children: "/2024" }) }), className: "framer-1pfmx1k", "data-framer-name": "Date", fonts: ["Inter"], layoutDependency, layoutId: "tSRILliqD", style: { "--framer-paragraph-spacing": "0px", opacity: 0.6 }, text: Jw4x6a9q6, verticalAlignment: "top", withExternalLayout: true })] })] }) }) }) });
});
var css3 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-E083R.framer-tdqycf, .framer-E083R .framer-tdqycf { display: block; }", ".framer-E083R.framer-c2zeop { align-content: flex-start; align-items: flex-start; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 34px; height: min-content; justify-content: center; overflow: hidden; padding: 0px; position: relative; width: 1128px; }", ".framer-E083R .framer-14pyp52 { aspect-ratio: 1.3888888888888888 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 812px); overflow: visible; position: relative; width: 100%; }", ".framer-E083R .framer-17ei0hq { align-content: flex-end; align-items: flex-end; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 15px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: min-content; }", ".framer-E083R .framer-4w8wn3, .framer-E083R .framer-1pfmx1k { flex: none; height: auto; position: relative; white-space: pre; width: auto; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-E083R.framer-c2zeop, .framer-E083R .framer-17ei0hq { gap: 0px; } .framer-E083R.framer-c2zeop > * { margin: 0px; margin-bottom: calc(34px / 2); margin-top: calc(34px / 2); } .framer-E083R.framer-c2zeop > :first-child { margin-top: 0px; } .framer-E083R.framer-c2zeop > :last-child { margin-bottom: 0px; } .framer-E083R .framer-17ei0hq > * { margin: 0px; margin-left: calc(15px / 2); margin-right: calc(15px / 2); } .framer-E083R .framer-17ei0hq > :first-child { margin-left: 0px; } .framer-E083R .framer-17ei0hq > :last-child { margin-right: 0px; } }", ".framer-E083R.framer-v-grfmoy.framer-c2zeop { gap: 18px; width: 390px; }", ".framer-E083R.framer-v-grfmoy .framer-14pyp52 { height: var(--framer-aspect-ratio-supported, 281px); order: 0; }", ".framer-E083R.framer-v-grfmoy .framer-17ei0hq { order: 1; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-E083R.framer-v-grfmoy.framer-c2zeop { gap: 0px; } .framer-E083R.framer-v-grfmoy.framer-c2zeop > * { margin: 0px; margin-bottom: calc(18px / 2); margin-top: calc(18px / 2); } .framer-E083R.framer-v-grfmoy.framer-c2zeop > :first-child { margin-top: 0px; } .framer-E083R.framer-v-grfmoy.framer-c2zeop > :last-child { margin-bottom: 0px; } }", ...css2, ...css];
var FramerysOIIzMbt = withCSS(Component, css3, "framer-E083R");
var stdin_default = FramerysOIIzMbt;
FramerysOIIzMbt.displayName = "Photo item";
FramerysOIIzMbt.defaultProps = { height: 868, width: 1128 };
addPropertyControls(FramerysOIIzMbt, { variant: { options: ["w_y1fprRT", "tw0giMFjy"], optionTitles: ["Desktop", "Phone"], title: "Variant", type: ControlType.Enum }, YJkDo5GsF: { title: "Image", type: ControlType.ResponsiveImage }, xegnZyZ_a: { defaultValue: "Title", displayTextArea: false, title: "Title", type: ControlType.String }, Jw4x6a9q6: { defaultValue: "/2024", displayTextArea: false, title: "Year", type: ControlType.String } });
addFonts(FramerysOIIzMbt, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5vvr9Vy74if2I6bQbJvbw7SY1pQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/EOr0mi4hNtlgWNn9if640EZzXCo.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/Y9k9QrlZAqio88Klkmbd8VoMQc.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/OYrD2tBIBPvoJXiIHnLoOXnY9M.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/JeYwfuaPfZHQhEG8U5gtPDZ7WQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/vQyevYAyHtARFwPqUzQGpnDs.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/b6Y37FthZeALduNqHicBT6FutY.woff2", weight: "400" }] }, ...getFontsFromSharedStyle(fonts2), ...getFontsFromSharedStyle(fonts)], { supportsExplicitInterCodegen: true });

// virtual:photo-item
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {
  "base": "tw0giMFjy",
  "xl": "w_y1fprRT"
};
stdin_default.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default);
export {
  ComponentWithRoot as default
};
