// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";

// virtual:created-by
import { Fragment as Fragment2 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/N10U22jxXY23ZYp8w0mb/0FQySw5R2wgCyRX1rvuq/PJOVhWHcj.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, getLoadingLazyAtYPosition, Image, Link, RichText, SVG, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var enabledGestures = { B0SH9BKq8: { hover: true } };
var cycleOrder = ["B0SH9BKq8", "twHrEezFe"];
var serializationHash = "framer-q3YIU";
var variantClassNames = { B0SH9BKq8: "framer-v-z99530", twHrEezFe: "framer-v-1hr5ly" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { delay: 0, duration: 0.25, ease: [0.96, -0.02, 0.38, 1.01], type: "tween" };
var toResponsiveImage = (value) => {
  if (typeof value === "object" && value !== null && typeof value.src === "string") {
    return value;
  }
  return typeof value === "string" ? { src: value } : void 0;
};
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { Desktop: "B0SH9BKq8", Phone: "twHrEezFe" };
var getProps = ({ height, id, image, text1, text2, width, ...props }) => {
  return { ...props, d7bblw14I: text2 ?? props.d7bblw14I ?? "Anatolii Dmitrienko", HXK9GfHoc: image ?? props.HXK9GfHoc ?? { alt: "Profile portrait of a man in a grey shirt", src: "https://framerusercontent.com/images/nr593SABZlC19HZOrgnV0sWc8A.jpg" }, qEnLTWHb7: text1 ?? props.qEnLTWHb7 ?? "Created by", variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "B0SH9BKq8" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className, layoutId, variant, qEnLTWHb7, HXK9GfHoc, d7bblw14I, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "B0SH9BKq8", enabledGestures, ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsx(Link, { href: "https://templifica.com/", motionChild: true, nodeId: "B0SH9BKq8", openInNewTab: true, scopeId: "PJOVhWHcj", children: /* @__PURE__ */ _jsxs(motion.a, { ...restProps, ...gestureHandlers, className: `${cx(scopingClassNames, "framer-z99530", className, classNames)} framer-nfrv0x`, "data-framer-name": "Desktop", layoutDependency, layoutId: "B0SH9BKq8", ref: refBinding, style: { ...style }, ...addPropertyOverrides({ "B0SH9BKq8-hover": { "data-framer-name": void 0 }, twHrEezFe: { "data-framer-name": "Phone" } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItTWVkaXVt", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "13px", "--framer-font-weight": "500", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "1.4em", "--framer-text-alignment": "center", "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Created by" }) }), className: "framer-8k0hw5", "data-framer-name": "Created by", fonts: ["Inter-Medium"], layoutDependency, layoutId: "E52FqXjbV", style: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", "--framer-paragraph-spacing": "0px", opacity: 0.6 }, text: qEnLTWHb7, verticalAlignment: "top", withExternalLayout: true }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-1e1s9u0", "data-framer-name": "Image", layoutDependency, layoutId: "JJ9xSSgPH", children: [/* @__PURE__ */ _jsx(Image, { background: { alt: "", fit: "fill", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + (0 + ((componentViewport?.height || 28) - 0 - 28) / 2) + 0), pixelHeight: 73, pixelWidth: 73, sizes: "28px", ...toResponsiveImage(HXK9GfHoc) }, className: "framer-1tyus1t", "data-framer-name": "Avatar", layoutDependency, layoutId: "AElEH8tHb", style: { borderBottomLeftRadius: "100%", borderBottomRightRadius: "100%", borderTopLeftRadius: "100%", borderTopRightRadius: "100%", scale: 1 }, variants: { "B0SH9BKq8-hover": { scale: 0.4 } }, ...addPropertyOverrides({ "B0SH9BKq8-hover": { background: { alt: "", fit: "fill", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + (0 + ((componentViewport?.height || 28) - 0 - 28) / 2) + -28), pixelHeight: 73, pixelWidth: 73, sizes: "28px", ...toResponsiveImage(HXK9GfHoc) } } }, baseVariant, gestureVariant) }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-h62gue", "data-framer-name": "Logo", layoutDependency, layoutId: "QJT5x9JFn", style: { backgroundColor: "rgb(210, 255, 55)", borderBottomLeftRadius: "100%", borderBottomRightRadius: "100%", borderTopLeftRadius: "100%", borderTopRightRadius: "100%", scale: 0.5 }, variants: { "B0SH9BKq8-hover": { scale: 1 } }, children: /* @__PURE__ */ _jsx(SVG, { className: "framer-15i71li", "data-framer-name": "logo", fill: "black", intrinsicHeight: 47, intrinsicWidth: 33, layoutDependency, layoutId: "eU6pUCU4v", svg: '<svg width="33" height="47" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M16.244 47a1.828 1.828 0 0 1-1.27-.515 1.73 1.73 0 0 1-.526-1.244V30.348l-12.653-.005c-.311 0-.617-.08-.888-.23a1.779 1.779 0 0 1-.655-.63 1.733 1.733 0 0 1-.034-1.74L15.101.92c.19-.344.492-.615.858-.772A1.83 1.83 0 0 1 17.12.054c.387.097.73.317.975.625.245.31.378.689.378 1.08v14.92h12.732c.311 0 .618.08.888.231.271.151.497.369.656.632a1.733 1.733 0 0 1 .03 1.741L17.82 46.085a1.776 1.776 0 0 1-.66.669 1.824 1.824 0 0 1-.915.246Z" fill="#222"/></svg>', withExternalLayout: true }) })] }), /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItTWVkaXVt", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "13px", "--framer-font-weight": "500", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "1.4em", "--framer-text-alignment": "center", "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Anatolii Dmitrienko" }) }), className: "framer-kriueg", "data-framer-name": "Anatolii Dmitrienko", fonts: ["Inter-Medium"], layoutDependency, layoutId: "WgF5DYaY1", style: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", "--framer-paragraph-spacing": "0px" }, text: d7bblw14I, verticalAlignment: "top", withExternalLayout: true })] }) }) }) }) });
});
var css = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-q3YIU.framer-nfrv0x, .framer-q3YIU .framer-nfrv0x { display: block; }", ".framer-q3YIU.framer-z99530 { align-content: center; align-items: center; cursor: pointer; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 6px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 0px; position: relative; text-decoration: none; width: min-content; }", ".framer-q3YIU .framer-8k0hw5, .framer-q3YIU .framer-kriueg { flex: none; height: auto; position: relative; white-space: pre; width: auto; }", ".framer-q3YIU .framer-1e1s9u0 { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 4px; height: min-content; justify-content: center; overflow: visible; padding: 0px; position: relative; width: min-content; }", ".framer-q3YIU .framer-1tyus1t { aspect-ratio: 1 / 1; flex: none; height: 28px; position: relative; width: var(--framer-aspect-ratio-supported, 28px); }", ".framer-q3YIU .framer-h62gue { align-content: center; align-items: center; aspect-ratio: 1 / 1; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 4px; height: var(--framer-aspect-ratio-supported, 28px); justify-content: center; left: 0px; padding: 0px; position: absolute; top: 28px; width: 28px; z-index: 1; }", ".framer-q3YIU .framer-15i71li { aspect-ratio: 0.7021276595744681 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 14px); position: relative; width: 10px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-q3YIU.framer-z99530, .framer-q3YIU .framer-1e1s9u0, .framer-q3YIU .framer-h62gue { gap: 0px; } .framer-q3YIU.framer-z99530 > * { margin: 0px; margin-left: calc(6px / 2); margin-right: calc(6px / 2); } .framer-q3YIU.framer-z99530 > :first-child, .framer-q3YIU .framer-1e1s9u0 > :first-child, .framer-q3YIU .framer-h62gue > :first-child { margin-left: 0px; } .framer-q3YIU.framer-z99530 > :last-child, .framer-q3YIU .framer-1e1s9u0 > :last-child, .framer-q3YIU .framer-h62gue > :last-child { margin-right: 0px; } .framer-q3YIU .framer-1e1s9u0 > *, .framer-q3YIU .framer-h62gue > * { margin: 0px; margin-left: calc(4px / 2); margin-right: calc(4px / 2); } }", ".framer-q3YIU.framer-v-1hr5ly.framer-z99530 { cursor: unset; }", ".framer-q3YIU.framer-v-z99530.hover .framer-1tyus1t { height: var(--framer-aspect-ratio-supported, 28px); left: 0px; position: absolute; top: -28px; width: 28px; z-index: 1; }", ".framer-q3YIU.framer-v-z99530.hover .framer-h62gue { left: unset; position: relative; top: unset; }"];
var FramerPJOVhWHcj = withCSS(Component, css, "framer-q3YIU");
var stdin_default = FramerPJOVhWHcj;
FramerPJOVhWHcj.displayName = "Created by";
FramerPJOVhWHcj.defaultProps = { height: 28, width: 210 };
addPropertyControls(FramerPJOVhWHcj, { variant: { options: ["B0SH9BKq8", "twHrEezFe"], optionTitles: ["Desktop", "Phone"], title: "Variant", type: ControlType.Enum }, qEnLTWHb7: { defaultValue: "Created by", displayTextArea: false, title: "Text 1", type: ControlType.String }, HXK9GfHoc: { __defaultAssetReference: "data:framer/asset-reference,nr593SABZlC19HZOrgnV0sWc8A.jpg?originalFilename=image+61.jpg&preferredSize=auto", __vekterDefault: { alt: "Profile portrait of a man in a grey shirt", assetReference: "data:framer/asset-reference,nr593SABZlC19HZOrgnV0sWc8A.jpg?originalFilename=image+61.jpg&preferredSize=auto" }, title: "Image", type: ControlType.ResponsiveImage }, d7bblw14I: { defaultValue: "Anatolii Dmitrienko", displayTextArea: false, title: "Text 2", type: ControlType.String } });
addFonts(FramerPJOVhWHcj, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5A3Ce6C9YYmCjpQx9M4inSaKU.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/Qx95Xyt0Ka3SGhinnbXIGpEIyP4.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/6mJuEAguuIuMog10gGvH5d3cl8.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/xYYWaj7wCU5zSQH0eXvSaS19wo.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/otTaNuNpVK4RbdlT7zDDdKvQBA.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/d3tHnaQIAeqiE5hGcRw4mmgWYU.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/DolVirEGb34pEXEp8t8FQBSK4.woff2", weight: "500" }] }], { supportsExplicitInterCodegen: true });

// virtual:created-by
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {};
stdin_default.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default);
export {
  ComponentWithRoot as default
};
