// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";

// virtual:steps
import { Fragment as Fragment2 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/pa1lStjkdNyFVnLJM7LW/M4yFLBqPxO3ZhHYTWaZz/PSo3NS0KW.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, useComponentViewport, useLocaleInfo, useVariantState, withCSS, withFX } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var MotionDivWithFX = withFX(motion.div);
var cycleOrder = ["YbnbRhkd0", "LM4PtgQYZ", "MXYbsQBHm", "O0HuyVtD_"];
var serializationHash = "framer-O9auB";
var variantClassNames = { LM4PtgQYZ: "framer-v-1gzslce", MXYbsQBHm: "framer-v-4wgxg2", O0HuyVtD_: "framer-v-j07t1s", YbnbRhkd0: "framer-v-twnopp" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { bounce: 0.2, delay: 0, duration: 0.4, type: "spring" };
var animation = { opacity: 0, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, x: 0, y: 0 };
var transition2 = { bounce: 0.1, delay: 0.8, duration: 0.9, type: "spring" };
var transition3 = { bounce: 0.1, delay: 1, duration: 0.9, type: "spring" };
var transition4 = { bounce: 0.1, delay: 1.2, duration: 0.9, type: "spring" };
var transition5 = { bounce: 0.1, delay: 1.4, duration: 0.9, type: "spring" };
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { "1": "YbnbRhkd0", "2": "LM4PtgQYZ", "3": "MXYbsQBHm", "4": "O0HuyVtD_" };
var getProps = ({ height, id, width, ...props }) => {
  return { ...props, variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "YbnbRhkd0" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className, layoutId, variant, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "YbnbRhkd0", ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsxs(motion.div, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-twnopp", className, classNames), "data-framer-name": "1", layoutDependency, layoutId: "YbnbRhkd0", ref: refBinding, style: { ...style }, ...addPropertyOverrides({ LM4PtgQYZ: { "data-framer-name": "2" }, MXYbsQBHm: { "data-framer-name": "3" }, O0HuyVtD_: { "data-framer-name": "4" } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsx(MotionDivWithFX, { __framer__animate: { transition: transition2 }, __framer__animateOnce: true, __framer__enter: animation, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-bun9v6", "data-framer-name": "Ellipse 5", layoutDependency, layoutId: "EWhE9Iz56", style: { backgroundColor: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", borderBottomLeftRadius: "100%", borderBottomRightRadius: "100%", borderTopLeftRadius: "100%", borderTopRightRadius: "100%" } }), /* @__PURE__ */ _jsx(MotionDivWithFX, { __framer__animate: { transition: transition3 }, __framer__animateOnce: true, __framer__enter: animation, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-d33dnr", "data-framer-name": "Ellipse 5", layoutDependency, layoutId: "jT3KjWbDL", style: { backgroundColor: "rgb(233, 233, 233)", borderBottomLeftRadius: "100%", borderBottomRightRadius: "100%", borderTopLeftRadius: "100%", borderTopRightRadius: "100%" }, variants: { LM4PtgQYZ: { backgroundColor: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))" }, MXYbsQBHm: { backgroundColor: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))" }, O0HuyVtD_: { backgroundColor: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))" } } }), /* @__PURE__ */ _jsx(MotionDivWithFX, { __framer__animate: { transition: transition4 }, __framer__animateOnce: true, __framer__enter: animation, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-1aylcff", "data-framer-name": "Ellipse 5", layoutDependency, layoutId: "Pm8M2Rznl", style: { backgroundColor: "rgb(233, 233, 233)", borderBottomLeftRadius: "100%", borderBottomRightRadius: "100%", borderTopLeftRadius: "100%", borderTopRightRadius: "100%" }, variants: { MXYbsQBHm: { backgroundColor: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))" }, O0HuyVtD_: { backgroundColor: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))" } } }), /* @__PURE__ */ _jsx(MotionDivWithFX, { __framer__animate: { transition: transition5 }, __framer__animateOnce: true, __framer__enter: animation, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-1gqgp3j", "data-framer-name": "Ellipse 5", layoutDependency, layoutId: "brkpIqaWO", style: { backgroundColor: "rgb(233, 233, 233)", borderBottomLeftRadius: "100%", borderBottomRightRadius: "100%", borderTopLeftRadius: "100%", borderTopRightRadius: "100%" }, variants: { O0HuyVtD_: { backgroundColor: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))" } } })] }) }) }) });
});
var css = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-O9auB.framer-1wmsma2, .framer-O9auB .framer-1wmsma2 { display: block; }", ".framer-O9auB.framer-twnopp { align-content: center; align-items: center; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 3px; height: min-content; justify-content: center; overflow: hidden; padding: 0px; position: relative; width: min-content; }", ".framer-O9auB .framer-bun9v6, .framer-O9auB .framer-d33dnr, .framer-O9auB .framer-1aylcff, .framer-O9auB .framer-1gqgp3j { flex: none; height: 8px; position: relative; width: 8px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-O9auB.framer-twnopp { gap: 0px; } .framer-O9auB.framer-twnopp > * { margin: 0px; margin-left: calc(3px / 2); margin-right: calc(3px / 2); } .framer-O9auB.framer-twnopp > :first-child { margin-left: 0px; } .framer-O9auB.framer-twnopp > :last-child { margin-right: 0px; } }"];
var FramerPSo3NS0KW = withCSS(Component, css, "framer-O9auB");
var stdin_default = FramerPSo3NS0KW;
FramerPSo3NS0KW.displayName = "Steps";
FramerPSo3NS0KW.defaultProps = { height: 8, width: 41 };
addPropertyControls(FramerPSo3NS0KW, { variant: { options: ["YbnbRhkd0", "LM4PtgQYZ", "MXYbsQBHm", "O0HuyVtD_"], optionTitles: ["1", "2", "3", "4"], title: "Variant", type: ControlType.Enum } });
addFonts(FramerPSo3NS0KW, [{ explicitInter: true, fonts: [] }], { supportsExplicitInterCodegen: true });

// virtual:steps
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {};
stdin_default.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default);
export {
  ComponentWithRoot as default
};
