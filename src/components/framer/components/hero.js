// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";
import {
  className as className2,
  css as css2,
  fonts as fonts2
} from "./chunks/chunk-QOI7VI3Y.js";
import {
  stdin_default as stdin_default4
} from "./chunks/chunk-25SFVEMK.js";
import {
  stdin_default as stdin_default3
} from "./chunks/chunk-WUWH5KEE.js";
import {
  className as className3,
  css as css3,
  fonts as fonts3
} from "./chunks/chunk-IWIJOZKQ.js";
import "./chunks/chunk-CVJIPDTS.js";
import {
  stdin_default
} from "./chunks/chunk-5GTX57J2.js";
import {
  Video
} from "./chunks/chunk-G73PZF75.js";
import {
  Grain
} from "./chunks/chunk-MO4ELLBD.js";
import {
  stdin_default as stdin_default2
} from "./chunks/chunk-CHS3IKS5.js";
import "./chunks/chunk-MKEJQGYO.js";
import {
  className,
  css,
  fonts
} from "./chunks/chunk-D6DQ2HQ2.js";
import "./chunks/chunk-3LMAB7ZO.js";
import "./chunks/chunk-YWUWNR35.js";

// virtual:hero
import { Fragment as Fragment2 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/YlnZT9T2TSVRxOqTj1Ks/l0iC658Wkj8ymMCsLgoA/W6mI3CngM.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ComponentViewportProvider, ControlType, cx, getFonts, getFontsFromSharedStyle, ResolveLinks, RichText, SmartComponentScopedContainer, useComponentViewport, useLocaleInfo, useRouter, useVariantState, withCSS, withFX, withOptimizedAppearEffect } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";

// /:https://framerusercontent.com/modules/2mMO1EX5xNxIoJZ10MHC/mseA4evZ2eHB8ECkN8B1/oFAZmwcVJ.js
import { fontStore } from "unframer";
fontStore.loadFonts(["Inter-SemiBold", "Inter-Bold", "Inter-BoldItalic", "Inter-SemiBoldItalic"]);
var fonts4 = [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/hyOgCu0Xnghbimh0pE8QTvtt2AU.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/NeGmSOXrPBfEFIy5YZeHq17LEDA.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/oYaAX5himiTPYuN8vLWnqBbfD2s.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/lEJLP4R0yuCaMCjSXYHtJw72M.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/cRJyLNuTJR5jbyKzGi33wU9cqIQ.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/1ZFS7N918ojhhd0nQWdj3jz4w.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/A0Wcc7NgXMjUuFdquHDrIZpzZw0.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/DpPBYI0sL4fYLgAkX8KXOPVt7c.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/4RAEQdEOrcnDkhHiiCbJOw92Lk.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/1K3W8DizY3v4emK8Mb08YHxTbs.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/tUSCtfYVM1I1IchuyCwz9gDdQ.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/VgYFWiwsAC5OYxAycRXXvhze58.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/DXD0Q7LSl7HEvDzucnyLnGBHM.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/GIryZETIX4IFypco5pYZONKhJIo.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/H89BbHkbHDzlxZzxi8uPzTsp90.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/u6gJwDuwB143kpNK1T1MDKDWkMc.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/43sJ6MfOPh1LCJt46OvyDuSbA6o.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/wccHG0r4gBDAIRhfHiOlq6oEkqw.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/WZ367JPwf9bRW6LdTHN8rXgSjw.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/QxmhnWTzLtyjIiZcfaLIJ8EFBXU.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/2A4Xx7CngadFGlVV4xrO06OBHY.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/vxBnBhH8768IFAXAb4Qf6wQHKs.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/zSsEuoJdh8mcFVk976C05ZfQr8.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/b8ezwLrN7h2AUoPEENcsTMVJ0.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/mvNEIBLyHbscgHtwfsByjXUz3XY.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/6FI2EneKzM3qBy5foOZXey7coCA.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/qrVgiXNd6RuQjETYQiVQ9nqCk.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/NHHeAKJVP0ZWHk5YZnQQChIsBM.woff2", weight: "600" }] }];
var css4 = [`.framer-WyC4r .framer-styles-preset-9v8dhs:not(.rich-text-wrapper), .framer-WyC4r .framer-styles-preset-9v8dhs.rich-text-wrapper p { --framer-font-family: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-open-type-features: 'blwf' on, 'cv09' on, 'cv03' on, 'cv04' on, 'cv11' on; --framer-font-size: 18px; --framer-font-style: normal; --framer-font-style-bold: normal; --framer-font-style-bold-italic: italic; --framer-font-style-italic: italic; --framer-font-variation-axes: normal; --framer-font-weight: 600; --framer-font-weight-bold: 700; --framer-font-weight-bold-italic: 700; --framer-font-weight-italic: 600; --framer-letter-spacing: -0.04em; --framer-line-height: 1.2em; --framer-paragraph-spacing: 20px; --framer-text-alignment: start; --framer-text-color: var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, #ffffff); --framer-text-decoration: none; --framer-text-stroke-color: initial; --framer-text-stroke-width: initial; --framer-text-transform: none; }`, `@media (max-width: 1199px) and (min-width: 810px) { .framer-WyC4r .framer-styles-preset-9v8dhs:not(.rich-text-wrapper), .framer-WyC4r .framer-styles-preset-9v8dhs.rich-text-wrapper p { --framer-font-family: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-open-type-features: 'blwf' on, 'cv09' on, 'cv03' on, 'cv04' on, 'cv11' on; --framer-font-size: 15px; --framer-font-style: normal; --framer-font-style-bold: normal; --framer-font-style-bold-italic: italic; --framer-font-style-italic: italic; --framer-font-variation-axes: normal; --framer-font-weight: 600; --framer-font-weight-bold: 700; --framer-font-weight-bold-italic: 700; --framer-font-weight-italic: 600; --framer-letter-spacing: -0.04em; --framer-line-height: 1.2em; --framer-paragraph-spacing: 20px; --framer-text-alignment: start; --framer-text-color: var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, #ffffff); --framer-text-decoration: none; --framer-text-stroke-color: initial; --framer-text-stroke-width: initial; --framer-text-transform: none; } }`, `@media (max-width: 809px) and (min-width: 0px) { .framer-WyC4r .framer-styles-preset-9v8dhs:not(.rich-text-wrapper), .framer-WyC4r .framer-styles-preset-9v8dhs.rich-text-wrapper p { --framer-font-family: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-open-type-features: 'blwf' on, 'cv09' on, 'cv03' on, 'cv04' on, 'cv11' on; --framer-font-size: 15px; --framer-font-style: normal; --framer-font-style-bold: normal; --framer-font-style-bold-italic: italic; --framer-font-style-italic: italic; --framer-font-variation-axes: normal; --framer-font-weight: 600; --framer-font-weight-bold: 700; --framer-font-weight-bold-italic: 700; --framer-font-weight-italic: 600; --framer-letter-spacing: -0.04em; --framer-line-height: 1.2em; --framer-paragraph-spacing: 20px; --framer-text-alignment: start; --framer-text-color: var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, #ffffff); --framer-text-decoration: none; --framer-text-stroke-color: initial; --framer-text-stroke-width: initial; --framer-text-transform: none; } }`];
var className4 = "framer-WyC4r";

// /:https://framerusercontent.com/modules/YlnZT9T2TSVRxOqTj1Ks/l0iC658Wkj8ymMCsLgoA/W6mI3CngM.js
var MotionDivWithFXWithOptimizedAppearEffect = withOptimizedAppearEffect(withFX(motion.div));
var RichTextWithFXWithOptimizedAppearEffect = withOptimizedAppearEffect(withFX(RichText));
var PlusIconFonts = getFonts(stdin_default);
var ContactCardFonts = getFonts(stdin_default4);
var VideoFonts = getFonts(Video);
var GrainFonts = getFonts(Grain);
var MotionDivWithFX = withFX(motion.div);
var IntroFonts = getFonts(stdin_default2);
var LogoCardSmallFonts = getFonts(stdin_default3);
var SmartComponentScopedContainerWithFX = withFX(SmartComponentScopedContainer);
var serializationHash = "framer-WiUcW";
var variantClassNames = { mZb2MK3Em: "framer-v-ykgwn3" };
var transition1 = { bounce: 0.2, delay: 0, duration: 0.4, type: "spring" };
var transition2 = { damping: 27, delay: 2.1, mass: 0.3, stiffness: 121, type: "spring" };
var animation = { opacity: 1, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, transition: transition2, x: 0, y: 0 };
var animation1 = { opacity: 1e-3, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, x: 0, y: 170 };
var transformTemplate1 = (_, t) => `translateY(-50%) ${t}`;
var transition3 = { damping: 27, delay: 2.08, mass: 0.3, stiffness: 121, type: "spring" };
var animation2 = { opacity: 1, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, transition: transition3, x: 0, y: 0 };
var animation3 = { opacity: 1e-3, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, x: 0, y: 160 };
var transition4 = { damping: 30, delay: 2.38, mass: 1, stiffness: 255, type: "spring" };
var animation4 = { opacity: 1, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, transition: transition4, x: 0, y: 0 };
var animation5 = { opacity: 1, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, x: 0, y: 31 };
var transition5 = { damping: 30, delay: 2.29, mass: 1, stiffness: 255, type: "spring" };
var animation6 = { opacity: 1, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, transition: transition5, x: 0, y: 0 };
var transition6 = { damping: 30, delay: 2.2, mass: 1, stiffness: 255, type: "spring" };
var animation7 = { opacity: 1, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, transition: transition6, x: 0, y: 0 };
var transition7 = { damping: 30, delay: 2.11, mass: 1, stiffness: 255, type: "spring" };
var animation8 = { opacity: 1, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, transition: transition7, x: 0, y: 0 };
var transition8 = { damping: 27, delay: 2.04, mass: 0.3, stiffness: 121, type: "spring" };
var animation9 = { opacity: 0.6, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, transition: transition8, x: 0, y: 0 };
var animation10 = { opacity: 1e-3, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, x: 0, y: 180 };
var transition9 = { damping: 27, delay: 2, mass: 0.3, stiffness: 121, type: "spring" };
var animation11 = { opacity: 1, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, transition: transition9, x: 0, y: 0 };
var transition10 = { damping: 27, delay: 2.05, mass: 0.3, stiffness: 121, type: "spring" };
var animation12 = { opacity: 1, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, transition: transition10, x: 0, y: 0 };
var animation13 = { opacity: 1e-3, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, x: 0, y: 250 };
var animation14 = { opacity: 0, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, x: 0, y: 0 };
var transition11 = { bounce: 0.1, delay: 0.2, duration: 1.2, type: "spring" };
var transition12 = { bounce: 0.1, delay: 0.1, duration: 1.2, type: "spring" };
var addImageAlt = (image, alt) => {
  if (!image || typeof image !== "object") {
    return;
  }
  return { ...image, alt };
};
var transition13 = { bounce: 0.1, delay: 0.3, duration: 1.2, type: "spring" };
var transition14 = { bounce: 0.1, delay: 0.6, duration: 1.2, type: "spring" };
var transition15 = { bounce: 0.1, delay: 0.4, duration: 1.2, type: "spring" };
var transition16 = { bounce: 0.1, delay: 0.5, duration: 1.2, type: "spring" };
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var getProps = ({ height, id, scrollSection, scrollSection2, width, ...props }) => {
  return { ...props, DgLQZwd8F: scrollSection2 ?? props.DgLQZwd8F, yhdjbZXJ7: scrollSection ?? props.yhdjbZXJ7 };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className: className5, layoutId, variant, yhdjbZXJ7, DgLQZwd8F, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ defaultVariant: "mZb2MK3Em", ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [className4, className3, className, className2];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  const router = useRouter();
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsxs(motion.section, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-ykgwn3", className5, classNames), "data-framer-name": "Variant 1", layoutDependency, layoutId: "mZb2MK3Em", ref: refBinding, style: { ...style }, children: [/* @__PURE__ */ _jsx(MotionDivWithFX, { __framer__styleTransformEffectEnabled: true, __framer__transformTargets: [{ target: { opacity: 1, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, x: 0, y: 0 } }, { ref: yhdjbZXJ7, target: { opacity: 1, rotate: 0, rotateX: -50, rotateY: 0, scale: 0.95, skewX: 0, skewY: 0, x: 0, y: 180 } }], __framer__transformTrigger: "onScrollTarget", __framer__transformViewportThreshold: 0.5, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-19j2lei", "data-framer-name": "First screen", layoutDependency, layoutId: "SsiyyAKOd", style: { backgroundColor: "rgb(245, 245, 245)" }, children: /* @__PURE__ */ _jsxs(motion.div, { className: "framer-n6szvs", "data-framer-name": "Container", layoutDependency, layoutId: "cDGIceBVm", children: [/* @__PURE__ */ _jsxs(motion.div, { className: "framer-1kxwov3", "data-framer-name": "Content", layoutDependency, layoutId: "OM0vY4Zzn", children: [/* @__PURE__ */ _jsxs(motion.div, { className: "framer-13u7xh5", "data-framer-name": "Top", layoutDependency, layoutId: "GoGJWrx2q", children: [/* @__PURE__ */ _jsxs(motion.div, { className: "framer-1spzsp8", "data-framer-name": "Company", layoutDependency, layoutId: "Islf6O_Sg", children: [/* @__PURE__ */ _jsxs(MotionDivWithFXWithOptimizedAppearEffect, { __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, animate: animation, className: "framer-al5dzj", "data-framer-appear-id": "al5dzj", "data-framer-name": "Title", initial: animation1, layoutDependency, layoutId: "jasZ0qyCv", optimized: true, children: [/* @__PURE__ */ _jsxs(motion.div, { className: "framer-13yua5i", "data-framer-name": "Container", layoutDependency, layoutId: "KIopm_d6y", children: [/* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "201.09981069002257px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.06em", "--framer-line-height": "120%", "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "fabrica" }) }), className: "framer-j23lg0", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "Z_sVlFJ8j", style: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", "--framer-paragraph-spacing": "0px" }, transformTemplate: transformTemplate1, verticalAlignment: "top", viewBox: "0 0 612 241", withExternalLayout: true }), /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "198.85472606989646px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.06em", "--framer-line-height": "83%", "--framer-text-color": "var(--extracted-r6o4lv, rgb(255, 255, 255))" }, children: "fabrica" }) }), className: "framer-1no2xs1", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "kmDEyHhv7", style: { "--extracted-r6o4lv": "rgb(255, 255, 255)", "--framer-paragraph-spacing": "0px", opacity: 0 }, verticalAlignment: "top", viewBox: "0 0 604.9312835641938 165", withExternalLayout: true })] }), /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "908.5190451173735px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.06em", "--framer-line-height": "110%", "--framer-text-color": "var(--extracted-r6o4lv, rgb(255, 255, 255))" }, children: "\xAE" }) }), className: "framer-1o25fny", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "x_pXVvWhE", style: { "--extracted-r6o4lv": "rgb(255, 255, 255)", "--framer-paragraph-spacing": "0px" }, verticalAlignment: "top", viewBox: "0 0 605.5111427070424 999", withExternalLayout: true })] }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-gxz29i", "data-framer-name": "Studio", layoutDependency, layoutId: "FqtFig7cN", children: [/* @__PURE__ */ _jsx(RichTextWithFXWithOptimizedAppearEffect, { __fromCanvasComponent: true, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, animate: animation2, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "51.3674213143858px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.06em", "--framer-line-height": "120%", "--framer-text-alignment": "right", "--framer-text-color": "var(--extracted-r6o4lv, rgb(255, 255, 255))" }, children: "Studio" }) }), className: "framer-11hmflf", "data-framer-appear-id": "11hmflf", fonts: ["Inter-SemiBold"], initial: animation3, layoutDependency, layoutId: "aVwampYiO", optimized: true, style: { "--extracted-r6o4lv": "rgb(255, 255, 255)", "--framer-paragraph-spacing": "0px" }, verticalAlignment: "top", viewBox: "0 0 144 62", viewBoxScale: 0.98, withExternalLayout: true }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-112dz82", "data-framer-name": "filler", layoutDependency, layoutId: "FOXnSCRyM" })] })] }), /* @__PURE__ */ _jsxs(MotionDivWithFXWithOptimizedAppearEffect, { __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, animate: animation, className: "framer-o59pbo", "data-framer-appear-id": "o59pbo", "data-framer-name": "Services", initial: animation1, layoutDependency, layoutId: "jHIIF8rGV", optimized: true, children: [/* @__PURE__ */ _jsx(motion.div, { className: "framer-qjo1yr", "data-framer-name": "1", layoutDependency, layoutId: "qcJNwLeig", children: /* @__PURE__ */ _jsx(RichTextWithFXWithOptimizedAppearEffect, { __fromCanvasComponent: true, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, animate: animation4, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-9v8dhs", "data-styles-preset": "oFAZmwcVJ", children: "Branding and Identity" }) }), className: "framer-ufy2l9", "data-framer-appear-id": "ufy2l9", fonts: ["Inter"], initial: animation5, layoutDependency, layoutId: "t7JRbHuw3", optimized: true, style: { "--framer-paragraph-spacing": "0px" }, verticalAlignment: "top", withExternalLayout: true }) }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-1ghbbfu", "data-framer-name": "2", layoutDependency, layoutId: "J931omt1m", children: /* @__PURE__ */ _jsx(RichTextWithFXWithOptimizedAppearEffect, { __fromCanvasComponent: true, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, animate: animation6, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-9v8dhs", "data-styles-preset": "oFAZmwcVJ", children: "Social Media Marketing" }) }), className: "framer-19u5y4d", "data-framer-appear-id": "19u5y4d", fonts: ["Inter"], initial: animation5, layoutDependency, layoutId: "EqvwkwoZT", optimized: true, style: { "--framer-paragraph-spacing": "0px" }, verticalAlignment: "top", withExternalLayout: true }) }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-2aaxi6", "data-framer-name": "3", layoutDependency, layoutId: "OKcURAIva", children: /* @__PURE__ */ _jsx(RichTextWithFXWithOptimizedAppearEffect, { __fromCanvasComponent: true, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, animate: animation7, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-9v8dhs", "data-styles-preset": "oFAZmwcVJ", children: "Web Design and Development" }) }), className: "framer-jehyco", "data-framer-appear-id": "jehyco", fonts: ["Inter"], initial: animation5, layoutDependency, layoutId: "iHL4bXBZh", optimized: true, style: { "--framer-paragraph-spacing": "0px" }, verticalAlignment: "top", withExternalLayout: true }) }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-7dqgyp", "data-framer-name": "4", layoutDependency, layoutId: "oQUiLJDNf", children: /* @__PURE__ */ _jsx(RichTextWithFXWithOptimizedAppearEffect, { __fromCanvasComponent: true, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, animate: animation8, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-9v8dhs", "data-styles-preset": "oFAZmwcVJ", children: "SEO Optimization" }) }), className: "framer-1cyb64j", "data-framer-appear-id": "1cyb64j", fonts: ["Inter"], initial: animation5, layoutDependency, layoutId: "kbXUfII1p", optimized: true, style: { "--framer-paragraph-spacing": "0px" }, verticalAlignment: "top", withExternalLayout: true }) })] })] }), /* @__PURE__ */ _jsxs(MotionDivWithFXWithOptimizedAppearEffect, { __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 0.6, animate: animation9, className: "framer-5d4iqh", "data-framer-appear-id": "5d4iqh", "data-framer-name": "Icons", initial: animation10, layoutDependency, layoutId: "tVtD6d21h", optimized: true, style: { opacity: 0.6 }, children: [/* @__PURE__ */ _jsx(motion.div, { className: "framer-1mqmmbv", "data-framer-name": "Container", layoutDependency, layoutId: "SBrkoOTKB", children: /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 16, width: "16px", children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-1x92z13-container", layoutDependency, layoutId: "agX48BCot-container", nodeId: "agX48BCot", rendersWithMotion: true, scopeId: "W6mI3CngM", children: /* @__PURE__ */ _jsx(stdin_default, { gUDzlhgLq: true, height: "100%", id: "agX48BCot", layoutId: "agX48BCot", Rl_qLe3MC: "rgb(255, 255, 255)", style: { height: "100%", width: "100%" }, width: "100%" }) }) }) }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-ok7hg1", "data-framer-name": "Container", layoutDependency, layoutId: "lrITJ4NVC", children: /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 16, width: "16px", children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-1oz0pio-container", layoutDependency, layoutId: "q4dA0tf1B-container", nodeId: "q4dA0tf1B", rendersWithMotion: true, scopeId: "W6mI3CngM", children: /* @__PURE__ */ _jsx(stdin_default, { gUDzlhgLq: true, height: "100%", id: "q4dA0tf1B", layoutId: "q4dA0tf1B", Rl_qLe3MC: "rgb(255, 255, 255)", style: { height: "100%", width: "100%" }, width: "100%" }) }) }) }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-tuz1zi", "data-framer-name": "Container", layoutDependency, layoutId: "YlKGZSpl3", children: /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 16, width: "16px", children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-3wo0sc-container", layoutDependency, layoutId: "LUbtxWG_C-container", nodeId: "LUbtxWG_C", rendersWithMotion: true, scopeId: "W6mI3CngM", children: /* @__PURE__ */ _jsx(stdin_default, { gUDzlhgLq: true, height: "100%", id: "LUbtxWG_C", layoutId: "LUbtxWG_C", Rl_qLe3MC: "rgb(255, 255, 255)", style: { height: "100%", width: "100%" }, width: "100%" }) }) }) }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-nogkar", "data-framer-name": "Container", layoutDependency, layoutId: "im30DcpUV", children: /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 16, width: "16px", children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-i0d0xb-container", layoutDependency, layoutId: "yh95_Z378-container", nodeId: "yh95_Z378", rendersWithMotion: true, scopeId: "W6mI3CngM", children: /* @__PURE__ */ _jsx(stdin_default, { gUDzlhgLq: true, height: "100%", id: "yh95_Z378", layoutId: "yh95_Z378", Rl_qLe3MC: "rgb(255, 255, 255)", style: { height: "100%", width: "100%" }, width: "100%" }) }) }) })] }), /* @__PURE__ */ _jsxs(MotionDivWithFXWithOptimizedAppearEffect, { __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, animate: animation11, className: "framer-hsxobt", "data-framer-appear-id": "hsxobt", "data-framer-name": "Bottom", initial: animation1, layoutDependency, layoutId: "DggJFRqOq", optimized: true, children: [/* @__PURE__ */ _jsxs(motion.div, { className: "framer-qj2zoe", "data-framer-name": "Text", layoutDependency, layoutId: "ERtSm2i6u", children: [/* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsxs(motion.p, { className: "framer-styles-preset-1oueo73", "data-styles-preset": "HLpRTFhim", children: ["                  No generic websites. No empty marketing promises. ", /* @__PURE__ */ _jsx(motion.span, { style: { "--framer-text-color": "var(--extracted-1w3ko1f, rgba(255, 255, 255, 0.7))" }, children: "Just tools and strategies that help your business grow and your brand shine." })] }) }), className: "framer-1h9hge5", fonts: ["Inter"], layoutDependency, layoutId: "jzM81bBPg", style: { "--extracted-1w3ko1f": "rgba(255, 255, 255, 0.7)", "--framer-paragraph-spacing": "0px" }, verticalAlignment: "top", withExternalLayout: true }), /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-txwsq6", "data-styles-preset": "fDRzSjw63", children: "\xA9 2025 fabrica\xAE Studio" }) }), className: "framer-lv0oqx", "data-framer-name": "Copyright Text", fonts: ["Inter"], layoutDependency, layoutId: "VH4qbJ8xZ", style: { "--framer-paragraph-spacing": "0px", opacity: 0.8 }, verticalAlignment: "top", withExternalLayout: true })] }), /* @__PURE__ */ _jsx(ResolveLinks, { links: [{ href: { webPageId: "hAlvrhFMj" }, implicitPathVariables: void 0 }], children: (resolvedLinks) => /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 161, width: `calc((max(${componentViewport?.width || "100vw"} - 8px, 1px) - 72px) / 4)`, children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-1vjzos2-container", layoutDependency, layoutId: "AtYOILTT5-container", nodeId: "AtYOILTT5", rendersWithMotion: true, scopeId: "W6mI3CngM", children: /* @__PURE__ */ _jsx(stdin_default4, { FggqkLwNL: "Team Lead", gBH5GgjXs: "Let\u2019s talk", height: "100%", i1j3qrwjd: "at fabrica\xAE ", id: "AtYOILTT5", layoutId: "AtYOILTT5", NZwcZIstG: resolvedLinks[0], rGlrlhWc1: "Lauren Thompson", style: { width: "100%" }, variant: "L0JatAwCn", width: "100%" }) }) }) })] })] }), /* @__PURE__ */ _jsxs(MotionDivWithFXWithOptimizedAppearEffect, { __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, animate: animation12, className: "framer-1b8ph4g", "data-framer-appear-id": "1b8ph4g", "data-framer-name": "BG", initial: animation13, layoutDependency, layoutId: "hdySRtS6f", optimized: true, style: { borderBottomLeftRadius: 25, borderBottomRightRadius: 25, borderTopLeftRadius: 25, borderTopRightRadius: 25 }, children: [/* @__PURE__ */ _jsx(ComponentViewportProvider, { children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-1ksavor-container", isAuthoredByUser: true, isModuleExternal: true, layoutDependency, layoutId: "MrQraeh6_-container", nodeId: "MrQraeh6_", rendersWithMotion: true, scopeId: "W6mI3CngM", children: /* @__PURE__ */ _jsx(Video, { backgroundColor: "rgba(0, 0, 0, 0)", borderRadius: 0, bottomLeftRadius: 0, bottomRightRadius: 0, controls: false, height: "100%", id: "MrQraeh6_", isMixedBorderRadius: false, layoutId: "MrQraeh6_", loop: true, muted: true, objectFit: "cover", playing: true, poster: "https://framerusercontent.com/images/x0XWYN3bXW4pXP0CEjajufoWs.jpg", posterEnabled: true, srcFile: "https://framerusercontent.com/assets/fQM1RaSPcHPbMZR0PCsoGur0U.mp4", srcType: "Upload", srcUrl: "https://assets.mixkit.co/active_storage/video_items/99921/**********/99921-video-720.mp4", startTime: 0, style: { height: "100%", width: "100%" }, topLeftRadius: 0, topRightRadius: 0, volume: 25, width: "100%" }) }) }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-1i21uq8", "data-framer-name": "Darken", layoutDependency, layoutId: "PCMaDDfuX", style: { backgroundColor: "rgba(13, 13, 13, 0.3)" } }), /* @__PURE__ */ _jsx(ComponentViewportProvider, { children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-1nah8ke-container", isAuthoredByUser: true, isModuleExternal: true, layoutDependency, layoutId: "How75sRtU-container", nodeId: "How75sRtU", rendersWithMotion: true, scopeId: "W6mI3CngM", children: /* @__PURE__ */ _jsx(Grain, { height: "100%", id: "How75sRtU", layoutId: "How75sRtU", opacity: 0.05, style: { height: "100%", width: "100%" }, width: "100%" }) }) })] })] }) }), /* @__PURE__ */ _jsx(MotionDivWithFX, { __framer__styleTransformEffectEnabled: true, __framer__transformTargets: [{ target: { opacity: 1, rotate: 0, rotateX: 0, rotateY: 0, scale: 0.95, skewX: 0, skewY: 0, x: 0, y: -290 } }, { ref: DgLQZwd8F, target: { opacity: 1, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, x: 0, y: 0 } }], __framer__transformTrigger: "onScrollTarget", __framer__transformViewportThreshold: 0.5, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-7u37s9", "data-framer-name": "Clients", layoutDependency, layoutId: "JEJFFVf6x", children: /* @__PURE__ */ _jsx(motion.div, { className: "framer-1tc7eep", "data-framer-name": "Static", layoutDependency, layoutId: "ok2YKx44i", children: /* @__PURE__ */ _jsxs(motion.div, { className: "framer-71ja9w", "data-framer-name": "Container", layoutDependency, layoutId: "VGBfJ0hsw", children: [/* @__PURE__ */ _jsxs(motion.div, { className: "framer-r2z2q9", "data-framer-name": "Top", layoutDependency, layoutId: "WyH1JOJl5", children: [/* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 22, width: `max((min(${componentViewport?.width || "100vw"} - 72px, 1520px) - 12px) / 4, 50px)`, y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 1278) - 0 - 1367) / 2 + 939 + 110) + 0 + 0 + 0 + 0 + 0 + 0 + 0 + 0, children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-597p28-container", layoutDependency, layoutId: "C7sa9NSQO-container", nodeId: "C7sa9NSQO", rendersWithMotion: true, scopeId: "W6mI3CngM", children: /* @__PURE__ */ _jsx(stdin_default2, { height: "100%", id: "C7sa9NSQO", k3Z3ztoi4: "Our clients", layoutId: "C7sa9NSQO", RaZgbjWXH: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", TmIm48vq7: "rgb(255, 255, 255)", variant: "VaDzezBN8", width: "100%", ws4vDxEZM: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))" }) }) }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-osej4p", "data-framer-name": "Content", layoutDependency, layoutId: "nah34nJ1Z", children: /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-1n1wh7h", "data-styles-preset": "gd6AWaps9", children: "(2016-25\xA9)" }) }), className: "framer-uzbb8i", fonts: ["Inter"], layoutDependency, layoutId: "fhpeVZmm9", style: { "--framer-paragraph-spacing": "0px" }, verticalAlignment: "top", withExternalLayout: true }) })] }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-1wou3v3", "data-framer-name": "Logo", layoutDependency, layoutId: "WzwI5DT_Y", children: [/* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 136, width: `max((min(${componentViewport?.width || "100vw"} - 72px, 1520px) - 20px) / 6, 50px)`, y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 1278) - 0 - 1367) / 2 + 939 + 110) + 0 + 0 + 0 + 0 + 0 + 182 + 0 + 0, children: /* @__PURE__ */ _jsx(SmartComponentScopedContainerWithFX, { __framer__animate: { transition: transition11 }, __framer__animateOnce: true, __framer__enter: animation14, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0.5, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-1opj8t5-container", layoutDependency, layoutId: "FkXGcSdXw-container", nodeId: "FkXGcSdXw", rendersWithMotion: true, scopeId: "W6mI3CngM", children: /* @__PURE__ */ _jsx(stdin_default3, { height: "100%", id: "FkXGcSdXw", layoutId: "FkXGcSdXw", style: { height: "100%", width: "100%" }, variant: "r3GmlGDnb", width: "100%" }) }) }), /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 136, width: `max((min(${componentViewport?.width || "100vw"} - 72px, 1520px) - 20px) / 6, 50px)`, y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 1278) - 0 - 1367) / 2 + 939 + 110) + 0 + 0 + 0 + 0 + 0 + 182 + 0 + 0, children: /* @__PURE__ */ _jsx(SmartComponentScopedContainerWithFX, { __framer__animate: { transition: transition12 }, __framer__animateOnce: true, __framer__enter: animation14, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0.5, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-zi0cmu-container", layoutDependency, layoutId: "mPFiWXKHj-container", nodeId: "mPFiWXKHj", rendersWithMotion: true, scopeId: "W6mI3CngM", children: /* @__PURE__ */ _jsx(stdin_default3, { height: "100%", id: "mPFiWXKHj", layoutId: "mPFiWXKHj", PiOn2dwG9: addImageAlt({ pixelHeight: 32, pixelWidth: 133, src: "https://framerusercontent.com/images/uwiCTWkuPCOpiACYPmBnkQDV8KA.svg" }, ""), style: { height: "100%", width: "100%" }, variant: "r3GmlGDnb", width: "100%" }) }) }), /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 136, width: `max((min(${componentViewport?.width || "100vw"} - 72px, 1520px) - 20px) / 6, 50px)`, y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 1278) - 0 - 1367) / 2 + 939 + 110) + 0 + 0 + 0 + 0 + 0 + 182 + 0 + 0, children: /* @__PURE__ */ _jsx(SmartComponentScopedContainerWithFX, { __framer__animate: { transition: transition13 }, __framer__animateOnce: true, __framer__enter: animation14, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0.5, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-d2z49u-container", layoutDependency, layoutId: "CQQC3BciI-container", nodeId: "CQQC3BciI", rendersWithMotion: true, scopeId: "W6mI3CngM", children: /* @__PURE__ */ _jsx(stdin_default3, { height: "100%", id: "CQQC3BciI", layoutId: "CQQC3BciI", PiOn2dwG9: addImageAlt({ pixelHeight: 50, pixelWidth: 50, src: "https://framerusercontent.com/images/qMtwqqlLyy1I0xtlJx1nQvCqsE.svg" }, ""), style: { height: "100%", width: "100%" }, variant: "r3GmlGDnb", width: "100%" }) }) }), /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 136, width: `max((min(${componentViewport?.width || "100vw"} - 72px, 1520px) - 20px) / 6, 50px)`, y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 1278) - 0 - 1367) / 2 + 939 + 110) + 0 + 0 + 0 + 0 + 0 + 182 + 0 + 0, children: /* @__PURE__ */ _jsx(SmartComponentScopedContainerWithFX, { __framer__animate: { transition: transition14 }, __framer__animateOnce: true, __framer__enter: animation14, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0.5, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-2xr870-container", layoutDependency, layoutId: "G041v9MdJ-container", nodeId: "G041v9MdJ", rendersWithMotion: true, scopeId: "W6mI3CngM", children: /* @__PURE__ */ _jsx(stdin_default3, { height: "100%", id: "G041v9MdJ", layoutId: "G041v9MdJ", PiOn2dwG9: addImageAlt({ pixelHeight: 28, pixelWidth: 130, src: "https://framerusercontent.com/images/IjvOxnf94qc0W01TH1Jt44VZRr4.svg" }, ""), style: { height: "100%", width: "100%" }, variant: "r3GmlGDnb", width: "100%" }) }) }), /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 136, width: `max((min(${componentViewport?.width || "100vw"} - 72px, 1520px) - 20px) / 6, 50px)`, y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 1278) - 0 - 1367) / 2 + 939 + 110) + 0 + 0 + 0 + 0 + 0 + 182 + 0 + 0, children: /* @__PURE__ */ _jsx(SmartComponentScopedContainerWithFX, { __framer__animate: { transition: transition15 }, __framer__animateOnce: true, __framer__enter: animation14, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0.5, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-1yd5p67-container", layoutDependency, layoutId: "X4_ZtGC6i-container", nodeId: "X4_ZtGC6i", rendersWithMotion: true, scopeId: "W6mI3CngM", children: /* @__PURE__ */ _jsx(stdin_default3, { height: "100%", id: "X4_ZtGC6i", layoutId: "X4_ZtGC6i", PiOn2dwG9: addImageAlt({ pixelHeight: 34, pixelWidth: 62, src: "https://framerusercontent.com/images/4HSt1fdOhF6F3PFBgxeUkOsTJiw.svg" }, ""), style: { height: "100%", width: "100%" }, variant: "r3GmlGDnb", width: "100%" }) }) }), /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 136, width: `max((min(${componentViewport?.width || "100vw"} - 72px, 1520px) - 20px) / 6, 50px)`, y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 1278) - 0 - 1367) / 2 + 939 + 110) + 0 + 0 + 0 + 0 + 0 + 182 + 0 + 0, children: /* @__PURE__ */ _jsx(SmartComponentScopedContainerWithFX, { __framer__animate: { transition: transition16 }, __framer__animateOnce: true, __framer__enter: animation14, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0.5, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-fdmpsz-container", layoutDependency, layoutId: "AFufmJE5y-container", nodeId: "AFufmJE5y", rendersWithMotion: true, scopeId: "W6mI3CngM", children: /* @__PURE__ */ _jsx(stdin_default3, { height: "100%", id: "AFufmJE5y", layoutId: "AFufmJE5y", PiOn2dwG9: addImageAlt({ pixelHeight: 32, pixelWidth: 38, src: "https://framerusercontent.com/images/AUrg765bxdJvG09Nkwtoo0n8A.svg" }, ""), style: { height: "100%", width: "100%" }, variant: "r3GmlGDnb", width: "100%" }) }) })] })] }) }) })] }) }) }) });
});
var css5 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-WiUcW.framer-1fs27rs, .framer-WiUcW .framer-1fs27rs { display: block; }", ".framer-WiUcW.framer-ykgwn3 { align-content: center; align-items: center; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 110px; height: min-content; justify-content: center; overflow: hidden; padding: 0px; position: relative; width: 1200px; }", ".framer-WiUcW .framer-19j2lei { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 0px; height: 939px; justify-content: center; overflow: hidden; padding: 61px 0px 0px 0px; position: relative; width: 100%; z-index: 3; }", ".framer-WiUcW .framer-n6szvs { align-content: center; align-items: center; display: flex; flex: 1 0 0px; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: 1px; justify-content: center; overflow: hidden; padding: 0px 4px 4px 4px; position: relative; width: 100%; }", ".framer-WiUcW .framer-1kxwov3 { align-content: center; align-items: center; display: flex; flex: 1 0 0px; flex-direction: column; flex-wrap: nowrap; height: 100%; justify-content: space-between; overflow: hidden; padding: 90px 36px 90px 36px; position: relative; width: 1px; z-index: 1; }", ".framer-WiUcW .framer-13u7xh5 { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: center; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-WiUcW .framer-1spzsp8 { align-content: flex-end; align-items: flex-end; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: center; overflow: visible; padding: 0px 120px 0px 0px; position: relative; width: 75%; }", ".framer-WiUcW .framer-al5dzj { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: flex-end; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-WiUcW .framer-13yua5i { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: center; overflow: visible; padding: 0px; position: relative; width: 85%; }", ".framer-WiUcW .framer-j23lg0 { flex: none; height: auto; position: absolute; right: 0px; top: 50%; white-space: pre; width: 100%; z-index: 1; }", ".framer-WiUcW .framer-1no2xs1 { flex: 1 0 0px; height: auto; position: relative; white-space: pre; width: 1px; }", ".framer-WiUcW .framer-1o25fny { flex: none; height: auto; position: relative; white-space: pre; width: 13%; }", ".framer-WiUcW .framer-gxz29i { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: flex-end; overflow: visible; padding: 0px; position: relative; width: 100%; z-index: 1; }", ".framer-WiUcW .framer-11hmflf { flex: none; height: auto; position: relative; white-space: pre; width: 20%; }", ".framer-WiUcW .framer-112dz82 { align-self: stretch; flex: none; height: auto; overflow: hidden; position: relative; width: 14%; }", ".framer-WiUcW .framer-o59pbo { align-content: flex-start; align-items: flex-start; display: flex; flex: 1 0 0px; flex-direction: column; flex-wrap: nowrap; gap: 12px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 0px; position: relative; width: 1px; }", ".framer-WiUcW .framer-qjo1yr, .framer-WiUcW .framer-1ghbbfu, .framer-WiUcW .framer-2aaxi6, .framer-WiUcW .framer-7dqgyp { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 0px; position: relative; width: 100%; }", ".framer-WiUcW .framer-ufy2l9, .framer-WiUcW .framer-19u5y4d, .framer-WiUcW .framer-jehyco, .framer-WiUcW .framer-1cyb64j { flex: 1 0 0px; height: auto; position: relative; white-space: pre-wrap; width: 1px; word-break: break-word; word-wrap: break-word; }", ".framer-WiUcW .framer-5d4iqh { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; height: min-content; justify-content: space-between; overflow: hidden; padding: 0px; position: relative; width: 100%; }", ".framer-WiUcW .framer-1mqmmbv, .framer-WiUcW .framer-ok7hg1, .framer-WiUcW .framer-tuz1zi, .framer-WiUcW .framer-nogkar { align-content: center; align-items: center; display: flex; flex: 1 0 0px; flex-direction: row; flex-wrap: nowrap; gap: 4px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 0px; position: relative; width: 1px; }", ".framer-WiUcW .framer-1x92z13-container, .framer-WiUcW .framer-1oz0pio-container, .framer-WiUcW .framer-3wo0sc-container, .framer-WiUcW .framer-i0d0xb-container { flex: none; height: 16px; position: relative; width: 16px; }", ".framer-WiUcW .framer-hsxobt { align-content: flex-end; align-items: flex-end; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; height: min-content; justify-content: space-between; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-WiUcW .framer-qj2zoe { align-content: flex-end; align-items: flex-end; display: flex; flex: 1 0 0px; flex-direction: row; flex-wrap: nowrap; height: min-content; justify-content: space-between; overflow: visible; padding: 0px 110px 0px 0px; position: relative; width: 1px; }", ".framer-WiUcW .framer-1h9hge5 { flex: 1 0 0px; height: auto; max-width: 490px; position: relative; white-space: pre-wrap; width: 1px; word-break: break-word; word-wrap: break-word; }", ".framer-WiUcW .framer-lv0oqx { flex: none; height: auto; position: relative; white-space: pre; width: auto; }", ".framer-WiUcW .framer-1vjzos2-container { flex: none; height: auto; position: relative; width: 25%; }", ".framer-WiUcW .framer-1b8ph4g { align-content: center; align-items: center; bottom: 6px; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 10px; justify-content: center; left: 6px; overflow: hidden; padding: 0px; position: absolute; right: 6px; top: 0px; will-change: var(--framer-will-change-override, transform); z-index: 0; }", ".framer-WiUcW .framer-1ksavor-container { bottom: 0px; flex: none; left: 0px; position: absolute; right: 0px; top: 0px; z-index: 1; }", ".framer-WiUcW .framer-1i21uq8 { bottom: 0px; flex: none; left: 0px; overflow: hidden; position: absolute; right: 0px; top: 0px; z-index: 1; }", ".framer-WiUcW .framer-1nah8ke-container { flex: none; height: 100%; position: relative; width: 100%; z-index: 1; }", ".framer-WiUcW .framer-7u37s9 { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 70px; height: min-content; justify-content: center; overflow: hidden; padding: 0px; position: relative; width: 100%; }", ".framer-WiUcW .framer-1tc7eep { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 70px; height: min-content; justify-content: center; overflow: hidden; padding: 0px 36px 0px 36px; position: relative; width: 100%; }", ".framer-WiUcW .framer-71ja9w { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 70px; height: min-content; justify-content: center; max-width: 1520px; overflow: hidden; padding: 0px; position: relative; width: 100%; }", ".framer-WiUcW .framer-r2z2q9 { display: grid; flex: none; gap: 4px; grid-auto-rows: minmax(0, 1fr); grid-template-columns: repeat(4, minmax(50px, 1fr)); grid-template-rows: repeat(1, minmax(0, 1fr)); height: min-content; justify-content: center; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-WiUcW .framer-597p28-container { align-self: start; flex: none; height: auto; justify-self: start; position: relative; width: 100%; }", ".framer-WiUcW .framer-osej4p { align-content: flex-start; align-items: flex-start; align-self: start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 70px; grid-column: span 3; height: min-content; justify-content: center; justify-self: start; overflow: hidden; padding: 0px; position: relative; width: 100%; }", ".framer-WiUcW .framer-uzbb8i { flex: none; height: auto; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }", ".framer-WiUcW .framer-1wou3v3 { display: grid; flex: none; gap: 4px; grid-auto-rows: minmax(0, 1fr); grid-template-columns: repeat(6, minmax(50px, 1fr)); grid-template-rows: repeat(1, minmax(0, 1fr)); height: min-content; justify-content: center; overflow: hidden; padding: 0px; position: relative; width: 100%; }", ".framer-WiUcW .framer-1opj8t5-container, .framer-WiUcW .framer-d2z49u-container, .framer-WiUcW .framer-2xr870-container, .framer-WiUcW .framer-fdmpsz-container { align-self: start; aspect-ratio: 1.3602941176470589 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 136px); justify-self: start; position: relative; width: 100%; }", ".framer-WiUcW .framer-zi0cmu-container, .framer-WiUcW .framer-1yd5p67-container { align-self: start; aspect-ratio: 1.3529411764705883 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 137px); justify-self: start; position: relative; width: 100%; }", ...css4, ...css3, ...css, ...css2];
var FramerW6mI3CngM = withCSS(Component, css5, "framer-WiUcW");
var stdin_default5 = FramerW6mI3CngM;
FramerW6mI3CngM.displayName = "Hero";
FramerW6mI3CngM.defaultProps = { height: 1278, width: 1200 };
addPropertyControls(FramerW6mI3CngM, { yhdjbZXJ7: { title: "Scroll Section", type: ControlType.ScrollSectionRef }, DgLQZwd8F: { title: "Scroll Section 2", type: ControlType.ScrollSectionRef } });
addFonts(FramerW6mI3CngM, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/hyOgCu0Xnghbimh0pE8QTvtt2AU.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/NeGmSOXrPBfEFIy5YZeHq17LEDA.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/oYaAX5himiTPYuN8vLWnqBbfD2s.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/lEJLP4R0yuCaMCjSXYHtJw72M.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/cRJyLNuTJR5jbyKzGi33wU9cqIQ.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/1ZFS7N918ojhhd0nQWdj3jz4w.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/A0Wcc7NgXMjUuFdquHDrIZpzZw0.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5vvr9Vy74if2I6bQbJvbw7SY1pQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/EOr0mi4hNtlgWNn9if640EZzXCo.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/Y9k9QrlZAqio88Klkmbd8VoMQc.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/OYrD2tBIBPvoJXiIHnLoOXnY9M.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/JeYwfuaPfZHQhEG8U5gtPDZ7WQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/vQyevYAyHtARFwPqUzQGpnDs.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/b6Y37FthZeALduNqHicBT6FutY.woff2", weight: "400" }] }, ...PlusIconFonts, ...ContactCardFonts, ...VideoFonts, ...GrainFonts, ...IntroFonts, ...LogoCardSmallFonts, ...getFontsFromSharedStyle(fonts4), ...getFontsFromSharedStyle(fonts3), ...getFontsFromSharedStyle(fonts), ...getFontsFromSharedStyle(fonts2)], { supportsExplicitInterCodegen: true });

// virtual:hero
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {};
stdin_default5.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default5,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default5, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default5);
export {
  ComponentWithRoot as default
};
