// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";
import {
  stdin_default
} from "./chunks/chunk-NEDE7LSE.js";
import "./chunks/chunk-5L2L6TVR.js";
import {
  className as className2,
  css as css2,
  fonts as fonts2
} from "./chunks/chunk-QOI7VI3Y.js";
import {
  className,
  css,
  fonts
} from "./chunks/chunk-F5ZOKVUG.js";

// virtual:projects
import { Fragment as Fragment2 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/lj6xtW3z0CEecnsPKstp/CzL6AGzcEG7H6XHoIDLI/ntVcqJNYe.js
import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { addFonts, ChildrenCanSuspend, ComponentViewportProvider, cx, getFonts, getFontsFromSharedStyle, PathVariablesContext, ResolveLinks, RichText, SmartComponentScopedContainer, useComponentViewport, useLocaleInfo, useQueryData, useRouter, useVariantState, withCSS, withFX } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";

// /:https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js
import { addPropertyControls as e3, ControlType as l2, QueryCache as t10, QueryEngine as a2 } from "unframer";

// /:https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub-0.js
import { ControlType as U } from "unframer";
import { ControlType as t_ } from "unframer";
var t;
var e;
var r;
var n = Object.create;
var i = Object.defineProperty;
var s = Object.getOwnPropertyDescriptor;
var a = Object.getOwnPropertyNames;
var o = Object.getPrototypeOf;
var u = Object.prototype.hasOwnProperty;
var l = (t102, e22) => function() {
  return e22 || (0, t102[a(t102)[0]])((e22 = { exports: {} }).exports, e22), e22.exports;
};
var h = (t102, e22, r22, n22) => {
  if (e22 && "object" == typeof e22 || "function" == typeof e22) for (let o22 of a(e22)) u.call(t102, o22) || o22 === r22 || i(t102, o22, { get: () => e22[o22], enumerable: !(n22 = s(e22, o22)) || n22.enumerable });
  return t102;
};
var f = (t102, e22, r22) => (r22 = null != t102 ? n(o(t102)) : {}, h(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  !e22 && t102 && t102.__esModule ? r22 : i(r22, "default", { value: t102, enumerable: true }),
  t102
));
var c = l({ "../../../node_modules/dataloader/index.js"(t102, e22) {
  var r22, n22 = /* @__PURE__ */ function() {
    function t112(t122, e42) {
      if ("function" != typeof t122) throw TypeError("DataLoader must be constructed with a function which accepts Array<key> and returns Promise<Array<value>>, but got: " + t122 + ".");
      this._batchLoadFn = t122, this._maxBatchSize = function(t13) {
        if (!(!t13 || false !== t13.batch)) return 1;
        var e52 = t13 && t13.maxBatchSize;
        if (void 0 === e52) return 1 / 0;
        if ("number" != typeof e52 || e52 < 1) throw TypeError("maxBatchSize must be a positive number: " + e52);
        return e52;
      }(e42), this._batchScheduleFn = function(t13) {
        var e52 = t13 && t13.batchScheduleFn;
        if (void 0 === e52) return i22;
        if ("function" != typeof e52) throw TypeError("batchScheduleFn must be a function: " + e52);
        return e52;
      }(e42), this._cacheKeyFn = function(t13) {
        var e52 = t13 && t13.cacheKeyFn;
        if (void 0 === e52) return function(t14) {
          return t14;
        };
        if ("function" != typeof e52) throw TypeError("cacheKeyFn must be a function: " + e52);
        return e52;
      }(e42), this._cacheMap = function(t13) {
        if (!(!t13 || false !== t13.cache)) return null;
        var e52 = t13 && t13.cacheMap;
        if (void 0 === e52) return /* @__PURE__ */ new Map();
        if (null !== e52) {
          var r3 = ["get", "set", "delete", "clear"].filter(function(t14) {
            return e52 && "function" != typeof e52[t14];
          });
          if (0 !== r3.length) throw TypeError("Custom cacheMap missing methods: " + r3.join(", "));
        }
        return e52;
      }(e42), this._batch = null, this.name = e42 && e42.name ? e42.name : null;
    }
    var e33 = t112.prototype;
    return e33.load = function(t122) {
      if (null == t122) throw TypeError("The loader.load() function must be called with a value, but got: " + String(t122) + ".");
      var e42 = function(t13) {
        var e52 = t13._batch;
        if (null !== e52 && !e52.hasDispatched && e52.keys.length < t13._maxBatchSize) return e52;
        var r4 = { hasDispatched: false, keys: [], callbacks: [] };
        return t13._batch = r4, t13._batchScheduleFn(function() {
          (function(t14, e62) {
            var r5;
            if (e62.hasDispatched = true, 0 === e62.keys.length) {
              a22(e62);
              return;
            }
            try {
              r5 = t14._batchLoadFn(e62.keys);
            } catch (r6) {
              return s22(t14, e62, TypeError("DataLoader must be constructed with a function which accepts Array<key> and returns Promise<Array<value>>, but the function errored synchronously: " + String(r6) + "."));
            }
            if (!r5 || "function" != typeof r5.then) return s22(t14, e62, TypeError("DataLoader must be constructed with a function which accepts Array<key> and returns Promise<Array<value>>, but the function did not return a Promise: " + String(r5) + "."));
            r5.then(function(t15) {
              if (!o22(t15)) throw TypeError("DataLoader must be constructed with a function which accepts Array<key> and returns Promise<Array<value>>, but the function did not return a Promise of an Array: " + String(t15) + ".");
              if (t15.length !== e62.keys.length) throw TypeError("DataLoader must be constructed with a function which accepts Array<key> and returns Promise<Array<value>>, but the function did not return a Promise of an Array of the same length as the Array of keys.\n\nKeys:\n" + String(e62.keys) + "\n\nValues:\n" + String(t15));
              a22(e62);
              for (var r6 = 0; r6 < e62.callbacks.length; r6++) {
                var n42 = t15[r6];
                n42 instanceof Error ? e62.callbacks[r6].reject(n42) : e62.callbacks[r6].resolve(n42);
              }
            }).catch(function(r6) {
              s22(t14, e62, r6);
            });
          })(t13, r4);
        }), r4;
      }(this), r3 = this._cacheMap, n32 = this._cacheKeyFn(t122);
      if (r3) {
        var i32 = r3.get(n32);
        if (i32) {
          var u22 = e42.cacheHits || (e42.cacheHits = []);
          return new Promise(function(t13) {
            u22.push(function() {
              t13(i32);
            });
          });
        }
      }
      e42.keys.push(t122);
      var l22 = new Promise(function(t13, r4) {
        e42.callbacks.push({ resolve: t13, reject: r4 });
      });
      return r3 && r3.set(n32, l22), l22;
    }, e33.loadMany = function(t122) {
      if (!o22(t122)) throw TypeError("The loader.loadMany() function must be called with Array<key> but got: " + t122 + ".");
      for (var e42 = [], r3 = 0; r3 < t122.length; r3++) e42.push(this.load(t122[r3]).catch(function(t13) {
        return t13;
      }));
      return Promise.all(e42);
    }, e33.clear = function(t122) {
      var e42 = this._cacheMap;
      if (e42) {
        var r3 = this._cacheKeyFn(t122);
        e42.delete(r3);
      }
      return this;
    }, e33.clearAll = function() {
      var t122 = this._cacheMap;
      return t122 && t122.clear(), this;
    }, e33.prime = function(t122, e42) {
      var r3 = this._cacheMap;
      if (r3) {
        var n32, i32 = this._cacheKeyFn(t122);
        void 0 === r3.get(i32) && (e42 instanceof Error ? (n32 = Promise.reject(e42)).catch(function() {
        }) : n32 = Promise.resolve(e42), r3.set(i32, n32));
      }
      return this;
    }, t112;
  }(), i22 = "object" == typeof process && "function" == typeof process.nextTick ? function(t112) {
    r22 || (r22 = Promise.resolve()), r22.then(function() {
      process.nextTick(t112);
    });
  } : "function" == typeof setImmediate ? function(t112) {
    setImmediate(t112);
  } : function(t112) {
    setTimeout(t112);
  };
  function s22(t112, e33, r3) {
    a22(e33);
    for (var n32 = 0; n32 < e33.keys.length; n32++) t112.clear(e33.keys[n32]), e33.callbacks[n32].reject(r3);
  }
  function a22(t112) {
    if (t112.cacheHits) for (var e33 = 0; e33 < t112.cacheHits.length; e33++) t112.cacheHits[e33]();
  }
  function o22(t112) {
    return "object" == typeof t112 && null !== t112 && "number" == typeof t112.length && (0 === t112.length || t112.length > 0 && Object.prototype.hasOwnProperty.call(t112, t112.length - 1));
  }
  e22.exports = n22;
} });
var g = l({ "../../../node_modules/whatwg-mimetype/lib/utils.js"(t102) {
  t102.removeLeadingAndTrailingHTTPWhitespace = (t112) => t112.replace(/^[ \t\n\r]+/u, "").replace(/[ \t\n\r]+$/u, ""), t102.removeTrailingHTTPWhitespace = (t112) => t112.replace(/[ \t\n\r]+$/u, ""), t102.isHTTPWhitespaceChar = (t112) => " " === t112 || "	" === t112 || "\n" === t112 || "\r" === t112, t102.solelyContainsHTTPTokenCodePoints = (t112) => /^[-!#$%&'*+.^_`|~A-Za-z0-9]*$/u.test(t112), t102.soleyContainsHTTPQuotedStringTokenCodePoints = (t112) => /^[\t\u0020-\u007E\u0080-\u00FF]*$/u.test(t112), t102.asciiLowercase = (t112) => t112.replace(/[A-Z]/ug, (t122) => t122.toLowerCase()), t102.collectAnHTTPQuotedString = (t112, e22) => {
    let r22 = "";
    for (e22++; ; ) {
      for (; e22 < t112.length && '"' !== t112[e22] && "\\" !== t112[e22]; ) r22 += t112[e22], ++e22;
      if (e22 >= t112.length) break;
      let n22 = t112[e22];
      if (++e22, "\\" === n22) {
        if (e22 >= t112.length) {
          r22 += "\\";
          break;
        }
        r22 += t112[e22], ++e22;
      } else break;
    }
    return [r22, e22];
  };
} });
var d = l({ "../../../node_modules/whatwg-mimetype/lib/mime-type-parameters.js"(t102, e22) {
  var { asciiLowercase: r22, solelyContainsHTTPTokenCodePoints: n22, soleyContainsHTTPQuotedStringTokenCodePoints: i22 } = g();
  e22.exports = class {
    get size() {
      return this._map.size;
    }
    get(t112) {
      return t112 = r22(String(t112)), this._map.get(t112);
    }
    has(t112) {
      return t112 = r22(String(t112)), this._map.has(t112);
    }
    set(t112, e33) {
      if (t112 = r22(String(t112)), e33 = String(e33), !n22(t112)) throw Error(`Invalid MIME type parameter name "${t112}": only HTTP token code points are valid.`);
      if (!i22(e33)) throw Error(`Invalid MIME type parameter value "${e33}": only HTTP quoted-string token code points are valid.`);
      return this._map.set(t112, e33);
    }
    clear() {
      this._map.clear();
    }
    delete(t112) {
      return t112 = r22(String(t112)), this._map.delete(t112);
    }
    forEach(t112, e33) {
      this._map.forEach(t112, e33);
    }
    keys() {
      return this._map.keys();
    }
    values() {
      return this._map.values();
    }
    entries() {
      return this._map.entries();
    }
    [Symbol.iterator]() {
      return this._map[Symbol.iterator]();
    }
    constructor(t112) {
      this._map = t112;
    }
  };
} });
var p = l({ "../../../node_modules/whatwg-mimetype/lib/parser.js"(t102, e22) {
  var { removeLeadingAndTrailingHTTPWhitespace: r22, removeTrailingHTTPWhitespace: n22, isHTTPWhitespaceChar: i22, solelyContainsHTTPTokenCodePoints: s22, soleyContainsHTTPQuotedStringTokenCodePoints: a22, asciiLowercase: o22, collectAnHTTPQuotedString: u22 } = g();
  e22.exports = (t112) => {
    t112 = r22(t112);
    let e33 = 0, l22 = "";
    for (; e33 < t112.length && "/" !== t112[e33]; ) l22 += t112[e33], ++e33;
    if (0 === l22.length || !s22(l22) || e33 >= t112.length) return null;
    ++e33;
    let h22 = "";
    for (; e33 < t112.length && ";" !== t112[e33]; ) h22 += t112[e33], ++e33;
    if (0 === (h22 = n22(h22)).length || !s22(h22)) return null;
    let f22 = { type: o22(l22), subtype: o22(h22), parameters: /* @__PURE__ */ new Map() };
    for (; e33 < t112.length; ) {
      for (++e33; i22(t112[e33]); ) ++e33;
      let r3 = "";
      for (; e33 < t112.length && ";" !== t112[e33] && "=" !== t112[e33]; ) r3 += t112[e33], ++e33;
      if (r3 = o22(r3), e33 < t112.length) {
        if (";" === t112[e33]) continue;
        ++e33;
      }
      let l32 = null;
      if ('"' === t112[e33]) for ([l32, e33] = u22(t112, e33); e33 < t112.length && ";" !== t112[e33]; ) ++e33;
      else {
        for (l32 = ""; e33 < t112.length && ";" !== t112[e33]; ) l32 += t112[e33], ++e33;
        if ("" === (l32 = n22(l32))) continue;
      }
      r3.length > 0 && s22(r3) && a22(l32) && !f22.parameters.has(r3) && f22.parameters.set(r3, l32);
    }
    return f22;
  };
} });
var y = l({ "../../../node_modules/whatwg-mimetype/lib/serializer.js"(t102, e22) {
  var { solelyContainsHTTPTokenCodePoints: r22 } = g();
  e22.exports = (t112) => {
    let e33 = `${t112.type}/${t112.subtype}`;
    if (0 === t112.parameters.size) return e33;
    for (let [n22, i22] of t112.parameters) e33 += ";" + n22 + "=", r22(i22) && 0 !== i22.length || (i22 = i22.replace(/(["\\])/ug, "\\$1"), i22 = `"${i22}"`), e33 += i22;
    return e33;
  };
} });
var v = l({ "../../../node_modules/whatwg-mimetype/lib/mime-type.js"(t102, e22) {
  var r22 = d(), n22 = p(), i22 = y(), { asciiLowercase: s22, solelyContainsHTTPTokenCodePoints: a22 } = g();
  e22.exports = class {
    static parse(t112) {
      try {
        return new this(t112);
      } catch (t122) {
        return null;
      }
    }
    get essence() {
      return `${this.type}/${this.subtype}`;
    }
    get type() {
      return this._type;
    }
    set type(t112) {
      if (0 === (t112 = s22(String(t112))).length) throw Error("Invalid type: must be a non-empty string");
      if (!a22(t112)) throw Error(`Invalid type ${t112}: must contain only HTTP token code points`);
      this._type = t112;
    }
    get subtype() {
      return this._subtype;
    }
    set subtype(t112) {
      if (0 === (t112 = s22(String(t112))).length) throw Error("Invalid subtype: must be a non-empty string");
      if (!a22(t112)) throw Error(`Invalid subtype ${t112}: must contain only HTTP token code points`);
      this._subtype = t112;
    }
    get parameters() {
      return this._parameters;
    }
    toString() {
      return i22(this);
    }
    isJavaScript({ prohibitParameters: t112 = false } = {}) {
      switch (this._type) {
        case "text":
          switch (this._subtype) {
            case "ecmascript":
            case "javascript":
            case "javascript1.0":
            case "javascript1.1":
            case "javascript1.2":
            case "javascript1.3":
            case "javascript1.4":
            case "javascript1.5":
            case "jscript":
            case "livescript":
            case "x-ecmascript":
            case "x-javascript":
              return !t112 || 0 === this._parameters.size;
            default:
              return false;
          }
        case "application":
          switch (this._subtype) {
            case "ecmascript":
            case "javascript":
            case "x-ecmascript":
            case "x-javascript":
              return !t112 || 0 === this._parameters.size;
            default:
              return false;
          }
        default:
          return false;
      }
    }
    isXML() {
      return "xml" === this._subtype && ("text" === this._type || "application" === this._type) || this._subtype.endsWith("+xml");
    }
    isHTML() {
      return "html" === this._subtype && "text" === this._type;
    }
    constructor(t112) {
      t112 = String(t112);
      let e33 = n22(t112);
      if (null === e33) throw Error(`Could not parse MIME type string "${t112}"`);
      this._type = e33.type, this._subtype = e33.subtype, this._parameters = new r22(e33.parameters);
    }
  };
} });
var m = f(c());
var w = { Uint8: 1, Uint16: 2, Uint32: 4, BigUint64: 8, Int8: 1, Int16: 2, Int32: 4, BigInt64: 8, Float32: 4, Float64: 8 };
var b = class {
  getOffset() {
    return this.offset;
  }
  ensureLength(t102) {
    let e22 = this.bytes.length;
    if (!(this.offset + t102 <= e22)) throw Error("Reading out of bounds");
  }
  readUint8() {
    let t102 = w.Uint8;
    this.ensureLength(t102);
    let e22 = this.view.getUint8(this.offset);
    return this.offset += t102, e22;
  }
  readUint16() {
    let t102 = w.Uint16;
    this.ensureLength(t102);
    let e22 = this.view.getUint16(this.offset);
    return this.offset += t102, e22;
  }
  readUint32() {
    let t102 = w.Uint32;
    this.ensureLength(t102);
    let e22 = this.view.getUint32(this.offset);
    return this.offset += t102, e22;
  }
  readUint64() {
    let t102 = this.readBigUint64();
    return Number(t102);
  }
  readBigUint64() {
    let t102 = w.BigUint64;
    this.ensureLength(t102);
    let e22 = this.view.getBigUint64(this.offset);
    return this.offset += t102, e22;
  }
  readInt8() {
    let t102 = w.Int8;
    this.ensureLength(t102);
    let e22 = this.view.getInt8(this.offset);
    return this.offset += t102, e22;
  }
  readInt16() {
    let t102 = w.Int16;
    this.ensureLength(t102);
    let e22 = this.view.getInt16(this.offset);
    return this.offset += t102, e22;
  }
  readInt32() {
    let t102 = w.Int32;
    this.ensureLength(t102);
    let e22 = this.view.getInt32(this.offset);
    return this.offset += t102, e22;
  }
  readInt64() {
    let t102 = this.readBigInt64();
    return Number(t102);
  }
  readBigInt64() {
    let t102 = w.BigInt64;
    this.ensureLength(t102);
    let e22 = this.view.getBigInt64(this.offset);
    return this.offset += t102, e22;
  }
  readFloat32() {
    let t102 = w.Float32;
    this.ensureLength(t102);
    let e22 = this.view.getFloat32(this.offset);
    return this.offset += t102, e22;
  }
  readFloat64() {
    let t102 = w.Float64;
    this.ensureLength(t102);
    let e22 = this.view.getFloat64(this.offset);
    return this.offset += t102, e22;
  }
  readBytes(t102) {
    let e22 = this.offset, r22 = e22 + t102, n22 = this.bytes.subarray(e22, r22);
    return this.offset = r22, n22;
  }
  readString() {
    let t102 = this.readUint32(), e22 = this.readBytes(t102);
    return this.decoder.decode(e22);
  }
  readJson() {
    let t102 = this.readString();
    return JSON.parse(t102);
  }
  constructor(t102) {
    this.bytes = t102, this.offset = 0, this.view = I(this.bytes), this.decoder = new TextDecoder();
  }
};
function I(t102) {
  return new DataView(t102.buffer, t102.byteOffset, t102.byteLength);
}
function k(t102, ...e22) {
  if (!t102) throw Error("Assertion Error" + (e22.length > 0 ? ": " + e22.join(" ") : ""));
}
function S(t102) {
  throw Error(`Unexpected value: ${t102}`);
}
var E = Uint8Array;
var M = Uint16Array;
var T = Int32Array;
var L = new E([
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  1,
  1,
  1,
  1,
  2,
  2,
  2,
  2,
  3,
  3,
  3,
  3,
  4,
  4,
  4,
  4,
  5,
  5,
  5,
  5,
  0,
  /* unused */
  0,
  0,
  /* impossible */
  0
]);
var _ = new E([
  0,
  0,
  0,
  0,
  1,
  1,
  2,
  2,
  3,
  3,
  4,
  4,
  5,
  5,
  6,
  6,
  7,
  7,
  8,
  8,
  9,
  9,
  10,
  10,
  11,
  11,
  12,
  12,
  13,
  13,
  /* unused */
  0,
  0
]);
var B = new E([16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15]);
var x = function(t102, e22) {
  for (var r22 = new M(31), n22 = 0; n22 < 31; ++n22) r22[n22] = e22 += 1 << t102[n22 - 1];
  for (var i22 = new T(r22[30]), n22 = 1; n22 < 30; ++n22) for (var s22 = r22[n22]; s22 < r22[n22 + 1]; ++s22) i22[s22] = s22 - r22[n22] << 5 | n22;
  return { b: r22, r: i22 };
};
var C = x(L, 2);
var A = C.b;
var F = C.r;
A[28] = 258, F[258] = 28;
var P = x(_, 0);
var j = P.b;
var D = P.r;
var N = new M(32768);
for (e = 0; e < 32768; ++e) t = (61680 & (t = (52428 & (t = (43690 & e) >> 1 | (21845 & e) << 1)) >> 2 | (13107 & t) << 2)) >> 4 | (3855 & t) << 4, N[e] = ((65280 & t) >> 8 | (255 & t) << 8) >> 1;
var $ = function(t102, e22, r22) {
  for (var n22, i22 = t102.length, s22 = 0, a22 = new M(e22); s22 < i22; ++s22) t102[s22] && ++a22[t102[s22] - 1];
  var o22 = new M(e22);
  for (s22 = 1; s22 < e22; ++s22) o22[s22] = o22[s22 - 1] + a22[s22 - 1] << 1;
  if (r22) {
    n22 = new M(1 << e22);
    var u22 = 15 - e22;
    for (s22 = 0; s22 < i22; ++s22) if (t102[s22]) for (var l22 = s22 << 4 | t102[s22], h22 = e22 - t102[s22], f22 = o22[t102[s22] - 1]++ << h22, c22 = f22 | (1 << h22) - 1; f22 <= c22; ++f22) n22[N[f22] >> u22] = l22;
  } else for (s22 = 0, n22 = new M(i22); s22 < i22; ++s22) t102[s22] && (n22[s22] = N[o22[t102[s22] - 1]++] >> 15 - t102[s22]);
  return n22;
};
var R = new E(288);
for (e = 0; e < 144; ++e) R[e] = 8;
for (e = 144; e < 256; ++e) R[e] = 9;
for (e = 256; e < 280; ++e) R[e] = 7;
for (e = 280; e < 288; ++e) R[e] = 8;
var O = new E(32);
for (e = 0; e < 32; ++e) O[e] = 5;
var q = /* @__PURE__ */ $(R, 9, 0);
var z = /* @__PURE__ */ $(R, 9, 1);
var H = /* @__PURE__ */ $(O, 5, 0);
var J = /* @__PURE__ */ $(O, 5, 1);
var W = function(t102) {
  for (var e22 = t102[0], r22 = 1; r22 < t102.length; ++r22) t102[r22] > e22 && (e22 = t102[r22]);
  return e22;
};
var G = function(t102, e22, r22) {
  var n22 = e22 / 8 | 0;
  return (t102[n22] | t102[n22 + 1] << 8) >> (7 & e22) & r22;
};
var K = function(t102, e22) {
  var r22 = e22 / 8 | 0;
  return (t102[r22] | t102[r22 + 1] << 8 | t102[r22 + 2] << 16) >> (7 & e22);
};
var V = function(t102) {
  return (t102 + 7) / 8 | 0;
};
var X = function(t102, e22, r22) {
  return (null == e22 || e22 < 0) && (e22 = 0), (null == r22 || r22 > t102.length) && (r22 = t102.length), new E(t102.subarray(e22, r22));
};
var Q = ["unexpected EOF", "invalid block type", "invalid length/literal", "invalid distance", "stream finished", "no stream handler", , "no callback", "invalid UTF-8 data", "extra field too long", "date not in range 1980-2099", "filename too long", "stream finishing", "invalid zip data"];
var Z = function(t102, e22, r22) {
  var n22 = Error(e22 || Q[t102]);
  if (n22.code = t102, Error.captureStackTrace && Error.captureStackTrace(n22, Z), !r22) throw n22;
  return n22;
};
var Y = function(t102, e22, r22, n22) {
  var i22 = t102.length, s22 = n22 ? n22.length : 0;
  if (!i22 || e22.f && !e22.l) return r22 || new E(0);
  var a22 = !r22, o22 = a22 || 2 != e22.i, u22 = e22.i;
  a22 && (r22 = new E(3 * i22));
  var l22 = function(t112) {
    var e33 = r22.length;
    if (t112 > e33) {
      var n32 = new E(Math.max(2 * e33, t112));
      n32.set(r22), r22 = n32;
    }
  }, h22 = e22.f || 0, f22 = e22.p || 0, c22 = e22.b || 0, g22 = e22.l, d22 = e22.d, p22 = e22.m, y22 = e22.n, v22 = 8 * i22;
  do {
    if (!g22) {
      h22 = G(t102, f22, 1);
      var m22 = G(t102, f22 + 1, 3);
      if (f22 += 3, m22) {
        if (1 == m22) g22 = z, d22 = J, p22 = 9, y22 = 5;
        else if (2 == m22) {
          var w22 = G(t102, f22, 31) + 257, b22 = G(t102, f22 + 10, 15) + 4, I22 = w22 + G(t102, f22 + 5, 31) + 1;
          f22 += 14;
          for (var U22 = new E(I22), k22 = new E(19), S22 = 0; S22 < b22; ++S22) k22[B[S22]] = G(t102, f22 + 3 * S22, 7);
          f22 += 3 * b22;
          for (var M2 = W(k22), T22 = (1 << M2) - 1, x22 = $(k22, M2, 1), S22 = 0; S22 < I22; ) {
            var C22 = x22[G(t102, f22, T22)];
            f22 += 15 & C22;
            var F22 = C22 >> 4;
            if (F22 < 16) U22[S22++] = F22;
            else {
              var P22 = 0, D22 = 0;
              for (16 == F22 ? (D22 = 3 + G(t102, f22, 3), f22 += 2, P22 = U22[S22 - 1]) : 17 == F22 ? (D22 = 3 + G(t102, f22, 7), f22 += 3) : 18 == F22 && (D22 = 11 + G(t102, f22, 127), f22 += 7); D22--; ) U22[S22++] = P22;
            }
          }
          var N22 = U22.subarray(0, w22), R22 = U22.subarray(w22);
          p22 = W(N22), y22 = W(R22), g22 = $(N22, p22, 1), d22 = $(R22, y22, 1);
        } else Z(1);
      } else {
        var F22 = V(f22) + 4, O22 = t102[F22 - 4] | t102[F22 - 3] << 8, q22 = F22 + O22;
        if (q22 > i22) {
          u22 && Z(0);
          break;
        }
        o22 && l22(c22 + O22), r22.set(t102.subarray(F22, q22), c22), e22.b = c22 += O22, e22.p = f22 = 8 * q22, e22.f = h22;
        continue;
      }
      if (f22 > v22) {
        u22 && Z(0);
        break;
      }
    }
    o22 && l22(c22 + 131072);
    for (var H22 = (1 << p22) - 1, Q22 = (1 << y22) - 1, Y22 = f22; ; Y22 = f22) {
      var P22 = g22[K(t102, f22) & H22], tt22 = P22 >> 4;
      if ((f22 += 15 & P22) > v22) {
        u22 && Z(0);
        break;
      }
      if (P22 || Z(2), tt22 < 256) r22[c22++] = tt22;
      else if (256 == tt22) {
        Y22 = f22, g22 = null;
        break;
      } else {
        var te22 = tt22 - 254;
        if (tt22 > 264) {
          var S22 = tt22 - 257, tr22 = L[S22];
          te22 = G(t102, f22, (1 << tr22) - 1) + A[S22], f22 += tr22;
        }
        var tn22 = d22[K(t102, f22) & Q22], ti22 = tn22 >> 4;
        tn22 || Z(3), f22 += 15 & tn22;
        var R22 = j[ti22];
        if (ti22 > 3) {
          var tr22 = _[ti22];
          R22 += K(t102, f22) & (1 << tr22) - 1, f22 += tr22;
        }
        if (f22 > v22) {
          u22 && Z(0);
          break;
        }
        o22 && l22(c22 + 131072);
        var ts22 = c22 + te22;
        if (c22 < R22) {
          var ta22 = s22 - R22, to22 = Math.min(R22, ts22);
          for (ta22 + c22 < 0 && Z(3); c22 < to22; ++c22) r22[c22] = n22[ta22 + c22];
        }
        for (; c22 < ts22; ++c22) r22[c22] = r22[c22 - R22];
      }
    }
    e22.l = g22, e22.p = Y22, e22.b = c22, e22.f = h22, g22 && (h22 = 1, e22.m = p22, e22.d = d22, e22.n = y22);
  } while (!h22);
  return c22 != r22.length && a22 ? X(r22, 0, c22) : r22.subarray(0, c22);
};
var tt = function(t102, e22, r22) {
  r22 <<= 7 & e22;
  var n22 = e22 / 8 | 0;
  t102[n22] |= r22, t102[n22 + 1] |= r22 >> 8;
};
var te = function(t102, e22, r22) {
  r22 <<= 7 & e22;
  var n22 = e22 / 8 | 0;
  t102[n22] |= r22, t102[n22 + 1] |= r22 >> 8, t102[n22 + 2] |= r22 >> 16;
};
var tr = function(t102, e22) {
  for (var r22 = [], n22 = 0; n22 < t102.length; ++n22) t102[n22] && r22.push({ s: n22, f: t102[n22] });
  var i22 = r22.length, s22 = r22.slice();
  if (!i22) return { t: tl, l: 0 };
  if (1 == i22) {
    var a22 = new E(r22[0].s + 1);
    return a22[r22[0].s] = 1, { t: a22, l: 1 };
  }
  r22.sort(function(t112, e33) {
    return t112.f - e33.f;
  }), r22.push({ s: -1, f: 25001 });
  var o22 = r22[0], u22 = r22[1], l22 = 0, h22 = 1, f22 = 2;
  for (r22[0] = { s: -1, f: o22.f + u22.f, l: o22, r: u22 }; h22 != i22 - 1; ) o22 = r22[r22[l22].f < r22[f22].f ? l22++ : f22++], u22 = r22[l22 != h22 && r22[l22].f < r22[f22].f ? l22++ : f22++], r22[h22++] = { s: -1, f: o22.f + u22.f, l: o22, r: u22 };
  for (var c22 = s22[0].s, n22 = 1; n22 < i22; ++n22) s22[n22].s > c22 && (c22 = s22[n22].s);
  var g22 = new M(c22 + 1), d22 = tn(r22[h22 - 1], g22, 0);
  if (d22 > e22) {
    var n22 = 0, p22 = 0, y22 = d22 - e22, v22 = 1 << y22;
    for (s22.sort(function(t112, e33) {
      return g22[e33.s] - g22[t112.s] || t112.f - e33.f;
    }); n22 < i22; ++n22) {
      var m22 = s22[n22].s;
      if (g22[m22] > e22) p22 += v22 - (1 << d22 - g22[m22]), g22[m22] = e22;
      else break;
    }
    for (p22 >>= y22; p22 > 0; ) {
      var w22 = s22[n22].s;
      g22[w22] < e22 ? p22 -= 1 << e22 - g22[w22]++ - 1 : ++n22;
    }
    for (; n22 >= 0 && p22; --n22) {
      var b22 = s22[n22].s;
      g22[b22] == e22 && (--g22[b22], ++p22);
    }
    d22 = e22;
  }
  return { t: new E(g22), l: d22 };
};
var tn = function(t102, e22, r22) {
  return -1 == t102.s ? Math.max(tn(t102.l, e22, r22 + 1), tn(t102.r, e22, r22 + 1)) : e22[t102.s] = r22;
};
var ti = function(t102) {
  for (var e22 = t102.length; e22 && !t102[--e22]; ) ;
  for (var r22 = new M(++e22), n22 = 0, i22 = t102[0], s22 = 1, a22 = function(t112) {
    r22[n22++] = t112;
  }, o22 = 1; o22 <= e22; ++o22) if (t102[o22] == i22 && o22 != e22) ++s22;
  else {
    if (!i22 && s22 > 2) {
      for (; s22 > 138; s22 -= 138) a22(32754);
      s22 > 2 && (a22(s22 > 10 ? s22 - 11 << 5 | 28690 : s22 - 3 << 5 | 12305), s22 = 0);
    } else if (s22 > 3) {
      for (a22(i22), --s22; s22 > 6; s22 -= 6) a22(8304);
      s22 > 2 && (a22(s22 - 3 << 5 | 8208), s22 = 0);
    }
    for (; s22--; ) a22(i22);
    s22 = 1, i22 = t102[o22];
  }
  return { c: r22.subarray(0, n22), n: e22 };
};
var ts = function(t102, e22) {
  for (var r22 = 0, n22 = 0; n22 < e22.length; ++n22) r22 += t102[n22] * e22[n22];
  return r22;
};
var ta = function(t102, e22, r22) {
  var n22 = r22.length, i22 = V(e22 + 2);
  t102[i22] = 255 & n22, t102[i22 + 1] = n22 >> 8, t102[i22 + 2] = 255 ^ t102[i22], t102[i22 + 3] = 255 ^ t102[i22 + 1];
  for (var s22 = 0; s22 < n22; ++s22) t102[i22 + s22 + 4] = r22[s22];
  return (i22 + 4 + n22) * 8;
};
var to = function(t102, e22, r22, n22, i22, s22, a22, o22, u22, l22, h22) {
  tt(e22, h22++, r22), ++i22[256];
  for (var f22, c22, g22, d22, p22 = tr(i22, 15), y22 = p22.t, v22 = p22.l, m22 = tr(s22, 15), w22 = m22.t, b22 = m22.l, I22 = ti(y22), U22 = I22.c, k22 = I22.n, S22 = ti(w22), E2 = S22.c, T22 = S22.n, x22 = new M(19), C22 = 0; C22 < U22.length; ++C22) ++x22[31 & U22[C22]];
  for (var C22 = 0; C22 < E2.length; ++C22) ++x22[31 & E2[C22]];
  for (var A22 = tr(x22, 7), F22 = A22.t, P22 = A22.l, j22 = 19; j22 > 4 && !F22[B[j22 - 1]]; --j22) ;
  var D22 = l22 + 5 << 3, N22 = ts(i22, R) + ts(s22, O) + a22, z22 = ts(i22, y22) + ts(s22, w22) + a22 + 14 + 3 * j22 + ts(x22, F22) + 2 * x22[16] + 3 * x22[17] + 7 * x22[18];
  if (u22 >= 0 && D22 <= N22 && D22 <= z22) return ta(e22, h22, t102.subarray(u22, u22 + l22));
  if (tt(e22, h22, 1 + (z22 < N22)), h22 += 2, z22 < N22) {
    f22 = $(y22, v22, 0), c22 = y22, g22 = $(w22, b22, 0), d22 = w22;
    var J22 = $(F22, P22, 0);
    tt(e22, h22, k22 - 257), tt(e22, h22 + 5, T22 - 1), tt(e22, h22 + 10, j22 - 4), h22 += 14;
    for (var C22 = 0; C22 < j22; ++C22) tt(e22, h22 + 3 * C22, F22[B[C22]]);
    h22 += 3 * j22;
    for (var W22 = [U22, E2], G22 = 0; G22 < 2; ++G22) for (var K22 = W22[G22], C22 = 0; C22 < K22.length; ++C22) {
      var V22 = 31 & K22[C22];
      tt(e22, h22, J22[V22]), h22 += F22[V22], V22 > 15 && (tt(e22, h22, K22[C22] >> 5 & 127), h22 += K22[C22] >> 12);
    }
  } else f22 = q, c22 = R, g22 = H, d22 = O;
  for (var C22 = 0; C22 < o22; ++C22) {
    var X22 = n22[C22];
    if (X22 > 255) {
      var V22 = X22 >> 18 & 31;
      te(e22, h22, f22[V22 + 257]), h22 += c22[V22 + 257], V22 > 7 && (tt(e22, h22, X22 >> 23 & 31), h22 += L[V22]);
      var Q22 = 31 & X22;
      te(e22, h22, g22[Q22]), h22 += d22[Q22], Q22 > 3 && (te(e22, h22, X22 >> 5 & 8191), h22 += _[Q22]);
    } else te(e22, h22, f22[X22]), h22 += c22[X22];
  }
  return te(e22, h22, f22[256]), h22 + c22[256];
};
var tu = /* @__PURE__ */ new T([65540, 131080, 131088, 131104, 262176, 1048704, 1048832, 2114560, 2117632]);
var tl = /* @__PURE__ */ new E(0);
var th = function(t102, e22, r22, n22, i22, s22) {
  var a22 = s22.z || t102.length, o22 = new E(n22 + a22 + 5 * (1 + Math.ceil(a22 / 7e3)) + i22), u22 = o22.subarray(n22, o22.length - i22), l22 = s22.l, h22 = 7 & (s22.r || 0);
  if (e22) {
    h22 && (u22[0] = s22.r >> 3);
    for (var f22 = tu[e22 - 1], c22 = f22 >> 13, g22 = 8191 & f22, d22 = (1 << r22) - 1, p22 = s22.p || new M(32768), y22 = s22.h || new M(d22 + 1), v22 = Math.ceil(r22 / 3), m22 = 2 * v22, w22 = function(e33) {
      return (t102[e33] ^ t102[e33 + 1] << v22 ^ t102[e33 + 2] << m22) & d22;
    }, b22 = new T(25e3), I22 = new M(288), U22 = new M(32), k22 = 0, S22 = 0, B22 = s22.i || 0, x22 = 0, C22 = s22.w || 0, A22 = 0; B22 + 2 < a22; ++B22) {
      var P22 = w22(B22), j22 = 32767 & B22, N22 = y22[P22];
      if (p22[j22] = N22, y22[P22] = j22, C22 <= B22) {
        var $22 = a22 - B22;
        if ((k22 > 7e3 || x22 > 24576) && ($22 > 423 || !l22)) {
          h22 = to(t102, u22, 0, b22, I22, U22, S22, x22, A22, B22 - A22, h22), x22 = k22 = S22 = 0, A22 = B22;
          for (var R22 = 0; R22 < 286; ++R22) I22[R22] = 0;
          for (var R22 = 0; R22 < 30; ++R22) U22[R22] = 0;
        }
        var O22 = 2, q22 = 0, z22 = g22, H22 = j22 - N22 & 32767;
        if ($22 > 2 && P22 == w22(B22 - H22)) for (var J22 = Math.min(c22, $22) - 1, W22 = Math.min(32767, B22), G22 = Math.min(258, $22); H22 <= W22 && --z22 && j22 != N22; ) {
          if (t102[B22 + O22] == t102[B22 + O22 - H22]) {
            for (var K22 = 0; K22 < G22 && t102[B22 + K22] == t102[B22 + K22 - H22]; ++K22) ;
            if (K22 > O22) {
              if (O22 = K22, q22 = H22, K22 > J22) break;
              for (var Q22 = Math.min(H22, K22 - 2), Z22 = 0, R22 = 0; R22 < Q22; ++R22) {
                var Y22 = B22 - H22 + R22 & 32767, tt22 = p22[Y22], te22 = Y22 - tt22 & 32767;
                te22 > Z22 && (Z22 = te22, N22 = Y22);
              }
            }
          }
          N22 = p22[j22 = N22], H22 += j22 - N22 & 32767;
        }
        if (q22) {
          b22[x22++] = 268435456 | F[O22] << 18 | D[q22];
          var tr22 = 31 & F[O22], tn22 = 31 & D[q22];
          S22 += L[tr22] + _[tn22], ++I22[257 + tr22], ++U22[tn22], C22 = B22 + O22, ++k22;
        } else b22[x22++] = t102[B22], ++I22[t102[B22]];
      }
    }
    for (B22 = Math.max(B22, C22); B22 < a22; ++B22) b22[x22++] = t102[B22], ++I22[t102[B22]];
    h22 = to(t102, u22, l22, b22, I22, U22, S22, x22, A22, B22 - A22, h22), l22 || (s22.r = 7 & h22 | u22[h22 / 8 | 0] << 3, h22 -= 7, s22.h = y22, s22.p = p22, s22.i = B22, s22.w = C22);
  } else {
    for (var B22 = s22.w || 0; B22 < a22 + l22; B22 += 65535) {
      var ti22 = B22 + 65535;
      ti22 >= a22 && (u22[h22 / 8 | 0] = l22, ti22 = a22), h22 = ta(u22, h22 + 1, t102.subarray(B22, ti22));
    }
    s22.i = a22;
  }
  return X(o22, 0, n22 + V(h22) + i22);
};
var tf = function(t102, e22, r22, n22, i22) {
  if (!i22 && (i22 = { l: 1 }, e22.dictionary)) {
    var s22 = e22.dictionary.subarray(-32768), a22 = new E(s22.length + t102.length);
    a22.set(s22), a22.set(t102, s22.length), t102 = a22, i22.w = s22.length;
  }
  return th(t102, null == e22.level ? 6 : e22.level, null == e22.mem ? i22.l ? Math.ceil(1.5 * Math.max(8, Math.min(13, Math.log(t102.length)))) : 20 : 12 + e22.mem, r22, n22, i22);
};
function tc(t102, e22) {
  return tf(t102, e22 || {}, 0, 0);
}
function tg(t102, e22) {
  return Y(t102, { i: 2 }, e22 && e22.out, e22 && e22.dictionary);
}
var td = "undefined" != typeof TextDecoder && /* @__PURE__ */ new TextDecoder();
var tp = 0;
try {
  td.decode(tl, { stream: true }), tp = 1;
} catch (t102) {
}
var ty = 1024;
var tv = 1.5;
var tm = (t102) => 2 ** t102 - 1;
var tw = (t102) => -(2 ** (t102 - 1));
var tb = (t102) => 2 ** (t102 - 1) - 1;
var tI = { Uint8: 0, Uint16: 0, Uint32: 0, Uint64: 0, BigUint64: 0, Int8: tw(8), Int16: tw(16), Int32: tw(32), Int64: Number.MIN_SAFE_INTEGER, BigInt64: -(BigInt(2) ** BigInt(63)) };
var tU = { Uint8: tm(8), Uint16: tm(16), Uint32: tm(32), Uint64: Number.MAX_SAFE_INTEGER, BigUint64: BigInt(2) ** BigInt(64) - BigInt(1), Int8: tb(8), Int16: tb(16), Int32: tb(32), Int64: Number.MAX_SAFE_INTEGER, BigInt64: BigInt(2) ** BigInt(63) - BigInt(1) };
function tk(t102, e22, r22, n22) {
  k(t102 >= e22, t102, "outside lower bound for", n22), k(t102 <= r22, t102, "outside upper bound for", n22);
}
var tS = class {
  getOffset() {
    return this.offset;
  }
  slice(t102 = 0, e22 = this.offset) {
    return this.bytes.slice(t102, e22);
  }
  subarray(t102 = 0, e22 = this.offset) {
    return this.bytes.subarray(t102, e22);
  }
  ensureLength(t102) {
    let e22 = this.bytes.length;
    if (this.offset + t102 <= e22) return;
    let r22 = new Uint8Array(Math.ceil(e22 * tv) + t102);
    r22.set(this.bytes), this.bytes = r22, this.view = I(r22);
  }
  writeUint8(t102) {
    tk(t102, tI.Uint8, tU.Uint8, "Uint8");
    let e22 = w.Uint8;
    this.ensureLength(e22), this.view.setUint8(this.offset, t102), this.offset += e22;
  }
  writeUint16(t102) {
    tk(t102, tI.Uint16, tU.Uint16, "Uint16");
    let e22 = w.Uint16;
    this.ensureLength(e22), this.view.setUint16(this.offset, t102), this.offset += e22;
  }
  writeUint32(t102) {
    tk(t102, tI.Uint32, tU.Uint32, "Uint32");
    let e22 = w.Uint32;
    this.ensureLength(e22), this.view.setUint32(this.offset, t102), this.offset += e22;
  }
  writeUint64(t102) {
    tk(t102, tI.Uint64, tU.Uint64, "Uint64");
    let e22 = BigInt(t102);
    this.writeBigUint64(e22);
  }
  writeBigUint64(t102) {
    tk(t102, tI.BigUint64, tU.BigUint64, "BigUint64");
    let e22 = w.BigUint64;
    this.ensureLength(e22), this.view.setBigUint64(this.offset, t102), this.offset += e22;
  }
  writeInt8(t102) {
    tk(t102, tI.Int8, tU.Int8, "Int8");
    let e22 = w.Int8;
    this.ensureLength(e22), this.view.setInt8(this.offset, t102), this.offset += e22;
  }
  writeInt16(t102) {
    tk(t102, tI.Int16, tU.Int16, "Int16");
    let e22 = w.Int16;
    this.ensureLength(e22), this.view.setInt16(this.offset, t102), this.offset += e22;
  }
  writeInt32(t102) {
    tk(t102, tI.Int32, tU.Int32, "Int32");
    let e22 = w.Int32;
    this.ensureLength(e22), this.view.setInt32(this.offset, t102), this.offset += e22;
  }
  writeInt64(t102) {
    tk(t102, tI.Int64, tU.Int64, "Int64");
    let e22 = BigInt(t102);
    this.writeBigInt64(e22);
  }
  writeBigInt64(t102) {
    tk(t102, tI.BigInt64, tU.BigInt64, "BigInt64");
    let e22 = w.BigInt64;
    this.ensureLength(e22), this.view.setBigInt64(this.offset, t102), this.offset += e22;
  }
  writeFloat32(t102) {
    let e22 = w.Float32;
    this.ensureLength(e22), this.view.setFloat32(this.offset, t102), this.offset += e22;
  }
  writeFloat64(t102) {
    let e22 = w.Float64;
    this.ensureLength(e22), this.view.setFloat64(this.offset, t102), this.offset += e22;
  }
  writeBytes(t102) {
    let e22 = t102.length;
    this.ensureLength(e22), this.bytes.set(t102, this.offset), this.offset += e22;
  }
  encodeString(t102) {
    let e22 = this.encodedStrings.get(t102);
    if (e22) return e22;
    let r22 = this.encoder.encode(t102);
    return this.encodedStrings.set(t102, r22), r22;
  }
  writeString(t102) {
    let e22 = this.encodeString(t102), r22 = e22.length;
    this.writeUint32(r22), this.writeBytes(e22);
  }
  writeJson(t102) {
    let e22 = JSON.stringify(t102);
    this.writeString(e22);
  }
  constructor() {
    this.offset = 0, this.bytes = new Uint8Array(ty), this.view = I(this.bytes), this.encoder = new TextEncoder(), this.encodedStrings = /* @__PURE__ */ new Map();
  }
};
function tE(t102) {
  return Number.isFinite(t102);
}
function tM(t102) {
  return void 0 === t102;
}
function tT(t102) {
  return null === t102;
}
var tL = class {
  static fromString(t102) {
    let [e22, r22, n22] = t102.split("/").map(Number);
    return k(tE(e22), "Invalid chunkId"), k(tE(r22), "Invalid offset"), k(tE(n22), "Invalid length"), new tL(e22, r22, n22);
  }
  toString() {
    return `${this.chunkId}/${this.offset}/${this.length}`;
  }
  static read(t102) {
    let e22 = t102.readUint16(), r22 = t102.readUint32(), n22 = t102.readUint32();
    return new tL(e22, r22, n22);
  }
  write(t102) {
    t102.writeUint16(this.chunkId), t102.writeUint32(this.offset), t102.writeUint32(this.length);
  }
  compare(t102) {
    return this.chunkId < t102.chunkId ? -1 : this.chunkId > t102.chunkId ? 1 : this.offset < t102.offset ? -1 : this.offset > t102.offset ? 1 : (k(this.length === t102.length), 0);
  }
  constructor(t102, e22, r22) {
    this.chunkId = t102, this.offset = e22, this.length = r22;
  }
};
function tB(t102) {
  if (tT(t102)) return 0;
  switch (t102.type) {
    case t_.Array:
      return 1;
    case t_.Boolean:
      return 2;
    case t_.Color:
      return 3;
    case t_.Date:
      return 4;
    case t_.Enum:
      return 5;
    case t_.File:
      return 6;
    case t_.ResponsiveImage:
      return 10;
    case t_.Link:
      return 7;
    case t_.Number:
      return 8;
    case t_.Object:
      return 9;
    case t_.RichText:
      return 11;
    case t_.String:
      return 12;
    default:
      S(t102);
  }
}
function tx(t102) {
  let e22 = t102.readUint16(), n22 = [];
  for (let i22 = 0; i22 < e22; i22++) {
    let e33 = r.read(t102);
    n22.push(e33);
  }
  return { type: t_.Array, value: n22 };
}
function tC(t102, e22) {
  for (let n22 of (t102.writeUint16(e22.value.length), e22.value)) r.write(t102, n22);
}
function tA(t102, e22, n22) {
  let i22 = t102.value.length, s22 = e22.value.length;
  if (i22 < s22) return -1;
  if (i22 > s22) return 1;
  for (let s32 = 0; s32 < i22; s32++) {
    let i32 = t102.value[s32], a22 = e22.value[s32], o22 = r.compare(i32, a22, n22);
    if (0 !== o22) return o22;
  }
  return 0;
}
function tF(t102) {
  return { type: t_.Boolean, value: 0 !== t102.readUint8() };
}
function tP(t102, e22) {
  t102.writeUint8(e22.value ? 1 : 0);
}
function tj(t102, e22) {
  return t102.value < e22.value ? -1 : t102.value > e22.value ? 1 : 0;
}
function tD(t102) {
  return { type: t_.Color, value: t102.readString() };
}
function tN(t102, e22) {
  t102.writeString(e22.value);
}
function t$(t102, e22) {
  return t102.value < e22.value ? -1 : t102.value > e22.value ? 1 : 0;
}
function tR(t102) {
  let e22 = t102.readInt64(), r22 = new Date(e22);
  return { type: t_.Date, value: r22.toISOString() };
}
function tO(t102, e22) {
  let r22 = new Date(e22.value), n22 = r22.getTime();
  t102.writeInt64(n22);
}
function tq(t102, e22) {
  let r22 = new Date(t102.value), n22 = new Date(e22.value);
  return r22 < n22 ? -1 : r22 > n22 ? 1 : 0;
}
function tz(t102) {
  return { type: t_.Enum, value: t102.readString() };
}
function tH(t102, e22) {
  t102.writeString(e22.value);
}
function tJ(t102, e22) {
  return t102.value < e22.value ? -1 : t102.value > e22.value ? 1 : 0;
}
function tW(t102) {
  return { type: t_.File, value: t102.readString() };
}
function tG(t102, e22) {
  t102.writeString(e22.value);
}
function tK(t102, e22) {
  return t102.value < e22.value ? -1 : t102.value > e22.value ? 1 : 0;
}
function tV(t102) {
  return { type: t_.Link, value: t102.readJson() };
}
function tX(t102, e22) {
  t102.writeJson(e22.value);
}
function tQ(t102, e22) {
  let r22 = JSON.stringify(t102.value), n22 = JSON.stringify(e22.value);
  return r22 < n22 ? -1 : r22 > n22 ? 1 : 0;
}
function tZ(t102) {
  return { type: t_.Number, value: t102.readFloat64() };
}
function tY(t102, e22) {
  t102.writeFloat64(e22.value);
}
function t1(t102, e22) {
  return t102.value < e22.value ? -1 : t102.value > e22.value ? 1 : 0;
}
function t0(t102) {
  let e22 = t102.readUint16(), n22 = {};
  for (let i22 = 0; i22 < e22; i22++) {
    let e33 = t102.readString();
    n22[e33] = r.read(t102);
  }
  return { type: t_.Object, value: n22 };
}
function t2(t102, e22) {
  let n22 = Object.entries(e22.value);
  for (let [e33, i22] of (t102.writeUint16(n22.length), n22)) t102.writeString(e33), r.write(t102, i22);
}
function t6(t102, e22, n22) {
  let i22 = Object.keys(t102.value).sort(), s22 = Object.keys(e22.value).sort();
  if (i22.length < s22.length) return -1;
  if (i22.length > s22.length) return 1;
  for (let a22 = 0; a22 < i22.length; a22++) {
    let o22 = i22[a22], u22 = s22[a22];
    if (o22 < u22) return -1;
    if (o22 > u22) return 1;
    let l22 = t102.value[o22] ?? null, h22 = e22.value[u22] ?? null, f22 = r.compare(l22, h22, n22);
    if (0 !== f22) return f22;
  }
  return 0;
}
function t3(t102) {
  return { type: t_.ResponsiveImage, value: t102.readJson() };
}
function t5(t102, e22) {
  t102.writeJson(e22.value);
}
function t4(t102, e22) {
  let r22 = JSON.stringify(t102.value), n22 = JSON.stringify(e22.value);
  return r22 < n22 ? -1 : r22 > n22 ? 1 : 0;
}
function t8(t102) {
  return { type: t_.RichText, value: t102.readUint32() };
}
function t7(t102, e22) {
  t102.writeUint32(e22.value);
}
function t9(t102, e22) {
  let r22 = t102.value, n22 = e22.value;
  return r22 < n22 ? -1 : r22 > n22 ? 1 : 0;
}
function et(t102) {
  return { type: t_.String, value: t102.readString() };
}
function ee(t102, e22) {
  t102.writeString(e22.value);
}
function er(t102, e22, r22) {
  let n22 = t102.value, i22 = e22.value;
  return (0 === r22.type && (n22 = t102.value.toLowerCase(), i22 = e22.value.toLowerCase()), n22 < i22) ? -1 : n22 > i22 ? 1 : 0;
}
((t102) => {
  t102.read = function(t112) {
    let e22 = t112.readUint8();
    switch (e22) {
      case 0:
        return null;
      case 1:
        return tx(t112);
      case 2:
        return tF(t112);
      case 3:
        return tD(t112);
      case 4:
        return tR(t112);
      case 5:
        return tz(t112);
      case 6:
        return tW(t112);
      case 7:
        return tV(t112);
      case 8:
        return tZ(t112);
      case 9:
        return t0(t112);
      case 10:
        return t3(t112);
      case 11:
        return t8(t112);
      case 12:
        return et(t112);
      default:
        S(e22);
    }
  }, t102.write = function(t112, e22) {
    let r22 = tB(e22);
    if (t112.writeUint8(r22), !tT(e22)) switch (e22.type) {
      case t_.Array:
        return tC(t112, e22);
      case t_.Boolean:
        return tP(t112, e22);
      case t_.Color:
        return tN(t112, e22);
      case t_.Date:
        return tO(t112, e22);
      case t_.Enum:
        return tH(t112, e22);
      case t_.File:
        return tG(t112, e22);
      case t_.Link:
        return tX(t112, e22);
      case t_.Number:
        return tY(t112, e22);
      case t_.Object:
        return t2(t112, e22);
      case t_.ResponsiveImage:
        return t5(t112, e22);
      case t_.RichText:
        return t7(t112, e22);
      case t_.String:
        return ee(t112, e22);
      default:
        S(e22);
    }
  }, t102.compare = function(t112, e22, r22) {
    let n22 = tB(t112), i22 = tB(e22);
    if (n22 < i22) return -1;
    if (n22 > i22) return 1;
    if (tT(t112) || tT(e22)) return 0;
    switch (t112.type) {
      case t_.Array:
        return k(e22.type === t_.Array), tA(t112, e22, r22);
      case t_.Boolean:
        return k(e22.type === t_.Boolean), tj(t112, e22);
      case t_.Color:
        return k(e22.type === t_.Color), t$(t112, e22);
      case t_.Date:
        return k(e22.type === t_.Date), tq(t112, e22);
      case t_.Enum:
        return k(e22.type === t_.Enum), tJ(t112, e22);
      case t_.File:
        return k(e22.type === t_.File), tK(t112, e22);
      case t_.Link:
        return k(e22.type === t_.Link), tQ(t112, e22);
      case t_.Number:
        return k(e22.type === t_.Number), t1(t112, e22);
      case t_.Object:
        return k(e22.type === t_.Object), t6(t112, e22, r22);
      case t_.ResponsiveImage:
        return k(e22.type === t_.ResponsiveImage), t4(t112, e22);
      case t_.RichText:
        return k(e22.type === t_.RichText), t9(t112, e22);
      case t_.String:
        return k(e22.type === t_.String), er(t112, e22, r22);
      default:
        S(t112);
    }
  };
})(r || (r = {}));
var en = class {
  sortEntries() {
    this.entries.sort((t102, e22) => {
      for (let n22 = 0; n22 < this.fieldNames.length; n22++) {
        let i22 = t102.values[n22], s22 = e22.values[n22], a22 = r.compare(i22, s22, this.options.collation);
        if (0 !== a22) return a22;
      }
      return t102.pointer.compare(e22.pointer);
    });
  }
  static deserialize(t102, e22) {
    e22 || (t102 = tg(t102));
    let n22 = new b(t102), i22 = n22.readJson(), s22 = n22.readUint8(), a22 = [];
    for (let t112 = 0; t112 < s22; t112++) {
      let t122 = n22.readString();
      a22.push(t122);
    }
    let o22 = new en(a22, { collation: i22, cmsBackendCompression: e22 }), u22 = n22.readUint32();
    for (let t112 = 0; t112 < u22; t112++) {
      let t122 = [];
      for (let e42 = 0; e42 < s22; e42++) {
        let e52 = r.read(n22);
        t122.push(e52);
      }
      let e33 = tL.read(n22);
      o22.entries.push({ values: t122, pointer: e33 });
    }
    return o22;
  }
  serialize() {
    let t102 = new tS();
    for (let e33 of (t102.writeJson(this.options.collation), t102.writeUint8(this.fieldNames.length), this.fieldNames)) t102.writeString(e33);
    for (let e33 of (this.sortEntries(), t102.writeUint32(this.entries.length), this.entries)) {
      let { values: n22, pointer: i22 } = e33;
      for (let e42 of n22) r.write(t102, e42);
      i22.write(t102);
    }
    let e22 = t102.subarray();
    return this.options.cmsBackendCompression ? e22 : tc(e22);
  }
  addItem(t102, e22) {
    let r22 = this.fieldNames.map((e33) => t102.getField(e33) ?? null);
    this.entries.push({ values: r22, pointer: e22 });
  }
  constructor(t102, e22) {
    this.fieldNames = t102, this.options = e22, this.entries = [];
  }
};
var ei = 3;
var es = 250;
var ea = [
  408,
  // Request Timeout
  429,
  // Too Many Requests
  500,
  // Internal Server Error
  502,
  // Bad Gateway
  503,
  // Service Unavailable
  504
];
var eo = async (t102, e22) => {
  let r22 = 0;
  for (; ; ) {
    try {
      let n22 = await fetch(t102, e22);
      if (!ea.includes(n22.status) || ++r22 > ei) return n22;
    } catch (t112) {
      if (e22?.signal?.aborted || ++r22 > ei) throw t112;
    }
    await eu(r22);
  }
};
async function eu(t102) {
  let e22 = Math.floor(es * (Math.random() + 1) * 2 ** (t102 - 1));
  await new Promise((t112) => {
    setTimeout(t112, e22);
  });
}
function el(t102) {
  let e22 = 3 & t102.length, r22 = t102.length - e22, n22 = 0, i22 = 0;
  for (; i22 < r22; ) {
    let e33 = 255 & t102.charCodeAt(i22) | (255 & t102.charCodeAt(++i22)) << 8 | (255 & t102.charCodeAt(++i22)) << 16 | (255 & t102.charCodeAt(++i22)) << 24;
    ++i22, n22 ^= e33 = (65535 & (e33 = (e33 = (65535 & e33) * 3432918353 + (((e33 >>> 16) * 3432918353 & 65535) << 16) & 4294967295) << 15 | e33 >>> 17)) * 461845907 + (((e33 >>> 16) * 461845907 & 65535) << 16) & 4294967295, n22 = n22 << 13 | n22 >>> 19;
    let r3 = (65535 & n22) * 5 + (((n22 >>> 16) * 5 & 65535) << 16) & 4294967295;
    n22 = (65535 & r3) + 27492 + (((r3 >>> 16) + 58964 & 65535) << 16);
  }
  let s22 = 0;
  return e22 >= 3 && (s22 ^= (255 & t102.charCodeAt(i22 + 2)) << 16), e22 >= 2 && (s22 ^= (255 & t102.charCodeAt(i22 + 1)) << 8), e22 >= 1 && (s22 ^= 255 & t102.charCodeAt(i22), n22 ^= s22 = (65535 & (s22 = (s22 = (65535 & s22) * 3432918353 + (((s22 >>> 16) * 3432918353 & 65535) << 16) & 4294967295) << 15 | s22 >>> 17)) * 461845907 + (((s22 >>> 16) * 461845907 & 65535) << 16) & 4294967295), n22 ^= t102.length, n22 ^= n22 >>> 16, n22 = (65535 & n22) * 2246822507 + (((n22 >>> 16) * 2246822507 & 65535) << 16) & 4294967295, n22 ^= n22 >>> 13, n22 = (65535 & n22) * 3266489909 + (((n22 >>> 16) * 3266489909 & 65535) << 16) & 4294967295, (n22 ^= n22 >>> 16) >>> 0;
}
var eh = f(v());
var ef = "\r\n";
var ec = "--";
async function eg(t102, e22) {
  let r22 = new URL(t102), n22 = eS(e22);
  r22.searchParams.set("rangeHash", el(n22).toString(16));
  let i22 = await eo(r22, { headers: { Range: n22 } });
  if (!i22.ok) throw Error(`Request failed: ${i22.status} ${i22.statusText}`);
  let s22 = await i22.arrayBuffer(), a22 = new Uint8Array(s22);
  if (200 === i22.status) return e22.map((t112) => {
    if (t112.to > a22.length) throw Error("Missing data");
    return a22.slice(t112.from, t112.to);
  });
  if (206 === i22.status) {
    let t112 = i22.headers.get("Content-Type");
    if (!t112) throw Error("Missing Content-Type header");
    let r3 = new em(), n32 = new eh.default(t112);
    if ("multipart" === n32.type && "byteranges" === n32.subtype) {
      let t122 = n32.parameters.get("boundary");
      if (!t122) throw Error("Missing boundary parameter");
      let e33 = ey(a22, t122);
      for (let { headers: t13, body: n42 } of e33) {
        let e42 = t13.get("Content-Range");
        if (!e42) throw Error("Missing Content-Range header");
        let i32 = ev(e42);
        if ("bytes" !== i32.unit) throw Error("Unsupported unit");
        r3.write(i32.start, n42);
      }
    } else {
      let t122 = i22.headers.get("Content-Range");
      if (!t122) throw Error("Missing Content-Range header");
      let e33 = ev(t122);
      if ("bytes" !== e33.unit) throw Error("Unsupported unit");
      r3.write(e33.start, a22);
    }
    return e22.map((t122) => r3.read(t122.from, t122.to - t122.from));
  }
  throw Error(`Unsupported response: ${i22.status} ${i22.statusText}`);
}
var ed = new TextEncoder();
var ep = new TextDecoder();
function ey(t102, e22) {
  let r22 = ed.encode(ec + e22), n22 = eb(t102, r22);
  if (tM(n22)) throw Error("Missing parts start");
  let i22 = n22 + r22.length, s22 = ed.encode(ef + ec + e22 + ec), a22 = eI(t102, s22);
  if (tM(a22)) throw Error("Missing parts end");
  let o22 = ed.encode(ef + ec + e22), u22 = t102.subarray(i22, a22), l22 = ew(u22, o22);
  return l22.map((t112) => {
    let e33 = ed.encode(ef + ef), r3 = eb(t112, e33);
    if (tM(r3)) throw Error("Missing headers end");
    let n32 = ed.encode(ef), i32 = t112.subarray(0, r3), s32 = ew(i32, n32), a32 = new Headers();
    for (let t122 of s32) {
      let e42 = ep.decode(t122), [r4, n42] = e42.split(": ", 2);
      r4 && n42 && a32.append(r4, n42);
    }
    let o32 = r3 + e33.length, u32 = t112.slice(o32);
    return { headers: a32, body: u32 };
  });
}
function ev(t102) {
  let e22 = RegExp("^(?<unit>\\w+) (?<start>\\d+)-(?<end>\\d+)\\/(?<size>\\d+|\\*)$", "u").exec(t102);
  if (!e22 || !e22.groups) throw Error("Invalid Content-Range header");
  let { unit: r22, start: n22, end: i22, size: s22 } = e22.groups;
  return { unit: r22, start: Number(n22), end: Number(i22), size: "*" !== s22 ? Number(s22) : null };
}
var em = class {
  read(t102, e22) {
    for (let r22 of this.chunks) {
      if (t102 < r22.start) break;
      if (t102 > r22.end) continue;
      if (t102 + e22 > r22.end) break;
      let n22 = t102 - r22.start, i22 = n22 + e22;
      return r22.data.slice(n22, i22);
    }
    throw Error("Missing data");
  }
  write(t102, e22) {
    let r22 = t102, n22 = r22 + e22.length, i22 = 0, s22 = this.chunks.length;
    for (; i22 < s22; i22++) {
      let t112 = this.chunks[i22];
      if (k(t112, "Missing chunk"), !(r22 > t112.end)) {
        if (r22 > t112.start) {
          let n32 = r22 - t112.start, i32 = t112.data.subarray(0, n32);
          e22 = ek(i32, e22), r22 = t112.start;
        }
        break;
      }
    }
    for (; s22 > i22; s22--) {
      let t112 = this.chunks[s22 - 1];
      if (k(t112, "Missing chunk"), !(n22 < t112.start)) {
        if (n22 < t112.end) {
          let r3 = n22 - t112.start, i32 = t112.data.subarray(r3);
          e22 = ek(e22, i32), n22 = t112.end;
        }
        break;
      }
    }
    let a22 = { start: r22, end: n22, data: e22 }, o22 = s22 - i22;
    this.chunks.splice(i22, o22, a22);
  }
  constructor() {
    this.chunks = [];
  }
};
function ew(t102, e22) {
  let r22 = [];
  for (; t102.length > 0; ) {
    let n22 = eb(t102, e22) ?? t102.length, i22 = t102.subarray(0, n22);
    r22.push(i22), t102 = t102.subarray(n22 + e22.length);
  }
  return r22;
}
function eb(t102, e22) {
  for (let r22 = 0; r22 < t102.length - e22.length; r22++) {
    let n22 = t102.subarray(r22);
    if (eU(n22, e22)) return r22;
  }
}
function eI(t102, e22) {
  for (let r22 = t102.length - e22.length; r22 >= 0; r22--) {
    let n22 = t102.subarray(r22);
    if (eU(n22, e22)) return r22;
  }
}
function eU(t102, e22) {
  for (let r22 = 0; r22 < e22.length; r22++) if (t102[r22] !== e22[r22]) return false;
  return true;
}
function ek(t102, e22) {
  let r22 = t102.length + e22.length, n22 = new Uint8Array(r22);
  return n22.set(t102, 0), n22.set(e22, t102.length), n22;
}
function eS(t102) {
  k(t102.length > 0, "Must have at least one range");
  let e22 = [...t102].sort((t112, e33) => t112.from - e33.from), r22 = [];
  for (let t112 of e22) {
    let e33 = r22.length - 1, n32 = r22[e33];
    n32 && t112.from <= n32.to ? r22[e33] = { from: n32.from, to: Math.max(n32.to, t112.to) } : r22.push(t112);
  }
  let n22 = r22.map((t112) => `${t112.from}-${t112.to - 1}`);
  return `bytes=${n22.join(",")}`;
}
async function eE(t102, e22, r22) {
  if (!r22) return eg(t102, e22);
  let n22 = eL(e22), i22 = [], s22 = 0;
  for (let t112 of n22) i22.push(`${t112.from}-${t112.to - 1}`), s22 += t112.to - t112.from;
  let a22 = new URL(t102), o22 = i22.join(",");
  a22.searchParams.set("range", o22);
  let u22 = await eo(a22);
  if (200 !== u22.status) throw Error(`Request failed: ${u22.status} ${u22.statusText}`);
  let l22 = await u22.arrayBuffer(), h22 = new Uint8Array(l22);
  if (h22.length !== s22) throw Error("Request failed: Unexpected response length");
  let f22 = new eM(), c22 = 0;
  for (let t112 of n22) {
    let e33 = t112.to - t112.from, r3 = c22 + e33, n32 = h22.subarray(c22, r3);
    f22.write(t112.from, n32), c22 = r3;
  }
  return e22.map((t112) => f22.read(t112.from, t112.to - t112.from));
}
var eM = class {
  read(t102, e22) {
    for (let r22 of this.chunks) {
      if (t102 < r22.start) break;
      if (t102 > r22.end) continue;
      if (t102 + e22 > r22.end) break;
      let n22 = t102 - r22.start, i22 = n22 + e22;
      return r22.data.slice(n22, i22);
    }
    throw Error("Missing data");
  }
  write(t102, e22) {
    let r22 = t102, n22 = r22 + e22.length, i22 = 0, s22 = this.chunks.length;
    for (; i22 < s22; i22++) {
      let t112 = this.chunks[i22];
      if (k(t112, "Missing chunk"), !(r22 > t112.end)) {
        if (r22 > t112.start) {
          let n32 = r22 - t112.start, i32 = t112.data.subarray(0, n32);
          e22 = eT(i32, e22), r22 = t112.start;
        }
        break;
      }
    }
    for (; s22 > i22; s22--) {
      let t112 = this.chunks[s22 - 1];
      if (k(t112, "Missing chunk"), !(n22 < t112.start)) {
        if (n22 < t112.end) {
          let r3 = n22 - t112.start, i32 = t112.data.subarray(r3);
          e22 = eT(e22, i32), n22 = t112.end;
        }
        break;
      }
    }
    let a22 = { start: r22, end: n22, data: e22 }, o22 = s22 - i22;
    this.chunks.splice(i22, o22, a22);
  }
  constructor() {
    this.chunks = [];
  }
};
function eT(t102, e22) {
  let r22 = t102.length + e22.length, n22 = new Uint8Array(r22);
  return n22.set(t102, 0), n22.set(e22, t102.length), n22;
}
function eL(t102) {
  k(t102.length > 0, "Must have at least one range");
  let e22 = [...t102].sort((t112, e33) => t112.from - e33.from), r22 = [];
  for (let t112 of e22) {
    let e33 = r22.length - 1, n22 = r22[e33];
    n22 && t112.from <= n22.to ? r22[e33] = { from: n22.from, to: Math.max(n22.to, t112.to) } : r22.push(t112);
  }
  return r22;
}
var e_ = class {
  async loadModel() {
    let [t102] = await eE(this.options.url, [this.options.range], this.options.cmsBackendCompression);
    return k(t102, "Failed to load model"), en.deserialize(t102, this.options.cmsBackendCompression);
  }
  async getModel() {
    return this.modelPromise ??= this.loadModel(), this.model ??= await this.modelPromise, this.model;
  }
  async lookupItems(t102) {
    k(t102.length === this.fields.length, "Invalid query length");
    let e22 = await this.getModel(), r22 = t102.reduce((t112, e33, r3) => t112.flatMap((t122) => {
      switch (e33.type) {
        case "All":
          return [t122];
        case "Equals":
          return this.queryEquals(t122, e33, r3);
        case "NotEquals":
          return this.queryNotEquals(t122, e33, r3);
        case "LessThan":
          return this.queryLessThan(t122, e33, r3);
        case "GreaterThan":
          return this.queryGreaterThan(t122, e33, r3);
        case "Contains":
          return this.queryContains(t122, e33, r3);
        case "StartsWith":
          return this.queryStartsWith(t122, e33, r3);
        case "EndsWith":
          return this.queryEndsWith(t122, e33, r3);
        default:
          S(e33);
      }
    }), [e22.entries]), n22 = [];
    for (let t112 of r22) for (let e33 of t112) {
      let t122 = {};
      for (let r3 = 0; r3 < this.options.fieldNames.length; r3++) {
        let n32 = this.options.fieldNames[r3], i22 = e33.values[r3];
        t122[n32] = i22;
      }
      n22.push({ pointer: e33.pointer.toString(), data: t122 });
    }
    return n22;
  }
  queryEquals(t102, e22, r22) {
    let n22 = this.getLeftMost(t102, r22, e22.value), i22 = this.getRightMost(t102, r22, e22.value), s22 = t102.slice(n22, i22 + 1);
    return s22.length > 0 ? [s22] : [];
  }
  queryNotEquals(t102, e22, r22) {
    let n22 = this.getLeftMost(t102, r22, e22.value), i22 = this.getRightMost(t102, r22, e22.value), s22 = [], a22 = t102.slice(0, n22);
    a22.length > 0 && s22.push(a22);
    let o22 = t102.slice(i22 + 1);
    return o22.length > 0 && s22.push(o22), s22;
  }
  queryLessThan(t102, e22, r22) {
    let n22 = this.getRightMost(t102, r22, null);
    if (t102 = t102.slice(n22 + 1), e22.inclusive) {
      let n32 = this.getRightMost(t102, r22, e22.value), i32 = t102.slice(0, n32 + 1);
      return i32.length > 0 ? [i32] : [];
    }
    let i22 = this.getLeftMost(t102, r22, e22.value), s22 = t102.slice(0, i22);
    return s22.length > 0 ? [s22] : [];
  }
  queryGreaterThan(t102, e22, r22) {
    let n22 = this.getRightMost(t102, r22, null);
    if (t102 = t102.slice(n22 + 1), e22.inclusive) {
      let n32 = this.getLeftMost(t102, r22, e22.value), i32 = t102.slice(n32);
      return i32.length > 0 ? [i32] : [];
    }
    let i22 = this.getRightMost(t102, r22, e22.value), s22 = t102.slice(i22 + 1);
    return s22.length > 0 ? [s22] : [];
  }
  queryContains(t102, e22, r22) {
    return this.findItems(t102, r22, (t112) => {
      if (t112?.type !== U.String || e22.value?.type !== U.String) return false;
      let r3 = t112.value, n22 = e22.value.value;
      return 0 === this.collation.type && (r3 = r3.toLowerCase(), n22 = n22.toLowerCase()), r3.includes(n22);
    });
  }
  queryStartsWith(t102, e22, r22) {
    return this.findItems(t102, r22, (t112) => {
      if (t112?.type !== U.String || e22.value?.type !== U.String) return false;
      let r3 = t112.value, n22 = e22.value.value;
      return 0 === this.collation.type && (r3 = r3.toLowerCase(), n22 = n22.toLowerCase()), r3.startsWith(n22);
    });
  }
  queryEndsWith(t102, e22, r22) {
    return this.findItems(t102, r22, (t112) => {
      if (t112?.type !== U.String || e22.value?.type !== U.String) return false;
      let r3 = t112.value, n22 = e22.value.value;
      return 0 === this.collation.type && (r3 = r3.toLowerCase(), n22 = n22.toLowerCase()), r3.endsWith(n22);
    });
  }
  /**
  * Returns the index of the left most entry that is equal to the target.
  *
  * ```text
  *   Left most
  *       ↓
  * ┌───┬───┬───┬───┬───┬───┐
  * │ 1 │ 2 │ 2 │ 2 │ 2 │ 3 │
  * └───┴───┴───┴───┴───┴───┘
  * ```
  *
  * @param entries The entries array to search in.
  * @param position The position of the value in the entry.
  * @param target The target value to search for.
  * @returns The index of the left most entry that is equal to the target.
  */
  getLeftMost(t102, e22, n22) {
    let i22 = 0, s22 = t102.length;
    for (; i22 < s22; ) {
      let a22 = i22 + s22 >> 1, o22 = t102[a22], u22 = o22.values[e22];
      0 > r.compare(u22, n22, this.collation) ? i22 = a22 + 1 : s22 = a22;
    }
    return i22;
  }
  /**
  * Returns the index of the right most entry that is equal to the target.
  *
  * ```text
  *              Right most
  *                   ↓
  * ┌───┬───┬───┬───┬───┬───┐
  * │ 1 │ 2 │ 2 │ 2 │ 2 │ 3 │
  * └───┴───┴───┴───┴───┴───┘
  * ```
  *
  * @param entries The entries array to search in.
  * @param position The position of the value in the entry.
  * @param target The target value to search for.
  * @returns The index of the right most entry that is equal to the target.
  */
  getRightMost(t102, e22, n22) {
    let i22 = 0, s22 = t102.length;
    for (; i22 < s22; ) {
      let a22 = i22 + s22 >> 1, o22 = t102[a22], u22 = o22.values[e22];
      r.compare(u22, n22, this.collation) > 0 ? s22 = a22 : i22 = a22 + 1;
    }
    return s22 - 1;
  }
  /**
  * Finds all items that are matching the predicate and groups adjacent items together.
  *
  * @param entries The entries array to search in.
  * @param position The position of the value in the entry.
  * @param predicate The predicate to match the values against.
  * @returns An array of chunks that match the predicate.
  */
  findItems(t102, e22, r22) {
    let n22 = [], i22 = 0;
    for (let s22 = 0; s22 < t102.length; s22++) {
      let a22 = t102[s22], o22 = a22.values[e22], u22 = r22(o22);
      if (!u22) {
        if (i22 < s22) {
          let e33 = t102.slice(i22, s22);
          n22.push(e33);
        }
        i22 = s22 + 1;
      }
    }
    if (i22 < t102.length) {
      let e33 = t102.slice(i22);
      n22.push(e33);
    }
    return n22;
  }
  constructor(t102) {
    this.options = t102, this.supportedLookupTypes = [
      "All",
      "Equals",
      "NotEquals",
      "LessThan",
      "GreaterThan",
      "Contains",
      "StartsWith",
      "EndsWith"
      /* EndsWith */
    ], this.collation = this.options.collation;
    let e22 = {}, r22 = [];
    for (let t112 of this.options.fieldNames) {
      let n22 = this.options.collectionSchema[t112];
      k(n22, "Missing definition for field", t112), e22[t112] = n22, r22.push({ type: "Identifier", name: t112 });
    }
    this.schema = e22, this.fields = r22;
  }
};
var eB = class {
  static read(t102, e22) {
    let n22 = new eB();
    if (e22) {
      let r22 = t102.readUint32(), n32 = t102.readBytes(r22), i32 = tg(n32, { dictionary: e22 });
      t102 = new b(i32);
    }
    let i22 = t102.readUint16();
    for (let e33 = 0; e33 < i22; e33++) {
      let e42 = t102.readString(), i32 = r.read(t102);
      n22.setField(e42, i32);
    }
    return n22;
  }
  writeUncompressed(t102) {
    for (let [e22, n22] of (t102.writeUint16(this.fields.size), this.fields)) t102.writeString(e22), r.write(t102, n22);
  }
  write(t102, e22) {
    if (e22) {
      let r22 = new tS();
      this.writeUncompressed(r22);
      let n22 = tc(r22.subarray(), { dictionary: e22 });
      t102.writeUint32(n22.length), t102.writeBytes(n22);
    } else this.writeUncompressed(t102);
  }
  getData() {
    let t102 = {};
    for (let [e22, r22] of this.fields) t102[e22] = r22;
    return t102;
  }
  setField(t102, e22) {
    this.fields.set(t102, e22);
  }
  getField(t102) {
    return this.fields.get(t102);
  }
  constructor() {
    this.fields = /* @__PURE__ */ new Map();
  }
};
var ex = class {
  scanItems() {
    return this.itemsPromise ??= Promise.all([eo(this.url), this.getCompressionDictionary()]).then(async ([t102, e22]) => {
      if (!t102.ok) throw Error(`Request failed: ${t102.status} ${t102.statusText}`);
      let r22 = await t102.arrayBuffer(), n22 = new Uint8Array(r22), i22 = new b(n22), s22 = [], a22 = i22.readUint32();
      for (let t112 = 0; t112 < a22; t112++) {
        let t122 = i22.getOffset(), r3 = eB.read(i22, e22), n32 = i22.getOffset() - t122, a32 = new tL(this.id, t122, n32), o22 = a32.toString(), u22 = { pointer: o22, data: r3.getData() };
        this.itemLoader.prime(o22, u22), s22.push(u22);
      }
      return s22;
    }), this.itemsPromise;
  }
  resolveItem(t102) {
    return this.itemLoader.load(t102);
  }
  getCompressionDictionary() {
    if (!this.compressionDictionaryUrl) return null;
    let t102 = this.compressionDictionaryCache.get(this.compressionDictionaryUrl);
    if (t102) return t102;
    let e22 = eo(this.compressionDictionaryUrl).then(async (t112) => {
      if (!t112.ok) throw Error(`Compression dictionary request failed: ${t112.status} ${t112.statusText}`);
      let e33 = await t112.arrayBuffer();
      return new Uint8Array(e33);
    });
    return this.compressionDictionaryCache.set(this.compressionDictionaryUrl, e22), e22;
  }
  constructor(t102, e22, r22, n22) {
    this.id = t102, this.url = e22, this.compressionDictionaryUrl = r22, this.compressionDictionaryCache = n22, this.itemLoader = new m.default(async (t112) => {
      let e33 = t112.map((t122) => {
        let e42 = tL.fromString(t122);
        return { from: e42.offset, to: e42.offset + e42.length };
      }), [r3, n32] = await Promise.all([eE(this.url, e33, !this.compressionDictionaryUrl), this.getCompressionDictionary()]);
      return r3.map((e42, r4) => {
        let i22 = new b(e42), s22 = eB.read(i22, n32), a22 = t112[r4];
        return k(a22, "Missing pointer"), { pointer: a22, data: s22.getData() };
      });
    });
  }
};
var eC = class {
  async scanItems() {
    let t102 = await Promise.all(this.chunks.map(async (t112) => t112.scanItems()));
    return t102.flat();
  }
  async resolveItems(t102) {
    return Promise.all(t102.map((t112) => {
      let e22 = tL.fromString(t112), r22 = this.chunks[e22.chunkId];
      return k(r22, "Missing chunk"), r22.resolveItem(t112);
    }));
  }
  compareItems(t102, e22) {
    let r22 = tL.fromString(t102.pointer), n22 = tL.fromString(e22.pointer);
    return r22.compare(n22);
  }
  compareValues(t102, e22, n22) {
    return r.compare(t102, e22, n22);
  }
  constructor(t102) {
    this.options = t102, this.schema = this.options.schema, this.indexes = this.options.indexes, this.resolveRichText = this.options.resolveRichText;
    let e22 = /* @__PURE__ */ new Map();
    this.chunks = this.options.chunks.map((t112, r22) => new ex(r22, t112, this.options.compressionDictionary, e22));
  }
};

// /:https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub-1.js
var e2 = [];
async function resolveRichText(t13) {
  let i4 = e2[t13];
  if (i4) return await i4();
}

// /:https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js
var m2 = { eonTB_66J: { isNullable: true, type: l2.ResponsiveImage }, eqHfP8fwy: { isNullable: true, type: l2.String }, HIwYwspJk: { isNullable: true, type: l2.String }, I1Lu9IENQ: { isNullable: true, type: l2.Link }, id: { isNullable: false, type: l2.String }, Kgz23TbUj: { isNullable: true, type: l2.ResponsiveImage }, ki3s5awac: { isNullable: true, type: l2.String }, Ld79nxY6Z: { isNullable: true, type: l2.String }, MtQTQhYJs: { isNullable: true, type: l2.String }, nextItemId: { isNullable: true, type: l2.String }, npspf8YZW: { definition: { definitions: { dE2rKA0g_: { isNullable: true, type: l2.ResponsiveImage }, id: { isNullable: false, type: l2.String } }, isNullable: true, type: l2.Object }, isNullable: true, type: l2.Array }, nQDQaUWt8: { isNullable: true, type: l2.String }, o0u8fDxiI: { isNullable: true, type: l2.String }, oKJFyWYP3: { isNullable: true, type: l2.String }, previousItemId: { isNullable: true, type: l2.String }, QHASXpEku: { isNullable: true, type: l2.String }, SXCBkRyNN: { isNullable: true, type: l2.String }, TSb3TBWfl: { isNullable: true, type: l2.String }, TTTtYQeWk: { isNullable: true, type: l2.ResponsiveImage }, VTCOI7DAa: { isNullable: true, type: l2.ResponsiveImage }, W4mj77nvi: { isNullable: true, type: l2.String }, wHRkNQQhU: { isNullable: true, type: l2.String }, xkT0g3LPE: { isNullable: true, type: l2.String }, XXA0p5zks: { isNullable: true, type: l2.ResponsiveImage }, zPgoiMX0P: { definition: { isNullable: true, type: l2.String }, isNullable: true, type: l2.Array } };
var n2 = ["id"];
var s2 = { type: 1 };
var c2 = ["previousItemId"];
var u2 = ["nextItemId"];
var d2 = ["id", "SXCBkRyNN"];
var p2 = ["SXCBkRyNN", "id"];
var f2 = ["MtQTQhYJs"];
var y2 = { type: 0 };
var g2 = ["SXCBkRyNN"];
var x2 = ["zPgoiMX0P"];
var N2 = ["TSb3TBWfl"];
var B2 = ["VTCOI7DAa"];
var w2 = ["Kgz23TbUj"];
var S2 = ["nQDQaUWt8"];
var b2 = ["ki3s5awac"];
var h2 = ["o0u8fDxiI"];
var k2 = ["Ld79nxY6Z"];
var I2 = ["XXA0p5zks"];
var R2 = ["TTTtYQeWk"];
var L2 = ["eonTB_66J"];
var C2 = ["xkT0g3LPE"];
var T2 = ["wHRkNQQhU"];
var O2 = ["oKJFyWYP3"];
var U2 = ["HIwYwspJk"];
var q2 = ["I1Lu9IENQ"];
var Q2 = ["npspf8YZW"];
var v2 = ["W4mj77nvi"];
var A2 = ["QHASXpEku"];
var V2 = ["eqHfP8fwy"];
var X2 = new a2();
var Y2 = new t10(X2);
var P2 = { collectionByLocaleId: { default: new eC({ chunks: [new URL("./B3xOiqtub-chunk-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/")], compressionDictionary: null, indexes: [new e_({ cmsBackendCompression: true, collation: s2, collectionSchema: m2, fieldNames: n2, range: { from: 0, to: 169 }, url: new URL("./B3xOiqtub-indexes-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/") }), new e_({ cmsBackendCompression: true, collation: s2, collectionSchema: m2, fieldNames: c2, range: { from: 169, to: 337 }, url: new URL("./B3xOiqtub-indexes-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/") }), new e_({ cmsBackendCompression: true, collation: s2, collectionSchema: m2, fieldNames: u2, range: { from: 337, to: 501 }, url: new URL("./B3xOiqtub-indexes-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/") }), new e_({ cmsBackendCompression: true, collation: s2, collectionSchema: m2, fieldNames: d2, range: { from: 501, to: 770 }, url: new URL("./B3xOiqtub-indexes-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/") }), new e_({ cmsBackendCompression: true, collation: s2, collectionSchema: m2, fieldNames: p2, range: { from: 770, to: 1039 }, url: new URL("./B3xOiqtub-indexes-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/") }), new e_({ cmsBackendCompression: true, collation: y2, collectionSchema: m2, fieldNames: f2, range: { from: 1039, to: 1224 }, url: new URL("./B3xOiqtub-indexes-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/") }), new e_({ cmsBackendCompression: true, collation: y2, collectionSchema: m2, fieldNames: g2, range: { from: 1224, to: 1403 }, url: new URL("./B3xOiqtub-indexes-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/") }), new e_({ cmsBackendCompression: true, collation: y2, collectionSchema: m2, fieldNames: x2, range: { from: 1403, to: 1681 }, url: new URL("./B3xOiqtub-indexes-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/") }), new e_({ cmsBackendCompression: true, collation: y2, collectionSchema: m2, fieldNames: N2, range: { from: 1681, to: 1827 }, url: new URL("./B3xOiqtub-indexes-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/") }), new e_({ cmsBackendCompression: true, collation: y2, collectionSchema: m2, fieldNames: B2, range: { from: 1827, to: 3455 }, url: new URL("./B3xOiqtub-indexes-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/") }), new e_({ cmsBackendCompression: true, collation: y2, collectionSchema: m2, fieldNames: w2, range: { from: 3455, to: 4041 }, url: new URL("./B3xOiqtub-indexes-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/") }), new e_({ cmsBackendCompression: true, collation: y2, collectionSchema: m2, fieldNames: S2, range: { from: 4041, to: 4235 }, url: new URL("./B3xOiqtub-indexes-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/") }), new e_({ cmsBackendCompression: true, collation: y2, collectionSchema: m2, fieldNames: b2, range: { from: 4235, to: 6241 }, url: new URL("./B3xOiqtub-indexes-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/") }), new e_({ cmsBackendCompression: true, collation: y2, collectionSchema: m2, fieldNames: h2, range: { from: 6241, to: 6507 }, url: new URL("./B3xOiqtub-indexes-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/") }), new e_({ cmsBackendCompression: true, collation: y2, collectionSchema: m2, fieldNames: k2, range: { from: 6507, to: 6677 }, url: new URL("./B3xOiqtub-indexes-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/") }), new e_({ cmsBackendCompression: true, collation: y2, collectionSchema: m2, fieldNames: I2, range: { from: 6677, to: 7262 }, url: new URL("./B3xOiqtub-indexes-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/") }), new e_({ cmsBackendCompression: true, collation: y2, collectionSchema: m2, fieldNames: R2, range: { from: 7262, to: 9484 }, url: new URL("./B3xOiqtub-indexes-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/") }), new e_({ cmsBackendCompression: true, collation: y2, collectionSchema: m2, fieldNames: L2, range: { from: 9484, to: 11658 }, url: new URL("./B3xOiqtub-indexes-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/") }), new e_({ cmsBackendCompression: true, collation: y2, collectionSchema: m2, fieldNames: C2, range: { from: 11658, to: 11840 }, url: new URL("./B3xOiqtub-indexes-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/") }), new e_({ cmsBackendCompression: true, collation: y2, collectionSchema: m2, fieldNames: T2, range: { from: 11840, to: 12826 }, url: new URL("./B3xOiqtub-indexes-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/") }), new e_({ cmsBackendCompression: true, collation: y2, collectionSchema: m2, fieldNames: O2, range: { from: 12826, to: 13800 }, url: new URL("./B3xOiqtub-indexes-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/") }), new e_({ cmsBackendCompression: true, collation: y2, collectionSchema: m2, fieldNames: U2, range: { from: 13800, to: 13994 }, url: new URL("./B3xOiqtub-indexes-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/") }), new e_({ cmsBackendCompression: true, collation: y2, collectionSchema: m2, fieldNames: q2, range: { from: 13994, to: 14254 }, url: new URL("./B3xOiqtub-indexes-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/") }), new e_({ cmsBackendCompression: true, collation: y2, collectionSchema: m2, fieldNames: Q2, range: { from: 14254, to: 21306 }, url: new URL("./B3xOiqtub-indexes-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/") }), new e_({ cmsBackendCompression: true, collation: y2, collectionSchema: m2, fieldNames: v2, range: { from: 21306, to: 21512 }, url: new URL("./B3xOiqtub-indexes-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/") }), new e_({ cmsBackendCompression: true, collation: y2, collectionSchema: m2, fieldNames: A2, range: { from: 21512, to: 22714 }, url: new URL("./B3xOiqtub-indexes-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/") }), new e_({ cmsBackendCompression: true, collation: y2, collectionSchema: m2, fieldNames: V2, range: { from: 22714, to: 23574 }, url: new URL("./B3xOiqtub-indexes-default-0.framercms", "https://framerusercontent.com/modules/7vEcikYsOmpgOk0P2AVy/HsALF1i4Uist4OoN93BQ/B3xOiqtub.js").href.replace("/modules/", "/cms/") })], resolveRichText, schema: m2 }) }, displayName: "Projects" };
var stdin_default2 = P2;
e3(P2, { MtQTQhYJs: { defaultValue: "", title: "Title", type: l2.String }, SXCBkRyNN: { title: "Slug", type: l2.String }, zPgoiMX0P: { dataIdentifier: "local-module:collection/qNTzVcuJS:default", title: "Categories", type: l2.MultiCollectionReference }, TSb3TBWfl: { defaultValue: "", title: "Year", type: l2.String }, VTCOI7DAa: { title: "Preview image", type: l2.ResponsiveImage }, Kgz23TbUj: { title: "Logo light", type: l2.ResponsiveImage }, nQDQaUWt8: { defaultValue: "", title: "Intro label", type: l2.String }, ki3s5awac: { defaultValue: "", displayTextArea: true, title: "Intro text", type: l2.String }, o0u8fDxiI: { defaultValue: "", title: "Industry", type: l2.String }, Ld79nxY6Z: { defaultValue: "", title: "Timeline", type: l2.String }, XXA0p5zks: { title: "Logo dark", type: l2.ResponsiveImage }, TTTtYQeWk: { title: "Image 1", type: l2.ResponsiveImage }, eonTB_66J: { title: "Image 2", type: l2.ResponsiveImage }, xkT0g3LPE: { defaultValue: "", title: "Info label", type: l2.String }, wHRkNQQhU: { defaultValue: "", displayTextArea: true, title: "Text large", type: l2.String }, oKJFyWYP3: { defaultValue: "", displayTextArea: true, title: "Text small", type: l2.String }, HIwYwspJk: { defaultValue: "", title: "Button title", type: l2.String }, I1Lu9IENQ: { title: "Link", type: l2.Link }, npspf8YZW: { __vekterDefault: [], control: { controls: { dE2rKA0g_: { title: "Image", type: "responsiveimage" } }, type: "object" }, title: "Gallery", type: l2.Array }, W4mj77nvi: { defaultValue: "", title: "Info label 2", type: l2.String }, QHASXpEku: { defaultValue: "", displayTextArea: true, title: "Text large 2", type: l2.String }, eqHfP8fwy: { defaultValue: "", displayTextArea: true, title: "Text small 2", type: l2.String }, previousItemId: { dataIdentifier: "local-module:collection/B3xOiqtub:default", title: "Previous", type: l2.CollectionReference }, nextItemId: { dataIdentifier: "local-module:collection/B3xOiqtub:default", title: "Next", type: l2.CollectionReference } });

// /:https://framerusercontent.com/modules/xqMlz63qKzLJKZQseE5v/5e05xMkHRisZbX7kOPJG/qNTzVcuJS.js
import { addPropertyControls as e6, ControlType as t12, QueryCache as l4, QueryEngine as i3 } from "unframer";

// /:https://framerusercontent.com/modules/xqMlz63qKzLJKZQseE5v/5e05xMkHRisZbX7kOPJG/qNTzVcuJS-0.js
import { ControlType as p3 } from "unframer";
import { ControlType as A3 } from "unframer";
var t11;
var e4 = Object.create;
var r2 = Object.defineProperty;
var n3 = Object.getOwnPropertyDescriptor;
var i2 = Object.getOwnPropertyNames;
var s3 = Object.getPrototypeOf;
var a3 = Object.prototype.hasOwnProperty;
var o2 = (t32, e42) => function() {
  return e42 || (0, t32[i2(t32)[0]])((e42 = { exports: {} }).exports, e42), e42.exports;
};
var u3 = (t32, e42, s22, o22) => {
  if (e42 && "object" == typeof e42 || "function" == typeof e42) for (let u22 of i2(e42)) a3.call(t32, u22) || u22 === s22 || r2(t32, u22, { get: () => e42[u22], enumerable: !(o22 = n3(e42, u22)) || o22.enumerable });
  return t32;
};
var l3 = (t32, n22, i22) => (i22 = null != t32 ? e4(s3(t32)) : {}, u3(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  !n22 && t32 && t32.__esModule ? i22 : r2(i22, "default", { value: t32, enumerable: true }),
  t32
));
var h3 = o2({ "../../../node_modules/dataloader/index.js"(t32, e42) {
  var r22, n22 = /* @__PURE__ */ function() {
    function t42(t52, e62) {
      if ("function" != typeof t52) throw TypeError("DataLoader must be constructed with a function which accepts Array<key> and returns Promise<Array<value>>, but got: " + t52 + ".");
      this._batchLoadFn = t52, this._maxBatchSize = function(t62) {
        if (!(!t62 || false !== t62.batch)) return 1;
        var e7 = t62 && t62.maxBatchSize;
        if (void 0 === e7) return 1 / 0;
        if ("number" != typeof e7 || e7 < 1) throw TypeError("maxBatchSize must be a positive number: " + e7);
        return e7;
      }(e62), this._batchScheduleFn = function(t62) {
        var e7 = t62 && t62.batchScheduleFn;
        if (void 0 === e7) return i22;
        if ("function" != typeof e7) throw TypeError("batchScheduleFn must be a function: " + e7);
        return e7;
      }(e62), this._cacheKeyFn = function(t62) {
        var e7 = t62 && t62.cacheKeyFn;
        if (void 0 === e7) return function(t72) {
          return t72;
        };
        if ("function" != typeof e7) throw TypeError("cacheKeyFn must be a function: " + e7);
        return e7;
      }(e62), this._cacheMap = function(t62) {
        if (!(!t62 || false !== t62.cache)) return null;
        var e7 = t62 && t62.cacheMap;
        if (void 0 === e7) return /* @__PURE__ */ new Map();
        if (null !== e7) {
          var r3 = ["get", "set", "delete", "clear"].filter(function(t72) {
            return e7 && "function" != typeof e7[t72];
          });
          if (0 !== r3.length) throw TypeError("Custom cacheMap missing methods: " + r3.join(", "));
        }
        return e7;
      }(e62), this._batch = null, this.name = e62 && e62.name ? e62.name : null;
    }
    var e52 = t42.prototype;
    return e52.load = function(t52) {
      if (null == t52) throw TypeError("The loader.load() function must be called with a value, but got: " + String(t52) + ".");
      var e62 = function(t62) {
        var e7 = t62._batch;
        if (null !== e7 && !e7.hasDispatched && e7.keys.length < t62._maxBatchSize) return e7;
        var r4 = { hasDispatched: false, keys: [], callbacks: [] };
        return t62._batch = r4, t62._batchScheduleFn(function() {
          (function(t72, e8) {
            var r5;
            if (e8.hasDispatched = true, 0 === e8.keys.length) {
              a22(e8);
              return;
            }
            try {
              r5 = t72._batchLoadFn(e8.keys);
            } catch (r6) {
              return s22(t72, e8, TypeError("DataLoader must be constructed with a function which accepts Array<key> and returns Promise<Array<value>>, but the function errored synchronously: " + String(r6) + "."));
            }
            if (!r5 || "function" != typeof r5.then) return s22(t72, e8, TypeError("DataLoader must be constructed with a function which accepts Array<key> and returns Promise<Array<value>>, but the function did not return a Promise: " + String(r5) + "."));
            r5.then(function(t82) {
              if (!o22(t82)) throw TypeError("DataLoader must be constructed with a function which accepts Array<key> and returns Promise<Array<value>>, but the function did not return a Promise of an Array: " + String(t82) + ".");
              if (t82.length !== e8.keys.length) throw TypeError("DataLoader must be constructed with a function which accepts Array<key> and returns Promise<Array<value>>, but the function did not return a Promise of an Array of the same length as the Array of keys.\n\nKeys:\n" + String(e8.keys) + "\n\nValues:\n" + String(t82));
              a22(e8);
              for (var r6 = 0; r6 < e8.callbacks.length; r6++) {
                var n42 = t82[r6];
                n42 instanceof Error ? e8.callbacks[r6].reject(n42) : e8.callbacks[r6].resolve(n42);
              }
            }).catch(function(r6) {
              s22(t72, e8, r6);
            });
          })(t62, r4);
        }), r4;
      }(this), r3 = this._cacheMap, n32 = this._cacheKeyFn(t52);
      if (r3) {
        var i32 = r3.get(n32);
        if (i32) {
          var u22 = e62.cacheHits || (e62.cacheHits = []);
          return new Promise(function(t62) {
            u22.push(function() {
              t62(i32);
            });
          });
        }
      }
      e62.keys.push(t52);
      var l22 = new Promise(function(t62, r4) {
        e62.callbacks.push({ resolve: t62, reject: r4 });
      });
      return r3 && r3.set(n32, l22), l22;
    }, e52.loadMany = function(t52) {
      if (!o22(t52)) throw TypeError("The loader.loadMany() function must be called with Array<key> but got: " + t52 + ".");
      for (var e62 = [], r3 = 0; r3 < t52.length; r3++) e62.push(this.load(t52[r3]).catch(function(t62) {
        return t62;
      }));
      return Promise.all(e62);
    }, e52.clear = function(t52) {
      var e62 = this._cacheMap;
      if (e62) {
        var r3 = this._cacheKeyFn(t52);
        e62.delete(r3);
      }
      return this;
    }, e52.clearAll = function() {
      var t52 = this._cacheMap;
      return t52 && t52.clear(), this;
    }, e52.prime = function(t52, e62) {
      var r3 = this._cacheMap;
      if (r3) {
        var n32, i32 = this._cacheKeyFn(t52);
        void 0 === r3.get(i32) && (e62 instanceof Error ? (n32 = Promise.reject(e62)).catch(function() {
        }) : n32 = Promise.resolve(e62), r3.set(i32, n32));
      }
      return this;
    }, t42;
  }(), i22 = "object" == typeof process && "function" == typeof process.nextTick ? function(t42) {
    r22 || (r22 = Promise.resolve()), r22.then(function() {
      process.nextTick(t42);
    });
  } : "function" == typeof setImmediate ? function(t42) {
    setImmediate(t42);
  } : function(t42) {
    setTimeout(t42);
  };
  function s22(t42, e52, r3) {
    a22(e52);
    for (var n32 = 0; n32 < e52.keys.length; n32++) t42.clear(e52.keys[n32]), e52.callbacks[n32].reject(r3);
  }
  function a22(t42) {
    if (t42.cacheHits) for (var e52 = 0; e52 < t42.cacheHits.length; e52++) t42.cacheHits[e52]();
  }
  function o22(t42) {
    return "object" == typeof t42 && null !== t42 && "number" == typeof t42.length && (0 === t42.length || t42.length > 0 && Object.prototype.hasOwnProperty.call(t42, t42.length - 1));
  }
  e42.exports = n22;
} });
var c3 = l3(h3());
var f3 = { Uint8: 1, Uint16: 2, Uint32: 4, BigUint64: 8, Int8: 1, Int16: 2, Int32: 4, BigInt64: 8, Float32: 4, Float64: 8 };
var g3 = class {
  getOffset() {
    return this.offset;
  }
  ensureLength(t32) {
    let e42 = this.bytes.length;
    if (!(this.offset + t32 <= e42)) throw Error("Reading out of bounds");
  }
  readUint8() {
    let t32 = f3.Uint8;
    this.ensureLength(t32);
    let e42 = this.view.getUint8(this.offset);
    return this.offset += t32, e42;
  }
  readUint16() {
    let t32 = f3.Uint16;
    this.ensureLength(t32);
    let e42 = this.view.getUint16(this.offset);
    return this.offset += t32, e42;
  }
  readUint32() {
    let t32 = f3.Uint32;
    this.ensureLength(t32);
    let e42 = this.view.getUint32(this.offset);
    return this.offset += t32, e42;
  }
  readUint64() {
    let t32 = this.readBigUint64();
    return Number(t32);
  }
  readBigUint64() {
    let t32 = f3.BigUint64;
    this.ensureLength(t32);
    let e42 = this.view.getBigUint64(this.offset);
    return this.offset += t32, e42;
  }
  readInt8() {
    let t32 = f3.Int8;
    this.ensureLength(t32);
    let e42 = this.view.getInt8(this.offset);
    return this.offset += t32, e42;
  }
  readInt16() {
    let t32 = f3.Int16;
    this.ensureLength(t32);
    let e42 = this.view.getInt16(this.offset);
    return this.offset += t32, e42;
  }
  readInt32() {
    let t32 = f3.Int32;
    this.ensureLength(t32);
    let e42 = this.view.getInt32(this.offset);
    return this.offset += t32, e42;
  }
  readInt64() {
    let t32 = this.readBigInt64();
    return Number(t32);
  }
  readBigInt64() {
    let t32 = f3.BigInt64;
    this.ensureLength(t32);
    let e42 = this.view.getBigInt64(this.offset);
    return this.offset += t32, e42;
  }
  readFloat32() {
    let t32 = f3.Float32;
    this.ensureLength(t32);
    let e42 = this.view.getFloat32(this.offset);
    return this.offset += t32, e42;
  }
  readFloat64() {
    let t32 = f3.Float64;
    this.ensureLength(t32);
    let e42 = this.view.getFloat64(this.offset);
    return this.offset += t32, e42;
  }
  readBytes(t32) {
    let e42 = this.offset, r22 = e42 + t32, n22 = this.bytes.subarray(e42, r22);
    return this.offset = r22, n22;
  }
  readString() {
    let t32 = this.readUint32(), e42 = this.readBytes(t32);
    return this.decoder.decode(e42);
  }
  readJson() {
    let t32 = this.readString();
    return JSON.parse(t32);
  }
  constructor(t32) {
    this.bytes = t32, this.offset = 0, this.view = d3(this.bytes), this.decoder = new TextDecoder();
  }
};
function d3(t32) {
  return new DataView(t32.buffer, t32.byteOffset, t32.byteLength);
}
var y3 = "undefined" != typeof window;
var v3 = y3 && "function" == typeof window.requestIdleCallback;
function w3(t32, ...e42) {
  if (!t32) throw Error("Assertion Error" + (e42.length > 0 ? ": " + e42.join(" ") : ""));
}
function m3(t32) {
  throw Error(`Unexpected value: ${t32}`);
}
var U3 = (t32) => 2 ** t32 - 1;
var S3 = (t32) => -(2 ** (t32 - 1));
var k3 = (t32) => 2 ** (t32 - 1) - 1;
var L3 = { Uint8: 0, Uint16: 0, Uint32: 0, Uint64: 0, BigUint64: 0, Int8: S3(8), Int16: S3(16), Int32: S3(32), Int64: Number.MIN_SAFE_INTEGER, BigInt64: -(BigInt(2) ** BigInt(63)) };
var B3 = { Uint8: U3(8), Uint16: U3(16), Uint32: U3(32), Uint64: Number.MAX_SAFE_INTEGER, BigUint64: BigInt(2) ** BigInt(64) - BigInt(1), Int8: k3(8), Int16: k3(16), Int32: k3(32), Int64: Number.MAX_SAFE_INTEGER, BigInt64: BigInt(2) ** BigInt(63) - BigInt(1) };
function F2(t32) {
  return Number.isFinite(t32);
}
function T3(t32) {
  return null === t32;
}
var N3 = class t22 {
  static fromString(e42) {
    let [r22, n22, i22] = e42.split("/").map(Number);
    return w3(F2(r22), "Invalid chunkId"), w3(F2(n22), "Invalid offset"), w3(F2(i22), "Invalid length"), new t22(r22, n22, i22);
  }
  toString() {
    return `${this.chunkId}/${this.offset}/${this.length}`;
  }
  static read(e42) {
    let r22 = e42.readUint16(), n22 = e42.readUint32(), i22 = e42.readUint32();
    return new t22(r22, n22, i22);
  }
  write(t32) {
    t32.writeUint16(this.chunkId), t32.writeUint32(this.offset), t32.writeUint32(this.length);
  }
  compare(t32) {
    return this.chunkId < t32.chunkId ? -1 : this.chunkId > t32.chunkId ? 1 : this.offset < t32.offset ? -1 : this.offset > t32.offset ? 1 : (w3(this.length === t32.length), 0);
  }
  constructor(t32, e42, r22) {
    this.chunkId = t32, this.offset = e42, this.length = r22;
  }
};
function O3(t32) {
  if (T3(t32)) return 0;
  switch (t32.type) {
    case A3.Array:
      return 1;
    case A3.Boolean:
      return 2;
    case A3.Color:
      return 3;
    case A3.Date:
      return 4;
    case A3.Enum:
      return 5;
    case A3.File:
      return 6;
    case A3.ResponsiveImage:
      return 10;
    case A3.Link:
      return 7;
    case A3.Number:
      return 8;
    case A3.Object:
      return 9;
    case A3.RichText:
      return 11;
    case A3.String:
      return 12;
    default:
      m3(t32);
  }
}
function x3(e42) {
  let r22 = e42.readUint16(), n22 = [];
  for (let i22 = 0; i22 < r22; i22++) {
    let r3 = t11.read(e42);
    n22.push(r3);
  }
  return { type: A3.Array, value: n22 };
}
function P3(e42, r22) {
  for (let n22 of (e42.writeUint16(r22.value.length), r22.value)) t11.write(e42, n22);
}
function q3(e42, r22, n22) {
  let i22 = e42.value.length, s22 = r22.value.length;
  if (i22 < s22) return -1;
  if (i22 > s22) return 1;
  for (let s32 = 0; s32 < i22; s32++) {
    let i32 = e42.value[s32], a22 = r22.value[s32], o22 = t11.compare(i32, a22, n22);
    if (0 !== o22) return o22;
  }
  return 0;
}
function R3(t32) {
  return { type: A3.Boolean, value: 0 !== t32.readUint8() };
}
function _2(t32, e42) {
  t32.writeUint8(e42.value ? 1 : 0);
}
function D2(t32, e42) {
  return t32.value < e42.value ? -1 : t32.value > e42.value ? 1 : 0;
}
function j2(t32) {
  return { type: A3.Color, value: t32.readString() };
}
function C3(t32, e42) {
  t32.writeString(e42.value);
}
function J2(t32, e42) {
  return t32.value < e42.value ? -1 : t32.value > e42.value ? 1 : 0;
}
function W2(t32) {
  let e42 = t32.readInt64(), r22 = new Date(e42);
  return { type: A3.Date, value: r22.toISOString() };
}
function $2(t32, e42) {
  let r22 = new Date(e42.value), n22 = r22.getTime();
  t32.writeInt64(n22);
}
function z2(t32, e42) {
  let r22 = new Date(t32.value), n22 = new Date(e42.value);
  return r22 < n22 ? -1 : r22 > n22 ? 1 : 0;
}
function G2(t32) {
  return { type: A3.Enum, value: t32.readString() };
}
function K2(t32, e42) {
  t32.writeString(e42.value);
}
function H2(t32, e42) {
  return t32.value < e42.value ? -1 : t32.value > e42.value ? 1 : 0;
}
function V3(t32) {
  return { type: A3.File, value: t32.readString() };
}
function X3(t32, e42) {
  t32.writeString(e42.value);
}
function Q3(t32, e42) {
  return t32.value < e42.value ? -1 : t32.value > e42.value ? 1 : 0;
}
function Y3(t32) {
  return { type: A3.Link, value: t32.readJson() };
}
function Z2(t32, e42) {
  t32.writeJson(e42.value);
}
function tt2(t32, e42) {
  let r22 = JSON.stringify(t32.value), n22 = JSON.stringify(e42.value);
  return r22 < n22 ? -1 : r22 > n22 ? 1 : 0;
}
function te2(t32) {
  return { type: A3.Number, value: t32.readFloat64() };
}
function tr2(t32, e42) {
  t32.writeFloat64(e42.value);
}
function tn2(t32, e42) {
  return t32.value < e42.value ? -1 : t32.value > e42.value ? 1 : 0;
}
function ti2(e42) {
  let r22 = e42.readUint16(), n22 = {};
  for (let i22 = 0; i22 < r22; i22++) {
    let r3 = e42.readString();
    n22[r3] = t11.read(e42);
  }
  return { type: A3.Object, value: n22 };
}
function ts2(e42, r22) {
  let n22 = Object.entries(r22.value);
  for (let [r3, i22] of (e42.writeUint16(n22.length), n22)) e42.writeString(r3), t11.write(e42, i22);
}
function ta2(e42, r22, n22) {
  let i22 = Object.keys(e42.value).sort(), s22 = Object.keys(r22.value).sort();
  if (i22.length < s22.length) return -1;
  if (i22.length > s22.length) return 1;
  for (let a22 = 0; a22 < i22.length; a22++) {
    let o22 = i22[a22], u22 = s22[a22];
    if (o22 < u22) return -1;
    if (o22 > u22) return 1;
    let l22 = e42.value[o22] ?? null, h22 = r22.value[u22] ?? null, c22 = t11.compare(l22, h22, n22);
    if (0 !== c22) return c22;
  }
  return 0;
}
function to2(t32) {
  return { type: A3.ResponsiveImage, value: t32.readJson() };
}
function tu2(t32, e42) {
  t32.writeJson(e42.value);
}
function tl2(t32, e42) {
  let r22 = JSON.stringify(t32.value), n22 = JSON.stringify(e42.value);
  return r22 < n22 ? -1 : r22 > n22 ? 1 : 0;
}
function th2(t32) {
  return { type: A3.RichText, value: t32.readUint32() };
}
function tc2(t32, e42) {
  t32.writeUint32(e42.value);
}
function tf2(t32, e42) {
  let r22 = t32.value, n22 = e42.value;
  return r22 < n22 ? -1 : r22 > n22 ? 1 : 0;
}
function tg2(t32) {
  return { type: A3.String, value: t32.readString() };
}
function td2(t32, e42) {
  t32.writeString(e42.value);
}
function tp2(t32, e42, r22) {
  let n22 = t32.value, i22 = e42.value;
  return (0 === r22.type && (n22 = t32.value.toLowerCase(), i22 = e42.value.toLowerCase()), n22 < i22) ? -1 : n22 > i22 ? 1 : 0;
}
((t32) => {
  t32.read = function(t42) {
    let e42 = t42.readUint8();
    switch (e42) {
      case 0:
        return null;
      case 1:
        return x3(t42);
      case 2:
        return R3(t42);
      case 3:
        return j2(t42);
      case 4:
        return W2(t42);
      case 5:
        return G2(t42);
      case 6:
        return V3(t42);
      case 7:
        return Y3(t42);
      case 8:
        return te2(t42);
      case 9:
        return ti2(t42);
      case 10:
        return to2(t42);
      case 11:
        return th2(t42);
      case 12:
        return tg2(t42);
      default:
        m3(e42);
    }
  }, t32.write = function(t42, e42) {
    let r22 = O3(e42);
    if (t42.writeUint8(r22), !T3(e42)) switch (e42.type) {
      case A3.Array:
        return P3(t42, e42);
      case A3.Boolean:
        return _2(t42, e42);
      case A3.Color:
        return C3(t42, e42);
      case A3.Date:
        return $2(t42, e42);
      case A3.Enum:
        return K2(t42, e42);
      case A3.File:
        return X3(t42, e42);
      case A3.Link:
        return Z2(t42, e42);
      case A3.Number:
        return tr2(t42, e42);
      case A3.Object:
        return ts2(t42, e42);
      case A3.ResponsiveImage:
        return tu2(t42, e42);
      case A3.RichText:
        return tc2(t42, e42);
      case A3.String:
        return td2(t42, e42);
      default:
        m3(e42);
    }
  }, t32.compare = function(t42, e42, r22) {
    let n22 = O3(t42), i22 = O3(e42);
    if (n22 < i22) return -1;
    if (n22 > i22) return 1;
    if (T3(t42) || T3(e42)) return 0;
    switch (t42.type) {
      case A3.Array:
        return w3(e42.type === A3.Array), q3(t42, e42, r22);
      case A3.Boolean:
        return w3(e42.type === A3.Boolean), D2(t42, e42);
      case A3.Color:
        return w3(e42.type === A3.Color), J2(t42, e42);
      case A3.Date:
        return w3(e42.type === A3.Date), z2(t42, e42);
      case A3.Enum:
        return w3(e42.type === A3.Enum), H2(t42, e42);
      case A3.File:
        return w3(e42.type === A3.File), Q3(t42, e42);
      case A3.Link:
        return w3(e42.type === A3.Link), tt2(t42, e42);
      case A3.Number:
        return w3(e42.type === A3.Number), tn2(t42, e42);
      case A3.Object:
        return w3(e42.type === A3.Object), ta2(t42, e42, r22);
      case A3.ResponsiveImage:
        return w3(e42.type === A3.ResponsiveImage), tl2(t42, e42);
      case A3.RichText:
        return w3(e42.type === A3.RichText), tf2(t42, e42);
      case A3.String:
        return w3(e42.type === A3.String), tp2(t42, e42, r22);
      default:
        m3(t42);
    }
  };
})(t11 || (t11 = {}));
var tv2 = 3;
var tw2 = 250;
var tm2 = [
  408,
  // Request Timeout
  429,
  // Too Many Requests
  500,
  // Internal Server Error
  502,
  // Bad Gateway
  503,
  // Service Unavailable
  504
];
var tI2 = async (t32, e42) => {
  let r22 = 0;
  for (; ; ) {
    try {
      let n22 = await fetch(t32, e42);
      if (!tm2.includes(n22.status) || ++r22 > tv2) return n22;
    } catch (t42) {
      if (e42?.signal?.aborted || ++r22 > tv2) throw t42;
    }
    await tb2(r22);
  }
};
async function tb2(t32) {
  let e42 = Math.floor(tw2 * (Math.random() + 1) * 2 ** (t32 - 1));
  await new Promise((t42) => {
    setTimeout(t42, e42);
  });
}
async function tU2(t32, e42) {
  let r22 = tL2(e42), n22 = [], i22 = 0;
  for (let t42 of r22) n22.push(`${t42.from}-${t42.to - 1}`), i22 += t42.to - t42.from;
  let s22 = new URL(t32), a22 = n22.join(",");
  s22.searchParams.set("range", a22);
  let o22 = await tI2(s22);
  if (200 !== o22.status) throw Error(`Request failed: ${o22.status} ${o22.statusText}`);
  let u22 = await o22.arrayBuffer(), l22 = new Uint8Array(u22);
  if (l22.length !== i22) throw Error("Request failed: Unexpected response length");
  let h22 = new tS2(), c22 = 0;
  for (let t42 of r22) {
    let e52 = t42.to - t42.from, r3 = c22 + e52, n32 = l22.subarray(c22, r3);
    h22.write(t42.from, n32), c22 = r3;
  }
  return e42.map((t42) => h22.read(t42.from, t42.to - t42.from));
}
var tS2 = class {
  read(t32, e42) {
    for (let r22 of this.chunks) {
      if (t32 < r22.start) break;
      if (t32 > r22.end) continue;
      if (t32 + e42 > r22.end) break;
      let n22 = t32 - r22.start, i22 = n22 + e42;
      return r22.data.slice(n22, i22);
    }
    throw Error("Missing data");
  }
  write(t32, e42) {
    let r22 = t32, n22 = r22 + e42.length, i22 = 0, s22 = this.chunks.length;
    for (; i22 < s22; i22++) {
      let t42 = this.chunks[i22];
      if (w3(t42, "Missing chunk"), !(r22 > t42.end)) {
        if (r22 > t42.start) {
          let n32 = r22 - t42.start, i32 = t42.data.subarray(0, n32);
          e42 = tk2(i32, e42), r22 = t42.start;
        }
        break;
      }
    }
    for (; s22 > i22; s22--) {
      let t42 = this.chunks[s22 - 1];
      if (w3(t42, "Missing chunk"), !(n22 < t42.start)) {
        if (n22 < t42.end) {
          let r3 = n22 - t42.start, i32 = t42.data.subarray(r3);
          e42 = tk2(e42, i32), n22 = t42.end;
        }
        break;
      }
    }
    let a22 = { start: r22, end: n22, data: e42 }, o22 = s22 - i22;
    this.chunks.splice(i22, o22, a22);
  }
  constructor() {
    this.chunks = [];
  }
};
function tk2(t32, e42) {
  let r22 = t32.length + e42.length, n22 = new Uint8Array(r22);
  return n22.set(t32, 0), n22.set(e42, t32.length), n22;
}
function tL2(t32) {
  w3(t32.length > 0, "Must have at least one range");
  let e42 = [...t32].sort((t42, e52) => t42.from - e52.from), r22 = [];
  for (let t42 of e42) {
    let e52 = r22.length - 1, n22 = r22[e52];
    n22 && t42.from <= n22.to ? r22[e52] = { from: n22.from, to: Math.max(n22.to, t42.to) } : r22.push(t42);
  }
  return r22;
}
var tE2 = class e32 {
  static read(r22) {
    let n22 = new e32(), i22 = r22.readUint16();
    for (let e42 = 0; e42 < i22; e42++) {
      let e52 = r22.readString(), i32 = t11.read(r22);
      n22.setField(e52, i32);
    }
    return n22;
  }
  write(e42) {
    for (let [r22, n22] of (e42.writeUint16(this.fields.size), this.fields)) e42.writeString(r22), t11.write(e42, n22);
  }
  getData() {
    let t32 = {};
    for (let [e42, r22] of this.fields) t32[e42] = r22;
    return t32;
  }
  setField(t32, e42) {
    this.fields.set(t32, e42);
  }
  getField(t32) {
    return this.fields.get(t32);
  }
  constructor() {
    this.fields = /* @__PURE__ */ new Map();
  }
};
var tM2 = class {
  scanItems() {
    return this.itemsPromise ??= tI2(this.url).then(async (t32) => {
      if (!t32.ok) throw Error(`Request failed: ${t32.status} ${t32.statusText}`);
      let e42 = await t32.arrayBuffer(), r22 = new Uint8Array(e42), n22 = new g3(r22), i22 = [], s22 = n22.readUint32();
      for (let t42 = 0; t42 < s22; t42++) {
        let t52 = n22.getOffset(), e52 = tE2.read(n22), r3 = n22.getOffset() - t52, s32 = new N3(this.id, t52, r3), a22 = s32.toString(), o22 = { pointer: a22, data: e52.getData() };
        this.itemLoader.prime(a22, o22), i22.push(o22);
      }
      return i22;
    }), this.itemsPromise;
  }
  resolveItem(t32) {
    return this.itemLoader.load(t32);
  }
  constructor(t32, e42) {
    this.id = t32, this.url = e42, this.itemLoader = new c3.default(async (t42) => {
      let e52 = t42.map((t52) => {
        let e62 = N3.fromString(t52);
        return { from: e62.offset, to: e62.offset + e62.length };
      }), r22 = await tU2(this.url, e52);
      return r22.map((e62, r3) => {
        let n22 = new g3(e62), i22 = tE2.read(n22), s22 = t42[r3];
        return w3(s22, "Missing pointer"), { pointer: s22, data: i22.getData() };
      });
    });
  }
};
var tF2 = class {
  async scanItems() {
    let t32 = await Promise.all(this.chunks.map(async (t42) => t42.scanItems()));
    return t32.flat();
  }
  async resolveItems(t32) {
    return Promise.all(t32.map((t42) => {
      let e42 = N3.fromString(t42), r22 = this.chunks[e42.chunkId];
      return w3(r22, "Missing chunk"), r22.resolveItem(t42);
    }));
  }
  compareItems(t32, e42) {
    let r22 = N3.fromString(t32.pointer), n22 = N3.fromString(e42.pointer);
    return r22.compare(n22);
  }
  compareValues(e42, r22, n22) {
    return t11.compare(e42, r22, n22);
  }
  constructor(t32) {
    this.options = t32, this.schema = this.options.schema, this.indexes = this.options.indexes, this.resolveRichText = this.options.resolveRichText, this.chunks = this.options.chunks.map((t42, e42) => new tM2(e42, t42));
  }
};

// /:https://framerusercontent.com/modules/xqMlz63qKzLJKZQseE5v/5e05xMkHRisZbX7kOPJG/qNTzVcuJS-1.js
var e5 = [];
async function resolveRichText2(t13) {
  let i4 = e5[t13];
  if (i4) return await i4();
}

// /:https://framerusercontent.com/modules/xqMlz63qKzLJKZQseE5v/5e05xMkHRisZbX7kOPJG/qNTzVcuJS.js
var n4 = { id: { isNullable: false, type: t12.String }, nextItemId: { isNullable: true, type: t12.String }, previousItemId: { isNullable: true, type: t12.String }, ulw6Us1gG: { isNullable: true, type: t12.String }, ZTCDifSmg: { isNullable: true, type: t12.String } };
var o3 = new i3();
var u4 = new l4(o3);
var p4 = { collectionByLocaleId: { default: new tF2({ chunks: [new URL("./qNTzVcuJS-chunk-default-0.framercms", "https://framerusercontent.com/modules/xqMlz63qKzLJKZQseE5v/5e05xMkHRisZbX7kOPJG/qNTzVcuJS.js").href.replace("/modules/", "/cms/")], indexes: [], resolveRichText: resolveRichText2, schema: n4 }) }, displayName: "Category" };
var stdin_default3 = p4;
e6(p4, { ulw6Us1gG: { defaultValue: "", title: "Title", type: t12.String }, ZTCDifSmg: { title: "Slug", type: t12.String }, previousItemId: { dataIdentifier: "local-module:collection/qNTzVcuJS:default", title: "Previous", type: t12.CollectionReference }, nextItemId: { dataIdentifier: "local-module:collection/qNTzVcuJS:default", title: "Next", type: t12.CollectionReference } });

// /:https://framerusercontent.com/modules/lj6xtW3z0CEecnsPKstp/CzL6AGzcEG7H6XHoIDLI/ntVcqJNYe.js
var RichTextWithFX = withFX(RichText);
var MotionDivWithFX = withFX(motion.div);
var ProjectCardFonts = getFonts(stdin_default);
var SmartComponentScopedContainerWithFX = withFX(SmartComponentScopedContainer);
var serializationHash = "framer-NYyWQ";
var variantClassNames = { p0nDCvxtQ: "framer-v-hv8rku" };
var transition1 = { bounce: 0.2, delay: 0, duration: 0.4, type: "spring" };
var animation = { opacity: 0, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, x: 0, y: 0 };
var transition2 = { bounce: 0.1, delay: 0.2, duration: 1.2, type: "spring" };
var toResponsiveImage = (value) => {
  if (typeof value === "object" && value !== null && typeof value.src === "string") {
    return value;
  }
  return typeof value === "string" ? { src: value } : void 0;
};
var QueryData = ({ query: query2, pageSize, children }) => {
  const data = useQueryData(query2);
  return children(data);
};
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var getProps = ({ height, id, width, ...props }) => {
  return { ...props };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className: className3, layoutId, variant, MtQTQhYJsYQHGsNvjO, zPgoiMX0PYQHGsNvjO, TSb3TBWflYQHGsNvjO, VTCOI7DAaYQHGsNvjO, Kgz23TbUjYQHGsNvjO, SXCBkRyNNYQHGsNvjO, idYQHGsNvjO, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ defaultVariant: "p0nDCvxtQ", ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [className2, className];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  const router = useRouter();
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsx(motion.section, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-hv8rku", className3, classNames), "data-framer-name": "Variant 1", layoutDependency, layoutId: "p0nDCvxtQ", ref: refBinding, style: { ...style }, children: /* @__PURE__ */ _jsxs(motion.div, { className: "framer-4olikp", "data-framer-name": "Container", layoutDependency, layoutId: "OkdTH7lBH", children: [/* @__PURE__ */ _jsxs(MotionDivWithFX, { __framer__adjustPosition: false, __framer__offset: 0, __framer__parallaxTransformEnabled: true, __framer__speed: 97, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-1hxsd0l", "data-framer-name": "Top", layoutDependency, layoutId: "w4cS_pv04", children: [/* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-1n1wh7h", "data-styles-preset": "gd6AWaps9", children: "(27)" }) }), className: "framer-fdxjai", "data-framer-name": "Project count", fonts: ["Inter"], layoutDependency, layoutId: "WjxM0oD69", style: { "--framer-paragraph-spacing": "0px", opacity: 0.6 }, verticalAlignment: "top", withExternalLayout: true }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-7l5kym", "data-framer-name": "Heading", layoutDependency, layoutId: "wxTK1G6DF", children: [/* @__PURE__ */ _jsx(RichTextWithFX, { __framer__animate: { transition: transition2 }, __framer__animateOnce: true, __framer__enter: animation, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0, __fromCanvasComponent: true, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.h2, { className: "framer-styles-preset-1yvd34u", "data-styles-preset": "GKtOymhXV", children: "Projects." }) }), className: "framer-1vc39i", fonts: ["Inter"], layoutDependency, layoutId: "tqHBT_NWp", style: { "--framer-paragraph-spacing": "0px" }, verticalAlignment: "top", withExternalLayout: true }), /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "36px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.06em", "--framer-line-height": "100%", "--framer-text-color": "var(--extracted-r6o4lv, var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10)))" }, children: "\xA92025" }) }), className: "framer-edjibz", "data-framer-name": "2025", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "Wvy6zbFvX", style: { "--extracted-r6o4lv": "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", "--framer-paragraph-spacing": "0px" }, verticalAlignment: "top", withExternalLayout: true })] }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-bkkbwn", "data-framer-name": "Description", layoutDependency, layoutId: "kttFM_G5D", children: /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-1n1wh7h", "data-styles-preset": "gd6AWaps9", children: "We\u2019ve helped businesses across industries achieve their goals. Here are some of our recent projects." }) }), className: "framer-1xp3zqa", fonts: ["Inter"], layoutDependency, layoutId: "gCBZUcIyj", style: { "--framer-paragraph-spacing": "0px", opacity: 0.6 }, verticalAlignment: "top", withExternalLayout: true }) })] }), /* @__PURE__ */ _jsx(MotionDivWithFX, { __framer__styleTransformEffectEnabled: true, __framer__transformTargets: [{ target: { opacity: 1, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, x: 0, y: 140 } }, { target: { opacity: 1, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, x: 0, y: 0 } }], __framer__transformTrigger: "onInView", __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-aeexdy", layoutDependency, layoutId: "YQHGsNvjO", children: /* @__PURE__ */ _jsx(ChildrenCanSuspend, { children: /* @__PURE__ */ _jsx(QueryData, { query: { from: { alias: "YQHGsNvjO", data: stdin_default2, type: "Collection" }, limit: { type: "LiteralValue", value: 6 }, select: [{ collection: "YQHGsNvjO", name: "MtQTQhYJs", type: "Identifier" }, { alias: "zPgoiMX0P", arguments: [{ from: { alias: "zPgoiMX0P", data: stdin_default3, type: "Collection" }, orderBy: [{ arguments: [{ collection: "YQHGsNvjO", name: "zPgoiMX0P", type: "Identifier" }, { collection: "zPgoiMX0P", name: "id", type: "Identifier" }], functionName: "INDEX_OF", type: "FunctionCall" }], select: [{ collection: "zPgoiMX0P", name: "id", type: "Identifier" }], type: "Select", where: { left: { collection: "zPgoiMX0P", name: "id", type: "Identifier" }, operator: "in", right: { collection: "YQHGsNvjO", name: "zPgoiMX0P", type: "Identifier" }, type: "BinaryOperation" } }], functionName: "FLAT_ARRAY", type: "FunctionCall" }, { collection: "YQHGsNvjO", name: "TSb3TBWfl", type: "Identifier" }, { collection: "YQHGsNvjO", name: "VTCOI7DAa", type: "Identifier" }, { collection: "YQHGsNvjO", name: "Kgz23TbUj", type: "Identifier" }, { collection: "YQHGsNvjO", name: "SXCBkRyNN", type: "Identifier" }, { collection: "YQHGsNvjO", name: "id", type: "Identifier" }] }, children: (collection, paginationInfo, loadMore) => /* @__PURE__ */ _jsx(_Fragment, { children: collection?.map(({ id: idYQHGsNvjO2, Kgz23TbUj: Kgz23TbUjYQHGsNvjO2, MtQTQhYJs: MtQTQhYJsYQHGsNvjO2, SXCBkRyNN: SXCBkRyNNYQHGsNvjO2, TSb3TBWfl: TSb3TBWflYQHGsNvjO2, VTCOI7DAa: VTCOI7DAaYQHGsNvjO2, zPgoiMX0P: zPgoiMX0PYQHGsNvjO2 }, index) => {
    MtQTQhYJsYQHGsNvjO2 ??= "";
    TSb3TBWflYQHGsNvjO2 ??= "";
    SXCBkRyNNYQHGsNvjO2 ??= "";
    return /* @__PURE__ */ _jsx(LayoutGroup, { id: `YQHGsNvjO-${idYQHGsNvjO2}`, children: /* @__PURE__ */ _jsx(PathVariablesContext.Provider, { value: { SXCBkRyNN: SXCBkRyNNYQHGsNvjO2 }, children: /* @__PURE__ */ _jsx(ResolveLinks, { links: [{ href: { pathVariables: { SXCBkRyNN: SXCBkRyNNYQHGsNvjO2 }, webPageId: "CSWMauv_K" }, implicitPathVariables: void 0 }], children: (resolvedLinks) => /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 473, width: `max((min(${componentViewport?.width || "100vw"} - 72px, 1520px) - 4px) / 2, 50px)`, y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 200) - 0 - 1374.48) / 2 + 0 + 0) + 0 + 424.48 + 0 + 0, children: /* @__PURE__ */ _jsx(SmartComponentScopedContainerWithFX, { __framer__animate: { transition: transition2 }, __framer__animateOnce: true, __framer__enter: animation, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-g46orm-container", layoutDependency, layoutId: "sJmfKnPgz-container", nodeId: "sJmfKnPgz", rendersWithMotion: true, scopeId: "ntVcqJNYe", children: /* @__PURE__ */ _jsx(stdin_default, { EOzevFWPq: resolvedLinks[0], height: "100%", id: "sJmfKnPgz", layoutId: "sJmfKnPgz", OuxDw9Wc4: MtQTQhYJsYQHGsNvjO2, qZAUb4V0E: toResponsiveImage(VTCOI7DAaYQHGsNvjO2), style: { width: "100%" }, t8xTis15R: zPgoiMX0PYQHGsNvjO2, variant: "RLnNDkK1c", width: "100%", WJHOOjgGx: toResponsiveImage(Kgz23TbUjYQHGsNvjO2), x3OlI7F0c: TSb3TBWflYQHGsNvjO2 }) }) }) }) }) }, idYQHGsNvjO2);
  }) }) }) }) })] }) }) }) }) });
});
var css3 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-NYyWQ.framer-1btjzmm, .framer-NYyWQ .framer-1btjzmm { display: block; }", ".framer-NYyWQ.framer-hv8rku { align-content: center; align-items: center; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 70px; height: min-content; justify-content: center; overflow: visible; padding: 0px 36px 0px 36px; position: relative; width: 1200px; }", ".framer-NYyWQ .framer-4olikp { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 90px; height: min-content; justify-content: center; max-width: 1520px; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-NYyWQ .framer-1hxsd0l { display: grid; flex: none; gap: 4px; grid-auto-rows: minmax(0, 1fr); grid-template-columns: repeat(4, minmax(50px, 1fr)); grid-template-rows: repeat(1, minmax(0, 1fr)); height: min-content; justify-content: center; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-NYyWQ .framer-fdxjai { align-self: start; flex: none; height: auto; justify-self: start; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }", ".framer-NYyWQ .framer-7l5kym { align-content: flex-start; align-items: flex-start; align-self: start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 22px; grid-column: span 2; height: min-content; justify-content: center; justify-self: start; overflow: hidden; padding: 0px; position: relative; width: 100%; }", ".framer-NYyWQ .framer-1vc39i, .framer-NYyWQ .framer-edjibz { flex: none; height: auto; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }", ".framer-NYyWQ .framer-bkkbwn { align-content: center; align-items: center; align-self: start; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 4px; height: min-content; justify-content: center; justify-self: start; overflow: hidden; padding: 64px 0px 0px 40px; position: relative; width: 100%; }", ".framer-NYyWQ .framer-1xp3zqa { --framer-text-wrap-override: balance; flex: 1 0 0px; height: auto; position: relative; width: 1px; }", ".framer-NYyWQ .framer-aeexdy { display: grid; flex: none; gap: 4px; grid-auto-rows: minmax(0, 1fr); grid-template-columns: repeat(2, minmax(50px, 1fr)); height: min-content; justify-content: center; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-NYyWQ .framer-g46orm-container { align-self: start; flex: none; height: auto; justify-self: start; position: relative; width: 100%; }", ...css2, ...css];
var FramerntVcqJNYe = withCSS(Component, css3, "framer-NYyWQ");
var stdin_default4 = FramerntVcqJNYe;
FramerntVcqJNYe.displayName = "Projects";
FramerntVcqJNYe.defaultProps = { height: 1709, width: 1200 };
addFonts(FramerntVcqJNYe, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5vvr9Vy74if2I6bQbJvbw7SY1pQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/EOr0mi4hNtlgWNn9if640EZzXCo.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/Y9k9QrlZAqio88Klkmbd8VoMQc.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/OYrD2tBIBPvoJXiIHnLoOXnY9M.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/JeYwfuaPfZHQhEG8U5gtPDZ7WQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/vQyevYAyHtARFwPqUzQGpnDs.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/b6Y37FthZeALduNqHicBT6FutY.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/hyOgCu0Xnghbimh0pE8QTvtt2AU.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/NeGmSOXrPBfEFIy5YZeHq17LEDA.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/oYaAX5himiTPYuN8vLWnqBbfD2s.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/lEJLP4R0yuCaMCjSXYHtJw72M.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/cRJyLNuTJR5jbyKzGi33wU9cqIQ.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/1ZFS7N918ojhhd0nQWdj3jz4w.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/A0Wcc7NgXMjUuFdquHDrIZpzZw0.woff2", weight: "600" }] }, ...ProjectCardFonts, ...getFontsFromSharedStyle(fonts2), ...getFontsFromSharedStyle(fonts)], { supportsExplicitInterCodegen: true });

// virtual:projects
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {};
stdin_default4.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default4,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default4, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default4);
export {
  ComponentWithRoot as default
};
