// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";

// virtual:social-icon-with-link
import { Fragment as Fragment2 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/i5Hm4CBhDTKcuXKITWLU/ztxMjkl0ioCLIqrFvH74/w0aJmgNIP.js
import { jsx as _jsx } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, Image, Link, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var enabledGestures = { wZ7oho0Jw: { hover: true } };
var serializationHash = "framer-ndbWQ";
var variantClassNames = { wZ7oho0Jw: "framer-v-150f1ik" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var numberToPixelString = (value) => {
  if (typeof value !== "number") return value;
  if (!Number.isFinite(value)) return void 0;
  return Math.max(0, value) + "px";
};
var transition1 = { damping: 60, delay: 0, mass: 1, stiffness: 500, type: "spring" };
var toResponsiveImage = (value) => {
  if (typeof value === "object" && value !== null && typeof value.src === "string") {
    return value;
  }
  return typeof value === "string" ? { src: value } : void 0;
};
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var getProps = ({ bGColor, height, icon, iconOpacity, id, link, padding, width, ...props }) => {
  return { ...props, sALz2CcTl: padding ?? props.sALz2CcTl ?? "0px", u7smZm8vG: icon ?? props.u7smZm8vG, ws1WndZLy: link ?? props.ws1WndZLy, xWHLtkdNN: bGColor ?? props.xWHLtkdNN ?? "rgb(255, 255, 255)", z3fb66msB: iconOpacity ?? props.z3fb66msB ?? 0.4 };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className, layoutId, variant, u7smZm8vG, z3fb66msB, ws1WndZLy, xWHLtkdNN, sALz2CcTl, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ defaultVariant: "wZ7oho0Jw", enabledGestures, ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsx(Link, { href: ws1WndZLy, motionChild: true, nodeId: "wZ7oho0Jw", openInNewTab: true, scopeId: "w0aJmgNIP", children: /* @__PURE__ */ _jsx(motion.a, { ...restProps, ...gestureHandlers, "aria-label": "Link to social network", className: `${cx(scopingClassNames, "framer-150f1ik", className, classNames)} framer-1wgfdlh`, "data-framer-name": "x", layoutDependency, layoutId: "wZ7oho0Jw", ref: refBinding, style: { "--17lwxom": numberToPixelString(sALz2CcTl), backgroundColor: xWHLtkdNN, borderBottomLeftRadius: 50, borderBottomRightRadius: 50, borderTopLeftRadius: 50, borderTopRightRadius: 50, ...style }, ...addPropertyOverrides({ "wZ7oho0Jw-hover": { "data-framer-name": void 0 } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx(Image, { background: { alt: "", fit: "fill", pixelHeight: 24, pixelWidth: 24, sizes: `calc(${componentViewport?.width || "100vw"} - ${sALz2CcTl * 2}px)`, ...toResponsiveImage(u7smZm8vG), ...{ positionX: "center", positionY: "center" } }, className: "framer-3y8i2b", layoutDependency, layoutId: "pH_Gfboei", style: { opacity: z3fb66msB }, variants: { "wZ7oho0Jw-hover": { opacity: 1 } } }) }) }) }) }) });
});
var css = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-ndbWQ.framer-1wgfdlh, .framer-ndbWQ .framer-1wgfdlh { display: block; }", ".framer-ndbWQ.framer-150f1ik { align-content: center; align-items: center; cursor: pointer; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: 22px; justify-content: center; overflow: visible; padding: var(--17lwxom); position: relative; text-decoration: none; width: 22px; }", ".framer-ndbWQ .framer-3y8i2b { flex: 1 0 0px; height: 1px; overflow: hidden; position: relative; width: 100%; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-ndbWQ.framer-150f1ik { gap: 0px; } .framer-ndbWQ.framer-150f1ik > * { margin: 0px; margin-bottom: calc(10px / 2); margin-top: calc(10px / 2); } .framer-ndbWQ.framer-150f1ik > :first-child { margin-top: 0px; } .framer-ndbWQ.framer-150f1ik > :last-child { margin-bottom: 0px; } }"];
var Framerw0aJmgNIP = withCSS(Component, css, "framer-ndbWQ");
var stdin_default = Framerw0aJmgNIP;
Framerw0aJmgNIP.displayName = "Social icon with link";
Framerw0aJmgNIP.defaultProps = { height: 22, width: 22 };
addPropertyControls(Framerw0aJmgNIP, { u7smZm8vG: { description: "Upload BLACK square SVG icon", title: "Icon", type: ControlType.ResponsiveImage }, z3fb66msB: { defaultValue: 0.4, max: 1, min: 0, step: 0.01, title: "Icon opacity", type: ControlType.Number }, ws1WndZLy: { title: "Link", type: ControlType.Link }, xWHLtkdNN: { defaultValue: "rgb(255, 255, 255)", title: "BG Color", type: ControlType.Color }, sALz2CcTl: { defaultValue: "0px", title: "Padding", type: ControlType.Padding } });
addFonts(Framerw0aJmgNIP, [{ explicitInter: true, fonts: [] }], { supportsExplicitInterCodegen: true });

// virtual:social-icon-with-link
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {};
stdin_default.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default);
export {
  ComponentWithRoot as default
};
