// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";

// virtual:faq
import { Fragment as Fragment3 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/jS7H9bgOZwv6l2bEyd11/5zqVDvjTY2RPXtEbQSj5/OBpl5Xksq.js
import { jsx as _jsx2 } from "react/jsx-runtime";
import { addFonts, addPropertyControls as addPropertyControls2, ComponentViewportProvider, ControlType as ControlType2, cx, getFonts, SmartComponentScopedContainer, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion as motion2, MotionConfigContext } from "unframer";
import * as React2 from "react";
import { useRef as useRef2 } from "react";

// /:https://framerusercontent.com/modules/BeoOkMASwMSx2anCpheJ/BfQd4QDD6UWpgHx1ZG0v/CustomAccordion.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import * as React from "react";
import { addPropertyControls, ControlType } from "unframer";
import { motion } from "unframer";
import styled from "styled-components";
var useRemoveFramerClasses = (ref) => {
  React.useEffect(() => {
    const timer = setTimeout(() => {
      if (ref.current) {
        const classes = [...ref.current.classList];
        classes.forEach((className) => {
          if (className.startsWith("sc-")) {
            ref.current.classList.remove(className);
          }
        });
      }
    }, 10);
    return () => clearTimeout(timer);
  }, []);
};
var QuestionText = (props) => {
  const spanRef = React.useRef(null);
  useRemoveFramerClasses(spanRef);
  const StyledSpan = styled.span`
    color: ${props.color || props.font?.color || "#333"};
    font-family: ${props.font?.fontFamily || "Inter"};
    font-size: ${props.font?.fontSize || "16px"};
    font-weight: ${props.font?.fontWeight || 400};
    line-height: ${props.font?.lineHeight || "1.5"};
    letter-spacing: ${props.font?.letterSpacing || "normal"};
  `;
  return /* @__PURE__ */ _jsx(StyledSpan, { ref: spanRef, ...props, children: props.children });
};
var AnswerText = (props) => {
  const pRef = React.useRef(null);
  useRemoveFramerClasses(pRef);
  const StyledP = styled.p`
    color: ${props.color || props.font?.color || "#333"};
    font-family: ${props.font?.fontFamily || "Inter"};
    font-size: ${props.font?.fontSize || "16px"};
    font-weight: ${props.font?.fontWeight || 400};
    line-height: ${props.font?.lineHeight || "1.5"};
    letter-spacing: ${props.font?.letterSpacing || "normal"};
    margin: 0;
  `;
  return /* @__PURE__ */ _jsx(StyledP, { ref: pRef, ...props, children: props.children });
};
function CustomAccordion(props) {
  const [isReady, setIsReady] = React.useState(false);
  const componentId = React.useId();
  React.useEffect(() => {
    const timer = setTimeout(() => setIsReady(true), 5);
    return () => clearTimeout(timer);
  }, []);
  const { faqs, gapSize, backgroundColor, openColor, hoverColor, borderRadius, borderWidthTop, borderWidthRight, borderWidthBottom, borderWidthLeft, isMixed, borderWidth, borderColor, borderHoverColor, fontFamily, showQuestionIcon, questionIcon, questionIconGap, questionIconRotation, questionTextColor, questionPaddingTop, questionPaddingRight, questionPaddingBottom, questionPaddingLeft, isQuestionPaddingMixed, questionPadding, answerPaddingTop, answerPaddingRight, answerPaddingBottom, answerPaddingLeft, isAnswerPaddingMixed, answerPadding, arrowSize, iconPosition, showAnswerIcon, answerIcon, answerIconGap, answerIconSize, questionFont, answerFont, answerTextColor, openTextColor, firstCardOpen, transition, applyBackgroundToParent, applyBorderToParent, applyBorderRadiusToParent, answerBackgroundColor } = props;
  const [openIndex, setOpenIndex] = React.useState(firstCardOpen && faqs.length > 0 ? 0 : null);
  const [hoveredIndex, setHoveredIndex] = React.useState(null);
  React.useEffect(() => {
    if (firstCardOpen && faqs.length > 0) {
      setOpenIndex(0);
    } else {
      setOpenIndex(null);
    }
  }, [firstCardOpen, faqs]);
  const toggleFAQ = (index) => {
    setOpenIndex((prevIndex) => prevIndex === index ? null : index);
  };
  const getBorderStyle = (index) => {
    if (isMixed) {
      return { borderTop: `${borderWidthTop}px solid ${borderColor}`, borderRight: `${borderWidthRight}px solid ${borderColor}`, borderBottom: `${borderWidthBottom}px solid ${borderColor}`, borderLeft: `${borderWidthLeft}px solid ${borderColor}` };
    } else {
      return { border: `${borderWidth}px solid ${borderColor}` };
    }
  };
  const getQuestionPaddingStyle = () => {
    if (isQuestionPaddingMixed) {
      return { paddingTop: `${questionPaddingTop}px`, paddingRight: `${questionPaddingRight}px`, paddingBottom: `${questionPaddingBottom}px`, paddingLeft: `${questionPaddingLeft}px` };
    } else {
      return { padding: `${questionPadding}px` };
    }
  };
  const getAnswerPaddingStyle = () => {
    if (isAnswerPaddingMixed) {
      return { paddingTop: `${answerPaddingTop}px`, paddingRight: `${answerPaddingRight}px`, paddingBottom: `${answerPaddingBottom}px`, paddingLeft: `${answerPaddingLeft}px` };
    } else {
      return { padding: `${answerPadding}px` };
    }
  };
  const content = /* @__PURE__ */ _jsx("div", { style: { width: "100%", ...props.style }, children: faqs.map((faq, index) => /* @__PURE__ */ _jsxs(motion.div, { style: { marginBottom: index === faqs.length - 1 ? "0px" : `${gapSize}px`, ...applyBorderToParent ? getBorderStyle(index) : {}, ...applyBorderRadiusToParent ? { borderRadius } : {}, ...applyBackgroundToParent ? { backgroundColor: openIndex === index ? openColor : hoveredIndex === index ? hoverColor : backgroundColor } : {}, overflow: applyBorderRadiusToParent ? "hidden" : "visible" }, animate: applyBorderToParent ? { borderColor: hoveredIndex === index ? borderHoverColor : borderColor } : {}, transition, className: `force-styles-${componentId}-item-${index}`, children: [/* @__PURE__ */ _jsxs(motion.div, { style: { display: "flex", justifyContent: "space-between", alignItems: "center", cursor: "pointer", ...!applyBorderRadiusToParent ? { borderRadius } : {}, ...!applyBorderToParent ? getBorderStyle(index) : {}, ...getQuestionPaddingStyle(), ...!applyBackgroundToParent ? { backgroundColor } : {} }, onClick: () => toggleFAQ(index), onMouseEnter: () => setHoveredIndex(index), onMouseLeave: () => setHoveredIndex(null), animate: !applyBackgroundToParent ? { backgroundColor: openIndex === index ? openColor : hoveredIndex === index ? hoverColor : backgroundColor, borderColor: hoveredIndex === index ? borderHoverColor : borderColor } : {}, transition, children: [/* @__PURE__ */ _jsxs("div", { style: { display: "flex", alignItems: "center" }, children: [showQuestionIcon && iconPosition === "before" && /* @__PURE__ */ _jsx(motion.img, { src: questionIcon, alt: "icon", animate: { rotate: openIndex === index ? questionIconRotation : 0 }, transition, style: { width: arrowSize, height: arrowSize, marginRight: `${questionIconGap}px` } }), /* @__PURE__ */ _jsx(QuestionText, { font: questionFont, color: openIndex === index ? openTextColor : questionTextColor, children: faq.question })] }), showQuestionIcon && iconPosition === "after" && /* @__PURE__ */ _jsx(motion.img, { src: questionIcon, alt: "icon", animate: { rotate: openIndex === index ? questionIconRotation : 0 }, transition, style: { width: arrowSize, height: arrowSize, marginLeft: `${questionIconGap}px` } })] }), /* @__PURE__ */ _jsx(AccordionContent, { isOpen: openIndex === index, transition, children: /* @__PURE__ */ _jsxs("div", { style: { display: "flex", alignItems: "start", ...getAnswerPaddingStyle(), backgroundColor: answerBackgroundColor || "" }, children: [showAnswerIcon && answerIcon && /* @__PURE__ */ _jsx("img", { src: answerIcon, alt: "icon", style: { width: answerIconSize, height: answerIconSize, marginRight: `${answerIconGap}px` } }), /* @__PURE__ */ _jsx(AnswerText, { font: answerFont, color: answerTextColor, children: faq.answer })] }) })] }, index)) });
  const forceStyles = `
    .force-styles-${componentId} {opacity: 1}
    ${faqs.map((_, index) => `.force-styles-${componentId}-item-${index} {transform: translateZ(0)}`).join("\n")}
    `;
  return /* @__PURE__ */ _jsxs(React.Fragment, { children: [/* @__PURE__ */ _jsx("style", { dangerouslySetInnerHTML: { __html: forceStyles } }), /* @__PURE__ */ _jsx("div", { className: `force-styles-${componentId}`, children: content })] });
}
function AccordionContent({ isOpen, children, transition }) {
  const contentRef = React.useRef(null);
  const [height, setHeight] = React.useState(0);
  React.useEffect(() => {
    if (contentRef.current) {
      contentRef.current.style.position = "absolute";
      contentRef.current.style.visibility = "hidden";
      contentRef.current.style.display = "block";
      const scrollHeight = contentRef.current.scrollHeight;
      contentRef.current.style.position = "";
      contentRef.current.style.visibility = "";
      contentRef.current.style.display = "";
      if (isOpen) {
        setHeight(scrollHeight);
      }
    }
  }, [isOpen, children]);
  return /* @__PURE__ */ _jsx(motion.div, { animate: { height: isOpen ? height : 0 }, initial: false, transition: { height: transition }, style: { overflow: "hidden", willChange: "height" }, children: /* @__PURE__ */ _jsx("div", { ref: contentRef, children }) });
}
CustomAccordion.defaultProps = { faqs: [{ question: "Why does Pixco offer free Framer components?", answer: "We love seeing great design come to life, and offering free Framer components is our way of helping the community." }, { question: "Is there a catch for these free components?", answer: "No catch at all! We genuinely enjoy helping designers and creators." }], gapSize: 16, backgroundColor: "#f5f5f5", openColor: "#e8e8e8", hoverColor: "#e0e0e0", borderRadius: 20, borderWidth: 1, borderWidthTop: 1, borderWidthRight: 1, borderWidthBottom: 1, borderWidthLeft: 1, isMixed: false, showQuestionIcon: true, questionIconGap: 20, questionIconRotation: 180, questionPadding: 28, questionPaddingTop: 28, questionPaddingRight: 32, questionPaddingBottom: 28, questionPaddingLeft: 32, isQuestionPaddingMixed: false, showAnswerIcon: true, answerIconGap: 16, answerPadding: 16, answerPaddingTop: 16, answerPaddingRight: 32, answerPaddingBottom: 16, answerPaddingLeft: 16, isAnswerPaddingMixed: false, borderColor: "#000", questionIcon: "https://framerusercontent.com/assets/FGjVAch6noK14FVKTMDGCLfzeNw.svg", arrowSize: 16, iconPosition: "after", answerIcon: "https://framerusercontent.com/assets/BgXDLyd69NDfZzDPv8tXHoN3dt4.svg", answerIconSize: 16, answerTextColor: "#333", firstCardOpen: false, openTextColor: "#333", transition: { type: "spring", stiffness: 800, damping: 60 }, applyBackgroundToParent: false, applyBorderToParent: false, applyBorderRadiusToParent: false, answerBackgroundColor: "" };
addPropertyControls(CustomAccordion, { faqs: { type: ControlType.Array, title: "FAQs", control: { type: ControlType.Object, controls: { question: { type: ControlType.String, title: "Question" }, answer: { type: ControlType.String, title: "Answer", displayTextArea: true } } }, defaultValue: CustomAccordion.defaultProps.faqs }, gapSize: { type: ControlType.Number, title: "Gap Size", defaultValue: 16, min: 0, max: 100, unit: "px", displayStepper: true }, applyBackgroundToParent: { type: ControlType.Boolean, title: "Background", defaultValue: false, enabledTitle: "Parent", disabledTitle: "Card", description: "Apply background to parent or question card" }, applyBorderToParent: { type: ControlType.Boolean, title: "Border", defaultValue: false, enabledTitle: "Parent", disabledTitle: "Card", description: "Apply border to parent or question card" }, applyBorderRadiusToParent: { type: ControlType.Boolean, title: "Border Radius", defaultValue: false, enabledTitle: "Parent", disabledTitle: "Card", description: "Apply border radius to parent or question card" }, backgroundColor: { type: ControlType.Color, title: "Background Color", defaultValue: CustomAccordion.defaultProps.backgroundColor }, answerBackgroundColor: { type: ControlType.Color, title: "Answer Background", defaultValue: "", optional: true }, openColor: { type: ControlType.Color, title: "Open Color", defaultValue: "#e8e8e8" }, openTextColor: { type: ControlType.Color, title: "Open Text Color", defaultValue: "#333" }, hoverColor: { type: ControlType.Color, title: "Hover Color", defaultValue: CustomAccordion.defaultProps.hoverColor }, borderWidth: { type: ControlType.FusedNumber, title: "Border Width", defaultValue: 1, toggleKey: "isMixed", toggleTitles: ["All", "Individual"], valueKeys: ["borderWidthTop", "borderWidthRight", "borderWidthBottom", "borderWidthLeft"], valueLabels: ["T", "R", "B", "L"], min: 0, max: 10, step: 1, unit: "px" }, borderColor: { type: ControlType.Color, title: "Border Color", defaultValue: "#000" }, borderHoverColor: { type: ControlType.Color, title: "Border Hover Color", defaultValue: "#000" }, borderRadius: { type: ControlType.Number, title: "Border Radius", defaultValue: 20, min: 0, max: 50, unit: "px", displayStepper: true }, questionFont: { type: ControlType.Font, title: "Question Font", controls: "extended", displayFontSize: true, displayTextAlignment: false, defaultFontType: "sans-serif", defaultValue: { fontFamily: "Inter", fontSize: 16, fontWeight: 400, lineHeight: "1.5", letterSpacing: "normal" } }, questionTextColor: { type: ControlType.Color, title: "Question Text Color", defaultValue: "#333" }, questionPadding: { type: ControlType.FusedNumber, title: "Question Padding", defaultValue: 28, toggleKey: "isQuestionPaddingMixed", toggleTitles: ["All", "Individual"], valueKeys: ["questionPaddingTop", "questionPaddingRight", "questionPaddingBottom", "questionPaddingLeft"], valueLabels: ["T", "R", "B", "L"], min: 0, max: 100, step: 1, unit: "px" }, showQuestionIcon: { type: ControlType.Boolean, title: "Show Question Icon", defaultValue: true, enabledTitle: "Yes", disabledTitle: "No" }, questionIcon: { type: ControlType.File, title: "Question Icon", allowedFileTypes: ["svg", "png", "jpg", "gif"], hidden: (props) => !props.showQuestionIcon }, questionIconRotation: { type: ControlType.Number, title: "Icon Rotation", defaultValue: 180, min: 0, max: 360, step: 1, unit: "\xB0", displayStepper: true, hidden: (props) => !props.showQuestionIcon, description: "Transitions on open and close states" }, arrowSize: { type: ControlType.Number, title: "Question Icon Size", defaultValue: CustomAccordion.defaultProps.arrowSize, min: 8, max: 32, unit: "px", displayStepper: true, hidden: (props) => !props.showQuestionIcon }, questionIconGap: { type: ControlType.Number, title: "Question Icon Gap", defaultValue: 20, min: 0, max: 50, unit: "px", displayStepper: true, hidden: (props) => !props.showQuestionIcon }, answerFont: { type: ControlType.Font, title: "Answer Font", controls: "extended", displayFontSize: true, displayTextAlignment: false, defaultFontType: "sans-serif", defaultValue: { fontFamily: "Inter", fontSize: 16, fontWeight: 400, lineHeight: "1.5", letterSpacing: "normal" } }, answerTextColor: { type: ControlType.Color, title: "Answer Text Color", defaultValue: "#333" }, answerPadding: { type: ControlType.FusedNumber, title: "Answer Padding", defaultValue: 16, toggleKey: "isAnswerPaddingMixed", toggleTitles: ["All", "Individual"], valueKeys: ["answerPaddingTop", "answerPaddingRight", "answerPaddingBottom", "answerPaddingLeft"], valueLabels: ["T", "R", "B", "L"], min: 0, max: 100, step: 1, unit: "px" }, showAnswerIcon: { type: ControlType.Boolean, title: "Show Answer Icon", defaultValue: true, enabledTitle: "Yes", disabledTitle: "No" }, answerIcon: { type: ControlType.File, title: "Answer Icon", allowedFileTypes: ["svg", "png", "jpg", "gif"], hidden: (props) => !props.showAnswerIcon }, answerIconSize: { type: ControlType.Number, title: "Answer Icon Size", defaultValue: CustomAccordion.defaultProps.answerIconSize, min: 8, max: 32, unit: "px", displayStepper: true, hidden: (props) => !props.showAnswerIcon }, answerIconGap: { type: ControlType.Number, title: "Answer Icon Gap", defaultValue: 16, min: 0, max: 50, unit: "px", displayStepper: true, hidden: (props) => !props.showAnswerIcon }, iconPosition: { type: ControlType.Enum, title: "Icon Position", options: ["before", "after"], optionTitles: ["Before", "After"], defaultValue: "after", hidden: (props) => !props.showQuestionIcon }, firstCardOpen: { type: ControlType.Boolean, title: "First Card Open", defaultValue: false, enabledTitle: "Yes", disabledTitle: "No" }, transition: { type: ControlType.Transition, title: "Transition", defaultValue: { type: "spring", stiffness: 800, damping: 60 }, description: "Discover more Framer resources at [Pixco](https://pixcodrops.com/resources)" } });

// /:https://framerusercontent.com/modules/jS7H9bgOZwv6l2bEyd11/5zqVDvjTY2RPXtEbQSj5/OBpl5Xksq.js
var CustomAccordionFonts = getFonts(CustomAccordion);
var cycleOrder = ["ihNqU1VyI", "vpbc0XzUl"];
var serializationHash = "framer-vw5it";
var variantClassNames = { ihNqU1VyI: "framer-v-10950d3", vpbc0XzUl: "framer-v-17c3jss" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { bounce: 0.2, delay: 0, duration: 0.4, type: "spring" };
var Transition = ({ value, children }) => {
  const config = React2.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React2.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx2(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion2.create(React2.Fragment);
var humanReadableVariantMap = { Desktop: "ihNqU1VyI", Phone: "vpbc0XzUl" };
var getProps = ({ height, id, width, ...props }) => {
  return { ...props, variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "ihNqU1VyI" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React2.forwardRef(function(props, ref) {
  const fallbackRef = useRef2(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React2.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className, layoutId, variant, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "ihNqU1VyI", ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  return /* @__PURE__ */ _jsx2(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx2(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx2(Transition, { value: transition1, children: /* @__PURE__ */ _jsx2(motion2.div, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-10950d3", className, classNames), "data-framer-name": "Desktop", layoutDependency, layoutId: "ihNqU1VyI", ref: refBinding, style: { ...style }, ...addPropertyOverrides({ vpbc0XzUl: { "data-framer-name": "Phone" } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx2(ComponentViewportProvider, { children: /* @__PURE__ */ _jsx2(SmartComponentScopedContainer, { className: "framer-mbtw9f-container", isAuthoredByUser: true, isModuleExternal: true, layoutDependency, layoutId: "ZkiGOZDsX-container", nodeId: "ZkiGOZDsX", rendersWithMotion: true, scopeId: "OBpl5Xksq", children: /* @__PURE__ */ _jsx2(CustomAccordion, { answerBackgroundColor: "", answerFont: { fontFamily: '"Inter", "Inter Placeholder", sans-serif', fontSize: "15px", fontStyle: "normal", fontWeight: 500, letterSpacing: "-0.04em", lineHeight: "1.4em" }, answerIcon: "https://framerusercontent.com/assets/XYbxWO3TFY2WvDPFG8MlDMVSoU.svg", answerIconGap: 16, answerIconSize: 16, answerPadding: 28, answerPaddingBottom: 28, answerPaddingLeft: 28, answerPaddingRight: 28, answerPaddingTop: 0, answerTextColor: "rgba(10, 10, 10, 0.6)", applyBackgroundToParent: true, applyBorderRadiusToParent: true, applyBorderToParent: true, arrowSize: 16, backgroundColor: "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", borderColor: "rgb(0, 0, 0)", borderHoverColor: "rgb(0, 0, 0)", borderRadius: 14, borderWidth: 0, borderWidthBottom: 0, borderWidthLeft: 0, borderWidthRight: 0, borderWidthTop: 0, faqs: [{ answer: "The timeline for building a website depends on its complexity and specific requirements. On average\u044E We\u2019ll provide a detailed timeline during the initial consultation to ensure clear expectations.", question: "How long does it take to build a website?" }, { answer: "We create fully custom websites tailored to your brand. No generic templates\u2014just unique, high-performing designs.", question: "Do you offer custom websites or use templates?" }, { answer: "We optimize your site structure, content, and speed, ensuring better search rankings and visibility.", question: "What\u2019s included in your SEO services?" }, { answer: "You pay a fixed monthly fee, and we handle everything\u2014design, updates, and ongoing support. No large upfront costs, just a seamless experience.", question: "How does the monthly subscription model work?" }, { answer: "Yes! We can refresh your current site while improving its design, functionality, and performance.", question: "Can you redesign my existing website?" }, { answer: "Just reach out! We\u2019ll discuss your needs, create a plan, and get to work on your website.", question: "How do I get started?" }], firstCardOpen: true, gapSize: 4, height: "100%", hoverColor: "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", iconPosition: "after", id: "ZkiGOZDsX", isAnswerPaddingMixed: true, isMixed: false, isQuestionPaddingMixed: false, layoutId: "ZkiGOZDsX", openColor: "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", openTextColor: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", questionFont: { fontFamily: '"Inter", "Inter Placeholder", sans-serif', fontSize: "18px", fontStyle: "normal", fontWeight: 500, letterSpacing: "-0.04em", lineHeight: "1.3em" }, questionIcon: "https://framerusercontent.com/assets/EylEyHpYUgvECHuVqqWePunGOeE.svg", questionIconGap: 16, questionIconRotation: 360, questionPadding: 26, questionPaddingBottom: 26, questionPaddingLeft: 26, questionPaddingRight: 26, questionPaddingTop: 26, questionTextColor: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", showAnswerIcon: false, showQuestionIcon: true, style: { width: "100%" }, transition: { delay: 0, duration: 0.3, ease: [0.68, 0, 0.16, 0.97], type: "tween" }, width: "100%", ...addPropertyOverrides({ vpbc0XzUl: { answerFont: { fontFamily: '"Inter", "Inter Placeholder", sans-serif', fontSize: "13px", fontStyle: "normal", fontWeight: 500, letterSpacing: "-0.04em", lineHeight: "1.4em" }, answerPaddingBottom: 20, answerPaddingLeft: 20, answerPaddingRight: 20, questionFont: { fontFamily: '"Inter", "Inter Placeholder", sans-serif', fontSize: "15px", fontStyle: "normal", fontWeight: 500, letterSpacing: "-0.04em", lineHeight: "1.3em" }, questionPadding: 20, questionPaddingBottom: 20, questionPaddingLeft: 20, questionPaddingRight: 20, questionPaddingTop: 20 } }, baseVariant, gestureVariant) }) }) }) }) }) }) });
});
var css = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-vw5it.framer-m1xcdu, .framer-vw5it .framer-m1xcdu { display: block; }", ".framer-vw5it.framer-10950d3 { align-content: center; align-items: center; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 4px; height: min-content; justify-content: center; overflow: hidden; padding: 0px; position: relative; width: 562px; }", ".framer-vw5it .framer-mbtw9f-container { flex: 1 0 0px; height: auto; position: relative; width: 1px; z-index: 1; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-vw5it.framer-10950d3 { gap: 0px; } .framer-vw5it.framer-10950d3 > * { margin: 0px; margin-left: calc(4px / 2); margin-right: calc(4px / 2); } .framer-vw5it.framer-10950d3 > :first-child { margin-left: 0px; } .framer-vw5it.framer-10950d3 > :last-child { margin-right: 0px; } }"];
var FramerOBpl5Xksq = withCSS(Component, css, "framer-vw5it");
var stdin_default = FramerOBpl5Xksq;
FramerOBpl5Xksq.displayName = "FAQ";
FramerOBpl5Xksq.defaultProps = { height: 563, width: 562 };
addPropertyControls2(FramerOBpl5Xksq, { variant: { options: ["ihNqU1VyI", "vpbc0XzUl"], optionTitles: ["Desktop", "Phone"], title: "Variant", type: ControlType2.Enum } });
addFonts(FramerOBpl5Xksq, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5A3Ce6C9YYmCjpQx9M4inSaKU.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/Qx95Xyt0Ka3SGhinnbXIGpEIyP4.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/6mJuEAguuIuMog10gGvH5d3cl8.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/xYYWaj7wCU5zSQH0eXvSaS19wo.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/otTaNuNpVK4RbdlT7zDDdKvQBA.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/d3tHnaQIAeqiE5hGcRw4mmgWYU.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/DolVirEGb34pEXEp8t8FQBSK4.woff2", weight: "500" }] }, ...CustomAccordionFonts], { supportsExplicitInterCodegen: true });

// virtual:faq
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {
  "base": "vpbc0XzUl",
  "xl": "ihNqU1VyI"
};
stdin_default.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default);
export {
  ComponentWithRoot as default
};
