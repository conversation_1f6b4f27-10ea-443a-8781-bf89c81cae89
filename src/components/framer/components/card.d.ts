/* This file was generated by <PERSON>framer, do not edit manually */
import type * as React from "react"

import type { UnframerBreakpoint } from "unframer"

type Locale = string
export interface Props {
    children?: React.ReactNode
    locale?: Locale
    style?: React.CSSProperties
    className?: string
    id?: string
    width?: any
    height?: any
    layoutId?: string
    "variant"?: 'Desktop' | 'Tablet' | 'Phone'
    "counter"?: number
    "suffix"?: string
    "speedMs"?: number
    "numberSmall"?: string
    "topText"?: string
    "bottomText"?: string
    "showLogos?"?: boolean
    "_01Logo"?: {src: string, srcSet?: string, alt?: string}
    "_02Logo"?: {src: string, srcSet?: string, alt?: string}
    "_03Logo"?: {src: string, srcSet?: string, alt?: string}
    "logoGrayscale"?: number
}

const CardFramerComponent = (props: Props) => any

type VariantsMap = Partial<Record<UnframerBreakpoint, Props['variant']>> & { base: Props['variant'] }

CardFramerComponent.Responsive = (props: Omit<Props, 'variant'> & {variants?: VariantsMap}) => any

export default CardFramerComponent

