// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";
import {
  A,
  F,
  I,
  N,
  O,
  R,
  S,
  a,
  b,
  f,
  h,
  h2,
  l,
  s,
  u,
  v
} from "./chunks/chunk-MKEJQGYO.js";

// virtual:ticker
import { Fragment as Fragment3 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/pRdtH3H3SzHXlVcu6fJq/gtdA974yag36O5Cqwsfp/YYl7PDyYl.js
import { jsx as _jsx3 } from "react/jsx-runtime";
import { addFonts as addFonts2, ComponentViewportProvider, cx as cx2, getFonts, SmartComponentScopedContainer, useComponentViewport as useComponentViewport2, useLocaleInfo as useLocaleInfo2, useVariantState as useVariantState2, withCSS as withCSS2 } from "unframer";
import { LayoutGroup as LayoutGroup3, motion as motion3, MotionConfigContext as MotionConfigContext2 } from "unframer";
import * as React2 from "react";
import { useRef as useRef3 } from "react";

// /:https://framerusercontent.com/modules/B2xAlJLcN0gOnt11mSPw/jyRNgY7vYWXe6t31T0wo/Ticker.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Children, useLayoutEffect, useEffect, useState, useRef, useMemo, useCallback, cloneElement } from "react";
import { addPropertyControls, ControlType, RenderTarget } from "unframer";
import { useReducedMotion, LayoutGroup, useInView, useMotionValue, useTransform, motion, frame } from "unframer";

// /:https://esm.sh/*@motionone/animation@10.18.0/node/animation.mjs
var w = { ease: l(0.25, 0.1, 0.25, 1), "ease-in": l(0.42, 0, 1, 1), "ease-in-out": l(0.42, 0, 0.58, 1), "ease-out": l(0, 0, 0.58, 1) };
var M = /\((.*?)\)/;
function m(s2) {
  if (I(s2)) return s2;
  if (A(s2)) return l(...s2);
  let t2 = w[s2];
  if (t2) return t2;
  if (s2.startsWith("steps")) {
    let a2 = M.exec(s2);
    if (a2) {
      let e2 = a2[1].split(",");
      return h2(parseFloat(e2[0]), e2[1].trim());
    }
  }
  return u;
}
var T = class {
  constructor(t2, a2 = [0, 1], { easing: e2, duration: l2 = b.duration, delay: p = b.delay, endDelay: S4 = b.endDelay, repeat: R3 = b.repeat, offset: A2, direction: c = "normal", autoplay: x2 = true } = {}) {
    if (this.startTime = null, this.rate = 1, this.t = 0, this.cancelTimestamp = null, this.easing = u, this.duration = 0, this.totalDuration = 0, this.repeat = 0, this.playState = "idle", this.finished = new Promise((r, h4) => {
      this.resolve = r, this.reject = h4;
    }), e2 = e2 || b.easing, F(e2)) {
      let r = e2.createAnimation(a2);
      e2 = r.easing, a2 = r.keyframes || a2, l2 = r.duration || l2;
    }
    this.repeat = R3, this.easing = a(e2) ? u : m(e2), this.updateDuration(l2);
    let E2 = S(a2, A2, a(e2) ? e2.map(m) : u);
    this.tick = (r) => {
      var h4;
      p = p;
      let i2 = 0;
      this.pauseTime !== void 0 ? i2 = this.pauseTime : i2 = (r - this.startTime) * this.rate, this.t = i2, i2 /= 1e3, i2 = Math.max(i2 - p, 0), this.playState === "finished" && this.pauseTime === void 0 && (i2 = this.totalDuration);
      let f2 = i2 / this.duration, g2 = Math.floor(f2), n = f2 % 1;
      !n && f2 >= 1 && (n = 1), n === 1 && g2--;
      let y = g2 % 2;
      (c === "reverse" || c === "alternate" && y || c === "alternate-reverse" && !y) && (n = 1 - n);
      let q3 = i2 >= this.totalDuration ? 1 : Math.min(n, 1), v2 = E2(this.easing(q3));
      t2(v2), this.pauseTime === void 0 && (this.playState === "finished" || i2 >= this.totalDuration + S4) ? (this.playState = "finished", (h4 = this.resolve) === null || h4 === void 0 || h4.call(this, v2)) : this.playState !== "idle" && (this.frameRequestId = requestAnimationFrame(this.tick));
    }, x2 && this.play();
  }
  play() {
    let t2 = performance.now();
    this.playState = "running", this.pauseTime !== void 0 ? this.startTime = t2 - this.pauseTime : this.startTime || (this.startTime = t2), this.cancelTimestamp = this.startTime, this.pauseTime = void 0, this.frameRequestId = requestAnimationFrame(this.tick);
  }
  pause() {
    this.playState = "paused", this.pauseTime = this.t;
  }
  finish() {
    this.playState = "finished", this.tick(0);
  }
  stop() {
    var t2;
    this.playState = "idle", this.frameRequestId !== void 0 && cancelAnimationFrame(this.frameRequestId), (t2 = this.reject) === null || t2 === void 0 || t2.call(this, false);
  }
  cancel() {
    this.stop(), this.tick(this.cancelTimestamp);
  }
  reverse() {
    this.rate *= -1;
  }
  commitStyles() {
  }
  updateDuration(t2) {
    this.duration = t2, this.totalDuration = t2 * (this.repeat + 1);
  }
  get currentTime() {
    return this.t;
  }
  set currentTime(t2) {
    this.pauseTime !== void 0 || this.rate === 0 ? this.pauseTime = t2 : this.startTime = performance.now() - t2 / this.rate;
  }
  get playbackRate() {
    return this.rate;
  }
  set playbackRate(t2) {
    this.rate = t2;
  }
};

// /:https://esm.sh/*hey-listen@1.0.8/node/hey-listen.mjs
var e = function() {
};

// /:https://esm.sh/*@motionone/types@10.17.1/node/types.mjs
var t = class {
  setAnimation(i2) {
    this.animation = i2, i2?.finished.then(() => this.clearAnimation()).catch(() => {
    });
  }
  clearAnimation() {
    this.animation = this.generator = void 0;
  }
};

// /:https://esm.sh/*tslib@2.8.1/node/tslib.mjs
function S2(e2, t2) {
  var r = {};
  for (var n in e2) Object.prototype.hasOwnProperty.call(e2, n) && t2.indexOf(n) < 0 && (r[n] = e2[n]);
  if (e2 != null && typeof Object.getOwnPropertySymbols == "function") for (var i2 = 0, n = Object.getOwnPropertySymbols(e2); i2 < n.length; i2++) t2.indexOf(n[i2]) < 0 && Object.prototype.propertyIsEnumerable.call(e2, n[i2]) && (r[n[i2]] = e2[n[i2]]);
  return r;
}

// /:https://esm.sh/*@motionone/generators@10.18.0/node/generators.mjs
var S3 = 5;
function x(t2, o, r) {
  let s2 = Math.max(o - S3, 0);
  return R(r - t2(s2), o - s2);
}
var h3 = { stiffness: 100, damping: 10, mass: 1 };
var w2 = (t2 = h3.stiffness, o = h3.damping, r = h3.mass) => o / (2 * Math.sqrt(t2 * r));
function k(t2, o, r) {
  return t2 < o && r >= o || t2 > o && r <= o;
}
var V = ({ stiffness: t2 = h3.stiffness, damping: o = h3.damping, mass: r = h3.mass, from: s2 = 0, to: n = 1, velocity: u2 = 0, restSpeed: l2, restDistance: d2 } = {}) => {
  u2 = u2 ? N.s(u2) : 0;
  let c = { done: false, hasReachedTarget: false, current: s2, target: n }, p = n - s2, m3 = Math.sqrt(t2 / r) / 1e3, a2 = w2(t2, o, r), T2 = Math.abs(p) < 5;
  l2 || (l2 = T2 ? 0.01 : 2), d2 || (d2 = T2 ? 5e-3 : 0.5);
  let M3;
  if (a2 < 1) {
    let i2 = m3 * Math.sqrt(1 - a2 * a2);
    M3 = (f2) => n - Math.exp(-a2 * m3 * f2) * ((-u2 + a2 * m3 * p) / i2 * Math.sin(i2 * f2) + p * Math.cos(i2 * f2));
  } else M3 = (i2) => n - Math.exp(-m3 * i2) * (p + (-u2 + m3 * p) * i2);
  return (i2) => {
    c.current = M3(i2);
    let f2 = i2 === 0 ? u2 : x(M3, i2, c.current), g2 = Math.abs(f2) <= l2, y = Math.abs(n - c.current) <= d2;
    return c.done = g2 && y, c.hasReachedTarget = k(s2, n, c.current), c;
  };
};
var L = ({ from: t2 = 0, velocity: o = 0, power: r = 0.8, decay: s2 = 0.325, bounceDamping: n, bounceStiffness: u2, changeTarget: l2, min: d2, max: c, restDistance: p = 0.5, restSpeed: m3 }) => {
  s2 = N.ms(s2);
  let a2 = { hasReachedTarget: false, done: false, current: t2, target: t2 }, T2 = (e2) => d2 !== void 0 && e2 < d2 || c !== void 0 && e2 > c, M3 = (e2) => d2 === void 0 ? c : c === void 0 || Math.abs(d2 - e2) < Math.abs(c - e2) ? d2 : c, i2 = r * o, f2 = t2 + i2, g2 = l2 === void 0 ? f2 : l2(f2);
  a2.target = g2, g2 !== f2 && (i2 = g2 - t2);
  let y = (e2) => -i2 * Math.exp(-e2 / s2), D = (e2) => g2 + y(e2), G = (e2) => {
    let b2 = y(e2), K2 = D(e2);
    a2.done = Math.abs(b2) <= p, a2.current = a2.done ? g2 : K2;
  }, R3, B2, F2 = (e2) => {
    T2(a2.current) && (R3 = e2, B2 = V({ from: a2.current, to: M3(a2.current), velocity: x(D, e2, a2.current), damping: n, stiffness: u2, restDistance: p, restSpeed: m3 }));
  };
  return F2(0), (e2) => {
    let b2 = false;
    return !B2 && R3 === void 0 && (b2 = true, G(e2), F2(e2)), R3 !== void 0 && e2 > R3 ? (a2.hasReachedTarget = true, B2(e2 - R3)) : (a2.hasReachedTarget = false, !b2 && G(e2), a2);
  };
};
var q = 10;
var $ = 1e4;
function j(t2, o = u) {
  let r, s2 = q, n = t2(0), u2 = [o(n.current)];
  for (; !n.done && s2 < $; ) n = t2(s2), u2.push(o(n.done ? n.target : n.current)), r === void 0 && n.hasReachedTarget && (r = s2), s2 += q;
  let l2 = s2 - q;
  return u2.length === 1 && u2.push(n.current), { keyframes: u2, duration: l2 / 1e3, overshootDuration: (r ?? l2) / 1e3 };
}

// /:https://esm.sh/*@motionone/dom@10.18.0/node/dom.mjs
var ft = /* @__PURE__ */ new WeakMap();
function j2(t2) {
  return ft.has(t2) || ft.set(t2, { transforms: [], values: /* @__PURE__ */ new Map() }), ft.get(t2);
}
function Vt(t2, e2) {
  return t2.has(e2) || t2.set(e2, new t()), t2.get(e2);
}
var he = ["", "X", "Y", "Z"];
var ve = ["translate", "scale", "rotate", "skew"];
var z = { x: "translateX", y: "translateY", z: "translateZ" };
var Wt = { syntax: "<angle>", initialValue: "0deg", toDefaultUnit: (t2) => t2 + "deg" };
var ye = { translate: { syntax: "<length-percentage>", initialValue: "0px", toDefaultUnit: (t2) => t2 + "px" }, rotate: Wt, scale: { syntax: "<number>", initialValue: 1, toDefaultUnit: u }, skew: Wt };
var L2 = /* @__PURE__ */ new Map();
var H = (t2) => `--motion-${t2}`;
var Y = ["x", "y", "z"];
ve.forEach((t2) => {
  he.forEach((e2) => {
    Y.push(t2 + e2), L2.set(H(t2 + e2), ye[t2]);
  });
});
var xe = (t2, e2) => Y.indexOf(t2) - Y.indexOf(e2);
var we = new Set(Y);
var $2 = (t2) => we.has(t2);
var Mt = (t2, e2) => {
  z[e2] && (e2 = z[e2]);
  let { transforms: n } = j2(t2);
  h(n, e2), t2.style.transform = lt(n);
};
var lt = (t2) => t2.sort(xe).reduce(Ee, "").trim();
var Ee = (t2, e2) => `${t2} ${e2}(var(${H(e2)}))`;
var R2 = (t2) => t2.startsWith("--");
var Pt = /* @__PURE__ */ new Set();
function Nt(t2) {
  if (!Pt.has(t2)) {
    Pt.add(t2);
    try {
      let { syntax: e2, initialValue: n } = L2.has(t2) ? L2.get(t2) : {};
      CSS.registerProperty({ name: t2, inherits: false, syntax: e2, initialValue: n });
    } catch {
    }
  }
}
var mt = (t2, e2) => document.createElement("div").animate(t2, e2);
var It = { cssRegisterProperty: () => typeof CSS < "u" && Object.hasOwnProperty.call(CSS, "registerProperty"), waapi: () => Object.hasOwnProperty.call(Element.prototype, "animate"), partialKeyframes: () => {
  try {
    mt({ opacity: [1] });
  } catch {
    return false;
  }
  return true;
}, finished: () => !!mt({ opacity: [0, 1] }, { duration: 1e-3 }).finished, linearEasing: () => {
  try {
    mt({ opacity: 0 }, { easing: "linear(0, 1)" });
  } catch {
    return false;
  }
  return true;
} };
var ut = {};
var V2 = {};
for (let t2 in It) V2[t2] = () => (ut[t2] === void 0 && (ut[t2] = It[t2]()), ut[t2]);
var Te = 0.015;
var Le = (t2, e2) => {
  let n = "", r = Math.round(e2 / Te);
  for (let o = 0; o < r; o++) n += t2(f(0, r - 1, o)) + ", ";
  return n.substring(0, n.length - 2);
};
var pt = (t2, e2) => I(t2) ? V2.linearEasing() ? `linear(${Le(t2, e2)})` : b.easing : A(t2) ? De(t2) : t2;
var De = ([t2, e2, n, r]) => `cubic-bezier(${t2}, ${e2}, ${n}, ${r})`;
function _t(t2, e2) {
  for (let n = 0; n < t2.length; n++) t2[n] === null && (t2[n] = n ? t2[n - 1] : e2());
  return t2;
}
var J = (t2) => Array.isArray(t2) ? t2 : [t2];
function W(t2) {
  return z[t2] && (t2 = z[t2]), $2(t2) ? H(t2) : t2;
}
var M2 = { get: (t2, e2) => {
  e2 = W(e2);
  let n = R2(e2) ? t2.style.getPropertyValue(e2) : getComputedStyle(t2)[e2];
  if (!n && n !== 0) {
    let r = L2.get(e2);
    r && (n = r.initialValue);
  }
  return n;
}, set: (t2, e2, n) => {
  e2 = W(e2), R2(e2) ? t2.style.setProperty(e2, n) : t2.style[e2] = n;
} };
function Q(t2, e2 = true) {
  if (!(!t2 || t2.playState === "finished")) try {
    t2.stop ? t2.stop() : (e2 && t2.commitStyles(), t2.cancel());
  } catch {
  }
}
function tt(t2, e2) {
  var n;
  let r = e2?.toDefaultUnit || u, o = t2[t2.length - 1];
  if (v(o)) {
    let i2 = ((n = o.match(/(-?[\d.]+)([a-z%]*)/)) === null || n === void 0 ? void 0 : n[2]) || "";
    i2 && (r = (s2) => s2 + i2);
  }
  return r;
}
function We() {
  return window.__MOTION_DEV_TOOLS_RECORD;
}
function B(t2, e2, n, r = {}, o) {
  let i2 = We(), s2 = r.record !== false && i2, l2, { duration: u2 = b.duration, delay: a2 = b.delay, endDelay: c = b.endDelay, repeat: d2 = b.repeat, easing: f2 = b.easing, persist: O2 = false, direction: S4, offset: m3, allowWebkitAcceleration: g2 = false, autoplay: x2 = true } = r, A2 = j2(t2), w3 = $2(e2), T2 = V2.waapi();
  w3 && Mt(t2, e2);
  let E2 = W(e2), b2 = Vt(A2.values, E2), p = L2.get(E2);
  return Q(b2.animation, !(F(f2) && b2.generator) && r.record !== false), () => {
    let v2 = () => {
      var y, C;
      return (C = (y = M2.get(t2, E2)) !== null && y !== void 0 ? y : p?.initialValue) !== null && C !== void 0 ? C : 0;
    }, h4 = _t(J(n), v2), _ = tt(h4, p);
    if (F(f2)) {
      let y = f2.createAnimation(h4, e2 !== "opacity", v2, E2, b2);
      f2 = y.easing, h4 = y.keyframes || h4, u2 = y.duration || u2;
    }
    if (R2(E2) && (V2.cssRegisterProperty() ? Nt(E2) : T2 = false), w3 && !V2.linearEasing() && (I(f2) || a(f2) && f2.some(I)) && (T2 = false), T2) {
      p && (h4 = h4.map((D) => s(D) ? p.toDefaultUnit(D) : D)), h4.length === 1 && (!V2.partialKeyframes() || s2) && h4.unshift(v2());
      let y = { delay: N.ms(a2), duration: N.ms(u2), endDelay: N.ms(c), easing: a(f2) ? void 0 : pt(f2, u2), direction: S4, iterations: d2 + 1, fill: "both" };
      l2 = t2.animate({ [E2]: h4, offset: m3, easing: a(f2) ? f2.map((D) => pt(D, u2)) : void 0 }, y), l2.finished || (l2.finished = new Promise((D, X) => {
        l2.onfinish = D, l2.oncancel = X;
      }));
      let C = h4[h4.length - 1];
      l2.finished.then(() => {
        O2 || (M2.set(t2, E2, C), l2.cancel());
      }).catch(O), g2 || (l2.playbackRate = 1.000001);
    } else if (o && w3) h4 = h4.map((y) => typeof y == "string" ? parseFloat(y) : y), h4.length === 1 && h4.unshift(parseFloat(v2())), l2 = new o((y) => {
      M2.set(t2, E2, _ ? _(y) : y);
    }, h4, Object.assign(Object.assign({}, r), { duration: u2, easing: f2 }));
    else {
      let y = h4[h4.length - 1];
      M2.set(t2, E2, p && s(y) ? p.toDefaultUnit(y) : y);
    }
    return s2 && i2(t2, e2, h4, { duration: u2, delay: a2, easing: f2, repeat: d2, offset: m3 }, "motion-one"), b2.setAnimation(l2), l2 && !x2 && l2.pause(), l2;
  };
}
var U = (t2, e2) => t2[e2] ? Object.assign(Object.assign({}, t2), t2[e2]) : Object.assign({}, t2);
function P(t2, e2) {
  var n;
  return typeof t2 == "string" ? e2 ? ((n = e2[t2]) !== null && n !== void 0 || (e2[t2] = document.querySelectorAll(t2)), t2 = e2[t2]) : t2 = document.querySelectorAll(t2) : t2 instanceof Element && (t2 = [t2]), Array.from(t2 || []);
}
var Ne = (t2) => t2();
var K = (t2, e2, n = b.duration) => new Proxy({ animations: t2.map(Ne).filter(Boolean), duration: n, options: e2 }, _e);
var Ie = (t2) => t2.animations[0];
var _e = { get: (t2, e2) => {
  let n = Ie(t2);
  switch (e2) {
    case "duration":
      return t2.duration;
    case "currentTime":
      return N.s(n?.[e2] || 0);
    case "playbackRate":
    case "playState":
      return n?.[e2];
    case "finished":
      return t2.finished || (t2.finished = Promise.all(t2.animations.map(Be)).catch(O)), t2.finished;
    case "stop":
      return () => {
        t2.animations.forEach((r) => Q(r));
      };
    case "forEachNative":
      return (r) => {
        t2.animations.forEach((o) => r(o, t2));
      };
    default:
      return typeof n?.[e2] > "u" ? void 0 : () => t2.animations.forEach((r) => r[e2]());
  }
}, set: (t2, e2, n) => {
  switch (e2) {
    case "currentTime":
      n = N.ms(n);
    case "playbackRate":
      for (let r = 0; r < t2.animations.length; r++) t2.animations[r][e2] = n;
      return true;
  }
  return false;
} };
var Be = (t2) => t2.finished;
function et(t2, e2, n) {
  return I(t2) ? t2(e2, n) : t2;
}
function ht(t2) {
  return function(n, r, o = {}) {
    n = P(n);
    let i2 = n.length;
    e(!!i2, "No valid element provided."), e(!!r, "No keyframes defined.");
    let s2 = [];
    for (let l2 = 0; l2 < i2; l2++) {
      let u2 = n[l2];
      for (let a2 in r) {
        let c = U(o, a2);
        c.delay = et(c.delay, l2, i2);
        let d2 = B(u2, a2, r[a2], c, t2);
        s2.push(d2);
      }
    }
    return K(s2, o, o.duration);
  };
}
var Ge = ht(T);
function Gt(t2) {
  return s(t2) && !isNaN(t2);
}
function xt(t2) {
  return v(t2) ? parseFloat(t2) : t2;
}
function nt(t2) {
  let e2 = /* @__PURE__ */ new WeakMap();
  return (n = {}) => {
    let r = /* @__PURE__ */ new Map(), o = (s2 = 0, l2 = 100, u2 = 0, a2 = false) => {
      let c = `${s2}-${l2}-${u2}-${a2}`;
      return r.has(c) || r.set(c, t2(Object.assign({ from: s2, to: l2, velocity: u2 }, n))), r.get(c);
    }, i2 = (s2, l2) => (e2.has(s2) || e2.set(s2, j(s2, l2)), e2.get(s2));
    return { createAnimation: (s2, l2 = true, u2, a2, c) => {
      let d2, f2, O2, S4 = 0, m3 = u, g2 = s2.length;
      if (l2) {
        m3 = tt(s2, a2 ? L2.get(W(a2)) : void 0);
        let x2 = s2[g2 - 1];
        if (O2 = xt(x2), g2 > 1 && s2[0] !== null) f2 = xt(s2[0]);
        else {
          let A2 = c?.generator;
          if (A2) {
            let { animation: w3, generatorStartTime: T2 } = c, E2 = w3?.startTime || T2 || 0, b2 = w3?.currentTime || performance.now() - E2, p = A2(b2).current;
            f2 = p, S4 = x((v2) => A2(v2).current, b2, p);
          } else u2 && (f2 = xt(u2()));
        }
      }
      if (Gt(f2) && Gt(O2)) {
        let x2 = o(f2, O2, S4, a2?.includes("scale"));
        d2 = Object.assign(Object.assign({}, i2(x2, m3)), { easing: "linear" }), c && (c.generator = x2, c.generatorStartTime = performance.now());
      }
      return d2 || (d2 = { easing: "ease", duration: i2(o(0, 100)).overshootDuration }), d2;
    } };
  };
}
var hn = nt(V);
var yn = nt(L);
var wn = { any: 0, all: 1 };
function wt(t2, e2, { root: n, margin: r, amount: o = "any" } = {}) {
  if (typeof IntersectionObserver > "u") return () => {
  };
  let i2 = P(t2), s2 = /* @__PURE__ */ new WeakMap(), l2 = (a2) => {
    a2.forEach((c) => {
      let d2 = s2.get(c.target);
      if (c.isIntersecting !== !!d2) if (c.isIntersecting) {
        let f2 = e2(c);
        I(f2) ? s2.set(c.target, f2) : u2.unobserve(c.target);
      } else d2 && (d2(c), s2.delete(c.target));
    });
  }, u2 = new IntersectionObserver(l2, { root: n, rootMargin: r, threshold: typeof o == "number" ? o : wn[o] });
  return i2.forEach((a2) => u2.observe(a2)), () => u2.disconnect();
}
var rt = /* @__PURE__ */ new WeakMap();
var N2;
function En(t2, e2) {
  if (e2) {
    let { inlineSize: n, blockSize: r } = e2[0];
    return { width: n, height: r };
  } else return t2 instanceof SVGElement && "getBBox" in t2 ? t2.getBBox() : { width: t2.offsetWidth, height: t2.offsetHeight };
}
function Sn({ target: t2, contentRect: e2, borderBoxSize: n }) {
  var r;
  (r = rt.get(t2)) === null || r === void 0 || r.forEach((o) => {
    o({ target: t2, contentSize: e2, get size() {
      return En(t2, n);
    } });
  });
}
function bn(t2) {
  t2.forEach(Sn);
}
function On() {
  typeof ResizeObserver > "u" || (N2 = new ResizeObserver(bn));
}
function Kt(t2, e2) {
  N2 || On();
  let n = P(t2);
  return n.forEach((r) => {
    let o = rt.get(r);
    o || (o = /* @__PURE__ */ new Set(), rt.set(r, o)), o.add(e2), N2?.observe(r);
  }), () => {
    n.forEach((r) => {
      let o = rt.get(r);
      o?.delete(e2), o?.size || N2?.unobserve(r);
    });
  };
}
var ot = /* @__PURE__ */ new Set();
var q2;
function An() {
  q2 = () => {
    let t2 = { width: window.innerWidth, height: window.innerHeight }, e2 = { target: window, size: t2, contentSize: t2 };
    ot.forEach((n) => n(e2));
  }, window.addEventListener("resize", q2);
}
function qt(t2) {
  return ot.add(t2), q2 || An(), () => {
    ot.delete(t2), !ot.size && q2 && (q2 = void 0);
  };
}
function Et(t2, e2) {
  return I(t2) ? qt(t2) : Kt(t2, e2);
}
function Z(t2, e2, n) {
  t2.dispatchEvent(new CustomEvent(e2, { detail: { originalEvent: n } }));
}
function Lt(t2, e2, n) {
  t2.dispatchEvent(new CustomEvent(e2, { detail: { originalEntry: n } }));
}
var ce = { isActive: (t2) => !!t2.inView, subscribe: (t2, { enable: e2, disable: n }, { inViewOptions: r = {} }) => {
  let { once: o } = r, i2 = S2(r, ["once"]);
  return wt(t2, (s2) => {
    if (e2(), Lt(t2, "viewenter", s2), !o) return (l2) => {
      n(), Lt(t2, "viewleave", l2);
    };
  }, i2);
} };
var fe = (t2, e2, n) => (r) => {
  r.pointerType && r.pointerType !== "mouse" || (n(), Z(t2, e2, r));
};
var le = { isActive: (t2) => !!t2.hover, subscribe: (t2, { enable: e2, disable: n }) => {
  let r = fe(t2, "hoverstart", e2), o = fe(t2, "hoverend", n);
  return t2.addEventListener("pointerenter", r), t2.addEventListener("pointerleave", o), () => {
    t2.removeEventListener("pointerenter", r), t2.removeEventListener("pointerleave", o);
  };
} };
var me = { isActive: (t2) => !!t2.press, subscribe: (t2, { enable: e2, disable: n }) => {
  let r = (i2) => {
    n(), Z(t2, "pressend", i2), window.removeEventListener("pointerup", r);
  }, o = (i2) => {
    e2(), Z(t2, "pressstart", i2), window.addEventListener("pointerup", r);
  };
  return t2.addEventListener("pointerdown", o), () => {
    t2.removeEventListener("pointerdown", o), window.removeEventListener("pointerup", r);
  };
} };
var at = { inView: ce, hover: le, press: me };
var ue = ["initial", "animate", ...Object.keys(at), "exit"];

// /:https://framerusercontent.com/modules/B2xAlJLcN0gOnt11mSPw/jyRNgY7vYWXe6t31T0wo/Ticker.js
var MAX_DUPLICATED_ITEMS = 100;
var directionTransformers = { left: (offset) => `translateX(-${offset}px)`, right: (offset) => `translateX(${offset}px)`, top: (offset) => `translateY(-${offset}px)`, bottom: (offset) => `translateY(${offset}px)` };
function Ticker(props) {
  let { slots = [], gap, padding, paddingPerSide, paddingTop, paddingRight, paddingBottom, paddingLeft, speed, hoverFactor, direction, alignment, sizingOptions, fadeOptions, style } = props;
  const { fadeContent, overflow, fadeWidth, fadeInset, fadeAlpha } = fadeOptions;
  const { widthType, heightType } = sizingOptions;
  const paddingValue = paddingPerSide ? `${paddingTop}px ${paddingRight}px ${paddingBottom}px ${paddingLeft}px` : `${padding}px`;
  const currentTarget = RenderTarget.current();
  const isCanvas = currentTarget === RenderTarget.canvas || currentTarget === RenderTarget.export;
  const filteredSlots = slots.filter(Boolean);
  const numChildren = Children.count(filteredSlots);
  const hasChildren = numChildren > 0;
  if (direction === true) {
    direction = "left";
  }
  const isHorizontal = direction === "left" || direction === "right";
  const offset = useMotionValue(0);
  const transformer = directionTransformers[direction];
  const transform = useTransform(offset, transformer);
  const parentRef = useRef(null);
  const childrenRef = useMemo(() => {
    return [{ current: null }, { current: null }];
  }, []);
  const [size, setSize] = useState({ parent: null, children: null });
  let clonedChildren = null;
  let dupedChildren = [];
  let duplicateBy = 0;
  let opacity = 0;
  if (isCanvas) {
    duplicateBy = numChildren ? Math.floor(10 / numChildren) : 0;
    opacity = 1;
  }
  if (!isCanvas && hasChildren && size.parent) {
    duplicateBy = Math.round(size.parent / size.children * 2) + 1;
    duplicateBy = Math.min(duplicateBy, MAX_DUPLICATED_ITEMS);
    opacity = 1;
  }
  const measure = useCallback(() => {
    if (hasChildren && parentRef.current) {
      const parentLength = isHorizontal ? parentRef.current.offsetWidth : parentRef.current.offsetHeight;
      const start = childrenRef[0].current ? isHorizontal ? childrenRef[0].current.offsetLeft : childrenRef[0].current.offsetTop : 0;
      const end = childrenRef[1].current ? isHorizontal ? childrenRef[1].current.offsetLeft + childrenRef[1].current.offsetWidth : childrenRef[1].current.offsetTop + childrenRef[1].current.offsetHeight : 0;
      const childrenLength = end - start + gap;
      setSize({ parent: parentLength, children: childrenLength });
    }
  }, []);
  const childrenStyles = isCanvas ? { contentVisibility: "auto" } : {};
  if (hasChildren) {
    if (!isCanvas) {
      let initialResize = useRef(true);
      useLayoutEffect(() => {
        frame.read(measure, false, true);
        return Et(parentRef.current, ({ contentSize }) => {
          if (!initialResize.current && (contentSize.width || contentSize.height)) {
            frame.read(measure, false, true);
          }
          initialResize.current = false;
        });
      }, []);
    }
    clonedChildren = Children.map(filteredSlots, (child, index) => {
      let ref;
      if (index === 0) {
        ref = childrenRef[0];
      }
      if (index === filteredSlots.length - 1) {
        ref = childrenRef[1];
      }
      const size2 = { width: widthType ? child.props?.width : "100%", height: heightType ? child.props?.height : "100%" };
      return /* @__PURE__ */ _jsx(LayoutGroup, { inherit: "id", children: /* @__PURE__ */ _jsx("li", { ref, style: size2, children: /* @__PURE__ */ cloneElement(child, { style: { ...child.props?.style, ...size2, flexShrink: 0, ...childrenStyles }, layoutId: child.props.layoutId ? child.props.layoutId + "-original-" + index : void 0 }, child.props?.children) }) });
    });
  }
  const isInView = isCanvas ? true : useInView(parentRef);
  if (!isCanvas) {
    for (let i2 = 0; i2 < duplicateBy; i2++) {
      dupedChildren = dupedChildren.concat(Children.map(filteredSlots, (child, childIndex) => {
        const size2 = { width: widthType ? child.props?.width : "100%", height: heightType ? child.props?.height : "100%", willChange: !isInView ? void 0 : "transform" };
        return /* @__PURE__ */ _jsx(LayoutGroup, { inherit: "id", children: /* @__PURE__ */ _jsx("li", { style: size2, "aria-hidden": true, children: /* @__PURE__ */ cloneElement(child, { key: i2 + " " + childIndex, style: { ...child.props?.style, width: widthType ? child.props?.width : "100%", height: heightType ? child.props?.height : "100%", flexShrink: 0, ...childrenStyles }, layoutId: child.props.layoutId ? child.props.layoutId + "-dupe-" + i2 : void 0 }, child.props?.children) }, i2 + "li" + childIndex) }, i2 + "lg" + childIndex);
      }));
    }
  }
  const animateToValue = size.children + size.children * Math.round(size.parent / size.children);
  const initialTime = useRef(null);
  const prevTime = useRef(null);
  const xOrY = useRef(0);
  const isHover = useRef(false);
  const isReducedMotion = useReducedMotion();
  const listRef = useRef(null);
  const animationRef = useRef(null);
  if (!isCanvas) {
    useEffect(() => {
      if (isReducedMotion || !animateToValue || !speed) {
        return;
      }
      animationRef.current = listRef.current.animate({ transform: [transformer(0), transformer(animateToValue)] }, { duration: Math.abs(animateToValue) / speed * 1e3, iterations: Infinity, easing: "linear" });
      return () => animationRef.current.cancel();
    }, [hoverFactor, animateToValue, speed]);
    const playOrPause = useCallback(() => {
      if (!animationRef.current) return;
      const hidden = document.hidden;
      if (isInView && !hidden && animationRef.current.playState === "paused") {
        animationRef.current.play();
      } else if ((!isInView || hidden) && animationRef.current.playState === "running") {
        animationRef.current.pause();
      }
    }, [isInView]);
    useEffect(() => {
      playOrPause();
    }, [isInView, hoverFactor, animateToValue, speed]);
    useEffect(() => {
      document.addEventListener("visibilitychange", playOrPause);
      return () => {
        document.removeEventListener("visibilitychange", playOrPause);
      };
    }, [playOrPause]);
  }
  const fadeDirection = isHorizontal ? "to right" : "to bottom";
  const fadeWidthStart = fadeWidth / 2;
  const fadeWidthEnd = 100 - fadeWidth / 2;
  const fadeInsetStart = clamp(fadeInset, 0, fadeWidthStart);
  const fadeInsetEnd = 100 - fadeInset;
  const fadeMask = `linear-gradient(${fadeDirection}, rgba(0, 0, 0, ${fadeAlpha}) ${fadeInsetStart}%, rgba(0, 0, 0, 1) ${fadeWidthStart}%, rgba(0, 0, 0, 1) ${fadeWidthEnd}%, rgba(0, 0, 0, ${fadeAlpha}) ${fadeInsetEnd}%)`;
  if (!hasChildren) {
    return /* @__PURE__ */ _jsxs("section", { style: placeholderStyles, children: [/* @__PURE__ */ _jsx("div", { style: emojiStyles, children: "\u2728" }), /* @__PURE__ */ _jsx("p", { style: titleStyles, children: "Connect to Content" }), /* @__PURE__ */ _jsx("p", { style: subtitleStyles, children: "Add layers or components to infinitely loop on your page." })] });
  }
  return /* @__PURE__ */ _jsx("section", { style: { ...containerStyle, opacity, WebkitMaskImage: fadeContent ? fadeMask : void 0, maskImage: fadeContent ? fadeMask : void 0, overflow: overflow ? "visible" : "hidden", padding: paddingValue }, ref: parentRef, children: /* @__PURE__ */ _jsxs(motion.ul, { ref: listRef, style: { ...containerStyle, gap, top: direction === "bottom" && isValidNumber(animateToValue) ? -animateToValue : void 0, left: direction === "right" && isValidNumber(animateToValue) ? -animateToValue : void 0, placeItems: alignment, position: "relative", flexDirection: isHorizontal ? "row" : "column", ...style, willChange: isCanvas || !isInView ? "auto" : "transform", transform: transformer(0) }, onMouseEnter: () => {
    isHover.current = true;
    if (animationRef.current) {
      animationRef.current.playbackRate = hoverFactor;
    }
  }, onMouseLeave: () => {
    isHover.current = false;
    if (animationRef.current) {
      animationRef.current.playbackRate = 1;
    }
  }, children: [clonedChildren, dupedChildren] }) });
}
Ticker.defaultProps = { gap: 10, padding: 10, sizingOptions: { widthType: true, heightType: true }, fadeOptions: { fadeContent: true, overflow: false, fadeWidth: 25, fadeAlpha: 0, fadeInset: 0 }, direction: true };
addPropertyControls(Ticker, { slots: { type: ControlType.Array, title: "Children", control: { type: ControlType.ComponentInstance } }, speed: { type: ControlType.Number, title: "Speed", min: 0, max: 1e3, defaultValue: 100, unit: "%", displayStepper: true, step: 5 }, direction: { type: ControlType.Enum, title: "Direction", options: ["left", "right", "top", "bottom"], optionIcons: ["direction-left", "direction-right", "direction-up", "direction-down"], optionTitles: ["Left", "Right", "Top", "Bottom"], defaultValue: "left", displaySegmentedControl: true }, alignment: { type: ControlType.Enum, title: "Align", options: ["flex-start", "center", "flex-end"], optionIcons: { direction: { right: ["align-top", "align-middle", "align-bottom"], left: ["align-top", "align-middle", "align-bottom"], top: ["align-left", "align-center", "align-right"], bottom: ["align-left", "align-center", "align-right"] } }, defaultValue: "center", displaySegmentedControl: true }, gap: { type: ControlType.Number, title: "Gap" }, padding: { title: "Padding", type: ControlType.FusedNumber, toggleKey: "paddingPerSide", toggleTitles: ["Padding", "Padding per side"], valueKeys: ["paddingTop", "paddingRight", "paddingBottom", "paddingLeft"], valueLabels: ["T", "R", "B", "L"], min: 0 }, sizingOptions: { type: ControlType.Object, title: "Sizing", controls: { widthType: { type: ControlType.Boolean, title: "Width", enabledTitle: "Auto", disabledTitle: "Stretch", defaultValue: true }, heightType: { type: ControlType.Boolean, title: "Height", enabledTitle: "Auto", disabledTitle: "Stretch", defaultValue: true } } }, fadeOptions: { type: ControlType.Object, title: "Clipping", controls: { fadeContent: { type: ControlType.Boolean, title: "Fade", defaultValue: true }, overflow: { type: ControlType.Boolean, title: "Overflow", enabledTitle: "Show", disabledTitle: "Hide", defaultValue: false, hidden(props) {
  return props.fadeContent === true;
} }, fadeWidth: { type: ControlType.Number, title: "Width", defaultValue: 25, min: 0, max: 100, unit: "%", hidden(props) {
  return props.fadeContent === false;
} }, fadeInset: { type: ControlType.Number, title: "Inset", defaultValue: 0, min: 0, max: 100, unit: "%", hidden(props) {
  return props.fadeContent === false;
} }, fadeAlpha: { type: ControlType.Number, title: "Opacity", defaultValue: 0, min: 0, max: 1, step: 0.05, hidden(props) {
  return props.fadeContent === false;
} } } }, hoverFactor: { type: ControlType.Number, title: "Hover", min: 0, max: 1, unit: "x", defaultValue: 1, step: 0.1, displayStepper: true, description: "Slows down the speed while you are hovering." } });
var containerStyle = { display: "flex", width: "100%", height: "100%", maxWidth: "100%", maxHeight: "100%", placeItems: "center", margin: 0, padding: 0, listStyleType: "none", textIndent: "none" };
var placeholderStyles = { display: "flex", width: "100%", height: "100%", placeContent: "center", placeItems: "center", flexDirection: "column", color: "#96F", background: "rgba(136, 85, 255, 0.1)", fontSize: 11, overflow: "hidden", padding: "20px 20px 30px 20px" };
var emojiStyles = { fontSize: 32, marginBottom: 10 };
var titleStyles = { margin: 0, marginBottom: 10, fontWeight: 600, textAlign: "center" };
var subtitleStyles = { margin: 0, opacity: 0.7, maxWidth: 150, lineHeight: 1.5, textAlign: "center" };
var clamp = (num, min, max) => Math.min(Math.max(num, min), max);
var isValidNumber = (value) => typeof value === "number" && !isNaN(value);

// /:https://framerusercontent.com/modules/JXipYeVE7fx0Gx9lfC1o/eEWwYAGd9qCIC36YeNjR/a_JFqsPzN.js
import { jsx as _jsx2 } from "react/jsx-runtime";
import { addFonts, addPropertyControls as addPropertyControls2, ControlType as ControlType2, cx, getLoadingLazyAtYPosition, Image, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup as LayoutGroup2, motion as motion2, MotionConfigContext } from "unframer";
import * as React from "react";
var cycleOrder = ["r3GmlGDnb", "PNXJSb_ER"];
var serializationHash = "framer-Nt3nr";
var variantClassNames = { PNXJSb_ER: "framer-v-q3exq2", r3GmlGDnb: "framer-v-sjdfbs" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { bounce: 0.2, delay: 0, duration: 0.4, type: "spring" };
var toResponsiveImage = (value) => {
  if (typeof value === "object" && value !== null && typeof value.src === "string") {
    return value;
  }
  return typeof value === "string" ? { src: value } : void 0;
};
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx2(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion2.create(React.Fragment);
var humanReadableVariantMap = { Desktop: "r3GmlGDnb", Phone: "PNXJSb_ER" };
var getProps = ({ height, id, image, width, ...props }) => {
  return { ...props, PiOn2dwG9: image ?? props.PiOn2dwG9 ?? { alt: "", src: "https://framerusercontent.com/images/2e9rGrOkACVfd78cX0SzqLLw.svg" }, variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "r3GmlGDnb" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const { activeLocale, setLocale } = useLocaleInfo();
  const { style, className, layoutId, variant, PiOn2dwG9, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "r3GmlGDnb", variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  const ref1 = React.useRef(null);
  const defaultLayoutId = React.useId();
  const componentViewport = useComponentViewport();
  return /* @__PURE__ */ _jsx2(LayoutGroup2, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx2(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx2(Transition, { value: transition1, children: /* @__PURE__ */ _jsx2(motion2.div, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-sjdfbs", className, classNames), "data-framer-name": "Desktop", layoutDependency, layoutId: "r3GmlGDnb", ref: ref ?? ref1, style: { backgroundColor: "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", borderBottomLeftRadius: 14, borderBottomRightRadius: 14, borderTopLeftRadius: 14, borderTopRightRadius: 14, ...style }, ...addPropertyOverrides({ PNXJSb_ER: { "data-framer-name": "Phone" } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx2(Image, { background: { alt: "", fit: "fit", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + (0 + ((componentViewport?.height || 180) - 0 - 42) / 2)), pixelHeight: 42, pixelWidth: 104, sizes: "104px", ...toResponsiveImage(PiOn2dwG9), ...{ positionX: "center", positionY: "center" } }, className: "framer-1mce2ck", layoutDependency, layoutId: "DT3u3elqt", ...addPropertyOverrides({ PNXJSb_ER: { background: { alt: "", fit: "fit", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + (0 + ((componentViewport?.height || 180) - 0 - 30) / 2)), pixelHeight: 42, pixelWidth: 104, sizes: "74px", ...toResponsiveImage(PiOn2dwG9), ...{ positionX: "center", positionY: "center" } } } }, baseVariant, gestureVariant) }) }) }) }) });
});
var css = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-Nt3nr.framer-pdfhgk, .framer-Nt3nr .framer-pdfhgk { display: block; }", ".framer-Nt3nr.framer-sjdfbs { align-content: center; align-items: center; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: 180px; justify-content: center; overflow: hidden; padding: 0px; position: relative; width: 250px; will-change: var(--framer-will-change-override, transform); }", ".framer-Nt3nr .framer-1mce2ck { flex: none; height: 42px; position: relative; width: 104px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-Nt3nr.framer-sjdfbs { gap: 0px; } .framer-Nt3nr.framer-sjdfbs > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } .framer-Nt3nr.framer-sjdfbs > :first-child { margin-left: 0px; } .framer-Nt3nr.framer-sjdfbs > :last-child { margin-right: 0px; } }", ".framer-Nt3nr.framer-v-q3exq2 .framer-1mce2ck { height: 30px; width: 74px; }"];
var Framera_JFqsPzN = withCSS(Component, css, "framer-Nt3nr");
var stdin_default = Framera_JFqsPzN;
Framera_JFqsPzN.displayName = "Logo card small";
Framera_JFqsPzN.defaultProps = { height: 180, width: 250 };
addPropertyControls2(Framera_JFqsPzN, { variant: { options: ["r3GmlGDnb", "PNXJSb_ER"], optionTitles: ["Desktop", "Phone"], title: "Variant", type: ControlType2.Enum }, PiOn2dwG9: { __defaultAssetReference: "data:framer/asset-reference,2e9rGrOkACVfd78cX0SzqLLw.svg?originalFilename=Logo-2.svg&preferredSize=auto", __vekterDefault: { alt: "", assetReference: "data:framer/asset-reference,2e9rGrOkACVfd78cX0SzqLLw.svg?originalFilename=Logo-2.svg&preferredSize=auto" }, title: "Image", type: ControlType2.ResponsiveImage } });
addFonts(Framera_JFqsPzN, [{ explicitInter: true, fonts: [] }], { supportsExplicitInterCodegen: true });

// /:https://framerusercontent.com/modules/pRdtH3H3SzHXlVcu6fJq/gtdA974yag36O5Cqwsfp/YYl7PDyYl.js
var LogoCardSmallFonts = getFonts(stdin_default);
var TickerFonts = getFonts(Ticker);
var serializationHash2 = "framer-SkCuN";
var variantClassNames2 = { NvqrYAv8i: "framer-v-9t411g" };
var transition12 = { bounce: 0.2, delay: 0, duration: 0.4, type: "spring" };
var addImageAlt = (image, alt) => {
  if (!image || typeof image !== "object") {
    return;
  }
  return { ...image, alt };
};
var Transition2 = ({ value, children }) => {
  const config = React2.useContext(MotionConfigContext2);
  const transition = value ?? config.transition;
  const contextValue = React2.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx3(MotionConfigContext2.Provider, { value: contextValue, children });
};
var Variants2 = motion3.create(React2.Fragment);
var getProps2 = ({ height, id, width, ...props }) => {
  return { ...props };
};
var createLayoutDependency2 = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component2 = /* @__PURE__ */ React2.forwardRef(function(props, ref) {
  const fallbackRef = useRef3(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React2.useId();
  const { activeLocale, setLocale } = useLocaleInfo2();
  const componentViewport = useComponentViewport2();
  const { style, className, layoutId, variant, ...restProps } = getProps2(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState2({ defaultVariant: "NvqrYAv8i", ref: refBinding, variant, variantClassNames: variantClassNames2 });
  const layoutDependency = createLayoutDependency2(props, variants);
  const sharedStyleClassNames = [];
  const scopingClassNames = cx2(serializationHash2, ...sharedStyleClassNames);
  return /* @__PURE__ */ _jsx3(LayoutGroup3, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx3(Variants2, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx3(Transition2, { value: transition12, children: /* @__PURE__ */ _jsx3(motion3.div, { ...restProps, ...gestureHandlers, className: cx2(scopingClassNames, "framer-9t411g", className, classNames), "data-framer-name": "Desktop", layoutDependency, layoutId: "NvqrYAv8i", ref: refBinding, style: { ...style }, children: /* @__PURE__ */ _jsx3(ComponentViewportProvider, { children: /* @__PURE__ */ _jsx3(SmartComponentScopedContainer, { className: "framer-h2jh38-container", isAuthoredByUser: true, isModuleExternal: true, layoutDependency, layoutId: "ouCIzK1Bs-container", nodeId: "ouCIzK1Bs", rendersWithMotion: true, scopeId: "YYl7PDyYl", children: /* @__PURE__ */ _jsx3(Ticker, { alignment: "center", direction: "left", fadeOptions: { fadeAlpha: 0, fadeContent: false, fadeInset: 0, fadeWidth: 0, overflow: false }, gap: 4, height: "100%", hoverFactor: 1, id: "ouCIzK1Bs", layoutId: "ouCIzK1Bs", padding: 0, paddingBottom: 0, paddingLeft: 0, paddingPerSide: false, paddingRight: 0, paddingTop: 0, sizingOptions: { heightType: true, widthType: true }, slots: [/* @__PURE__ */ _jsx3(ComponentViewportProvider, { height: 180, width: "250px", children: /* @__PURE__ */ _jsx3(SmartComponentScopedContainer, { className: "framer-1ln7f7p-container", inComponentSlot: true, layoutDependency, layoutId: "iAOf3unKI-container", nodeId: "iAOf3unKI", rendersWithMotion: true, scopeId: "YYl7PDyYl", children: /* @__PURE__ */ _jsx3(stdin_default, { height: "100%", id: "iAOf3unKI", layoutId: "iAOf3unKI", style: { height: "100%", width: "100%" }, variant: "r3GmlGDnb", width: "100%" }) }) }), /* @__PURE__ */ _jsx3(ComponentViewportProvider, { height: 180, width: "250px", children: /* @__PURE__ */ _jsx3(SmartComponentScopedContainer, { className: "framer-womxc8-container", inComponentSlot: true, layoutDependency, layoutId: "qCZnCosXV-container", nodeId: "qCZnCosXV", rendersWithMotion: true, scopeId: "YYl7PDyYl", children: /* @__PURE__ */ _jsx3(stdin_default, { height: "100%", id: "qCZnCosXV", layoutId: "qCZnCosXV", PiOn2dwG9: addImageAlt({ src: "https://framerusercontent.com/images/uwiCTWkuPCOpiACYPmBnkQDV8KA.svg" }, ""), style: { height: "100%", width: "100%" }, variant: "r3GmlGDnb", width: "100%" }) }) }), /* @__PURE__ */ _jsx3(ComponentViewportProvider, { height: 180, width: "250px", children: /* @__PURE__ */ _jsx3(SmartComponentScopedContainer, { className: "framer-10vqiar-container", inComponentSlot: true, layoutDependency, layoutId: "VRGQnniPG-container", nodeId: "VRGQnniPG", rendersWithMotion: true, scopeId: "YYl7PDyYl", children: /* @__PURE__ */ _jsx3(stdin_default, { height: "100%", id: "VRGQnniPG", layoutId: "VRGQnniPG", PiOn2dwG9: addImageAlt({ src: "https://framerusercontent.com/images/IjvOxnf94qc0W01TH1Jt44VZRr4.svg" }, ""), style: { height: "100%", width: "100%" }, variant: "r3GmlGDnb", width: "100%" }) }) }), /* @__PURE__ */ _jsx3(ComponentViewportProvider, { height: 180, width: "250px", children: /* @__PURE__ */ _jsx3(SmartComponentScopedContainer, { className: "framer-usfhp5-container", inComponentSlot: true, layoutDependency, layoutId: "XqyIS8f75-container", nodeId: "XqyIS8f75", rendersWithMotion: true, scopeId: "YYl7PDyYl", children: /* @__PURE__ */ _jsx3(stdin_default, { height: "100%", id: "XqyIS8f75", layoutId: "XqyIS8f75", PiOn2dwG9: addImageAlt({ src: "https://framerusercontent.com/images/qMtwqqlLyy1I0xtlJx1nQvCqsE.svg" }, ""), style: { height: "100%", width: "100%" }, variant: "r3GmlGDnb", width: "100%" }) }) }), /* @__PURE__ */ _jsx3(ComponentViewportProvider, { height: 180, width: "250px", children: /* @__PURE__ */ _jsx3(SmartComponentScopedContainer, { className: "framer-jabeci-container", inComponentSlot: true, layoutDependency, layoutId: "hRgw6tDiu-container", nodeId: "hRgw6tDiu", rendersWithMotion: true, scopeId: "YYl7PDyYl", children: /* @__PURE__ */ _jsx3(stdin_default, { height: "100%", id: "hRgw6tDiu", layoutId: "hRgw6tDiu", PiOn2dwG9: addImageAlt({ src: "https://framerusercontent.com/images/4HSt1fdOhF6F3PFBgxeUkOsTJiw.svg" }, ""), style: { height: "100%", width: "100%" }, variant: "r3GmlGDnb", width: "100%" }) }) }), /* @__PURE__ */ _jsx3(ComponentViewportProvider, { height: 180, width: "250px", children: /* @__PURE__ */ _jsx3(SmartComponentScopedContainer, { className: "framer-1cbsayb-container", inComponentSlot: true, layoutDependency, layoutId: "NXb1EFeAQ-container", nodeId: "NXb1EFeAQ", rendersWithMotion: true, scopeId: "YYl7PDyYl", children: /* @__PURE__ */ _jsx3(stdin_default, { height: "100%", id: "NXb1EFeAQ", layoutId: "NXb1EFeAQ", PiOn2dwG9: addImageAlt({ src: "https://framerusercontent.com/images/AUrg765bxdJvG09Nkwtoo0n8A.svg" }, ""), style: { height: "100%", width: "100%" }, variant: "r3GmlGDnb", width: "100%" }) }) })], speed: 20, style: { height: "100%", width: "100%" }, width: "100%" }) }) }) }) }) }) });
});
var css2 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-SkCuN.framer-1vrpdtr, .framer-SkCuN .framer-1vrpdtr { display: block; }", ".framer-SkCuN.framer-9t411g { align-content: center; align-items: center; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 4px; height: min-content; justify-content: center; overflow: hidden; padding: 0px; position: relative; width: 1128px; }", ".framer-SkCuN .framer-h2jh38-container { flex: 1 0 0px; height: 180px; position: relative; width: 1px; }", ".framer-SkCuN .framer-1ln7f7p-container, .framer-SkCuN .framer-womxc8-container, .framer-SkCuN .framer-10vqiar-container, .framer-SkCuN .framer-usfhp5-container, .framer-SkCuN .framer-jabeci-container, .framer-SkCuN .framer-1cbsayb-container { height: 180px; position: relative; width: 250px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-SkCuN.framer-9t411g { gap: 0px; } .framer-SkCuN.framer-9t411g > * { margin: 0px; margin-left: calc(4px / 2); margin-right: calc(4px / 2); } .framer-SkCuN.framer-9t411g > :first-child { margin-left: 0px; } .framer-SkCuN.framer-9t411g > :last-child { margin-right: 0px; } }"];
var FramerYYl7PDyYl = withCSS2(Component2, css2, "framer-SkCuN");
var stdin_default2 = FramerYYl7PDyYl;
FramerYYl7PDyYl.displayName = "Ticker";
FramerYYl7PDyYl.defaultProps = { height: 180, width: 1128 };
addFonts2(FramerYYl7PDyYl, [{ explicitInter: true, fonts: [] }, ...LogoCardSmallFonts, ...TickerFonts], { supportsExplicitInterCodegen: true });

// virtual:ticker
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {};
stdin_default2.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default2,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default2, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default2);
export {
  ComponentWithRoot as default
};
