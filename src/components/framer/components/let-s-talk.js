// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";
import {
  Icon
} from "./chunks/chunk-BTSBQWPZ.js";
import {
  className as className6,
  css as css6,
  fonts as fonts6
} from "./chunks/chunk-6MRKUUEA.js";
import {
  className as className5,
  css as css5,
  fonts as fonts5
} from "./chunks/chunk-5L2L6TVR.js";
import {
  stdin_default
} from "./chunks/chunk-BPUMIZRX.js";
import {
  className as className3,
  css as css3,
  fonts as fonts3
} from "./chunks/chunk-FGPXEFVT.js";
import {
  stdin_default as stdin_default2
} from "./chunks/chunk-CVJIPDTS.js";
import {
  className as className4,
  css as css4,
  fonts as fonts4
} from "./chunks/chunk-SATCLWQE.js";
import {
  Video
} from "./chunks/chunk-G73PZF75.js";
import {
  Grain
} from "./chunks/chunk-MO4ELLBD.js";
import "./chunks/chunk-MKEJQGYO.js";
import {
  className,
  css,
  fonts
} from "./chunks/chunk-45SLURIC.js";
import {
  className as className2,
  css as css2,
  fonts as fonts2
} from "./chunks/chunk-VY5WWL2S.js";

// virtual:let-s-talk
import { Fragment as Fragment7 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/44urrVjRtyCNsT713Qdf/lVfX2LlSRSXVPq0OXNzK/q5ePSxRPK.js
import { jsx as _jsx6, jsxs as _jsxs5, Fragment as _Fragment } from "react/jsx-runtime";
import { addFonts as addFonts6, addPropertyControls as addPropertyControls6, ComponentViewportProvider as ComponentViewportProvider4, ControlType as ControlType6, cx as cx6, FormContainer, FormPlainTextInput, getFonts as getFonts4, getFontsFromSharedStyle as getFontsFromSharedStyle2, Link, RichText as RichText5, SmartComponentScopedContainer as SmartComponentScopedContainer4, useComponentViewport as useComponentViewport6, useLocaleInfo as useLocaleInfo6, useVariantState as useVariantState6, withCSS as withCSS6, withFX as withFX2, withVariantAppearEffect } from "unframer";
import { LayoutGroup as LayoutGroup6, motion as motion6, MotionConfigContext as MotionConfigContext6 } from "unframer";
import * as React6 from "react";
import { useRef as useRef6 } from "react";

// /:https://framerusercontent.com/modules/KSWOGapdzc7mxJ6AkjqX/thTJaJ25WfWPZmEPEJeT/GKtOymhXV.js
import { fontStore } from "unframer";
fontStore.loadFonts(["Inter-SemiBold", "Inter-Bold", "Inter-BoldItalic", "Inter-SemiBoldItalic"]);
var fonts7 = [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/hyOgCu0Xnghbimh0pE8QTvtt2AU.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/NeGmSOXrPBfEFIy5YZeHq17LEDA.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/oYaAX5himiTPYuN8vLWnqBbfD2s.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/lEJLP4R0yuCaMCjSXYHtJw72M.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/cRJyLNuTJR5jbyKzGi33wU9cqIQ.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/1ZFS7N918ojhhd0nQWdj3jz4w.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/A0Wcc7NgXMjUuFdquHDrIZpzZw0.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/DpPBYI0sL4fYLgAkX8KXOPVt7c.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/4RAEQdEOrcnDkhHiiCbJOw92Lk.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/1K3W8DizY3v4emK8Mb08YHxTbs.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/tUSCtfYVM1I1IchuyCwz9gDdQ.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/VgYFWiwsAC5OYxAycRXXvhze58.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/DXD0Q7LSl7HEvDzucnyLnGBHM.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/GIryZETIX4IFypco5pYZONKhJIo.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/H89BbHkbHDzlxZzxi8uPzTsp90.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/u6gJwDuwB143kpNK1T1MDKDWkMc.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/43sJ6MfOPh1LCJt46OvyDuSbA6o.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/wccHG0r4gBDAIRhfHiOlq6oEkqw.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/WZ367JPwf9bRW6LdTHN8rXgSjw.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/QxmhnWTzLtyjIiZcfaLIJ8EFBXU.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/2A4Xx7CngadFGlVV4xrO06OBHY.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/vxBnBhH8768IFAXAb4Qf6wQHKs.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/zSsEuoJdh8mcFVk976C05ZfQr8.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/b8ezwLrN7h2AUoPEENcsTMVJ0.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/mvNEIBLyHbscgHtwfsByjXUz3XY.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/6FI2EneKzM3qBy5foOZXey7coCA.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/qrVgiXNd6RuQjETYQiVQ9nqCk.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/NHHeAKJVP0ZWHk5YZnQQChIsBM.woff2", weight: "600" }] }];
var css7 = ['.framer-tOAQB .framer-styles-preset-1yvd34u:not(.rich-text-wrapper), .framer-tOAQB .framer-styles-preset-1yvd34u.rich-text-wrapper h2 { --framer-font-family: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-open-type-features: normal; --framer-font-size: 144px; --framer-font-style: normal; --framer-font-style-bold: normal; --framer-font-style-bold-italic: italic; --framer-font-style-italic: italic; --framer-font-variation-axes: normal; --framer-font-weight: 600; --framer-font-weight-bold: 700; --framer-font-weight-bold-italic: 700; --framer-font-weight-italic: 600; --framer-letter-spacing: -0.06em; --framer-line-height: 92%; --framer-paragraph-spacing: 40px; --framer-text-alignment: start; --framer-text-color: var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, #0a0a0a); --framer-text-decoration: none; --framer-text-stroke-color: initial; --framer-text-stroke-width: initial; --framer-text-transform: none; }', '@media (max-width: 1199px) and (min-width: 810px) { .framer-tOAQB .framer-styles-preset-1yvd34u:not(.rich-text-wrapper), .framer-tOAQB .framer-styles-preset-1yvd34u.rich-text-wrapper h2 { --framer-font-family: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-open-type-features: normal; --framer-font-size: 99px; --framer-font-style: normal; --framer-font-style-bold: normal; --framer-font-style-bold-italic: italic; --framer-font-style-italic: italic; --framer-font-variation-axes: normal; --framer-font-weight: 600; --framer-font-weight-bold: 700; --framer-font-weight-bold-italic: 700; --framer-font-weight-italic: 600; --framer-letter-spacing: -0.06em; --framer-line-height: 92%; --framer-paragraph-spacing: 40px; --framer-text-alignment: start; --framer-text-color: var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, #0a0a0a); --framer-text-decoration: none; --framer-text-stroke-color: initial; --framer-text-stroke-width: initial; --framer-text-transform: none; } }', '@media (max-width: 809px) and (min-width: 0px) { .framer-tOAQB .framer-styles-preset-1yvd34u:not(.rich-text-wrapper), .framer-tOAQB .framer-styles-preset-1yvd34u.rich-text-wrapper h2 { --framer-font-family: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-open-type-features: normal; --framer-font-size: 62px; --framer-font-style: normal; --framer-font-style-bold: normal; --framer-font-style-bold-italic: italic; --framer-font-style-italic: italic; --framer-font-variation-axes: normal; --framer-font-weight: 600; --framer-font-weight-bold: 700; --framer-font-weight-bold-italic: 700; --framer-font-weight-italic: 600; --framer-letter-spacing: -0.06em; --framer-line-height: 92%; --framer-paragraph-spacing: 40px; --framer-text-alignment: start; --framer-text-color: var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, #0a0a0a); --framer-text-decoration: none; --framer-text-stroke-color: initial; --framer-text-stroke-width: initial; --framer-text-transform: none; } }'];
var className7 = "framer-tOAQB";

// /:https://framerusercontent.com/modules/1aeta7xc6zx0W5JNyFOV/r3RNaJt8KTXvhORSuRQp/nCQNaN8LD.js
import { fontStore as fontStore2 } from "unframer";
fontStore2.loadFonts([]);
var fonts8 = [{ explicitInter: true, fonts: [] }];
var css8 = [".framer-m9VkI .framer-styles-preset-1wi7vce:not(.rich-text-wrapper), .framer-m9VkI .framer-styles-preset-1wi7vce.rich-text-wrapper a { --framer-link-current-text-color: #111111; --framer-link-current-text-decoration: none; --framer-link-hover-text-color: rgba(10, 10, 10, 0.8); --framer-link-hover-text-decoration: none; --framer-link-text-color: var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, #0a0a0a); --framer-link-text-decoration: none; transition: color 0.2s cubic-bezier(0.44, 0, 0.56, 1) 0s; }"];
var className8 = "framer-m9VkI";

// /:https://framerusercontent.com/modules/oQDCvavtkx6jLdMjb7pO/6nTJTjO0zVyQDSbr1HPm/l0AhafeLr.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ComponentViewportProvider, ControlType, cx, getFonts, getLoadingLazyAtYPosition, getPropertyControls, Image, SmartComponentScopedContainer, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var VideoFonts = getFonts(Video);
var GrainFonts = getFonts(Grain);
var VideoControls = getPropertyControls(Video);
var cycleOrder = ["DQjSdvNWb", "t56mZZuyf"];
var serializationHash = "framer-eOQ89";
var variantClassNames = { DQjSdvNWb: "framer-v-lahi66", t56mZZuyf: "framer-v-1roe3i4" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { bounce: 0.2, delay: 0, duration: 0.4, type: "spring" };
var negate = (value) => {
  return !value;
};
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { Desktop: "DQjSdvNWb", Phone: "t56mZZuyf" };
var getProps = ({ file, hasVideo, height, id, source, videoOpacity, width, ...props }) => {
  return { ...props, EfstzZdWy: videoOpacity ?? props.EfstzZdWy ?? 0.5, fgfZL9eoW: source ?? props.fgfZL9eoW ?? "Upload", nHfCx4e4R: file ?? props.nHfCx4e4R ?? "https://framerusercontent.com/assets/G0NwzP4bivPvK55b3ubxNslUs.mp4", NXkb7129J: hasVideo ?? props.NXkb7129J, variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "DQjSdvNWb" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className: className9, layoutId, variant, NXkb7129J, fgfZL9eoW, nHfCx4e4R, EfstzZdWy, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "DQjSdvNWb", ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  const visible = negate(NXkb7129J);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsxs(motion.div, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-lahi66", className9, classNames), "data-framer-name": "Desktop", layoutDependency, layoutId: "DQjSdvNWb", ref: refBinding, style: { backgroundColor: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", borderBottomLeftRadius: 25, borderBottomRightRadius: 25, borderTopLeftRadius: 25, borderTopRightRadius: 25, ...style }, variants: { t56mZZuyf: { borderBottomLeftRadius: 0, borderBottomRightRadius: 0, borderTopLeftRadius: 0, borderTopRightRadius: 0 } }, ...addPropertyOverrides({ t56mZZuyf: { "data-framer-name": "Phone" } }, baseVariant, gestureVariant), children: [visible && /* @__PURE__ */ _jsx(Image, { background: { alt: "Dark gradiend background", fit: "stretch", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0), pixelHeight: 1402, pixelWidth: 1912, positionX: "center", positionY: "center", sizes: componentViewport?.width || "100vw", src: "https://framerusercontent.com/images/vrhxHFTuxnCduP4nljUulqZcuQ.jpg", srcSet: "https://framerusercontent.com/images/vrhxHFTuxnCduP4nljUulqZcuQ.jpg?scale-down-to=512 512w,https://framerusercontent.com/images/vrhxHFTuxnCduP4nljUulqZcuQ.jpg?scale-down-to=1024 1024w,https://framerusercontent.com/images/vrhxHFTuxnCduP4nljUulqZcuQ.jpg 1912w" }, className: "framer-1o5ljvc", "data-framer-name": "Image", layoutDependency, layoutId: "tLHnNKIht", style: { filter: "brightness(1.15)", WebkitFilter: "brightness(1.15)" } }), NXkb7129J && /* @__PURE__ */ _jsx(ComponentViewportProvider, { children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-143hdec-container", isAuthoredByUser: true, isModuleExternal: true, layoutDependency, layoutId: "AteaD_2hO-container", nodeId: "AteaD_2hO", rendersWithMotion: true, scopeId: "l0AhafeLr", style: { filter: "grayscale(1)", opacity: EfstzZdWy, WebkitFilter: "grayscale(1)" }, children: /* @__PURE__ */ _jsx(Video, { backgroundColor: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", borderRadius: 0, bottomLeftRadius: 0, bottomRightRadius: 0, controls: false, height: "100%", id: "AteaD_2hO", isMixedBorderRadius: false, layoutId: "AteaD_2hO", loop: true, muted: true, objectFit: "cover", playing: true, poster: "https://framerusercontent.com/images/vrhxHFTuxnCduP4nljUulqZcuQ.jpg", posterEnabled: true, srcFile: nHfCx4e4R, srcType: fgfZL9eoW, srcUrl: "https://assets.mixkit.co/videos/43192/43192-720.mp4", startTime: 10, style: { height: "100%", width: "100%" }, topLeftRadius: 0, topRightRadius: 0, volume: 25, width: "100%" }) }) }), /* @__PURE__ */ _jsx(ComponentViewportProvider, { children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-1on14o5-container", isAuthoredByUser: true, isModuleExternal: true, layoutDependency, layoutId: "vbq2CXiRx-container", nodeId: "vbq2CXiRx", rendersWithMotion: true, scopeId: "l0AhafeLr", children: /* @__PURE__ */ _jsx(Grain, { height: "100%", id: "vbq2CXiRx", layoutId: "vbq2CXiRx", opacity: 0.05, style: { height: "100%", width: "100%" }, width: "100%" }) }) })] }) }) }) });
});
var css9 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-eOQ89.framer-14it2bz, .framer-eOQ89 .framer-14it2bz { display: block; }", ".framer-eOQ89.framer-lahi66 { align-content: center; align-items: center; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: 850px; justify-content: center; overflow: hidden; padding: 0px; position: relative; width: 1188px; will-change: var(--framer-will-change-override, transform); }", ".framer-eOQ89 .framer-1o5ljvc { bottom: 0px; flex: none; left: 0px; position: absolute; right: 0px; top: 0px; z-index: 0; }", ".framer-eOQ89 .framer-143hdec-container { bottom: 0px; flex: none; left: 0px; position: absolute; right: 0px; top: 0px; z-index: 2; }", ".framer-eOQ89 .framer-1on14o5-container { flex: none; height: 100%; position: relative; width: 100%; z-index: 0; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-eOQ89.framer-lahi66 { gap: 0px; } .framer-eOQ89.framer-lahi66 > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } .framer-eOQ89.framer-lahi66 > :first-child { margin-left: 0px; } .framer-eOQ89.framer-lahi66 > :last-child { margin-right: 0px; } }", ".framer-eOQ89.framer-v-1roe3i4.framer-lahi66 { width: 390px; will-change: unset; }"];
var Framerl0AhafeLr = withCSS(Component, css9, "framer-eOQ89");
var stdin_default3 = Framerl0AhafeLr;
Framerl0AhafeLr.displayName = "Gradient background";
Framerl0AhafeLr.defaultProps = { height: 850, width: 1188 };
addPropertyControls(Framerl0AhafeLr, { variant: { options: ["DQjSdvNWb", "t56mZZuyf"], optionTitles: ["Desktop", "Phone"], title: "Variant", type: ControlType.Enum }, NXkb7129J: { defaultValue: false, title: "Has video?", type: ControlType.Boolean }, fgfZL9eoW: VideoControls?.["srcType"] && { ...VideoControls["srcType"], defaultValue: "Upload", description: "", hidden: void 0, title: "Source" }, nHfCx4e4R: VideoControls?.["srcFile"] && { ...VideoControls["srcFile"], __defaultAssetReference: "data:framer/asset-reference,G0NwzP4bivPvK55b3ubxNslUs.mp4?originalFilename=compressed3.mp4", description: void 0, hidden: void 0, title: "File" }, EfstzZdWy: { defaultValue: 0.5, max: 1, min: 0, step: 0.01, title: "Video opacity", type: ControlType.Number } });
addFonts(Framerl0AhafeLr, [{ explicitInter: true, fonts: [] }, ...VideoFonts, ...GrainFonts], { supportsExplicitInterCodegen: true });

// /:https://framerusercontent.com/modules/6bfz2672fNDwFlMNmD6D/Rcw6MtrajjM2drLODo28/pCa0ZyMgp.js
import { jsx as _jsx2, jsxs as _jsxs2 } from "react/jsx-runtime";
import { addFonts as addFonts2, addPropertyControls as addPropertyControls2, ComponentViewportProvider as ComponentViewportProvider2, ControlType as ControlType2, cx as cx2, getFonts as getFonts2, getLoadingLazyAtYPosition as getLoadingLazyAtYPosition2, Image as Image2, RichText, SmartComponentScopedContainer as SmartComponentScopedContainer2, useComponentViewport as useComponentViewport2, useLocaleInfo as useLocaleInfo2, useVariantState as useVariantState2, withCSS as withCSS2 } from "unframer";
import { LayoutGroup as LayoutGroup2, motion as motion2, MotionConfigContext as MotionConfigContext2 } from "unframer";
import * as React2 from "react";
import { useRef as useRef2 } from "react";
var SmallButtonFonts = getFonts2(stdin_default2);
var enabledGestures = { L0JatAwCn: { hover: true } };
var cycleOrder2 = ["L0JatAwCn", "eEaIQeMBV"];
var serializationHash2 = "framer-UUXpR";
var variantClassNames2 = { eEaIQeMBV: "framer-v-1doshb7", L0JatAwCn: "framer-v-1wwdfxz" };
function addPropertyOverrides2(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition12 = { bounce: 0.2, delay: 0, duration: 0.4, type: "spring" };
var toResponsiveImage = (value) => {
  if (typeof value === "object" && value !== null && typeof value.src === "string") {
    return value;
  }
  return typeof value === "string" ? { src: value } : void 0;
};
var isSet = (value) => {
  if (Array.isArray(value)) return value.length > 0;
  return value !== void 0 && value !== null && value !== "";
};
var Transition2 = ({ value, children }) => {
  const config = React2.useContext(MotionConfigContext2);
  const transition = value ?? config.transition;
  const contextValue = React2.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx2(MotionConfigContext2.Provider, { value: contextValue, children });
};
var Variants2 = motion2.create(React2.Fragment);
var humanReadableVariantMap2 = { Desktop: "L0JatAwCn", Phone: "eEaIQeMBV" };
var getProps2 = ({ button, company, height, id, image, link, name1, title, width, ...props }) => {
  return { ...props, FggqkLwNL: title ?? props.FggqkLwNL ?? "Team Lead", gBH5GgjXs: button ?? props.gBH5GgjXs ?? "Let\u2019s talk", i1j3qrwjd: company ?? props.i1j3qrwjd ?? "at fabrica\xAE ", NZwcZIstG: link ?? props.NZwcZIstG, rGlrlhWc1: name1 ?? props.rGlrlhWc1 ?? "Name", variant: humanReadableVariantMap2[props.variant] ?? props.variant ?? "L0JatAwCn", WHWOTj3TQ: image ?? props.WHWOTj3TQ ?? { src: "https://framerusercontent.com/images/XBirhPxPnqDiGkAtvDrRCiK4pS8.jpg" } };
};
var createLayoutDependency2 = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component2 = /* @__PURE__ */ React2.forwardRef(function(props, ref) {
  const fallbackRef = useRef2(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React2.useId();
  const { activeLocale, setLocale } = useLocaleInfo2();
  const componentViewport = useComponentViewport2();
  const { style, className: className9, layoutId, variant, FggqkLwNL, WHWOTj3TQ, i1j3qrwjd, rGlrlhWc1, gBH5GgjXs, NZwcZIstG, ...restProps } = getProps2(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState2({ cycleOrder: cycleOrder2, defaultVariant: "L0JatAwCn", enabledGestures, ref: refBinding, variant, variantClassNames: variantClassNames2 });
  const layoutDependency = createLayoutDependency2(props, variants);
  const sharedStyleClassNames = [];
  const scopingClassNames = cx2(serializationHash2, ...sharedStyleClassNames);
  const visible = isSet(FggqkLwNL);
  const visible1 = isSet(i1j3qrwjd);
  const visible2 = isSet(rGlrlhWc1);
  return /* @__PURE__ */ _jsx2(LayoutGroup2, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx2(Variants2, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx2(Transition2, { value: transition12, children: /* @__PURE__ */ _jsxs2(motion2.div, { ...restProps, ...gestureHandlers, className: cx2(scopingClassNames, "framer-1wwdfxz", className9, classNames), "data-framer-name": "Desktop", layoutDependency, layoutId: "L0JatAwCn", ref: refBinding, style: { ...style }, ...addPropertyOverrides2({ "L0JatAwCn-hover": { "data-framer-name": void 0 }, eEaIQeMBV: { "data-framer-name": "Phone" } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsx2(motion2.div, { className: "framer-gcir4z", "data-framer-name": "Image", layoutDependency, layoutId: "AtZXGF9d_", style: { backgroundColor: "rgb(255, 255, 255)", borderBottomLeftRadius: 16, borderBottomRightRadius: 16, borderTopLeftRadius: 16, borderTopRightRadius: 16 }, variants: { "L0JatAwCn-hover": { borderBottomRightRadius: 0, borderTopRightRadius: 0 } }, children: /* @__PURE__ */ _jsx2(Image2, { background: { alt: "", fit: "fill", loading: getLoadingLazyAtYPosition2((componentViewport?.y || 0) + (0 + ((componentViewport?.height || 161) - 0 - ((componentViewport?.height || 161) - 0) * 1) / 2) + 6), pixelHeight: 288, pixelWidth: 226, sizes: "113px", ...toResponsiveImage(WHWOTj3TQ) }, className: "framer-bhhpli", "data-framer-name": "Team Member Image", layoutDependency, layoutId: "gVJmktcZu", style: { borderBottomLeftRadius: 12, borderBottomRightRadius: 12, borderTopLeftRadius: 12, borderTopRightRadius: 12 } }) }), /* @__PURE__ */ _jsxs2(motion2.div, { className: "framer-f4gtfg", "data-framer-name": "Card", layoutDependency, layoutId: "FR8PBmhFu", style: { backgroundColor: "rgb(255, 255, 255)", borderBottomLeftRadius: 16, borderBottomRightRadius: 16, borderTopLeftRadius: 16, borderTopRightRadius: 16 }, variants: { "L0JatAwCn-hover": { borderBottomLeftRadius: 0, borderTopLeftRadius: 0 } }, children: [/* @__PURE__ */ _jsxs2(motion2.div, { className: "framer-ilxhip", "data-framer-name": "Text", layoutDependency, layoutId: "MXygXDTR3", children: [/* @__PURE__ */ _jsxs2(motion2.div, { className: "framer-1o4mrbe", "data-framer-name": "Team Member Info", layoutDependency, layoutId: "dmORCFf8O", children: [visible && /* @__PURE__ */ _jsx2(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx2(React2.Fragment, { children: /* @__PURE__ */ _jsx2(motion2.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "14px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "100%", "--framer-text-color": "var(--extracted-r6o4lv, var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10)))" }, children: "Team Lead" }) }), className: "framer-7iz30i", "data-framer-name": "Team Member Role", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "j88Hr0CbV", style: { "--extracted-r6o4lv": "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", "--framer-paragraph-spacing": "0px" }, text: FggqkLwNL, verticalAlignment: "top", withExternalLayout: true }), visible1 && /* @__PURE__ */ _jsx2(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx2(React2.Fragment, { children: /* @__PURE__ */ _jsx2(motion2.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "12px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "110%", "--framer-text-color": "var(--extracted-r6o4lv, rgb(9, 9, 9))" }, children: "at fabrica\xAE " }) }), className: "framer-1dkoxx", "data-framer-name": "Team Member Company", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "lBriKx_di", style: { "--extracted-r6o4lv": "rgb(9, 9, 9)", "--framer-paragraph-spacing": "0px", opacity: 0.6 }, text: i1j3qrwjd, verticalAlignment: "top", withExternalLayout: true })] }), visible2 && /* @__PURE__ */ _jsx2(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx2(React2.Fragment, { children: /* @__PURE__ */ _jsx2(motion2.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "22px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "115%", "--framer-text-color": "var(--extracted-r6o4lv, rgb(9, 9, 9))" }, children: "Lauren Thompson" }) }), className: "framer-3w6c98", "data-framer-name": "Team Member Name", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "iH_QIizJO", style: { "--extracted-r6o4lv": "rgb(9, 9, 9)", "--framer-paragraph-spacing": "0px" }, text: rGlrlhWc1, verticalAlignment: "top", withExternalLayout: true })] }), /* @__PURE__ */ _jsx2(ComponentViewportProvider2, { height: 30, y: (componentViewport?.y || 0) + (0 + ((componentViewport?.height || 161) - 0 - 370.5) / 2) + 24 + 292.5, children: /* @__PURE__ */ _jsx2(SmartComponentScopedContainer2, { className: "framer-1ulhjzu-container", layoutDependency, layoutId: "EEYsqtKv0-container", nodeId: "EEYsqtKv0", rendersWithMotion: true, scopeId: "pCa0ZyMgp", children: /* @__PURE__ */ _jsx2(stdin_default2, { GK_bavkEs: NZwcZIstG, height: "100%", id: "EEYsqtKv0", J1bovNTGx: gBH5GgjXs, layoutId: "EEYsqtKv0", variant: "oEh913J4W", width: "100%", ...addPropertyOverrides2({ eEaIQeMBV: { variant: "efPssla96" } }, baseVariant, gestureVariant) }) }) })] })] }) }) }) });
});
var css10 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-UUXpR.framer-1hc3jpa, .framer-UUXpR .framer-1hc3jpa { display: block; }", ".framer-UUXpR.framer-1wwdfxz { align-content: center; align-items: center; cursor: default; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: flex-start; max-width: 410px; overflow: visible; padding: 0px; position: relative; width: 280px; }", ".framer-UUXpR .framer-gcir4z { align-content: center; align-items: center; align-self: stretch; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: auto; justify-content: flex-start; overflow: visible; padding: 6px; position: relative; width: 125px; }", ".framer-UUXpR .framer-bhhpli { bottom: 6px; flex: none; left: 6px; position: absolute; right: 6px; top: 6px; z-index: 1; }", ".framer-UUXpR .framer-f4gtfg { align-content: flex-start; align-items: flex-start; display: flex; flex: 1 0 0px; flex-direction: column; flex-wrap: nowrap; gap: 20px; height: min-content; justify-content: flex-start; overflow: visible; padding: 24px; position: relative; width: 1px; }", ".framer-UUXpR .framer-ilxhip { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 6px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-UUXpR .framer-1o4mrbe { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: wrap; gap: 4px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-UUXpR .framer-7iz30i, .framer-UUXpR .framer-1dkoxx, .framer-UUXpR .framer-3w6c98 { flex: none; height: auto; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }", ".framer-UUXpR .framer-1ulhjzu-container { flex: none; height: auto; position: relative; width: auto; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-UUXpR.framer-1wwdfxz, .framer-UUXpR .framer-gcir4z, .framer-UUXpR .framer-f4gtfg, .framer-UUXpR .framer-ilxhip, .framer-UUXpR .framer-1o4mrbe { gap: 0px; } .framer-UUXpR.framer-1wwdfxz > * { margin: 0px; margin-left: calc(0px / 2); margin-right: calc(0px / 2); } .framer-UUXpR.framer-1wwdfxz > :first-child, .framer-UUXpR .framer-gcir4z > :first-child { margin-left: 0px; } .framer-UUXpR.framer-1wwdfxz > :last-child, .framer-UUXpR .framer-gcir4z > :last-child { margin-right: 0px; } .framer-UUXpR .framer-gcir4z > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } .framer-UUXpR .framer-f4gtfg > * { margin: 0px; margin-bottom: calc(20px / 2); margin-top: calc(20px / 2); } .framer-UUXpR .framer-f4gtfg > :first-child, .framer-UUXpR .framer-ilxhip > :first-child, .framer-UUXpR .framer-1o4mrbe > :first-child { margin-top: 0px; } .framer-UUXpR .framer-f4gtfg > :last-child, .framer-UUXpR .framer-ilxhip > :last-child, .framer-UUXpR .framer-1o4mrbe > :last-child { margin-bottom: 0px; } .framer-UUXpR .framer-ilxhip > * { margin: 0px; margin-bottom: calc(6px / 2); margin-top: calc(6px / 2); } .framer-UUXpR .framer-1o4mrbe > * { margin: 0px; margin-bottom: calc(4px / 2); margin-top: calc(4px / 2); } }"];
var FramerpCa0ZyMgp = withCSS2(Component2, css10, "framer-UUXpR");
var stdin_default4 = FramerpCa0ZyMgp;
FramerpCa0ZyMgp.displayName = "Contact card";
FramerpCa0ZyMgp.defaultProps = { height: 161, width: 280 };
addPropertyControls2(FramerpCa0ZyMgp, { variant: { options: ["L0JatAwCn", "eEaIQeMBV"], optionTitles: ["Desktop", "Phone"], title: "Variant", type: ControlType2.Enum }, FggqkLwNL: { defaultValue: "Team Lead", displayTextArea: false, title: "Title", type: ControlType2.String }, WHWOTj3TQ: { __defaultAssetReference: "data:framer/asset-reference,XBirhPxPnqDiGkAtvDrRCiK4pS8.jpg?originalFilename=Team+Member+Image.jpg&preferredSize=auto", title: "Image", type: ControlType2.ResponsiveImage }, i1j3qrwjd: { defaultValue: "at fabrica\xAE ", displayTextArea: false, title: "Company", type: ControlType2.String }, rGlrlhWc1: { defaultValue: "Name", displayTextArea: false, title: "Name", type: ControlType2.String }, gBH5GgjXs: { defaultValue: "Let\u2019s talk", displayTextArea: false, title: "Button", type: ControlType2.String }, NZwcZIstG: { title: "Link", type: ControlType2.Link } });
addFonts2(FramerpCa0ZyMgp, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/hyOgCu0Xnghbimh0pE8QTvtt2AU.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/NeGmSOXrPBfEFIy5YZeHq17LEDA.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/oYaAX5himiTPYuN8vLWnqBbfD2s.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/lEJLP4R0yuCaMCjSXYHtJw72M.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/cRJyLNuTJR5jbyKzGi33wU9cqIQ.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/1ZFS7N918ojhhd0nQWdj3jz4w.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/A0Wcc7NgXMjUuFdquHDrIZpzZw0.woff2", weight: "600" }] }, ...SmallButtonFonts], { supportsExplicitInterCodegen: true });

// /:https://framerusercontent.com/modules/zw9OR6U2ucGcBK3TzK0e/LSxltdvoTFLfIzApY5HK/qBz_H6jdv.js
import { jsx as _jsx3 } from "react/jsx-runtime";
import { addFonts as addFonts3, addPropertyControls as addPropertyControls3, ControlType as ControlType3, cx as cx3, RichText as RichText2, useComponentViewport as useComponentViewport3, useLocaleInfo as useLocaleInfo3, useVariantState as useVariantState3, withCSS as withCSS3 } from "unframer";
import { LayoutGroup as LayoutGroup3, motion as motion3, MotionConfigContext as MotionConfigContext3 } from "unframer";
import * as React3 from "react";
var cycleOrder3 = ["evMN1mTNM", "ZPM_PrHzb", "wNhkD5cob"];
var serializationHash3 = "framer-uNobG";
var variantClassNames3 = { evMN1mTNM: "framer-v-1ppeyn8", wNhkD5cob: "framer-v-1igudlo", ZPM_PrHzb: "framer-v-ki7hw0" };
function addPropertyOverrides3(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition13 = { bounce: 0.2, delay: 0, duration: 0.4, type: "spring" };
var Transition3 = ({ value, children }) => {
  const config = React3.useContext(MotionConfigContext3);
  const transition = value ?? config.transition;
  const contextValue = React3.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx3(MotionConfigContext3.Provider, { value: contextValue, children });
};
var Variants3 = motion3.create(React3.Fragment);
var humanReadableVariantMap3 = { Desktop: "evMN1mTNM", Phone: "wNhkD5cob", Tablet: "ZPM_PrHzb" };
var getProps3 = ({ color, height, id, width, ...props }) => {
  return { ...props, HZSElEzAS: color ?? props.HZSElEzAS ?? "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", variant: humanReadableVariantMap3[props.variant] ?? props.variant ?? "evMN1mTNM" };
};
var createLayoutDependency3 = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component3 = /* @__PURE__ */ React3.forwardRef(function(props, ref) {
  const { activeLocale, setLocale } = useLocaleInfo3();
  const { style, className: className9, layoutId, variant, HZSElEzAS, ...restProps } = getProps3(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState3({ cycleOrder: cycleOrder3, defaultVariant: "evMN1mTNM", variant, variantClassNames: variantClassNames3 });
  const layoutDependency = createLayoutDependency3(props, variants);
  const sharedStyleClassNames = [];
  const scopingClassNames = cx3(serializationHash3, ...sharedStyleClassNames);
  const ref1 = React3.useRef(null);
  const defaultLayoutId = React3.useId();
  const componentViewport = useComponentViewport3();
  return /* @__PURE__ */ _jsx3(LayoutGroup3, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx3(Variants3, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx3(Transition3, { value: transition13, children: /* @__PURE__ */ _jsx3(motion3.div, { ...restProps, ...gestureHandlers, className: cx3(scopingClassNames, "framer-1ppeyn8", className9, classNames), "data-framer-name": "Desktop", layoutDependency, layoutId: "evMN1mTNM", ref: ref ?? ref1, style: { ...style }, ...addPropertyOverrides3({ wNhkD5cob: { "data-framer-name": "Phone" }, ZPM_PrHzb: { "data-framer-name": "Tablet" } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx3(RichText2, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx3(React3.Fragment, { children: /* @__PURE__ */ _jsx3(motion3.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "20px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "110%", "--framer-text-color": "var(--extracted-r6o4lv, var(--variable-reference-HZSElEzAS-qBz_H6jdv))" }, children: "fabrica\xAE " }) }), className: "framer-1lz6zv4", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "CKJr1zoZd", style: { "--extracted-r6o4lv": "var(--variable-reference-HZSElEzAS-qBz_H6jdv)", "--framer-paragraph-spacing": "0px", "--variable-reference-HZSElEzAS-qBz_H6jdv": HZSElEzAS }, verticalAlignment: "top", withExternalLayout: true, ...addPropertyOverrides3({ wNhkD5cob: { children: /* @__PURE__ */ _jsx3(React3.Fragment, { children: /* @__PURE__ */ _jsx3(motion3.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "18px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "110%", "--framer-text-color": "var(--extracted-r6o4lv, var(--variable-reference-HZSElEzAS-qBz_H6jdv))" }, children: "fabrica\xAE " }) }) }, ZPM_PrHzb: { children: /* @__PURE__ */ _jsx3(React3.Fragment, { children: /* @__PURE__ */ _jsx3(motion3.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "19px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "110%", "--framer-text-color": "var(--extracted-r6o4lv, var(--variable-reference-HZSElEzAS-qBz_H6jdv))" }, children: "fabrica\xAE " }) }) } }, baseVariant, gestureVariant) }) }) }) }) });
});
var css11 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-uNobG.framer-aa2ccv, .framer-uNobG .framer-aa2ccv { display: block; }", ".framer-uNobG.framer-1ppeyn8 { align-content: center; align-items: center; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: center; overflow: hidden; padding: 0px; position: relative; width: min-content; }", ".framer-uNobG .framer-1lz6zv4 { flex: none; height: auto; position: relative; white-space: pre; width: auto; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-uNobG.framer-1ppeyn8 { gap: 0px; } .framer-uNobG.framer-1ppeyn8 > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } .framer-uNobG.framer-1ppeyn8 > :first-child { margin-left: 0px; } .framer-uNobG.framer-1ppeyn8 > :last-child { margin-right: 0px; } }"];
var FramerqBz_H6jdv = withCSS3(Component3, css11, "framer-uNobG");
var stdin_default5 = FramerqBz_H6jdv;
FramerqBz_H6jdv.displayName = "Logo";
FramerqBz_H6jdv.defaultProps = { height: 22, width: 79 };
addPropertyControls3(FramerqBz_H6jdv, { variant: { options: ["evMN1mTNM", "ZPM_PrHzb", "wNhkD5cob"], optionTitles: ["Desktop", "Tablet", "Phone"], title: "Variant", type: ControlType3.Enum }, HZSElEzAS: { defaultValue: 'var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10)) /* {"name":"Black"} */', title: "Color", type: ControlType3.Color } });
addFonts3(FramerqBz_H6jdv, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/hyOgCu0Xnghbimh0pE8QTvtt2AU.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/NeGmSOXrPBfEFIy5YZeHq17LEDA.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/oYaAX5himiTPYuN8vLWnqBbfD2s.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/lEJLP4R0yuCaMCjSXYHtJw72M.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/cRJyLNuTJR5jbyKzGi33wU9cqIQ.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/1ZFS7N918ojhhd0nQWdj3jz4w.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/A0Wcc7NgXMjUuFdquHDrIZpzZw0.woff2", weight: "600" }] }], { supportsExplicitInterCodegen: true });

// /:https://framerusercontent.com/modules/jmz8JicKobTrI3XpEl8M/Ojn5Qm9Xr4RxhrDVvxcw/vTiEQWJOl.js
import { jsx as _jsx4, jsxs as _jsxs3 } from "react/jsx-runtime";
import { addFonts as addFonts4, addPropertyControls as addPropertyControls4, ComponentViewportProvider as ComponentViewportProvider3, ControlType as ControlType4, cx as cx4, getFonts as getFonts3, getFontsFromSharedStyle, getPropertyControls as getPropertyControls2, RichText as RichText3, SmartComponentScopedContainer as SmartComponentScopedContainer3, useComponentViewport as useComponentViewport4, useLocaleInfo as useLocaleInfo4, useVariantState as useVariantState4, withCSS as withCSS4 } from "unframer";
import { LayoutGroup as LayoutGroup4, motion as motion4, MotionConfigContext as MotionConfigContext4 } from "unframer";
import * as React4 from "react";
import { useRef as useRef4 } from "react";
var PhosphorFonts = getFonts3(Icon);
var PhosphorControls = getPropertyControls2(Icon);
var cycleOrder4 = ["di1TdPrBR", "EMSWDxYWx"];
var serializationHash4 = "framer-N2Igf";
var variantClassNames4 = { di1TdPrBR: "framer-v-75j668", EMSWDxYWx: "framer-v-vb0put" };
function addPropertyOverrides4(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition14 = { bounce: 0.2, delay: 0, duration: 0.4, type: "spring" };
var Transition4 = ({ value, children }) => {
  const config = React4.useContext(MotionConfigContext4);
  const transition = value ?? config.transition;
  const contextValue = React4.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx4(MotionConfigContext4.Provider, { value: contextValue, children });
};
var Variants4 = motion4.create(React4.Fragment);
var humanReadableVariantMap4 = { Desktop: "di1TdPrBR", Phone: "EMSWDxYWx" };
var getProps4 = ({ description, height, icon, id, title, width, ...props }) => {
  return { ...props, D4TTIBv6c: title ?? props.D4TTIBv6c ?? "Title", dBJ7yrzpe: icon ?? props.dBJ7yrzpe ?? "Speedometer", variant: humanReadableVariantMap4[props.variant] ?? props.variant ?? "di1TdPrBR", WJwcqyIbg: description ?? props.WJwcqyIbg ?? "Description" };
};
var createLayoutDependency4 = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component4 = /* @__PURE__ */ React4.forwardRef(function(props, ref) {
  const fallbackRef = useRef4(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React4.useId();
  const { activeLocale, setLocale } = useLocaleInfo4();
  const componentViewport = useComponentViewport4();
  const { style, className: className9, layoutId, variant, dBJ7yrzpe, D4TTIBv6c, WJwcqyIbg, ...restProps } = getProps4(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState4({ cycleOrder: cycleOrder4, defaultVariant: "di1TdPrBR", ref: refBinding, variant, variantClassNames: variantClassNames4 });
  const layoutDependency = createLayoutDependency4(props, variants);
  const sharedStyleClassNames = [className5, className4];
  const scopingClassNames = cx4(serializationHash4, ...sharedStyleClassNames);
  return /* @__PURE__ */ _jsx4(LayoutGroup4, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx4(Variants4, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx4(Transition4, { value: transition14, children: /* @__PURE__ */ _jsxs3(motion4.div, { ...restProps, ...gestureHandlers, className: cx4(scopingClassNames, "framer-75j668", className9, classNames), "data-framer-name": "Desktop", layoutDependency, layoutId: "di1TdPrBR", ref: refBinding, style: { ...style }, ...addPropertyOverrides4({ EMSWDxYWx: { "data-framer-name": "Phone" } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsxs3(motion4.div, { className: "framer-tx0xy4", "data-framer-name": "Quick Response Info", layoutDependency, layoutId: "wIM2zDaPg", children: [/* @__PURE__ */ _jsx4(ComponentViewportProvider3, { children: /* @__PURE__ */ _jsx4(SmartComponentScopedContainer3, { className: "framer-t3xn95-container", isAuthoredByUser: true, isModuleExternal: true, layoutDependency, layoutId: "QpDK4oInL-container", nodeId: "QpDK4oInL", rendersWithMotion: true, scopeId: "vTiEQWJOl", children: /* @__PURE__ */ _jsx4(Icon, { color: "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", height: "100%", iconSearch: "speedometer", iconSelection: dBJ7yrzpe, id: "QpDK4oInL", layoutId: "QpDK4oInL", mirrored: false, selectByList: true, style: { height: "100%", width: "100%" }, weight: "regular", width: "100%" }) }) }), /* @__PURE__ */ _jsx4(RichText3, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx4(React4.Fragment, { children: /* @__PURE__ */ _jsx4(motion4.p, { className: "framer-styles-preset-9v8dhs", "data-styles-preset": "oFAZmwcVJ", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Quick response." }) }), className: "framer-1g7f53", "data-framer-name": "Quick Response Title", fonts: ["Inter"], layoutDependency, layoutId: "Q_UW0caOT", style: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", "--framer-paragraph-spacing": "0px" }, text: D4TTIBv6c, verticalAlignment: "top", withExternalLayout: true })] }), /* @__PURE__ */ _jsx4(RichText3, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx4(React4.Fragment, { children: /* @__PURE__ */ _jsx4(motion4.p, { className: "framer-styles-preset-1mf8d9g", "data-styles-preset": "ypR5VEWEl", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Description" }) }), className: "framer-r0ha9m", "data-framer-name": "Join Us Description", fonts: ["Inter"], layoutDependency, layoutId: "bWj8XdYhC", style: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", "--framer-paragraph-spacing": "0px", opacity: 0.6 }, text: WJwcqyIbg, verticalAlignment: "top", withExternalLayout: true })] }) }) }) });
});
var css12 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-N2Igf.framer-ihn9ej, .framer-N2Igf .framer-ihn9ej { display: block; }", ".framer-N2Igf.framer-75j668 { align-content: flex-start; align-items: flex-start; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 20px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 30px 0px 0px; position: relative; width: 300px; }", ".framer-N2Igf .framer-tx0xy4 { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 16px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-N2Igf .framer-t3xn95-container { flex: none; height: 25px; position: relative; width: 25px; }", ".framer-N2Igf .framer-1g7f53 { flex: 1 0 0px; height: auto; position: relative; white-space: pre-wrap; width: 1px; word-break: break-word; word-wrap: break-word; }", ".framer-N2Igf .framer-r0ha9m { --framer-text-wrap-override: none; flex: none; height: auto; max-width: 280px; position: relative; width: 100%; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-N2Igf.framer-75j668, .framer-N2Igf .framer-tx0xy4 { gap: 0px; } .framer-N2Igf.framer-75j668 > * { margin: 0px; margin-bottom: calc(20px / 2); margin-top: calc(20px / 2); } .framer-N2Igf.framer-75j668 > :first-child { margin-top: 0px; } .framer-N2Igf.framer-75j668 > :last-child { margin-bottom: 0px; } .framer-N2Igf .framer-tx0xy4 > * { margin: 0px; margin-left: calc(16px / 2); margin-right: calc(16px / 2); } .framer-N2Igf .framer-tx0xy4 > :first-child { margin-left: 0px; } .framer-N2Igf .framer-tx0xy4 > :last-child { margin-right: 0px; } }", ".framer-N2Igf.framer-v-vb0put.framer-75j668 { gap: 14px; padding: 0px; }", ".framer-N2Igf.framer-v-vb0put .framer-tx0xy4 { gap: 12px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-N2Igf.framer-v-vb0put.framer-75j668, .framer-N2Igf.framer-v-vb0put .framer-tx0xy4 { gap: 0px; } .framer-N2Igf.framer-v-vb0put.framer-75j668 > * { margin: 0px; margin-bottom: calc(14px / 2); margin-top: calc(14px / 2); } .framer-N2Igf.framer-v-vb0put.framer-75j668 > :first-child { margin-top: 0px; } .framer-N2Igf.framer-v-vb0put.framer-75j668 > :last-child { margin-bottom: 0px; } .framer-N2Igf.framer-v-vb0put .framer-tx0xy4 > * { margin: 0px; margin-left: calc(12px / 2); margin-right: calc(12px / 2); } .framer-N2Igf.framer-v-vb0put .framer-tx0xy4 > :first-child { margin-left: 0px; } .framer-N2Igf.framer-v-vb0put .framer-tx0xy4 > :last-child { margin-right: 0px; } }", ...css5, ...css4];
var FramervTiEQWJOl = withCSS4(Component4, css12, "framer-N2Igf");
var stdin_default6 = FramervTiEQWJOl;
FramervTiEQWJOl.displayName = "Contact section item";
FramervTiEQWJOl.defaultProps = { height: 66, width: 300 };
addPropertyControls4(FramervTiEQWJOl, { variant: { options: ["di1TdPrBR", "EMSWDxYWx"], optionTitles: ["Desktop", "Phone"], title: "Variant", type: ControlType4.Enum }, dBJ7yrzpe: PhosphorControls?.["iconSelection"] && { ...PhosphorControls["iconSelection"], defaultValue: "Speedometer", description: void 0, hidden: void 0, title: "Icon" }, D4TTIBv6c: { defaultValue: "Title", displayTextArea: true, title: "Title", type: ControlType4.String }, WJwcqyIbg: { defaultValue: "Description", displayTextArea: true, title: "Description", type: ControlType4.String } });
addFonts4(FramervTiEQWJOl, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5vvr9Vy74if2I6bQbJvbw7SY1pQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/EOr0mi4hNtlgWNn9if640EZzXCo.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/Y9k9QrlZAqio88Klkmbd8VoMQc.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/OYrD2tBIBPvoJXiIHnLoOXnY9M.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/JeYwfuaPfZHQhEG8U5gtPDZ7WQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/vQyevYAyHtARFwPqUzQGpnDs.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/b6Y37FthZeALduNqHicBT6FutY.woff2", weight: "400" }] }, ...PhosphorFonts, ...getFontsFromSharedStyle(fonts5), ...getFontsFromSharedStyle(fonts4)], { supportsExplicitInterCodegen: true });

// /:https://framerusercontent.com/modules/kFH0BlvE0NUW5ULtike1/JxLwaSV0InF8NIeGsDIf/ZsPpcvZL7.js
import { jsx as _jsx5, jsxs as _jsxs4 } from "react/jsx-runtime";
import { addFonts as addFonts5, addPropertyControls as addPropertyControls5, ControlType as ControlType5, cx as cx5, RichText as RichText4, useComponentViewport as useComponentViewport5, useLocaleInfo as useLocaleInfo5, useVariantState as useVariantState5, withCSS as withCSS5, withFX } from "unframer";
import { LayoutGroup as LayoutGroup5, motion as motion5, MotionConfigContext as MotionConfigContext5 } from "unframer";
import * as React5 from "react";
import { useRef as useRef5 } from "react";
var MotionDivWithFX = withFX(motion5.div);
var enabledGestures2 = { wYDONdaYp: { hover: true } };
var cycleOrder5 = ["wYDONdaYp", "klvioFlTK", "eaKPgDO7P", "dq7iWPLH9", "wDK8O1Ezz"];
var serializationHash5 = "framer-GdT3P";
var variantClassNames5 = { dq7iWPLH9: "framer-v-p8053s", eaKPgDO7P: "framer-v-2gmwtk", klvioFlTK: "framer-v-1rmx67o", wDK8O1Ezz: "framer-v-1h3xpm4", wYDONdaYp: "framer-v-4lucl9" };
function addPropertyOverrides5(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition15 = { bounce: 0.35, delay: 0, duration: 0.39, type: "spring" };
var transformTemplate1 = (_, t) => `translateX(-50%) ${t}`;
var transition2 = { delay: 0, duration: 1, ease: [0, 0, 1, 1], type: "tween" };
var animation = { opacity: 1, rotate: 360, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, x: 0, y: 0 };
var Transition5 = ({ value, children }) => {
  const config = React5.useContext(MotionConfigContext5);
  const transition = value ?? config.transition;
  const contextValue = React5.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx5(MotionConfigContext5.Provider, { value: contextValue, children });
};
var Variants5 = motion5.create(React5.Fragment);
var humanReadableVariantMap5 = { Default: "wYDONdaYp", Disabled: "eaKPgDO7P", Error: "wDK8O1Ezz", Loading: "klvioFlTK", Success: "dq7iWPLH9" };
var getProps5 = ({ bG, color, height, hoverBG, hoverColor, id, title, width, ...props }) => {
  return { ...props, GNYtni0Of: color ?? props.GNYtni0Of ?? "rgb(255, 255, 255)", MoodCIitJ: bG ?? props.MoodCIitJ ?? "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", UZBAPRl6f: hoverBG ?? props.UZBAPRl6f ?? "rgb(0, 0, 0)", variant: humanReadableVariantMap5[props.variant] ?? props.variant ?? "wYDONdaYp", X9Xju9FBn: title ?? props.X9Xju9FBn ?? "Submit", xKHL3eyCS: hoverColor ?? props.xKHL3eyCS ?? "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))" };
};
var createLayoutDependency5 = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component5 = /* @__PURE__ */ React5.forwardRef(function(props, ref) {
  const fallbackRef = useRef5(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React5.useId();
  const { activeLocale, setLocale } = useLocaleInfo5();
  const componentViewport = useComponentViewport5();
  const { style, className: className9, layoutId, variant, X9Xju9FBn, GNYtni0Of, MoodCIitJ, xKHL3eyCS, UZBAPRl6f, ...restProps } = getProps5(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState5({ cycleOrder: cycleOrder5, defaultVariant: "wYDONdaYp", enabledGestures: enabledGestures2, ref: refBinding, variant, variantClassNames: variantClassNames5 });
  const layoutDependency = createLayoutDependency5(props, variants);
  const sharedStyleClassNames = [];
  const scopingClassNames = cx5(serializationHash5, ...sharedStyleClassNames);
  const isDisplayed = () => {
    if (baseVariant === "klvioFlTK") return false;
    return true;
  };
  const isDisplayed1 = () => {
    if (baseVariant === "klvioFlTK") return true;
    return false;
  };
  return /* @__PURE__ */ _jsx5(LayoutGroup5, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx5(Variants5, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx5(Transition5, { value: transition15, children: /* @__PURE__ */ _jsxs4(motion5.button, { ...restProps, ...gestureHandlers, className: cx5(scopingClassNames, "framer-4lucl9", className9, classNames), "data-framer-name": "Default", "data-reset": "button", layoutDependency, layoutId: "wYDONdaYp", ref: refBinding, style: { backgroundColor: MoodCIitJ, borderBottomLeftRadius: 50, borderBottomRightRadius: 50, borderTopLeftRadius: 50, borderTopRightRadius: 50, opacity: 1, ...style }, variants: { "wYDONdaYp-hover": { backgroundColor: UZBAPRl6f, opacity: 1 }, dq7iWPLH9: { opacity: 1 }, eaKPgDO7P: { opacity: 0.5 }, wDK8O1Ezz: { backgroundColor: "rgba(255, 34, 68, 0.15)", opacity: 1 } }, ...addPropertyOverrides5({ "wYDONdaYp-hover": { "data-framer-name": void 0 }, dq7iWPLH9: { "data-framer-name": "Success" }, eaKPgDO7P: { "data-framer-name": "Disabled" }, klvioFlTK: { "data-framer-name": "Loading" }, wDK8O1Ezz: { "data-framer-name": "Error" } }, baseVariant, gestureVariant), children: [isDisplayed() && /* @__PURE__ */ _jsx5(RichText4, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx5(React5.Fragment, { children: /* @__PURE__ */ _jsx5(motion5.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "18px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-text-color": "var(--extracted-r6o4lv, var(--variable-reference-GNYtni0Of-ZsPpcvZL7))" }, children: "Submit" }) }), className: "framer-o38np2", "data-framer-name": "Submit 1", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "C10wEhuIa", style: { "--extracted-r6o4lv": "var(--variable-reference-GNYtni0Of-ZsPpcvZL7)", "--framer-link-text-color": "rgb(0, 153, 255)", "--framer-link-text-decoration": "underline", "--variable-reference-GNYtni0Of-ZsPpcvZL7": GNYtni0Of, "--variable-reference-xKHL3eyCS-ZsPpcvZL7": xKHL3eyCS, opacity: 0 }, text: X9Xju9FBn, transformTemplate: transformTemplate1, variants: { "wYDONdaYp-hover": { "--extracted-r6o4lv": "var(--variable-reference-xKHL3eyCS-ZsPpcvZL7)", "--variable-reference-xKHL3eyCS-ZsPpcvZL7": xKHL3eyCS, opacity: 1 }, dq7iWPLH9: { "--extracted-r6o4lv": "rgb(255, 255, 255)" }, wDK8O1Ezz: { "--extracted-r6o4lv": "rgb(255, 34, 68)" } }, verticalAlignment: "top", withExternalLayout: true, ...addPropertyOverrides5({ "wYDONdaYp-hover": { children: /* @__PURE__ */ _jsx5(React5.Fragment, { children: /* @__PURE__ */ _jsx5(motion5.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "18px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-text-color": "var(--extracted-r6o4lv, var(--variable-reference-xKHL3eyCS-ZsPpcvZL7))" }, children: "Submit" }) }), transformTemplate: void 0 }, dq7iWPLH9: { children: /* @__PURE__ */ _jsx5(React5.Fragment, { children: /* @__PURE__ */ _jsx5(motion5.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "18px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-text-color": "var(--extracted-r6o4lv, rgb(255, 255, 255))" }, children: "Thank you!" }) }), text: void 0 }, wDK8O1Ezz: { children: /* @__PURE__ */ _jsx5(React5.Fragment, { children: /* @__PURE__ */ _jsx5(motion5.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "18px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-text-color": "var(--extracted-r6o4lv, rgb(255, 34, 68))" }, children: "Something went wrong" }) }), text: void 0 } }, baseVariant, gestureVariant) }), isDisplayed() && /* @__PURE__ */ _jsx5(RichText4, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx5(React5.Fragment, { children: /* @__PURE__ */ _jsx5(motion5.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "18px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-text-color": "var(--extracted-r6o4lv, var(--variable-reference-GNYtni0Of-ZsPpcvZL7))" }, children: "Submit" }) }), className: "framer-1zgpu1", "data-framer-name": "Submit 2", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "g4yB2dsE8", style: { "--extracted-r6o4lv": "var(--variable-reference-GNYtni0Of-ZsPpcvZL7)", "--framer-link-text-color": "rgb(0, 153, 255)", "--framer-link-text-decoration": "underline", "--variable-reference-GNYtni0Of-ZsPpcvZL7": GNYtni0Of, "--variable-reference-xKHL3eyCS-ZsPpcvZL7": xKHL3eyCS, opacity: 1 }, text: X9Xju9FBn, variants: { "wYDONdaYp-hover": { "--extracted-r6o4lv": "var(--variable-reference-xKHL3eyCS-ZsPpcvZL7)", "--variable-reference-xKHL3eyCS-ZsPpcvZL7": xKHL3eyCS, opacity: 0 }, dq7iWPLH9: { "--extracted-r6o4lv": "rgb(255, 255, 255)" }, wDK8O1Ezz: { "--extracted-r6o4lv": "rgb(255, 34, 68)" } }, verticalAlignment: "top", withExternalLayout: true, ...addPropertyOverrides5({ "wYDONdaYp-hover": { children: /* @__PURE__ */ _jsx5(React5.Fragment, { children: /* @__PURE__ */ _jsx5(motion5.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "18px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-text-color": "var(--extracted-r6o4lv, var(--variable-reference-xKHL3eyCS-ZsPpcvZL7))" }, children: "Submit" }) }), transformTemplate: transformTemplate1 }, dq7iWPLH9: { children: /* @__PURE__ */ _jsx5(React5.Fragment, { children: /* @__PURE__ */ _jsx5(motion5.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "18px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-text-color": "var(--extracted-r6o4lv, rgb(255, 255, 255))" }, children: "Thank you!" }) }), text: void 0 }, wDK8O1Ezz: { children: /* @__PURE__ */ _jsx5(React5.Fragment, { children: /* @__PURE__ */ _jsx5(motion5.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "18px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-text-color": "var(--extracted-r6o4lv, rgb(255, 34, 68))" }, children: "Something went wrong" }) }), text: void 0 } }, baseVariant, gestureVariant) }), isDisplayed1() && /* @__PURE__ */ _jsx5(motion5.div, { className: "framer-6agcka", "data-framer-name": "Spinner", layoutDependency, layoutId: "F5gWyYcTT", style: { mask: "url('https://framerusercontent.com/images/pGiXYozQ3mE4cilNOItfe2L2fUA.svg') alpha no-repeat center / cover add", WebkitMask: "url('https://framerusercontent.com/images/pGiXYozQ3mE4cilNOItfe2L2fUA.svg') alpha no-repeat center / cover add" }, children: /* @__PURE__ */ _jsx5(MotionDivWithFX, { __framer__loop: animation, __framer__loopEffectEnabled: true, __framer__loopRepeatDelay: 0, __framer__loopRepeatType: "loop", __framer__loopTransition: transition2, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-18aqab3", "data-framer-name": "Conic", layoutDependency, layoutId: "Aaebayspd", style: { background: "conic-gradient(from 180deg at 50% 50%, rgb(68, 204, 255) 0deg, rgb(68, 204, 255) 360deg)", backgroundColor: "rgb(68, 204, 255)", mask: "none", WebkitMask: "none" }, variants: { klvioFlTK: { background: "conic-gradient(from 0deg at 50% 50%, rgba(255, 255, 255, 0) 7.208614864864882deg, rgb(255, 255, 255) 342deg)", backgroundColor: "rgba(0, 0, 0, 0)", mask: "url('https://framerusercontent.com/images/pGiXYozQ3mE4cilNOItfe2L2fUA.svg') alpha no-repeat center / cover add", WebkitMask: "url('https://framerusercontent.com/images/pGiXYozQ3mE4cilNOItfe2L2fUA.svg') alpha no-repeat center / cover add" } }, children: /* @__PURE__ */ _jsx5(motion5.div, { className: "framer-1r4sjfr", "data-framer-name": "Rounding", layoutDependency, layoutId: "vvMLhy71E", style: { backgroundColor: "rgb(255, 255, 255)", borderBottomLeftRadius: 1, borderBottomRightRadius: 1, borderTopLeftRadius: 1, borderTopRightRadius: 1 }, transformTemplate: transformTemplate1 }) }) })] }) }) }) });
});
var css13 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-GdT3P.framer-my2n2e, .framer-GdT3P .framer-my2n2e { display: block; }", ".framer-GdT3P.framer-4lucl9 { align-content: center; align-items: center; cursor: pointer; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 0px; height: 58px; justify-content: center; overflow: hidden; padding: 18px; position: relative; width: 240px; will-change: var(--framer-will-change-override, transform); }", ".framer-GdT3P .framer-o38np2 { -webkit-user-select: none; flex: none; height: auto; left: 50%; position: absolute; top: -22px; user-select: none; white-space: pre; width: auto; z-index: 1; }", ".framer-GdT3P .framer-1zgpu1 { -webkit-user-select: none; flex: none; height: auto; position: relative; user-select: none; white-space: pre; width: auto; }", ".framer-GdT3P .framer-6agcka { aspect-ratio: 1 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 20px); overflow: hidden; position: relative; width: 20px; }", ".framer-GdT3P .framer-18aqab3 { bottom: 0px; flex: none; left: 0px; overflow: visible; position: absolute; right: 0px; top: 0px; }", ".framer-GdT3P .framer-1r4sjfr { aspect-ratio: 1 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 2px); left: 50%; overflow: visible; position: absolute; top: 0px; width: 2px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-GdT3P.framer-4lucl9 { gap: 0px; } .framer-GdT3P.framer-4lucl9 > * { margin: 0px; margin-left: calc(0px / 2); margin-right: calc(0px / 2); } .framer-GdT3P.framer-4lucl9 > :first-child { margin-left: 0px; } .framer-GdT3P.framer-4lucl9 > :last-child { margin-right: 0px; } }", ".framer-GdT3P.framer-v-1rmx67o.framer-4lucl9, .framer-GdT3P.framer-v-2gmwtk.framer-4lucl9, .framer-GdT3P.framer-v-p8053s.framer-4lucl9, .framer-GdT3P.framer-v-1h3xpm4.framer-4lucl9 { cursor: unset; }", ".framer-GdT3P.framer-v-1rmx67o .framer-18aqab3 { overflow: hidden; }", ".framer-GdT3P.framer-v-4lucl9.hover .framer-o38np2 { left: unset; position: relative; top: unset; }", ".framer-GdT3P.framer-v-4lucl9.hover .framer-1zgpu1 { left: 50%; position: absolute; top: 59px; z-index: 1; }"];
var FramerZsPpcvZL7 = withCSS5(Component5, css13, "framer-GdT3P");
var stdin_default7 = FramerZsPpcvZL7;
FramerZsPpcvZL7.displayName = "Large button submit";
FramerZsPpcvZL7.defaultProps = { height: 58, width: 240 };
addPropertyControls5(FramerZsPpcvZL7, { variant: { options: ["wYDONdaYp", "klvioFlTK", "eaKPgDO7P", "dq7iWPLH9", "wDK8O1Ezz"], optionTitles: ["Default", "Loading", "Disabled", "Success", "Error"], title: "Variant", type: ControlType5.Enum }, X9Xju9FBn: { defaultValue: "Submit", displayTextArea: false, title: "Title", type: ControlType5.String }, GNYtni0Of: { defaultValue: "rgb(255, 255, 255)", title: "Color", type: ControlType5.Color }, MoodCIitJ: { defaultValue: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", title: "BG", type: ControlType5.Color }, xKHL3eyCS: { defaultValue: 'var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)) /* {"name":"White"} */', title: "Hover color", type: ControlType5.Color }, UZBAPRl6f: { defaultValue: "rgb(0, 0, 0)", title: "Hover BG", type: ControlType5.Color } });
addFonts5(FramerZsPpcvZL7, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/hyOgCu0Xnghbimh0pE8QTvtt2AU.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/NeGmSOXrPBfEFIy5YZeHq17LEDA.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/oYaAX5himiTPYuN8vLWnqBbfD2s.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/lEJLP4R0yuCaMCjSXYHtJw72M.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/cRJyLNuTJR5jbyKzGi33wU9cqIQ.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/1ZFS7N918ojhhd0nQWdj3jz4w.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/A0Wcc7NgXMjUuFdquHDrIZpzZw0.woff2", weight: "600" }] }], { supportsExplicitInterCodegen: true });

// /:https://framerusercontent.com/modules/44urrVjRtyCNsT713Qdf/lVfX2LlSRSXVPq0OXNzK/q5ePSxRPK.js
var LogoFonts = getFonts4(stdin_default5);
var LargeButtonSubmitFonts = getFonts4(stdin_default7);
var MotionDivWithFX2 = withFX2(motion6.div);
var RichTextWithFX = withFX2(RichText5);
var AnimatedLineFonts = getFonts4(stdin_default);
var AnimatedLineWithVariantAppearEffect = withVariantAppearEffect(stdin_default);
var ContactSectionItemFonts = getFonts4(stdin_default6);
var SmartComponentScopedContainerWithFX = withFX2(SmartComponentScopedContainer4);
var ContactCardFonts = getFonts4(stdin_default4);
var GradientBackgroundFonts = getFonts4(stdin_default3);
var MotionSectionWithFX = withFX2(motion6.section);
var cycleOrder6 = ["yF5sJ1EZX", "mIxVoKe0g", "XGTRK4uxq"];
var serializationHash6 = "framer-3ZPt7";
var variantClassNames6 = { mIxVoKe0g: "framer-v-1kyjdo6", XGTRK4uxq: "framer-v-4xp2ac", yF5sJ1EZX: "framer-v-1isdow" };
function addPropertyOverrides6(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var animation2 = { opacity: 0, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, x: 0, y: 0 };
var transition16 = { damping: 35, delay: 0.2, mass: 1, stiffness: 200, type: "spring" };
var animation1 = { opacity: 0, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, transition: transition16, x: 0, y: 0 };
var transition22 = { bounce: 0.2, delay: 0, duration: 0.4, type: "spring" };
var transition3 = { bounce: 0.1, delay: 0.1, duration: 1.2, type: "spring" };
var formVariants = (form, variants, currentVariant) => {
  switch (form.state) {
    case "success":
      return variants.success ?? currentVariant;
    case "pending":
      return variants.pending ?? currentVariant;
    case "error":
      return variants.error ?? currentVariant;
    case "incomplete":
      return variants.incomplete ?? currentVariant;
  }
};
var transition4 = { bounce: 0.1, delay: 0, duration: 1.2, type: "spring" };
var transition5 = { bounce: 0.1, delay: 0.2, duration: 1.2, type: "spring" };
var transition6 = { bounce: 0.1, delay: 0.3, duration: 1.2, type: "spring" };
var transition7 = { bounce: 0.1, delay: 0.4, duration: 1.2, type: "spring" };
var transition8 = { bounce: 0.1, delay: 0.6, duration: 1.2, type: "spring" };
var Transition6 = ({ value, children }) => {
  const config = React6.useContext(MotionConfigContext6);
  const transition = value ?? config.transition;
  const contextValue = React6.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx6(MotionConfigContext6.Provider, { value: contextValue, children });
};
var Variants6 = motion6.create(React6.Fragment);
var humanReadableVariantMap6 = { Desktop: "yF5sJ1EZX", Phone: "XGTRK4uxq", Tablet: "mIxVoKe0g" };
var getProps6 = ({ height, id, width, ...props }) => {
  return { ...props, variant: humanReadableVariantMap6[props.variant] ?? props.variant ?? "yF5sJ1EZX" };
};
var createLayoutDependency6 = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component6 = /* @__PURE__ */ React6.forwardRef(function(props, ref) {
  const fallbackRef = useRef6(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React6.useId();
  const { activeLocale, setLocale } = useLocaleInfo6();
  const componentViewport = useComponentViewport6();
  const { style, className: className9, layoutId, variant, ...restProps } = getProps6(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState6({ cycleOrder: cycleOrder6, defaultVariant: "yF5sJ1EZX", ref: refBinding, variant, variantClassNames: variantClassNames6 });
  const layoutDependency = createLayoutDependency6(props, variants);
  const sharedStyleClassNames = [className6, className2, className8, className3, className7, className];
  const scopingClassNames = cx6(serializationHash6, ...sharedStyleClassNames);
  const isDisplayed = () => {
    if (baseVariant === "XGTRK4uxq") return false;
    return true;
  };
  return /* @__PURE__ */ _jsx6(LayoutGroup6, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx6(Variants6, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx6(Transition6, { value: transition22, children: /* @__PURE__ */ _jsxs5(MotionSectionWithFX, { ...restProps, ...gestureHandlers, __framer__animate: { transition: transition16 }, __framer__animateOnce: true, __framer__enter: animation2, __framer__exit: animation1, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: cx6(scopingClassNames, "framer-1isdow", className9, classNames), "data-framer-name": "Desktop", layoutDependency, layoutId: "yF5sJ1EZX", ref: refBinding, style: { backgroundColor: "var(--token-eea70a16-506d-4b3b-87b7-e85e653a6e7c, rgb(245, 245, 245))", ...style }, ...addPropertyOverrides6({ mIxVoKe0g: { "data-framer-name": "Tablet" }, XGTRK4uxq: { "data-framer-name": "Phone" } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsxs5(motion6.div, { className: "framer-1ozc41g", "data-framer-name": "Container", layoutDependency, layoutId: "AXcrMuaO0", children: [/* @__PURE__ */ _jsxs5(MotionDivWithFX2, { __framer__animate: { transition: transition3 }, __framer__animateOnce: true, __framer__enter: animation2, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-eu98t", "data-framer-name": "Form", layoutDependency, layoutId: "mrDCdc9vv", children: [/* @__PURE__ */ _jsxs5(motion6.div, { className: "framer-85f0w8", "data-framer-name": "Container", layoutDependency, layoutId: "CPdlZ4yfx", children: [/* @__PURE__ */ _jsx6(FormContainer, { action: "https://api.framer.com/forms/v1/forms/fac8365e-8a84-4118-8589-e53c903a2e72/submit", className: "framer-1jgjsc0", layoutDependency, layoutId: "uJhyfwtZC", style: { backgroundColor: "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", borderBottomLeftRadius: 18, borderBottomRightRadius: 18, borderTopLeftRadius: 18, borderTopRightRadius: 18 }, variants: { XGTRK4uxq: { borderBottomLeftRadius: 12, borderBottomRightRadius: 12, borderTopLeftRadius: 12, borderTopRightRadius: 12 } }, children: (formState) => /* @__PURE__ */ _jsxs5(_Fragment, { children: [/* @__PURE__ */ _jsxs5(motion6.div, { className: "framer-10kx5zv", "data-framer-name": "Heading", layoutDependency, layoutId: "l7ro1OGgQ", children: [/* @__PURE__ */ _jsx6(ComponentViewportProvider4, { height: 22, y: (componentViewport?.y || 0) + (200 + ((componentViewport?.height || 1296) - 400 - 830.48) / 2) + 0 + 0 + 0 + 0 + 40 + 0 + 0 + 0, ...addPropertyOverrides6({ mIxVoKe0g: { y: (componentViewport?.y || 0) + (100 + ((componentViewport?.height || 1070) - 200 - 728.48) / 2) + 0 + 0 + 0 + 0 + 32 + 0 + 0 + 0 }, XGTRK4uxq: { y: (componentViewport?.y || 0) + (20 + ((componentViewport?.height || 1548) - 40 - 1522.28) / 2) + 0 + 0 + 0 + 0 + 0 + 30 + 0 + 0 + 0 } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx6(SmartComponentScopedContainer4, { className: "framer-1to4b2v-container", layoutDependency, layoutId: "Js4pOljq5-container", nodeId: "Js4pOljq5", rendersWithMotion: true, scopeId: "q5ePSxRPK", children: /* @__PURE__ */ _jsx6(stdin_default5, { height: "100%", HZSElEzAS: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", id: "Js4pOljq5", layoutId: "Js4pOljq5", variant: "evMN1mTNM", width: "100%", ...addPropertyOverrides6({ mIxVoKe0g: { variant: "ZPM_PrHzb" }, XGTRK4uxq: { variant: "wNhkD5cob" } }, baseVariant, gestureVariant) }) }) }), /* @__PURE__ */ _jsx6(RichText5, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx6(React6.Fragment, { children: /* @__PURE__ */ _jsxs5(motion6.p, { className: "framer-styles-preset-hik9eh", "data-styles-preset": "zgy6bak25", children: ["Have a project ", /* @__PURE__ */ _jsx6(motion6.span, { style: { "--framer-text-color": "var(--extracted-1w3ko1f, rgba(10, 10, 10, 0.6))" }, children: "in mind?" })] }) }), className: "framer-6pcljh", "data-framer-name": "Form Title", fonts: ["Inter"], layoutDependency, layoutId: "pfibxUdoa", style: { "--extracted-1w3ko1f": "rgba(10, 10, 10, 0.6)", "--framer-paragraph-spacing": "0px" }, verticalAlignment: "top", withExternalLayout: true })] }), /* @__PURE__ */ _jsxs5(motion6.div, { className: "framer-196j8ry", "data-framer-name": "Inputs", layoutDependency, layoutId: "FHRxKwCeo", children: [/* @__PURE__ */ _jsxs5(motion6.label, { className: "framer-erbiyd", layoutDependency, layoutId: "TCUpDk9GL", children: [/* @__PURE__ */ _jsx6(RichText5, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx6(React6.Fragment, { children: /* @__PURE__ */ _jsx6(motion6.p, { className: "framer-styles-preset-2s58fc", "data-styles-preset": "svYtzYwMA", children: "Your name*" }) }), className: "framer-vjzsr1", "data-framer-name": "Name Label", fonts: ["Inter"], layoutDependency, layoutId: "jF5KPx9d_", style: { "--framer-paragraph-spacing": "0px" }, verticalAlignment: "top", withExternalLayout: true }), /* @__PURE__ */ _jsx6(FormPlainTextInput, { className: "framer-5iy7ol", inputName: "Name", layoutDependency, layoutId: "l0URk7BFJ", placeholder: "John Doe", required: true, style: { "--framer-input-background": "rgb(245, 245, 245)", "--framer-input-border-radius-bottom-left": "10px", "--framer-input-border-radius-bottom-right": "10px", "--framer-input-border-radius-top-left": "10px", "--framer-input-border-radius-top-right": "10px", "--framer-input-font-color": "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", "--framer-input-icon-color": "rgb(153, 153, 153)", "--framer-input-placeholder-color": "rgb(153, 153, 153)" }, type: "text" })] }), /* @__PURE__ */ _jsxs5(motion6.label, { className: "framer-1an6z9p", layoutDependency, layoutId: "YO5Yoj5Li", children: [/* @__PURE__ */ _jsx6(RichText5, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx6(React6.Fragment, { children: /* @__PURE__ */ _jsx6(motion6.p, { className: "framer-styles-preset-2s58fc", "data-styles-preset": "svYtzYwMA", children: "E-mail*" }) }), className: "framer-n8v7pc", "data-framer-name": "Name Label", fonts: ["Inter"], layoutDependency, layoutId: "MDKlgKJPs", style: { "--framer-paragraph-spacing": "0px" }, verticalAlignment: "top", withExternalLayout: true }), /* @__PURE__ */ _jsx6(FormPlainTextInput, { className: "framer-5rqycq", inputName: "E-mail", layoutDependency, layoutId: "rYDUSXxHe", placeholder: "<EMAIL>", required: true, style: { "--framer-input-background": "rgb(245, 245, 245)", "--framer-input-border-radius-bottom-left": "10px", "--framer-input-border-radius-bottom-right": "10px", "--framer-input-border-radius-top-left": "10px", "--framer-input-border-radius-top-right": "10px", "--framer-input-font-color": "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", "--framer-input-icon-color": "rgb(153, 153, 153)", "--framer-input-placeholder-color": "rgb(153, 153, 153)" }, type: "email" })] }), /* @__PURE__ */ _jsxs5(motion6.label, { className: "framer-1i4wi2g", layoutDependency, layoutId: "Dpjr6jcqU", children: [/* @__PURE__ */ _jsx6(RichText5, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx6(React6.Fragment, { children: /* @__PURE__ */ _jsx6(motion6.p, { className: "framer-styles-preset-2s58fc", "data-styles-preset": "svYtzYwMA", children: "Message" }) }), className: "framer-2sqwk2", "data-framer-name": "Name Label", fonts: ["Inter"], layoutDependency, layoutId: "jv1r_XvGI", style: { "--framer-paragraph-spacing": "0px" }, verticalAlignment: "top", withExternalLayout: true }), /* @__PURE__ */ _jsx6(FormPlainTextInput, { className: "framer-102j1h0", inputName: "Message", layoutDependency, layoutId: "l1YPOt0HQ", placeholder: "Your message", required: true, style: { "--framer-input-background": "rgb(245, 245, 245)", "--framer-input-border-radius-bottom-left": "10px", "--framer-input-border-radius-bottom-right": "10px", "--framer-input-border-radius-top-left": "10px", "--framer-input-border-radius-top-right": "10px", "--framer-input-font-color": "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", "--framer-input-icon-color": "rgb(153, 153, 153)", "--framer-input-placeholder-color": "rgb(153, 153, 153)" }, type: "text" })] })] }), /* @__PURE__ */ _jsx6(ComponentViewportProvider4, { height: 58, width: `calc(max(max((min(max(${componentViewport?.width || "100vw"} - 72px, 1px), 1520px) - 4px) / 2, 1px) * 0.69, 1px) - 80px)`, y: (componentViewport?.y || 0) + (200 + ((componentViewport?.height || 1296) - 400 - 830.48) / 2) + 0 + 0 + 0 + 0 + 40 + 591.8, ...addPropertyOverrides6({ mIxVoKe0g: { width: `calc(max(max((min(max(${componentViewport?.width || "100vw"} - 60px, 1px), 1520px) - 4px) / 2, 1px) * 0.87, 1px) - 64px)`, y: (componentViewport?.y || 0) + (100 + ((componentViewport?.height || 1070) - 200 - 728.48) / 2) + 0 + 0 + 0 + 0 + 32 + 554.8 }, XGTRK4uxq: { width: `calc(max(min(max(${componentViewport?.width || "100vw"} - 40px, 1px), 1520px), 1px) - 60px)`, y: (componentViewport?.y || 0) + (20 + ((componentViewport?.height || 1548) - 40 - 1522.28) / 2) + 0 + 0 + 0 + 0 + 0 + 30 + 561.8 } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx6(SmartComponentScopedContainer4, { className: "framer-wcxm05-container", layoutDependency, layoutId: "Ep5RxkbIL-container", nodeId: "Ep5RxkbIL", rendersWithMotion: true, scopeId: "q5ePSxRPK", children: /* @__PURE__ */ _jsx6(stdin_default7, { GNYtni0Of: "rgb(255, 255, 255)", height: "100%", id: "Ep5RxkbIL", layoutId: "Ep5RxkbIL", MoodCIitJ: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", style: { width: "100%" }, type: "submit", UZBAPRl6f: "rgb(0, 0, 0)", variant: formVariants(formState, { pending: "klvioFlTK", success: "dq7iWPLH9" }, "wYDONdaYp"), width: "100%", X9Xju9FBn: "Send Message", xKHL3eyCS: "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))" }) }) }), /* @__PURE__ */ _jsx6(RichText5, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx6(React6.Fragment, { children: /* @__PURE__ */ _jsxs5(motion6.p, { className: "framer-styles-preset-2s58fc", "data-styles-preset": "svYtzYwMA", children: [/* @__PURE__ */ _jsx6(motion6.span, { style: { "--framer-text-color": "var(--extracted-1w3ko1f, rgba(10, 10, 10, 0.6))" }, children: "By submitting, you agree to our" }), /* @__PURE__ */ _jsx6(motion6.span, { style: { "--framer-text-color": "var(--extracted-3sq8v0, rgba(0, 0, 0, 0.6))" }, children: " " }), /* @__PURE__ */ _jsx6(Link, { href: { pathVariables: { TMEoQtt6b: "terms-of-service" }, unresolvedPathSlugs: { TMEoQtt6b: { collectionId: "op7RHXBeI", collectionItemId: "SYN_Cviyw" } }, webPageId: "CmPsU4Vle" }, motionChild: true, nodeId: "gunISLpQG", openInNewTab: false, scopeId: "q5ePSxRPK", smoothScroll: false, children: /* @__PURE__ */ _jsx6(motion6.a, { className: "framer-styles-preset-1wi7vce", "data-styles-preset": "nCQNaN8LD", children: "Terms" }) }), " ", /* @__PURE__ */ _jsx6(motion6.span, { style: { "--framer-text-color": "var(--extracted-1ais0t9, rgba(10, 10, 10, 0.6))" }, children: "and" }), " ", /* @__PURE__ */ _jsx6(Link, { href: { pathVariables: { TMEoQtt6b: "privacy-policy" }, unresolvedPathSlugs: { TMEoQtt6b: { collectionId: "op7RHXBeI", collectionItemId: "EnwiwcuRN" } }, webPageId: "CmPsU4Vle" }, motionChild: true, nodeId: "gunISLpQG", openInNewTab: false, scopeId: "q5ePSxRPK", smoothScroll: false, children: /* @__PURE__ */ _jsx6(motion6.a, { className: "framer-styles-preset-1wi7vce", "data-styles-preset": "nCQNaN8LD", children: "Privacy Policy" }) }), "."] }) }), className: "framer-1lqx3gx", "data-framer-name": "Terms and Privacy Policy", fonts: ["Inter"], layoutDependency, layoutId: "gunISLpQG", style: { "--extracted-1ais0t9": "rgba(10, 10, 10, 0.6)", "--extracted-1w3ko1f": "rgba(10, 10, 10, 0.6)", "--extracted-3sq8v0": "rgba(0, 0, 0, 0.6)", "--framer-paragraph-spacing": "0px" }, verticalAlignment: "top", withExternalLayout: true })] }) }), isDisplayed() && /* @__PURE__ */ _jsx6(motion6.div, { className: "framer-1yz6f84", "data-framer-name": "Filler", layoutDependency, layoutId: "Lch6SDI0b" })] }), isDisplayed() && /* @__PURE__ */ _jsx6(motion6.div, { className: "framer-v9n5lz", "data-framer-name": "Filler", layoutDependency, layoutId: "PDLt32Cuk" }), isDisplayed() && /* @__PURE__ */ _jsx6(RichText5, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx6(React6.Fragment, { children: /* @__PURE__ */ _jsx6(motion6.p, { className: "framer-styles-preset-txwsq6", "data-styles-preset": "fDRzSjw63", children: "\xA9 2025 fabrica\xAE Studio" }) }), className: "framer-1294aeh", "data-framer-name": "Copyright Text", fonts: ["Inter"], layoutDependency, layoutId: "xwV_4d4KP", style: { "--framer-paragraph-spacing": "0px", opacity: 0.8 }, verticalAlignment: "top", withExternalLayout: true })] }), /* @__PURE__ */ _jsxs5(motion6.div, { className: "framer-nkouq", "data-framer-name": "Content", layoutDependency, layoutId: "dppKUFre0", children: [/* @__PURE__ */ _jsx6(RichTextWithFX, { __framer__animate: { transition: transition4 }, __framer__animateOnce: true, __framer__enter: animation2, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0, __fromCanvasComponent: true, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, children: /* @__PURE__ */ _jsx6(React6.Fragment, { children: /* @__PURE__ */ _jsx6(motion6.h2, { className: "framer-styles-preset-1yvd34u", "data-styles-preset": "GKtOymhXV", style: { "--framer-text-color": "var(--extracted-1of0zx5, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Let\u2019s talk." }) }), className: "framer-1n4c633", fonts: ["Inter"], layoutDependency, layoutId: "llL1UXRqy", style: { "--extracted-1of0zx5": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", "--framer-paragraph-spacing": "0px" }, verticalAlignment: "top", withExternalLayout: true }), /* @__PURE__ */ _jsxs5(motion6.div, { className: "framer-1ds7la7", "data-framer-name": "Text", layoutDependency, layoutId: "RvVXREfO9", children: [/* @__PURE__ */ _jsxs5(motion6.div, { className: "framer-bcl97z", "data-framer-name": "Description", layoutDependency, layoutId: "HBz4AxAjc", children: [/* @__PURE__ */ _jsx6(RichTextWithFX, { __framer__animate: { transition: transition5 }, __framer__animateOnce: true, __framer__enter: animation2, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0, __fromCanvasComponent: true, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, children: /* @__PURE__ */ _jsx6(React6.Fragment, { children: /* @__PURE__ */ _jsxs5(motion6.p, { className: "framer-styles-preset-1rii1wr", "data-styles-preset": "pAxoS1kOX", style: { "--framer-text-color": "var(--extracted-r6o4lv, rgba(255, 255, 255, 0.7))" }, children: [/* @__PURE__ */ _jsx6(motion6.span, { style: { "--framer-text-color": "var(--extracted-1w3ko1f, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Tell us about your project" }), "\u2014whether it\u2019s a website, SEO, or marketing."] }) }), className: "framer-eat9pp", fonts: ["Inter"], layoutDependency, layoutId: "ytXDKof2j", style: { "--extracted-1w3ko1f": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", "--extracted-r6o4lv": "rgba(255, 255, 255, 0.7)", "--framer-paragraph-spacing": "0px" }, verticalAlignment: "top", withExternalLayout: true }), /* @__PURE__ */ _jsx6(ComponentViewportProvider4, { height: 1, width: `max((min(max(${componentViewport?.width || "100vw"} - 72px, 1px), 1520px) - 4px) / 2, 1px)`, y: (componentViewport?.y || 0) + (200 + ((componentViewport?.height || 1296) - 400 - 830.48) / 2) + 0 + 0 + 192.48 + 0 + 0 + 0 + 240, ...addPropertyOverrides6({ mIxVoKe0g: { width: `max((min(max(${componentViewport?.width || "100vw"} - 60px, 1px), 1520px) - 4px) / 2, 1px)`, y: (componentViewport?.y || 0) + (100 + ((componentViewport?.height || 1070) - 200 - 728.48) / 2) + 0 + 0 + 172.48 + 0 + 0 + 0 + 224 }, XGTRK4uxq: { width: `min(max(${componentViewport?.width || "100vw"} - 40px, 1px), 1520px)`, y: (componentViewport?.y || 0) + (20 + ((componentViewport?.height || 1548) - 40 - 1522.28) / 2) + 0 + 839.8 + 0 + 162.48 + 0 + 0 + 0 + 214 } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx6(SmartComponentScopedContainer4, { className: "framer-1jq32c0-container", layoutDependency, layoutId: "MwVDSFBr5-container", nodeId: "MwVDSFBr5", rendersWithMotion: true, scopeId: "q5ePSxRPK", children: /* @__PURE__ */ _jsx6(AnimatedLineWithVariantAppearEffect, { __framer__animateOnce: true, __framer__obscuredVariantId: "KTHclA_ym", __framer__threshold: 0.5, __framer__variantAppearEffectEnabled: true, __framer__visibleVariantId: "W8r8zwETw", eV0W9xNCm: 0.1, height: "100%", id: "MwVDSFBr5", L7khgg0dA: "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", layoutId: "MwVDSFBr5", style: { width: "100%" }, variant: "W8r8zwETw", width: "100%" }) }) }), /* @__PURE__ */ _jsxs5(motion6.div, { className: "framer-v29o3c", "data-framer-name": "Items", layoutDependency, layoutId: "UKiMLiXwA", children: [/* @__PURE__ */ _jsx6(ComponentViewportProvider4, { height: 66, width: `max((max((min(max(${componentViewport?.width || "100vw"} - 72px, 1px), 1520px) - 4px) / 2, 1px) - 4px) / 2, 1px)`, y: (componentViewport?.y || 0) + (200 + ((componentViewport?.height || 1296) - 400 - 830.48) / 2) + 0 + 0 + 192.48 + 0 + 0 + 0 + 301 + 0, ...addPropertyOverrides6({ mIxVoKe0g: { width: `max((max((min(max(${componentViewport?.width || "100vw"} - 60px, 1px), 1520px) - 4px) / 2, 1px) - 4px) / 2, 1px)`, y: (componentViewport?.y || 0) + (100 + ((componentViewport?.height || 1070) - 200 - 728.48) / 2) + 0 + 0 + 172.48 + 0 + 0 + 0 + 269 + 0 }, XGTRK4uxq: { width: `max((min(max(${componentViewport?.width || "100vw"} - 40px, 1px), 1520px) - 24px) / 2, 1px)`, y: (componentViewport?.y || 0) + (20 + ((componentViewport?.height || 1548) - 40 - 1522.28) / 2) + 0 + 839.8 + 0 + 162.48 + 0 + 0 + 0 + 249 + 0 } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx6(SmartComponentScopedContainerWithFX, { __framer__animate: { transition: transition6 }, __framer__animateOnce: true, __framer__enter: animation2, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-omss02-container", layoutDependency, layoutId: "yXvtWtkJM-container", nodeId: "yXvtWtkJM", rendersWithMotion: true, scopeId: "q5ePSxRPK", children: /* @__PURE__ */ _jsx6(stdin_default6, { D4TTIBv6c: "Quick response.", dBJ7yrzpe: "Speedometer", height: "100%", id: "yXvtWtkJM", layoutId: "yXvtWtkJM", style: { width: "100%" }, variant: "di1TdPrBR", width: "100%", WJwcqyIbg: "If you\u2019re ready to create and collaborate, we\u2019d love to hear from you.", ...addPropertyOverrides6({ XGTRK4uxq: { variant: "EMSWDxYWx" } }, baseVariant, gestureVariant) }) }) }), /* @__PURE__ */ _jsx6(ComponentViewportProvider4, { height: 66, width: `max((max((min(max(${componentViewport?.width || "100vw"} - 72px, 1px), 1520px) - 4px) / 2, 1px) - 4px) / 2, 1px)`, y: (componentViewport?.y || 0) + (200 + ((componentViewport?.height || 1296) - 400 - 830.48) / 2) + 0 + 0 + 192.48 + 0 + 0 + 0 + 301 + 0, ...addPropertyOverrides6({ mIxVoKe0g: { width: `max((max((min(max(${componentViewport?.width || "100vw"} - 60px, 1px), 1520px) - 4px) / 2, 1px) - 4px) / 2, 1px)`, y: (componentViewport?.y || 0) + (100 + ((componentViewport?.height || 1070) - 200 - 728.48) / 2) + 0 + 0 + 172.48 + 0 + 0 + 0 + 269 + 0 }, XGTRK4uxq: { width: `max((min(max(${componentViewport?.width || "100vw"} - 40px, 1px), 1520px) - 24px) / 2, 1px)`, y: (componentViewport?.y || 0) + (20 + ((componentViewport?.height || 1548) - 40 - 1522.28) / 2) + 0 + 839.8 + 0 + 162.48 + 0 + 0 + 0 + 249 + 0 } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx6(SmartComponentScopedContainerWithFX, { __framer__animate: { transition: transition7 }, __framer__animateOnce: true, __framer__enter: animation2, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-2yovjc-container", layoutDependency, layoutId: "DtOFB9fWe-container", nodeId: "DtOFB9fWe", rendersWithMotion: true, scopeId: "q5ePSxRPK", children: /* @__PURE__ */ _jsx6(stdin_default6, { D4TTIBv6c: "Clear next steps.", dBJ7yrzpe: "Steps", height: "100%", id: "DtOFB9fWe", layoutId: "DtOFB9fWe", style: { width: "100%" }, variant: "di1TdPrBR", width: "100%", WJwcqyIbg: "After the consultation, we\u2019ll provide you with a detailed plan and timeline.", ...addPropertyOverrides6({ XGTRK4uxq: { variant: "EMSWDxYWx" } }, baseVariant, gestureVariant) }) }) })] })] }), /* @__PURE__ */ _jsx6(ComponentViewportProvider4, { height: 161, width: `max((min(max(${componentViewport?.width || "100vw"} - 72px, 1px), 1520px) - 4px) / 2, 1px)`, y: (componentViewport?.y || 0) + (200 + ((componentViewport?.height || 1296) - 400 - 830.48) / 2) + 0 + 0 + 192.48 + 0 + 477, ...addPropertyOverrides6({ mIxVoKe0g: { width: `max((min(max(${componentViewport?.width || "100vw"} - 60px, 1px), 1520px) - 4px) / 2, 1px)`, y: (componentViewport?.y || 0) + (100 + ((componentViewport?.height || 1070) - 200 - 728.48) / 2) + 0 + 0 + 172.48 + 0 + 395 }, XGTRK4uxq: { width: `min(max(${componentViewport?.width || "100vw"} - 40px, 1px), 1520px)`, y: (componentViewport?.y || 0) + (20 + ((componentViewport?.height || 1548) - 40 - 1522.28) / 2) + 0 + 839.8 + 0 + 162.48 + 0 + 359 } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx6(SmartComponentScopedContainerWithFX, { __framer__animate: { transition: transition8 }, __framer__animateOnce: true, __framer__enter: animation2, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-1h2me9d-container", layoutDependency, layoutId: "CmUmadQE_-container", nodeId: "CmUmadQE_", rendersWithMotion: true, scopeId: "q5ePSxRPK", ...addPropertyOverrides6({ mIxVoKe0g: { __framer__styleAppearEffectEnabled: void 0 } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx6(stdin_default4, { FggqkLwNL: "Team Lead", gBH5GgjXs: "Ask directly", height: "100%", i1j3qrwjd: "at fabrica\xAE ", id: "CmUmadQE_", layoutId: "CmUmadQE_", NZwcZIstG: "mailto:<EMAIL>", rGlrlhWc1: "Lauren Thompson", style: { width: "100%" }, variant: "L0JatAwCn", width: "100%", ...addPropertyOverrides6({ XGTRK4uxq: { variant: "eEaIQeMBV" } }, baseVariant, gestureVariant) }) }) })] })] })] }), /* @__PURE__ */ _jsx6(ComponentViewportProvider4, { height: (componentViewport?.height || 1296) - 0, width: `calc(${componentViewport?.width || "100vw"} - 12px)`, y: (componentViewport?.y || 0) + 0, ...addPropertyOverrides6({ mIxVoKe0g: { height: (componentViewport?.height || 1070) - 0, width: `calc(${componentViewport?.width || "100vw"} - 8px)` }, XGTRK4uxq: { height: (componentViewport?.height || 1548) - 0, width: componentViewport?.width || "100vw" } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx6(SmartComponentScopedContainer4, { className: "framer-1fjpw9b-container", layoutDependency, layoutId: "Y9OjCbOQz-container", nodeId: "Y9OjCbOQz", rendersWithMotion: true, scopeId: "q5ePSxRPK", children: /* @__PURE__ */ _jsx6(stdin_default3, { EfstzZdWy: 0.5, fgfZL9eoW: "Upload", height: "100%", id: "Y9OjCbOQz", layoutId: "Y9OjCbOQz", NXkb7129J: true, style: { height: "100%", width: "100%" }, variant: "DQjSdvNWb", width: "100%", ...addPropertyOverrides6({ XGTRK4uxq: { variant: "t56mZZuyf" } }, baseVariant, gestureVariant) }) }) })] }) }) }) });
});
var css14 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-3ZPt7.framer-or6vbq, .framer-3ZPt7 .framer-or6vbq { display: block; }", ".framer-3ZPt7.framer-1isdow { align-content: center; align-items: center; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: center; overflow: hidden; padding: 200px 36px 200px 36px; position: relative; width: 1200px; }", ".framer-3ZPt7 .framer-1ozc41g { align-content: flex-start; align-items: flex-start; display: flex; flex: 1 0 0px; flex-direction: row; flex-wrap: nowrap; gap: 4px; height: min-content; justify-content: flex-start; max-width: 1520px; overflow: visible; padding: 0px; position: relative; width: 1px; z-index: 1; }", ".framer-3ZPt7 .framer-eu98t { align-content: flex-start; align-items: flex-start; align-self: stretch; display: flex; flex: 1 0 0px; flex-direction: column; flex-wrap: nowrap; gap: 4px; height: auto; justify-content: flex-start; overflow: hidden; padding: 0px; position: relative; width: 1px; }", ".framer-3ZPt7 .framer-85f0w8 { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: center; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-3ZPt7 .framer-1jgjsc0 { align-content: flex-start; align-items: flex-start; display: flex; flex: 1 0 0px; flex-direction: column; flex-wrap: nowrap; gap: 30px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 40px; position: relative; width: 1px; will-change: var(--framer-will-change-override, transform); }", ".framer-3ZPt7 .framer-10kx5zv { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 12px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-3ZPt7 .framer-1to4b2v-container { flex: none; height: auto; position: relative; width: auto; }", ".framer-3ZPt7 .framer-6pcljh, .framer-3ZPt7 .framer-1lqx3gx, .framer-3ZPt7 .framer-eat9pp { --framer-text-wrap-override: balance; flex: none; height: auto; position: relative; width: 100%; }", ".framer-3ZPt7 .framer-196j8ry { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 16px; height: min-content; justify-content: center; overflow: hidden; padding: 0px; position: relative; width: 100%; }", ".framer-3ZPt7 .framer-erbiyd, .framer-3ZPt7 .framer-1an6z9p, .framer-3ZPt7 .framer-1i4wi2g { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 12px; height: min-content; justify-content: flex-start; padding: 0px; position: relative; width: 100%; }", ".framer-3ZPt7 .framer-vjzsr1, .framer-3ZPt7 .framer-n8v7pc, .framer-3ZPt7 .framer-2sqwk2 { flex: none; height: auto; position: relative; white-space: pre; width: auto; }", '.framer-3ZPt7 .framer-5iy7ol, .framer-3ZPt7 .framer-5rqycq, .framer-3ZPt7 .framer-102j1h0 { --framer-input-focused-background: var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, #ffffff); --framer-input-focused-border-color: #ebebeb; --framer-input-focused-border-style: solid; --framer-input-focused-border-width: 1px; --framer-input-font-family: "Inter"; --framer-input-font-letter-spacing: -0.04em; --framer-input-font-line-height: 1.2em; --framer-input-font-size: 18px; --framer-input-font-weight: 500; --framer-input-padding: 12px 12px 12px 16px; flex: none; height: 58px; position: relative; width: 100%; }', ".framer-3ZPt7 .framer-wcxm05-container, .framer-3ZPt7 .framer-1h2me9d-container { flex: none; height: auto; position: relative; width: 100%; }", ".framer-3ZPt7 .framer-1yz6f84 { align-self: stretch; flex: none; height: auto; overflow: hidden; position: relative; width: 31%; }", ".framer-3ZPt7 .framer-v9n5lz { flex: 1 0 0px; height: 1px; min-height: 80px; overflow: hidden; position: relative; width: 100%; }", ".framer-3ZPt7 .framer-1294aeh, .framer-3ZPt7 .framer-1n4c633 { flex: none; height: auto; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }", ".framer-3ZPt7 .framer-nkouq { align-content: flex-start; align-items: flex-start; display: flex; flex: 1 0 0px; flex-direction: column; flex-wrap: nowrap; gap: 60px; height: min-content; justify-content: center; overflow: visible; padding: 0px; position: relative; width: 1px; }", ".framer-3ZPt7 .framer-1ds7la7 { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 110px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-3ZPt7 .framer-bcl97z { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 60px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-3ZPt7 .framer-1jq32c0-container { flex: none; height: auto; position: relative; width: 100%; z-index: 1; }", ".framer-3ZPt7 .framer-v29o3c { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 4px; height: min-content; justify-content: center; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-3ZPt7 .framer-omss02-container, .framer-3ZPt7 .framer-2yovjc-container { flex: 1 0 0px; height: auto; position: relative; width: 1px; }", ".framer-3ZPt7 .framer-1fjpw9b-container { bottom: 0px; flex: none; left: 6px; position: absolute; right: 6px; top: 0px; z-index: 0; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-3ZPt7.framer-1isdow, .framer-3ZPt7 .framer-1ozc41g, .framer-3ZPt7 .framer-eu98t, .framer-3ZPt7 .framer-85f0w8, .framer-3ZPt7 .framer-1jgjsc0, .framer-3ZPt7 .framer-10kx5zv, .framer-3ZPt7 .framer-196j8ry, .framer-3ZPt7 .framer-erbiyd, .framer-3ZPt7 .framer-1an6z9p, .framer-3ZPt7 .framer-1i4wi2g, .framer-3ZPt7 .framer-nkouq, .framer-3ZPt7 .framer-1ds7la7, .framer-3ZPt7 .framer-bcl97z, .framer-3ZPt7 .framer-v29o3c { gap: 0px; } .framer-3ZPt7.framer-1isdow > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } .framer-3ZPt7.framer-1isdow > :first-child, .framer-3ZPt7 .framer-1ozc41g > :first-child, .framer-3ZPt7 .framer-85f0w8 > :first-child, .framer-3ZPt7 .framer-v29o3c > :first-child { margin-left: 0px; } .framer-3ZPt7.framer-1isdow > :last-child, .framer-3ZPt7 .framer-1ozc41g > :last-child, .framer-3ZPt7 .framer-85f0w8 > :last-child, .framer-3ZPt7 .framer-v29o3c > :last-child { margin-right: 0px; } .framer-3ZPt7 .framer-1ozc41g > *, .framer-3ZPt7 .framer-v29o3c > * { margin: 0px; margin-left: calc(4px / 2); margin-right: calc(4px / 2); } .framer-3ZPt7 .framer-eu98t > * { margin: 0px; margin-bottom: calc(4px / 2); margin-top: calc(4px / 2); } .framer-3ZPt7 .framer-eu98t > :first-child, .framer-3ZPt7 .framer-1jgjsc0 > :first-child, .framer-3ZPt7 .framer-10kx5zv > :first-child, .framer-3ZPt7 .framer-196j8ry > :first-child, .framer-3ZPt7 .framer-erbiyd > :first-child, .framer-3ZPt7 .framer-1an6z9p > :first-child, .framer-3ZPt7 .framer-1i4wi2g > :first-child, .framer-3ZPt7 .framer-nkouq > :first-child, .framer-3ZPt7 .framer-1ds7la7 > :first-child, .framer-3ZPt7 .framer-bcl97z > :first-child { margin-top: 0px; } .framer-3ZPt7 .framer-eu98t > :last-child, .framer-3ZPt7 .framer-1jgjsc0 > :last-child, .framer-3ZPt7 .framer-10kx5zv > :last-child, .framer-3ZPt7 .framer-196j8ry > :last-child, .framer-3ZPt7 .framer-erbiyd > :last-child, .framer-3ZPt7 .framer-1an6z9p > :last-child, .framer-3ZPt7 .framer-1i4wi2g > :last-child, .framer-3ZPt7 .framer-nkouq > :last-child, .framer-3ZPt7 .framer-1ds7la7 > :last-child, .framer-3ZPt7 .framer-bcl97z > :last-child { margin-bottom: 0px; } .framer-3ZPt7 .framer-85f0w8 > * { margin: 0px; margin-left: calc(0px / 2); margin-right: calc(0px / 2); } .framer-3ZPt7 .framer-1jgjsc0 > * { margin: 0px; margin-bottom: calc(30px / 2); margin-top: calc(30px / 2); } .framer-3ZPt7 .framer-10kx5zv > *, .framer-3ZPt7 .framer-erbiyd > *, .framer-3ZPt7 .framer-1an6z9p > *, .framer-3ZPt7 .framer-1i4wi2g > * { margin: 0px; margin-bottom: calc(12px / 2); margin-top: calc(12px / 2); } .framer-3ZPt7 .framer-196j8ry > * { margin: 0px; margin-bottom: calc(16px / 2); margin-top: calc(16px / 2); } .framer-3ZPt7 .framer-nkouq > *, .framer-3ZPt7 .framer-bcl97z > * { margin: 0px; margin-bottom: calc(60px / 2); margin-top: calc(60px / 2); } .framer-3ZPt7 .framer-1ds7la7 > * { margin: 0px; margin-bottom: calc(110px / 2); margin-top: calc(110px / 2); } }", ".framer-3ZPt7.framer-v-1kyjdo6.framer-1isdow { padding: 100px 30px 100px 30px; width: 810px; }", ".framer-3ZPt7.framer-v-1kyjdo6 .framer-1jgjsc0 { gap: 20px; padding: 32px; }", ".framer-3ZPt7.framer-v-1kyjdo6 .framer-196j8ry { gap: 12px; }", ".framer-3ZPt7.framer-v-1kyjdo6 .framer-erbiyd, .framer-3ZPt7.framer-v-1kyjdo6 .framer-1an6z9p, .framer-3ZPt7.framer-v-1kyjdo6 .framer-1i4wi2g { gap: 9px; }", ".framer-3ZPt7.framer-v-1kyjdo6 .framer-5iy7ol, .framer-3ZPt7.framer-v-1kyjdo6 .framer-5rqycq, .framer-3ZPt7.framer-v-1kyjdo6 .framer-102j1h0, .framer-3ZPt7.framer-v-4xp2ac .framer-5iy7ol, .framer-3ZPt7.framer-v-4xp2ac .framer-5rqycq, .framer-3ZPt7.framer-v-4xp2ac .framer-102j1h0 { --framer-input-font-size: 17px; }", ".framer-3ZPt7.framer-v-1kyjdo6 .framer-1yz6f84 { width: 13%; }", ".framer-3ZPt7.framer-v-1kyjdo6 .framer-nkouq { gap: 40px; }", ".framer-3ZPt7.framer-v-1kyjdo6 .framer-1ds7la7 { gap: 60px; }", ".framer-3ZPt7.framer-v-1kyjdo6 .framer-bcl97z, .framer-3ZPt7.framer-v-4xp2ac .framer-1ds7la7 { gap: 44px; }", ".framer-3ZPt7.framer-v-1kyjdo6 .framer-1fjpw9b-container { left: 4px; right: 4px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-3ZPt7.framer-v-1kyjdo6 .framer-1jgjsc0, .framer-3ZPt7.framer-v-1kyjdo6 .framer-196j8ry, .framer-3ZPt7.framer-v-1kyjdo6 .framer-erbiyd, .framer-3ZPt7.framer-v-1kyjdo6 .framer-1an6z9p, .framer-3ZPt7.framer-v-1kyjdo6 .framer-1i4wi2g, .framer-3ZPt7.framer-v-1kyjdo6 .framer-nkouq, .framer-3ZPt7.framer-v-1kyjdo6 .framer-1ds7la7, .framer-3ZPt7.framer-v-1kyjdo6 .framer-bcl97z { gap: 0px; } .framer-3ZPt7.framer-v-1kyjdo6 .framer-1jgjsc0 > * { margin: 0px; margin-bottom: calc(20px / 2); margin-top: calc(20px / 2); } .framer-3ZPt7.framer-v-1kyjdo6 .framer-1jgjsc0 > :first-child, .framer-3ZPt7.framer-v-1kyjdo6 .framer-196j8ry > :first-child, .framer-3ZPt7.framer-v-1kyjdo6 .framer-erbiyd > :first-child, .framer-3ZPt7.framer-v-1kyjdo6 .framer-1an6z9p > :first-child, .framer-3ZPt7.framer-v-1kyjdo6 .framer-1i4wi2g > :first-child, .framer-3ZPt7.framer-v-1kyjdo6 .framer-nkouq > :first-child, .framer-3ZPt7.framer-v-1kyjdo6 .framer-1ds7la7 > :first-child, .framer-3ZPt7.framer-v-1kyjdo6 .framer-bcl97z > :first-child { margin-top: 0px; } .framer-3ZPt7.framer-v-1kyjdo6 .framer-1jgjsc0 > :last-child, .framer-3ZPt7.framer-v-1kyjdo6 .framer-196j8ry > :last-child, .framer-3ZPt7.framer-v-1kyjdo6 .framer-erbiyd > :last-child, .framer-3ZPt7.framer-v-1kyjdo6 .framer-1an6z9p > :last-child, .framer-3ZPt7.framer-v-1kyjdo6 .framer-1i4wi2g > :last-child, .framer-3ZPt7.framer-v-1kyjdo6 .framer-nkouq > :last-child, .framer-3ZPt7.framer-v-1kyjdo6 .framer-1ds7la7 > :last-child, .framer-3ZPt7.framer-v-1kyjdo6 .framer-bcl97z > :last-child { margin-bottom: 0px; } .framer-3ZPt7.framer-v-1kyjdo6 .framer-196j8ry > * { margin: 0px; margin-bottom: calc(12px / 2); margin-top: calc(12px / 2); } .framer-3ZPt7.framer-v-1kyjdo6 .framer-erbiyd > *, .framer-3ZPt7.framer-v-1kyjdo6 .framer-1an6z9p > *, .framer-3ZPt7.framer-v-1kyjdo6 .framer-1i4wi2g > * { margin: 0px; margin-bottom: calc(9px / 2); margin-top: calc(9px / 2); } .framer-3ZPt7.framer-v-1kyjdo6 .framer-nkouq > * { margin: 0px; margin-bottom: calc(40px / 2); margin-top: calc(40px / 2); } .framer-3ZPt7.framer-v-1kyjdo6 .framer-1ds7la7 > * { margin: 0px; margin-bottom: calc(60px / 2); margin-top: calc(60px / 2); } .framer-3ZPt7.framer-v-1kyjdo6 .framer-bcl97z > * { margin: 0px; margin-bottom: calc(44px / 2); margin-top: calc(44px / 2); } }", ".framer-3ZPt7.framer-v-4xp2ac.framer-1isdow { padding: 20px; width: 390px; }", ".framer-3ZPt7.framer-v-4xp2ac .framer-1ozc41g { flex-direction: column; gap: 60px; }", ".framer-3ZPt7.framer-v-4xp2ac .framer-eu98t { align-self: unset; flex: none; gap: 30px; height: min-content; width: 100%; }", ".framer-3ZPt7.framer-v-4xp2ac .framer-85f0w8 { order: 0; }", ".framer-3ZPt7.framer-v-4xp2ac .framer-1jgjsc0 { gap: 22px; padding: 30px; }", ".framer-3ZPt7.framer-v-4xp2ac .framer-10kx5zv { gap: 8px; }", ".framer-3ZPt7.framer-v-4xp2ac .framer-196j8ry { gap: 14px; }", ".framer-3ZPt7.framer-v-4xp2ac .framer-erbiyd, .framer-3ZPt7.framer-v-4xp2ac .framer-1an6z9p, .framer-3ZPt7.framer-v-4xp2ac .framer-1i4wi2g { gap: 10px; }", ".framer-3ZPt7.framer-v-4xp2ac .framer-nkouq { flex: none; gap: 30px; width: 100%; }", ".framer-3ZPt7.framer-v-4xp2ac .framer-bcl97z { gap: 34px; }", ".framer-3ZPt7.framer-v-4xp2ac .framer-v29o3c { gap: 24px; }", ".framer-3ZPt7.framer-v-4xp2ac .framer-1fjpw9b-container { left: 0px; right: 0px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-3ZPt7.framer-v-4xp2ac .framer-1ozc41g, .framer-3ZPt7.framer-v-4xp2ac .framer-eu98t, .framer-3ZPt7.framer-v-4xp2ac .framer-1jgjsc0, .framer-3ZPt7.framer-v-4xp2ac .framer-10kx5zv, .framer-3ZPt7.framer-v-4xp2ac .framer-196j8ry, .framer-3ZPt7.framer-v-4xp2ac .framer-erbiyd, .framer-3ZPt7.framer-v-4xp2ac .framer-1an6z9p, .framer-3ZPt7.framer-v-4xp2ac .framer-1i4wi2g, .framer-3ZPt7.framer-v-4xp2ac .framer-nkouq, .framer-3ZPt7.framer-v-4xp2ac .framer-1ds7la7, .framer-3ZPt7.framer-v-4xp2ac .framer-bcl97z, .framer-3ZPt7.framer-v-4xp2ac .framer-v29o3c { gap: 0px; } .framer-3ZPt7.framer-v-4xp2ac .framer-1ozc41g > * { margin: 0px; margin-bottom: calc(60px / 2); margin-top: calc(60px / 2); } .framer-3ZPt7.framer-v-4xp2ac .framer-1ozc41g > :first-child, .framer-3ZPt7.framer-v-4xp2ac .framer-eu98t > :first-child, .framer-3ZPt7.framer-v-4xp2ac .framer-1jgjsc0 > :first-child, .framer-3ZPt7.framer-v-4xp2ac .framer-10kx5zv > :first-child, .framer-3ZPt7.framer-v-4xp2ac .framer-196j8ry > :first-child, .framer-3ZPt7.framer-v-4xp2ac .framer-erbiyd > :first-child, .framer-3ZPt7.framer-v-4xp2ac .framer-1an6z9p > :first-child, .framer-3ZPt7.framer-v-4xp2ac .framer-1i4wi2g > :first-child, .framer-3ZPt7.framer-v-4xp2ac .framer-nkouq > :first-child, .framer-3ZPt7.framer-v-4xp2ac .framer-1ds7la7 > :first-child, .framer-3ZPt7.framer-v-4xp2ac .framer-bcl97z > :first-child { margin-top: 0px; } .framer-3ZPt7.framer-v-4xp2ac .framer-1ozc41g > :last-child, .framer-3ZPt7.framer-v-4xp2ac .framer-eu98t > :last-child, .framer-3ZPt7.framer-v-4xp2ac .framer-1jgjsc0 > :last-child, .framer-3ZPt7.framer-v-4xp2ac .framer-10kx5zv > :last-child, .framer-3ZPt7.framer-v-4xp2ac .framer-196j8ry > :last-child, .framer-3ZPt7.framer-v-4xp2ac .framer-erbiyd > :last-child, .framer-3ZPt7.framer-v-4xp2ac .framer-1an6z9p > :last-child, .framer-3ZPt7.framer-v-4xp2ac .framer-1i4wi2g > :last-child, .framer-3ZPt7.framer-v-4xp2ac .framer-nkouq > :last-child, .framer-3ZPt7.framer-v-4xp2ac .framer-1ds7la7 > :last-child, .framer-3ZPt7.framer-v-4xp2ac .framer-bcl97z > :last-child { margin-bottom: 0px; } .framer-3ZPt7.framer-v-4xp2ac .framer-eu98t > *, .framer-3ZPt7.framer-v-4xp2ac .framer-nkouq > * { margin: 0px; margin-bottom: calc(30px / 2); margin-top: calc(30px / 2); } .framer-3ZPt7.framer-v-4xp2ac .framer-1jgjsc0 > * { margin: 0px; margin-bottom: calc(22px / 2); margin-top: calc(22px / 2); } .framer-3ZPt7.framer-v-4xp2ac .framer-10kx5zv > * { margin: 0px; margin-bottom: calc(8px / 2); margin-top: calc(8px / 2); } .framer-3ZPt7.framer-v-4xp2ac .framer-196j8ry > * { margin: 0px; margin-bottom: calc(14px / 2); margin-top: calc(14px / 2); } .framer-3ZPt7.framer-v-4xp2ac .framer-erbiyd > *, .framer-3ZPt7.framer-v-4xp2ac .framer-1an6z9p > *, .framer-3ZPt7.framer-v-4xp2ac .framer-1i4wi2g > * { margin: 0px; margin-bottom: calc(10px / 2); margin-top: calc(10px / 2); } .framer-3ZPt7.framer-v-4xp2ac .framer-1ds7la7 > * { margin: 0px; margin-bottom: calc(44px / 2); margin-top: calc(44px / 2); } .framer-3ZPt7.framer-v-4xp2ac .framer-bcl97z > * { margin: 0px; margin-bottom: calc(34px / 2); margin-top: calc(34px / 2); } .framer-3ZPt7.framer-v-4xp2ac .framer-v29o3c > * { margin: 0px; margin-left: calc(24px / 2); margin-right: calc(24px / 2); } .framer-3ZPt7.framer-v-4xp2ac .framer-v29o3c > :first-child { margin-left: 0px; } .framer-3ZPt7.framer-v-4xp2ac .framer-v29o3c > :last-child { margin-right: 0px; } }", ...css6, ...css2, ...css8, ...css3, ...css7, ...css];
var Framerq5ePSxRPK = withCSS6(Component6, css14, "framer-3ZPt7");
var stdin_default8 = Framerq5ePSxRPK;
Framerq5ePSxRPK.displayName = "Let's talk";
Framerq5ePSxRPK.defaultProps = { height: 1296, width: 1200 };
addPropertyControls6(Framerq5ePSxRPK, { variant: { options: ["yF5sJ1EZX", "mIxVoKe0g", "XGTRK4uxq"], optionTitles: ["Desktop", "Tablet", "Phone"], title: "Variant", type: ControlType6.Enum } });
addFonts6(Framerq5ePSxRPK, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5vvr9Vy74if2I6bQbJvbw7SY1pQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/EOr0mi4hNtlgWNn9if640EZzXCo.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/Y9k9QrlZAqio88Klkmbd8VoMQc.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/OYrD2tBIBPvoJXiIHnLoOXnY9M.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/JeYwfuaPfZHQhEG8U5gtPDZ7WQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/vQyevYAyHtARFwPqUzQGpnDs.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/b6Y37FthZeALduNqHicBT6FutY.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5A3Ce6C9YYmCjpQx9M4inSaKU.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/Qx95Xyt0Ka3SGhinnbXIGpEIyP4.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/6mJuEAguuIuMog10gGvH5d3cl8.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/xYYWaj7wCU5zSQH0eXvSaS19wo.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/otTaNuNpVK4RbdlT7zDDdKvQBA.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/d3tHnaQIAeqiE5hGcRw4mmgWYU.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/DolVirEGb34pEXEp8t8FQBSK4.woff2", weight: "500" }] }, ...LogoFonts, ...LargeButtonSubmitFonts, ...AnimatedLineFonts, ...ContactSectionItemFonts, ...ContactCardFonts, ...GradientBackgroundFonts, ...getFontsFromSharedStyle2(fonts6), ...getFontsFromSharedStyle2(fonts2), ...getFontsFromSharedStyle2(fonts8), ...getFontsFromSharedStyle2(fonts3), ...getFontsFromSharedStyle2(fonts7), ...getFontsFromSharedStyle2(fonts)], { supportsExplicitInterCodegen: true });

// virtual:let-s-talk
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {
  "base": "XGTRK4uxq",
  "md": "mIxVoKe0g",
  "xl": "yF5sJ1EZX"
};
stdin_default8.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default8,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default8, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default8);
export {
  ComponentWithRoot as default
};
