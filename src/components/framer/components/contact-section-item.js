// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";
import {
  Icon
} from "./chunks/chunk-BTSBQWPZ.js";
import {
  className as className2,
  css as css2,
  fonts as fonts2
} from "./chunks/chunk-5L2L6TVR.js";
import {
  className,
  css,
  fonts
} from "./chunks/chunk-SATCLWQE.js";

// virtual:contact-section-item
import { Fragment as Fragment2 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/qYAToHIuf9k41NVgtpE7/Nw8dm4Wk4ng8kkdcRuF6/vTiEQWJOl.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ComponentViewportProvider, ControlType, cx, getFonts, getFontsFromSharedStyle, getPropertyControls, RichText, SmartComponentScopedContainer, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var PhosphorFonts = getFonts(Icon);
var PhosphorControls = getPropertyControls(Icon);
var cycleOrder = ["di1TdPrBR", "EMSWDxYWx"];
var serializationHash = "framer-N2Igf";
var variantClassNames = { di1TdPrBR: "framer-v-75j668", EMSWDxYWx: "framer-v-vb0put" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { bounce: 0.2, delay: 0, duration: 0.4, type: "spring" };
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { Desktop: "di1TdPrBR", Phone: "EMSWDxYWx" };
var getProps = ({ description, height, icon, id, title, width, ...props }) => {
  return { ...props, D4TTIBv6c: title ?? props.D4TTIBv6c ?? "Title", dBJ7yrzpe: icon ?? props.dBJ7yrzpe ?? "Speedometer", variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "di1TdPrBR", WJwcqyIbg: description ?? props.WJwcqyIbg ?? "Description" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className: className3, layoutId, variant, dBJ7yrzpe, D4TTIBv6c, WJwcqyIbg, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "di1TdPrBR", ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [className2, className];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsxs(motion.div, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-75j668", className3, classNames), "data-framer-name": "Desktop", layoutDependency, layoutId: "di1TdPrBR", ref: refBinding, style: { ...style }, ...addPropertyOverrides({ EMSWDxYWx: { "data-framer-name": "Phone" } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsxs(motion.div, { className: "framer-tx0xy4", "data-framer-name": "Quick Response Info", layoutDependency, layoutId: "wIM2zDaPg", children: [/* @__PURE__ */ _jsx(ComponentViewportProvider, { children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-t3xn95-container", isAuthoredByUser: true, isModuleExternal: true, layoutDependency, layoutId: "QpDK4oInL-container", nodeId: "QpDK4oInL", rendersWithMotion: true, scopeId: "vTiEQWJOl", children: /* @__PURE__ */ _jsx(Icon, { color: "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", height: "100%", iconSearch: "speedometer", iconSelection: dBJ7yrzpe, id: "QpDK4oInL", layoutId: "QpDK4oInL", mirrored: false, selectByList: true, style: { height: "100%", width: "100%" }, weight: "regular", width: "100%" }) }) }), /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-9v8dhs", "data-styles-preset": "oFAZmwcVJ", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Quick response." }) }), className: "framer-1g7f53", "data-framer-name": "Quick Response Title", fonts: ["Inter"], layoutDependency, layoutId: "Q_UW0caOT", style: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", "--framer-paragraph-spacing": "0px" }, text: D4TTIBv6c, verticalAlignment: "top", withExternalLayout: true })] }), /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-1mf8d9g", "data-styles-preset": "ypR5VEWEl", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Description" }) }), className: "framer-r0ha9m", "data-framer-name": "Join Us Description", fonts: ["Inter"], layoutDependency, layoutId: "bWj8XdYhC", style: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", "--framer-paragraph-spacing": "0px", opacity: 0.6 }, text: WJwcqyIbg, verticalAlignment: "top", withExternalLayout: true })] }) }) }) });
});
var css3 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-N2Igf.framer-ihn9ej, .framer-N2Igf .framer-ihn9ej { display: block; }", ".framer-N2Igf.framer-75j668 { align-content: flex-start; align-items: flex-start; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 20px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 30px 0px 0px; position: relative; width: 300px; }", ".framer-N2Igf .framer-tx0xy4 { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 16px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-N2Igf .framer-t3xn95-container { flex: none; height: 25px; position: relative; width: 25px; }", ".framer-N2Igf .framer-1g7f53 { flex: 1 0 0px; height: auto; position: relative; white-space: pre-wrap; width: 1px; word-break: break-word; word-wrap: break-word; }", ".framer-N2Igf .framer-r0ha9m { --framer-text-wrap-override: none; flex: none; height: auto; max-width: 280px; position: relative; width: 100%; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-N2Igf.framer-75j668, .framer-N2Igf .framer-tx0xy4 { gap: 0px; } .framer-N2Igf.framer-75j668 > * { margin: 0px; margin-bottom: calc(20px / 2); margin-top: calc(20px / 2); } .framer-N2Igf.framer-75j668 > :first-child { margin-top: 0px; } .framer-N2Igf.framer-75j668 > :last-child { margin-bottom: 0px; } .framer-N2Igf .framer-tx0xy4 > * { margin: 0px; margin-left: calc(16px / 2); margin-right: calc(16px / 2); } .framer-N2Igf .framer-tx0xy4 > :first-child { margin-left: 0px; } .framer-N2Igf .framer-tx0xy4 > :last-child { margin-right: 0px; } }", ".framer-N2Igf.framer-v-vb0put.framer-75j668 { gap: 14px; padding: 0px; }", ".framer-N2Igf.framer-v-vb0put .framer-tx0xy4 { gap: 12px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-N2Igf.framer-v-vb0put.framer-75j668, .framer-N2Igf.framer-v-vb0put .framer-tx0xy4 { gap: 0px; } .framer-N2Igf.framer-v-vb0put.framer-75j668 > * { margin: 0px; margin-bottom: calc(14px / 2); margin-top: calc(14px / 2); } .framer-N2Igf.framer-v-vb0put.framer-75j668 > :first-child { margin-top: 0px; } .framer-N2Igf.framer-v-vb0put.framer-75j668 > :last-child { margin-bottom: 0px; } .framer-N2Igf.framer-v-vb0put .framer-tx0xy4 > * { margin: 0px; margin-left: calc(12px / 2); margin-right: calc(12px / 2); } .framer-N2Igf.framer-v-vb0put .framer-tx0xy4 > :first-child { margin-left: 0px; } .framer-N2Igf.framer-v-vb0put .framer-tx0xy4 > :last-child { margin-right: 0px; } }", ...css2, ...css];
var FramervTiEQWJOl = withCSS(Component, css3, "framer-N2Igf");
var stdin_default = FramervTiEQWJOl;
FramervTiEQWJOl.displayName = "Contact section item";
FramervTiEQWJOl.defaultProps = { height: 66, width: 300 };
addPropertyControls(FramervTiEQWJOl, { variant: { options: ["di1TdPrBR", "EMSWDxYWx"], optionTitles: ["Desktop", "Phone"], title: "Variant", type: ControlType.Enum }, dBJ7yrzpe: PhosphorControls?.["iconSelection"] && { ...PhosphorControls["iconSelection"], defaultValue: "Speedometer", description: void 0, hidden: void 0, title: "Icon" }, D4TTIBv6c: { defaultValue: "Title", displayTextArea: true, title: "Title", type: ControlType.String }, WJwcqyIbg: { defaultValue: "Description", displayTextArea: true, title: "Description", type: ControlType.String } });
addFonts(FramervTiEQWJOl, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5vvr9Vy74if2I6bQbJvbw7SY1pQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/EOr0mi4hNtlgWNn9if640EZzXCo.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/Y9k9QrlZAqio88Klkmbd8VoMQc.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/OYrD2tBIBPvoJXiIHnLoOXnY9M.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/JeYwfuaPfZHQhEG8U5gtPDZ7WQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/vQyevYAyHtARFwPqUzQGpnDs.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/b6Y37FthZeALduNqHicBT6FutY.woff2", weight: "400" }] }, ...PhosphorFonts, ...getFontsFromSharedStyle(fonts2), ...getFontsFromSharedStyle(fonts)], { supportsExplicitInterCodegen: true });

// virtual:contact-section-item
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {
  "base": "EMSWDxYWx",
  "xl": "di1TdPrBR"
};
stdin_default.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default);
export {
  ComponentWithRoot as default
};
