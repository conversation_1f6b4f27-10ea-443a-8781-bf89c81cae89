// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";
import {
  stdin_default as stdin_default2
} from "./chunks/chunk-XKETCZDK.js";
import {
  className as className2,
  css as css2,
  fonts as fonts2
} from "./chunks/chunk-5L2L6TVR.js";
import {
  stdin_default
} from "./chunks/chunk-YNN2WKUD.js";
import {
  className,
  css,
  fonts
} from "./chunks/chunk-VY5WWL2S.js";

// virtual:testimonial
import { Fragment as Fragment2 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/t9kmVKgwV1DLW0GG8nPN/piyVQ3r6E2NxsJQlQ3Pt/fTmrIoq5f.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ComponentViewportProvider, ControlType, cx, getFonts, getFontsFromSharedStyle, getLoadingLazyAtYPosition, Image, RichText, SmartComponentScopedContainer, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";

// /:https://framerusercontent.com/modules/9SnJWnLLsQu1WYKv4TtR/hhvSb1XFv2UspFIa9bFx/fN1_sGlJp.js
import { fontStore } from "unframer";
fontStore.loadFonts(["Inter-Medium", "Inter-Bold", "Inter-BoldItalic", "Inter-MediumItalic"]);
var fonts3 = [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5A3Ce6C9YYmCjpQx9M4inSaKU.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/Qx95Xyt0Ka3SGhinnbXIGpEIyP4.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/6mJuEAguuIuMog10gGvH5d3cl8.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/xYYWaj7wCU5zSQH0eXvSaS19wo.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/otTaNuNpVK4RbdlT7zDDdKvQBA.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/d3tHnaQIAeqiE5hGcRw4mmgWYU.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/DolVirEGb34pEXEp8t8FQBSK4.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/DpPBYI0sL4fYLgAkX8KXOPVt7c.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/4RAEQdEOrcnDkhHiiCbJOw92Lk.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/1K3W8DizY3v4emK8Mb08YHxTbs.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/tUSCtfYVM1I1IchuyCwz9gDdQ.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/VgYFWiwsAC5OYxAycRXXvhze58.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/DXD0Q7LSl7HEvDzucnyLnGBHM.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/GIryZETIX4IFypco5pYZONKhJIo.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/H89BbHkbHDzlxZzxi8uPzTsp90.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/u6gJwDuwB143kpNK1T1MDKDWkMc.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/43sJ6MfOPh1LCJt46OvyDuSbA6o.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/wccHG0r4gBDAIRhfHiOlq6oEkqw.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/WZ367JPwf9bRW6LdTHN8rXgSjw.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/QxmhnWTzLtyjIiZcfaLIJ8EFBXU.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/2A4Xx7CngadFGlVV4xrO06OBHY.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/khkJkwSL66WFg8SX6Wa726c.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/0E7IMbDzcGABpBwwqNEt60wU0w.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/NTJ0nQgIF0gcDelS14zQ9NR9Q.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/QrcNhgEPfRl0LS8qz5Ln8olanl8.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/JEXmejW8mXOYMtt0hyRg811kHac.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/ksvR4VsLksjpSwnC2fPgHRNMw.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/uy9s0iWuxiNnVt8EpTI3gzohpwo.woff2", weight: "500" }] }];
var css3 = ['.framer-XgzOh .framer-styles-preset-1hin0ji:not(.rich-text-wrapper), .framer-XgzOh .framer-styles-preset-1hin0ji.rich-text-wrapper p { --framer-font-family: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-open-type-features: normal; --framer-font-size: 26px; --framer-font-style: normal; --framer-font-style-bold: normal; --framer-font-style-bold-italic: italic; --framer-font-style-italic: italic; --framer-font-variation-axes: normal; --framer-font-weight: 500; --framer-font-weight-bold: 700; --framer-font-weight-bold-italic: 700; --framer-font-weight-italic: 500; --framer-letter-spacing: -0.04em; --framer-line-height: 115%; --framer-paragraph-spacing: 20px; --framer-text-alignment: start; --framer-text-color: #090909; --framer-text-decoration: none; --framer-text-stroke-color: initial; --framer-text-stroke-width: initial; --framer-text-transform: none; }', '@media (max-width: 1199px) and (min-width: 810px) { .framer-XgzOh .framer-styles-preset-1hin0ji:not(.rich-text-wrapper), .framer-XgzOh .framer-styles-preset-1hin0ji.rich-text-wrapper p { --framer-font-family: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-open-type-features: normal; --framer-font-size: 23px; --framer-font-style: normal; --framer-font-style-bold: normal; --framer-font-style-bold-italic: italic; --framer-font-style-italic: italic; --framer-font-variation-axes: normal; --framer-font-weight: 500; --framer-font-weight-bold: 700; --framer-font-weight-bold-italic: 700; --framer-font-weight-italic: 500; --framer-letter-spacing: -0.04em; --framer-line-height: 115%; --framer-paragraph-spacing: 20px; --framer-text-alignment: start; --framer-text-color: #090909; --framer-text-decoration: none; --framer-text-stroke-color: initial; --framer-text-stroke-width: initial; --framer-text-transform: none; } }', '@media (max-width: 809px) and (min-width: 0px) { .framer-XgzOh .framer-styles-preset-1hin0ji:not(.rich-text-wrapper), .framer-XgzOh .framer-styles-preset-1hin0ji.rich-text-wrapper p { --framer-font-family: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-open-type-features: normal; --framer-font-size: 22px; --framer-font-style: normal; --framer-font-style-bold: normal; --framer-font-style-bold-italic: italic; --framer-font-style-italic: italic; --framer-font-variation-axes: normal; --framer-font-weight: 500; --framer-font-weight-bold: 700; --framer-font-weight-bold-italic: 700; --framer-font-weight-italic: 500; --framer-letter-spacing: -0.04em; --framer-line-height: 115%; --framer-paragraph-spacing: 20px; --framer-text-alignment: start; --framer-text-color: #090909; --framer-text-decoration: none; --framer-text-stroke-color: initial; --framer-text-stroke-width: initial; --framer-text-transform: none; } }'];
var className3 = "framer-XgzOh";

// /:https://framerusercontent.com/modules/t9kmVKgwV1DLW0GG8nPN/piyVQ3r6E2NxsJQlQ3Pt/fTmrIoq5f.js
var RatingStarsFonts = getFonts(stdin_default2);
var PlusIconFonts = getFonts(stdin_default);
var enabledGestures = { aUwTuNsVb: { hover: true }, isAPdhbEi: { hover: true } };
var cycleOrder = ["isAPdhbEi", "aUwTuNsVb", "rHtk7mxj3"];
var serializationHash = "framer-k80mU";
var variantClassNames = { aUwTuNsVb: "framer-v-1hfzc71", isAPdhbEi: "framer-v-1xa06lp", rHtk7mxj3: "framer-v-hfi02n" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { delay: 0, duration: 0.45, ease: [0.82, 0.11, 0.37, 0.82], type: "tween" };
var toResponsiveImage = (value) => {
  if (typeof value === "object" && value !== null && typeof value.src === "string") {
    return value;
  }
  return typeof value === "string" ? { src: value } : void 0;
};
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { "Desktop reversed": "aUwTuNsVb", Desktop: "isAPdhbEi", Phone: "rHtk7mxj3" };
var getProps = ({ height, id, image, name1, subtitle, testimomnial, width, ...props }) => {
  return { ...props, CNR_PIjaw: image ?? props.CNR_PIjaw ?? { alt: "", src: "https://framerusercontent.com/images/v2v5H7JyNs32xnkaBSwUWhCt8.jpg" }, gmWSXR53y: name1 ?? props.gmWSXR53y ?? "Name", IUuoDhO3z: subtitle ?? props.IUuoDhO3z ?? "Subtitle", variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "isAPdhbEi", xKE54n4Ed: testimomnial ?? props.xKE54n4Ed ?? "Testimomnial" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className: className4, layoutId, variant, gmWSXR53y, IUuoDhO3z, CNR_PIjaw, xKE54n4Ed, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "isAPdhbEi", enabledGestures, ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [className2, className, className3];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsxs(motion.div, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-1xa06lp", className4, classNames), "data-framer-name": "Desktop", layoutDependency, layoutId: "isAPdhbEi", ref: refBinding, style: { ...style }, ...addPropertyOverrides({ "aUwTuNsVb-hover": { "data-framer-name": void 0 }, "isAPdhbEi-hover": { "data-framer-name": void 0 }, aUwTuNsVb: { "data-framer-name": "Desktop reversed" }, rHtk7mxj3: { "data-framer-name": "Phone" } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsx(motion.div, { className: "framer-1nedhuv", "data-framer-name": "Top", layoutDependency, layoutId: "BjTKylqsP", style: { backgroundColor: "rgb(255, 255, 255)", borderBottomLeftRadius: 18, borderBottomRightRadius: 18, borderTopLeftRadius: 18, borderTopRightRadius: 18 }, variants: { "aUwTuNsVb-hover": { borderTopLeftRadius: 0, borderTopRightRadius: 0 }, "isAPdhbEi-hover": { borderBottomLeftRadius: 0, borderBottomRightRadius: 0 } }, children: /* @__PURE__ */ _jsxs(motion.div, { className: "framer-d927ns", "data-framer-name": "User Info Container", layoutDependency, layoutId: "i8RLZ97bk", children: [/* @__PURE__ */ _jsx(Image, { background: { alt: "", fit: "fill", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0 + (((componentViewport?.height || 642) - 0 - (248 + Math.max(0, ((componentViewport?.height || 642) - 0 - 252) / 1) * 1 + 4)) / 2 + 0 + 0) + 30 + 0 + 70.5), sizes: "46px", ...toResponsiveImage(CNR_PIjaw) }, className: "framer-1ibhtdp", "data-framer-name": "User Image", layoutDependency, layoutId: "C4PhoRQh_", style: { borderBottomLeftRadius: 7, borderBottomRightRadius: 7, borderTopLeftRadius: 7, borderTopRightRadius: 7 }, ...addPropertyOverrides({ "aUwTuNsVb-hover": { background: { alt: "", fit: "fill", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0 + (((componentViewport?.height || 646) - 0 - (Math.max(0, ((componentViewport?.height || 646) - 0 - 248) / 1) * 1 + 248 + 0)) / 2 + Math.max(0, ((componentViewport?.height || 646) - 0 - 248) / 1) * 1 + 0) + 30 + 0 + 70.5), sizes: "46px", ...toResponsiveImage(CNR_PIjaw) } }, "isAPdhbEi-hover": { background: { alt: "", fit: "fill", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0 + (((componentViewport?.height || 646) - 0 - (248 + Math.max(0, ((componentViewport?.height || 646) - 0 - 248) / 1) * 1 + 0)) / 2 + 0 + 0) + 30 + 0 + 70.5), sizes: "46px", ...toResponsiveImage(CNR_PIjaw) } }, aUwTuNsVb: { background: { alt: "", fit: "fill", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0 + (((componentViewport?.height || 642) - 0 - (Math.max(0, ((componentViewport?.height || 642) - 0 - 252) / 1) * 1 + 248 + 4)) / 2 + Math.max(0, ((componentViewport?.height || 642) - 0 - 252) / 1) * 1 + 4) + 30 + 0 + 70.5), sizes: "46px", ...toResponsiveImage(CNR_PIjaw) } }, rHtk7mxj3: { background: { alt: "", fit: "fill", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0 + (((componentViewport?.height || 400) - 0 - (Math.max(0, ((componentViewport?.height || 400) - 0 - 240) / 1) * 1 + 236 + 4)) / 2 + Math.max(0, ((componentViewport?.height || 400) - 0 - 240) / 1) * 1 + 4) + 24 + 0 + 70.5), sizes: "46px", ...toResponsiveImage(CNR_PIjaw) } } }, baseVariant, gestureVariant) }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-oibkzm", "data-framer-name": "User Info", layoutDependency, layoutId: "K4O8UKhji", children: [/* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-9v8dhs", "data-styles-preset": "oFAZmwcVJ", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10)))" }, children: "Name" }) }), className: "framer-oyhvcg", "data-framer-name": "Join Us Title", fonts: ["Inter"], layoutDependency, layoutId: "Wnaj6J5iw", style: { "--extracted-r6o4lv": "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", "--framer-paragraph-spacing": "0px" }, text: gmWSXR53y, verticalAlignment: "top", withExternalLayout: true }), /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-2s58fc", "data-styles-preset": "svYtzYwMA", children: "Subtitle" }) }), className: "framer-1wuq2o9", "data-framer-name": "Join Us Description", fonts: ["Inter"], layoutDependency, layoutId: "S5Aynt694", style: { "--framer-paragraph-spacing": "0px", opacity: 0.6 }, text: IUuoDhO3z, verticalAlignment: "top", withExternalLayout: true })] })] }) }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-1flz52s", "data-framer-name": "Bottom", layoutDependency, layoutId: "eZTZ_5lvB", style: { backgroundColor: "rgb(255, 255, 255)", borderBottomLeftRadius: 18, borderBottomRightRadius: 18, borderTopLeftRadius: 18, borderTopRightRadius: 18 }, variants: { "aUwTuNsVb-hover": { borderBottomLeftRadius: 0, borderBottomRightRadius: 0 }, "isAPdhbEi-hover": { borderTopLeftRadius: 0, borderTopRightRadius: 0 } }, children: [/* @__PURE__ */ _jsxs(motion.div, { className: "framer-jx6g4t", "data-framer-name": "Top", layoutDependency, layoutId: "nyPhRk8yI", children: [/* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 12, width: "56px", y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 642) - 0 - (248 + Math.max(0, ((componentViewport?.height || 642) - 0 - 252) / 1) * 1 + 4)) / 2 + 248 + 4) + 30 + (0 + 0 + (Math.max(0, ((componentViewport?.height || 642) - 0 - 252) / 1) * 1 - 60 - 165.5) / 1 * 0) + 0, ...addPropertyOverrides({ "aUwTuNsVb-hover": { y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 646) - 0 - (Math.max(0, ((componentViewport?.height || 646) - 0 - 248) / 1) * 1 + 248 + 0)) / 2 + 0 + 0) + 30 + (0 + 149.5 + (Math.max(0, ((componentViewport?.height || 646) - 0 - 248) / 1) * 1 - 60 - 165.5) / 1 * 1) + 0 }, "isAPdhbEi-hover": { y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 646) - 0 - (248 + Math.max(0, ((componentViewport?.height || 646) - 0 - 248) / 1) * 1 + 0)) / 2 + 248 + 0) + 30 + (0 + 0 + (Math.max(0, ((componentViewport?.height || 646) - 0 - 248) / 1) * 1 - 60 - 165.5) / 1 * 0) + 0 }, aUwTuNsVb: { y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 642) - 0 - (Math.max(0, ((componentViewport?.height || 642) - 0 - 252) / 1) * 1 + 248 + 4)) / 2 + 0 + 0) + 30 + (0 + 149.5 + (Math.max(0, ((componentViewport?.height || 642) - 0 - 252) / 1) * 1 - 60 - 165.5) / 1 * 1) + 0 }, rHtk7mxj3: { y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 400) - 0 - (Math.max(0, ((componentViewport?.height || 400) - 0 - 240) / 1) * 1 + 236 + 4)) / 2 + 0 + 0) + 24 + (0 + 0 + (Math.max(0, ((componentViewport?.height || 400) - 0 - 240) / 1) * 1 - 48 - 165.5) / 1 * 0) + 0 } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-1efuek3-container", layoutDependency, layoutId: "daMvPQsNb-container", nodeId: "daMvPQsNb", rendersWithMotion: true, scopeId: "fTmrIoq5f", children: /* @__PURE__ */ _jsx(stdin_default2, { height: "100%", id: "daMvPQsNb", layoutId: "daMvPQsNb", style: { height: "100%", width: "100%" }, width: "100%" }) }) }), /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 16, width: "16px", y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 642) - 0 - (248 + Math.max(0, ((componentViewport?.height || 642) - 0 - 252) / 1) * 1 + 4)) / 2 + 248 + 4) + 30 + (0 + 0 + (Math.max(0, ((componentViewport?.height || 642) - 0 - 252) / 1) * 1 - 60 - 165.5) / 1 * 0) + 0, ...addPropertyOverrides({ "aUwTuNsVb-hover": { y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 646) - 0 - (Math.max(0, ((componentViewport?.height || 646) - 0 - 248) / 1) * 1 + 248 + 0)) / 2 + 0 + 0) + 30 + (0 + 149.5 + (Math.max(0, ((componentViewport?.height || 646) - 0 - 248) / 1) * 1 - 60 - 165.5) / 1 * 1) + 0 }, "isAPdhbEi-hover": { y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 646) - 0 - (248 + Math.max(0, ((componentViewport?.height || 646) - 0 - 248) / 1) * 1 + 0)) / 2 + 248 + 0) + 30 + (0 + 0 + (Math.max(0, ((componentViewport?.height || 646) - 0 - 248) / 1) * 1 - 60 - 165.5) / 1 * 0) + 0 }, aUwTuNsVb: { y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 642) - 0 - (Math.max(0, ((componentViewport?.height || 642) - 0 - 252) / 1) * 1 + 248 + 4)) / 2 + 0 + 0) + 30 + (0 + 149.5 + (Math.max(0, ((componentViewport?.height || 642) - 0 - 252) / 1) * 1 - 60 - 165.5) / 1 * 1) + 0 }, rHtk7mxj3: { y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 400) - 0 - (Math.max(0, ((componentViewport?.height || 400) - 0 - 240) / 1) * 1 + 236 + 4)) / 2 + 0 + 0) + 24 + (0 + 0 + (Math.max(0, ((componentViewport?.height || 400) - 0 - 240) / 1) * 1 - 48 - 165.5) / 1 * 0) + 0 } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-jhnbua-container", layoutDependency, layoutId: "rxVUdbIcR-container", nodeId: "rxVUdbIcR", rendersWithMotion: true, scopeId: "fTmrIoq5f", style: { opacity: 0.3, rotate: 0 }, variants: { "aUwTuNsVb-hover": { rotate: 90 }, "isAPdhbEi-hover": { rotate: 90 } }, children: /* @__PURE__ */ _jsx(stdin_default, { height: "100%", id: "rxVUdbIcR", layoutId: "rxVUdbIcR", Rl_qLe3MC: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", style: { height: "100%", width: "100%" }, width: "100%" }) }) })] }), /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-1hin0ji", "data-styles-preset": "fN1_sGlJp", children: "Testimomnial" }) }), className: "framer-j1a15y", "data-framer-name": "Collaboration Text", fonts: ["Inter"], layoutDependency, layoutId: "R1iVZ5uFl", style: { "--framer-paragraph-spacing": "0px" }, text: xKE54n4Ed, verticalAlignment: "top", withExternalLayout: true })] })] }) }) }) });
});
var css4 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-k80mU.framer-1155hn2, .framer-k80mU .framer-1155hn2 { display: block; }", ".framer-k80mU.framer-1xa06lp { align-content: center; align-items: center; cursor: default; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 4px; height: 642px; justify-content: center; overflow: hidden; padding: 0px; position: relative; width: 279px; }", ".framer-k80mU .framer-1nedhuv { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 4px; height: min-content; justify-content: flex-start; overflow: visible; padding: 30px; position: relative; width: 100%; }", ".framer-k80mU .framer-d927ns { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 16px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-k80mU .framer-1ibhtdp { aspect-ratio: 1 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 46px); position: relative; width: 46px; }", ".framer-k80mU .framer-oibkzm { align-content: flex-start; align-items: flex-start; display: flex; flex: 1 0 0px; flex-direction: column; flex-wrap: nowrap; gap: 2px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: 1px; }", ".framer-k80mU .framer-oyhvcg, .framer-k80mU .framer-1wuq2o9, .framer-k80mU .framer-j1a15y { cursor: text; flex: none; height: auto; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }", ".framer-k80mU .framer-1flz52s { align-content: flex-start; align-items: flex-start; display: flex; flex: 1 0 0px; flex-direction: column; flex-wrap: nowrap; height: 1px; justify-content: space-between; overflow: visible; padding: 30px; position: relative; width: 100%; }", ".framer-k80mU .framer-jx6g4t { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; height: min-content; justify-content: space-between; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-k80mU .framer-1efuek3-container { flex: none; height: 12px; position: relative; width: 56px; }", ".framer-k80mU .framer-jhnbua-container { flex: none; height: 16px; position: relative; width: 16px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-k80mU.framer-1xa06lp, .framer-k80mU .framer-1nedhuv, .framer-k80mU .framer-d927ns, .framer-k80mU .framer-oibkzm { gap: 0px; } .framer-k80mU.framer-1xa06lp > *, .framer-k80mU .framer-1nedhuv > * { margin: 0px; margin-bottom: calc(4px / 2); margin-top: calc(4px / 2); } .framer-k80mU.framer-1xa06lp > :first-child, .framer-k80mU .framer-1nedhuv > :first-child, .framer-k80mU .framer-oibkzm > :first-child { margin-top: 0px; } .framer-k80mU.framer-1xa06lp > :last-child, .framer-k80mU .framer-1nedhuv > :last-child, .framer-k80mU .framer-oibkzm > :last-child { margin-bottom: 0px; } .framer-k80mU .framer-d927ns > * { margin: 0px; margin-left: calc(16px / 2); margin-right: calc(16px / 2); } .framer-k80mU .framer-d927ns > :first-child { margin-left: 0px; } .framer-k80mU .framer-d927ns > :last-child { margin-right: 0px; } .framer-k80mU .framer-oibkzm > * { margin: 0px; margin-bottom: calc(2px / 2); margin-top: calc(2px / 2); } }", ".framer-k80mU.framer-v-1hfzc71 .framer-1nedhuv, .framer-k80mU.framer-v-1hfzc71 .framer-jx6g4t { order: 1; }", ".framer-k80mU.framer-v-1hfzc71 .framer-1flz52s, .framer-k80mU.framer-v-1hfzc71 .framer-j1a15y { order: 0; }", ".framer-k80mU.framer-v-hfi02n.framer-1xa06lp { height: 400px; }", ".framer-k80mU.framer-v-hfi02n .framer-1nedhuv { order: 1; padding: 24px; }", ".framer-k80mU.framer-v-hfi02n .framer-1flz52s { order: 0; padding: 24px; }", ".framer-k80mU.framer-v-1xa06lp.hover.framer-1xa06lp, .framer-k80mU.framer-v-1hfzc71.hover.framer-1xa06lp { gap: 0px; height: 646px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-k80mU.framer-v-1xa06lp.hover.framer-1xa06lp { gap: 0px; } .framer-k80mU.framer-v-1xa06lp.hover.framer-1xa06lp > * { margin: 0px; margin-bottom: calc(0px / 2); margin-top: calc(0px / 2); } .framer-k80mU.framer-v-1xa06lp.hover.framer-1xa06lp > :first-child { margin-top: 0px; } .framer-k80mU.framer-v-1xa06lp.hover.framer-1xa06lp > :last-child { margin-bottom: 0px; } }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-k80mU.framer-v-1hfzc71.hover.framer-1xa06lp { gap: 0px; } .framer-k80mU.framer-v-1hfzc71.hover.framer-1xa06lp > * { margin: 0px; margin-bottom: calc(0px / 2); margin-top: calc(0px / 2); } .framer-k80mU.framer-v-1hfzc71.hover.framer-1xa06lp > :first-child { margin-top: 0px; } .framer-k80mU.framer-v-1hfzc71.hover.framer-1xa06lp > :last-child { margin-bottom: 0px; } }", ...css2, ...css, ...css3];
var FramerfTmrIoq5f = withCSS(Component, css4, "framer-k80mU");
var stdin_default3 = FramerfTmrIoq5f;
FramerfTmrIoq5f.displayName = "Testimonial";
FramerfTmrIoq5f.defaultProps = { height: 642, width: 279 };
addPropertyControls(FramerfTmrIoq5f, { variant: { options: ["isAPdhbEi", "aUwTuNsVb", "rHtk7mxj3"], optionTitles: ["Desktop", "Desktop reversed", "Phone"], title: "Variant", type: ControlType.Enum }, gmWSXR53y: { defaultValue: "Name", displayTextArea: false, title: "Name", type: ControlType.String }, IUuoDhO3z: { defaultValue: "Subtitle", displayTextArea: false, title: "Subtitle", type: ControlType.String }, CNR_PIjaw: { __defaultAssetReference: "data:framer/asset-reference,v2v5H7JyNs32xnkaBSwUWhCt8.jpg?originalFilename=User+Image.jpg&preferredSize=auto", __vekterDefault: { alt: "", assetReference: "data:framer/asset-reference,v2v5H7JyNs32xnkaBSwUWhCt8.jpg?originalFilename=User+Image.jpg&preferredSize=auto" }, title: "Image", type: ControlType.ResponsiveImage }, xKE54n4Ed: { defaultValue: "Testimomnial", displayTextArea: true, title: "Testimomnial", type: ControlType.String } });
addFonts(FramerfTmrIoq5f, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5vvr9Vy74if2I6bQbJvbw7SY1pQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/EOr0mi4hNtlgWNn9if640EZzXCo.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/Y9k9QrlZAqio88Klkmbd8VoMQc.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/OYrD2tBIBPvoJXiIHnLoOXnY9M.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/JeYwfuaPfZHQhEG8U5gtPDZ7WQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/vQyevYAyHtARFwPqUzQGpnDs.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/b6Y37FthZeALduNqHicBT6FutY.woff2", weight: "400" }] }, ...RatingStarsFonts, ...PlusIconFonts, ...getFontsFromSharedStyle(fonts2), ...getFontsFromSharedStyle(fonts), ...getFontsFromSharedStyle(fonts3)], { supportsExplicitInterCodegen: true });

// virtual:testimonial
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {
  "base": "rHtk7mxj3",
  "xl": "aUwTuNsVb"
};
stdin_default3.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default3,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default3, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default3);
export {
  ComponentWithRoot as default
};
