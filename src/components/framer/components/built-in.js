// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";
import {
  className,
  css,
  fonts
} from "./chunks/chunk-SATCLWQE.js";

// virtual:built-in
import { Fragment as Fragment2 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/sKy2U5CCL7dNlNvddsNT/jduSarGkVeMtg4GIVij9/do0Th5ePX.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, getFontsFromSharedStyle, Link, RichText, SVG, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var enabledGestures = { ghgmq1Qhl: { hover: true } };
var serializationHash = "framer-7WGE7";
var variantClassNames = { ghgmq1Qhl: "framer-v-123xkl1" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { bounce: 0.35, delay: 0, duration: 0.39, type: "spring" };
var transformTemplate1 = (_, t) => `translateX(-50%) ${t}`;
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var getProps = ({ height, id, link, width, ...props }) => {
  return { ...props, ztzs9EY6t: link ?? props.ztzs9EY6t };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className: className2, layoutId, variant, ztzs9EY6t, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ defaultVariant: "ghgmq1Qhl", enabledGestures, ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [className];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsx(Link, { href: ztzs9EY6t, motionChild: true, nodeId: "ghgmq1Qhl", scopeId: "do0Th5ePX", children: /* @__PURE__ */ _jsxs(motion.a, { ...restProps, ...gestureHandlers, className: `${cx(scopingClassNames, "framer-123xkl1", className2, classNames)} framer-44hzxt`, "data-framer-name": "Desktop", layoutDependency, layoutId: "ghgmq1Qhl", ref: refBinding, style: { ...style }, ...addPropertyOverrides({ "ghgmq1Qhl-hover": { "data-framer-name": void 0 } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsx(SVG, { className: "framer-ppptcv", "data-framer-name": "Vector", fill: "rgba(0,0,0,1)", intrinsicHeight: 14, intrinsicWidth: 9, layoutDependency, layoutId: "nXr4DYYz0", svg: '<svg width="9" height="14" viewBox="0 0 9 14" fill="none" xmlns="http://www.w3.org/2000/svg">\n<path d="M0 0H9V4.66667H4.5L0 0ZM0 4.66667H4.5L9 9.33333H0V4.66667ZM0 9.33333H4.5V14L0 9.33333Z" fill="white"/>\n</svg>\n', withExternalLayout: true }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-1q28y8v", "data-framer-name": "Text", layoutDependency, layoutId: "ODEQbNwvB", children: [/* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-1mf8d9g", "data-styles-preset": "ypR5VEWEl", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Built in Framer" }) }), className: "framer-nr3htp", fonts: ["Inter"], layoutDependency, layoutId: "WfcZxbg9T", style: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", "--framer-paragraph-spacing": "0px" }, transformTemplate: transformTemplate1, verticalAlignment: "top", withExternalLayout: true, ...addPropertyOverrides({ "ghgmq1Qhl-hover": { transformTemplate: void 0 } }, baseVariant, gestureVariant) }), /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-1mf8d9g", "data-styles-preset": "ypR5VEWEl", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Built in Framer" }) }), className: "framer-1vopv58", fonts: ["Inter"], layoutDependency, layoutId: "xjYxhhect", style: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", "--framer-paragraph-spacing": "0px" }, verticalAlignment: "top", withExternalLayout: true, ...addPropertyOverrides({ "ghgmq1Qhl-hover": { transformTemplate: transformTemplate1 } }, baseVariant, gestureVariant) })] })] }) }) }) }) });
});
var css2 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-7WGE7.framer-44hzxt, .framer-7WGE7 .framer-44hzxt { display: block; }", ".framer-7WGE7.framer-123xkl1 { align-content: center; align-items: center; cursor: pointer; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 8px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 0px; position: relative; text-decoration: none; width: min-content; }", ".framer-7WGE7 .framer-ppptcv { flex: none; height: 14px; position: relative; width: 9px; }", ".framer-7WGE7 .framer-1q28y8v { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 4px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: min-content; }", ".framer-7WGE7 .framer-nr3htp { flex: none; height: auto; left: 50%; position: absolute; top: -18px; white-space: pre; width: auto; z-index: 1; }", ".framer-7WGE7 .framer-1vopv58 { flex: none; height: auto; position: relative; white-space: pre; width: auto; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-7WGE7.framer-123xkl1, .framer-7WGE7 .framer-1q28y8v { gap: 0px; } .framer-7WGE7.framer-123xkl1 > * { margin: 0px; margin-left: calc(8px / 2); margin-right: calc(8px / 2); } .framer-7WGE7.framer-123xkl1 > :first-child { margin-left: 0px; } .framer-7WGE7.framer-123xkl1 > :last-child { margin-right: 0px; } .framer-7WGE7 .framer-1q28y8v > * { margin: 0px; margin-bottom: calc(4px / 2); margin-top: calc(4px / 2); } .framer-7WGE7 .framer-1q28y8v > :first-child { margin-top: 0px; } .framer-7WGE7 .framer-1q28y8v > :last-child { margin-bottom: 0px; } }", ".framer-7WGE7.framer-v-123xkl1.hover .framer-nr3htp { left: unset; position: relative; top: unset; }", ".framer-7WGE7.framer-v-123xkl1.hover .framer-1vopv58 { bottom: -18px; left: 50%; position: absolute; z-index: 1; }", ...css];
var Framerdo0Th5ePX = withCSS(Component, css2, "framer-7WGE7");
var stdin_default = Framerdo0Th5ePX;
Framerdo0Th5ePX.displayName = "Built in";
Framerdo0Th5ePX.defaultProps = { height: 21, width: 111 };
addPropertyControls(Framerdo0Th5ePX, { ztzs9EY6t: { title: "Link", type: ControlType.Link } });
addFonts(Framerdo0Th5ePX, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5vvr9Vy74if2I6bQbJvbw7SY1pQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/EOr0mi4hNtlgWNn9if640EZzXCo.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/Y9k9QrlZAqio88Klkmbd8VoMQc.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/OYrD2tBIBPvoJXiIHnLoOXnY9M.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/JeYwfuaPfZHQhEG8U5gtPDZ7WQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/vQyevYAyHtARFwPqUzQGpnDs.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/b6Y37FthZeALduNqHicBT6FutY.woff2", weight: "400" }] }, ...getFontsFromSharedStyle(fonts)], { supportsExplicitInterCodegen: true });

// virtual:built-in
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {};
stdin_default.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default);
export {
  ComponentWithRoot as default
};
