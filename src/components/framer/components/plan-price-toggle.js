// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";

// virtual:plan-price-toggle
import { Fragment as Fragment2 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/rtWkvH5fSSlZDlo2o6ww/HnHVN87wcIU64TRg16LZ/kiJu4DQjn.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, RichText, useActiveVariantCallback, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var cycleOrder = ["AL3iiTHVX", "lxhOXGrif"];
var serializationHash = "framer-BIVqF";
var variantClassNames = { AL3iiTHVX: "framer-v-1i80q33", lxhOXGrif: "framer-v-1ijfsua" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { duration: 0, type: "tween" };
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { Active: "AL3iiTHVX", Inactive: "lxhOXGrif" };
var getProps = ({ height, id, price, width, ...props }) => {
  return { ...props, urFn9GbzH: price ?? props.urFn9GbzH ?? "+$1,490", variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "AL3iiTHVX" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className, layoutId, variant, urFn9GbzH, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "AL3iiTHVX", ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const { activeVariantCallback, delay } = useActiveVariantCallback(baseVariant);
  const onTapupq9do = activeVariantCallback(async (...args) => {
    setGestureState({ isPressed: false });
    setVariant("lxhOXGrif");
  });
  const onTap10d5jw9 = activeVariantCallback(async (...args) => {
    setGestureState({ isPressed: false });
    setVariant("AL3iiTHVX");
  });
  const sharedStyleClassNames = [];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsxs(motion.div, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-1i80q33", className, classNames), "data-framer-name": "Active", "data-highlight": true, layoutDependency, layoutId: "AL3iiTHVX", onTap: onTapupq9do, ref: refBinding, style: { ...style }, ...addPropertyOverrides({ lxhOXGrif: { "data-framer-name": "Inactive", onTap: onTap10d5jw9 } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "27px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.06em", "--framer-line-height": "100%", "--framer-text-alignment": "right", "--framer-text-color": "var(--extracted-r6o4lv, rgb(255, 255, 255))" }, children: "+$1,490" }) }), className: "framer-17px2cp", "data-framer-name": "Delivery Time Value", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "qsAgGrpGP", style: { "--extracted-r6o4lv": "rgb(255, 255, 255)", "--framer-paragraph-spacing": "0px", opacity: 1 }, text: urFn9GbzH, variants: { lxhOXGrif: { opacity: 0.4 } }, verticalAlignment: "top", withExternalLayout: true }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-1bse1vs", "data-framer-name": "Plan Toggle", layoutDependency, layoutId: "W_Z04d9Jr", style: { backgroundColor: "rgb(255, 255, 255)", borderBottomLeftRadius: 50, borderBottomRightRadius: 50, borderTopLeftRadius: 50, borderTopRightRadius: 50 }, children: /* @__PURE__ */ _jsx(motion.div, { className: "framer-12n0kb", "data-framer-name": "Ellipse 3", layoutDependency, layoutId: "D1VsuW1QP", style: { backgroundColor: "rgb(19, 19, 19)", borderBottomLeftRadius: "100%", borderBottomRightRadius: "100%", borderTopLeftRadius: "100%", borderTopRightRadius: "100%" } }) })] }) }) }) });
});
var css = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-BIVqF.framer-104gdkn, .framer-BIVqF .framer-104gdkn { display: block; }", ".framer-BIVqF.framer-1i80q33 { align-content: center; align-items: center; cursor: pointer; display: flex; flex-direction: row; flex-wrap: wrap; gap: 20px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: 179px; }", ".framer-BIVqF .framer-17px2cp { flex: none; height: auto; position: relative; white-space: pre; width: auto; }", ".framer-BIVqF .framer-1bse1vs { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: 44px; justify-content: flex-end; overflow: visible; padding: 4px; position: relative; width: 76px; }", ".framer-BIVqF .framer-12n0kb { flex: none; height: 34px; position: relative; width: 34px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-BIVqF.framer-1i80q33, .framer-BIVqF .framer-1bse1vs { gap: 0px; } .framer-BIVqF.framer-1i80q33 > * { margin: 0px; margin-left: calc(20px / 2); margin-right: calc(20px / 2); } .framer-BIVqF.framer-1i80q33 > :first-child, .framer-BIVqF .framer-1bse1vs > :first-child { margin-left: 0px; } .framer-BIVqF.framer-1i80q33 > :last-child, .framer-BIVqF .framer-1bse1vs > :last-child { margin-right: 0px; } .framer-BIVqF .framer-1bse1vs > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } }", ".framer-BIVqF.framer-v-1ijfsua .framer-1bse1vs { justify-content: flex-start; }"];
var FramerkiJu4DQjn = withCSS(Component, css, "framer-BIVqF");
var stdin_default = FramerkiJu4DQjn;
FramerkiJu4DQjn.displayName = "Plan Price Toggle";
FramerkiJu4DQjn.defaultProps = { height: 91, width: 179 };
addPropertyControls(FramerkiJu4DQjn, { variant: { options: ["AL3iiTHVX", "lxhOXGrif"], optionTitles: ["Active", "Inactive"], title: "Variant", type: ControlType.Enum }, urFn9GbzH: { defaultValue: "+$1,490", displayTextArea: false, title: "Price", type: ControlType.String } });
addFonts(FramerkiJu4DQjn, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/hyOgCu0Xnghbimh0pE8QTvtt2AU.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/NeGmSOXrPBfEFIy5YZeHq17LEDA.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/oYaAX5himiTPYuN8vLWnqBbfD2s.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/lEJLP4R0yuCaMCjSXYHtJw72M.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/cRJyLNuTJR5jbyKzGi33wU9cqIQ.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/1ZFS7N918ojhhd0nQWdj3jz4w.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/A0Wcc7NgXMjUuFdquHDrIZpzZw0.woff2", weight: "600" }] }], { supportsExplicitInterCodegen: true });

// virtual:plan-price-toggle
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {
  "base": "AL3iiTHVX"
};
stdin_default.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default);
export {
  ComponentWithRoot as default
};
