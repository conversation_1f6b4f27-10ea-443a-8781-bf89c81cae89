/* This file was generated by Unframer, do not edit manually */
import type * as React from "react"

import type { UnframerBreakpoint } from "unframer"

type Locale = string
export interface Props {
    children?: React.ReactNode
    locale?: Locale
    style?: React.CSSProperties
    className?: string
    id?: string
    width?: any
    height?: any
    layoutId?: string
    "title"?: string
    "showNumber"?: boolean
    "number"?: string
    "link"?: string
}

const MenuItemFramerComponent = (props: Props) => any

type VariantsMap = Partial<Record<UnframerBreakpoint, Props['variant']>> & { base: Props['variant'] }

MenuItemFramerComponent.Responsive = (props: Omit<Props, 'variant'> & {variants?: VariantsMap}) => any

export default MenuItemFramerComponent

