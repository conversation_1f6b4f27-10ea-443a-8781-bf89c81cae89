/* This file was generated by Unframer, do not edit manually */
import type * as React from "react"

import type { UnframerBreakpoint } from "unframer"

type Locale = string
export interface Props {
    children?: React.ReactNode
    locale?: Locale
    style?: React.CSSProperties
    className?: string
    id?: string
    width?: any
    height?: any
    layoutId?: string
    "amount"?: string
    "image1"?: {src: string, srcSet?: string, alt?: string}
    "image2"?: {src: string, srcSet?: string, alt?: string}
    "image3"?: {src: string, srcSet?: string, alt?: string}
    "image4"?: {src: string, srcSet?: string, alt?: string}
    "text1"?: string
    "text2"?: string
}

const RatingFramerComponent = (props: Props) => any

type VariantsMap = Partial<Record<UnframerBreakpoint, Props['variant']>> & { base: Props['variant'] }

RatingFramerComponent.Responsive = (props: Omit<Props, 'variant'> & {variants?: VariantsMap}) => any

export default RatingFramerComponent

