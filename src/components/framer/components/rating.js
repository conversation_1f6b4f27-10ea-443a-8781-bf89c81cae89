// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";
import {
  stdin_default
} from "./chunks/chunk-XKETCZDK.js";

// virtual:rating
import { Fragment as Fragment2 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/zEo4GdvWJC14Pp4F1FlC/MBRPj4XNBFtQ3JDonsi9/uTEGFhvJJ.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ComponentViewportProvider, ControlType, cx, getFonts, Image, RichText, SmartComponentScopedContainer, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var RatingStarsFonts = getFonts(stdin_default);
var serializationHash = "framer-yGNK9";
var variantClassNames = { h_cMDlEjr: "framer-v-1t33i6z" };
var transition1 = { bounce: 0.2, delay: 0, duration: 0.4, type: "spring" };
var toResponsiveImage = (value) => {
  if (typeof value === "object" && value !== null && typeof value.src === "string") {
    return value;
  }
  return typeof value === "string" ? { src: value } : void 0;
};
var isSet = (value) => {
  if (Array.isArray(value)) return value.length > 0;
  return value !== void 0 && value !== null && value !== "";
};
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var getProps = ({ amount, height, id, image1, image2, image3, image4, text1, text2, width, ...props }) => {
  return { ...props, Ags4r5t7K: image4 ?? props.Ags4r5t7K ?? { alt: "", src: "https://framerusercontent.com/images/lVMA2BWo8D0yz8GINpzGpDx4.jpg" }, Dsqc3tVs9: image1 ?? props.Dsqc3tVs9 ?? { alt: "", src: "https://framerusercontent.com/images/7XElicIcn53vdnwyFHTpct98.jpg" }, gCMYsiPtV: text2 ?? props.gCMYsiPtV ?? "clients worldwide", I1q2j8ExU: text1 ?? props.I1q2j8ExU ?? "Trusted by", IZ55npdxw: image2 ?? props.IZ55npdxw ?? { alt: "", src: "https://framerusercontent.com/images/D53nCbgrC45WamdByYxomUf9c.jpg" }, pWb8XMXN5: image3 ?? props.pWb8XMXN5 ?? { alt: "", src: "https://framerusercontent.com/images/fqOOPJWEd96G4368QW9n1dcVU.jpg" }, UHxkMn1dd: amount ?? props.UHxkMn1dd ?? "56+" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className, layoutId, variant, UHxkMn1dd, Dsqc3tVs9, IZ55npdxw, pWb8XMXN5, Ags4r5t7K, I1q2j8ExU, gCMYsiPtV, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ defaultVariant: "h_cMDlEjr", ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  const visible = isSet(I1q2j8ExU);
  const visible1 = isSet(gCMYsiPtV);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsxs(motion.div, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-1t33i6z", className, classNames), "data-framer-name": "Default", layoutDependency, layoutId: "h_cMDlEjr", ref: refBinding, style: { ...style }, children: [/* @__PURE__ */ _jsxs(motion.div, { className: "framer-1mqkkmi", "data-framer-name": "User Ratings", layoutDependency, layoutId: "Fr4QcQgc_", children: [/* @__PURE__ */ _jsx(Image, { background: { alt: "", fit: "fill", sizes: "40px", ...toResponsiveImage(Dsqc3tVs9) }, className: "framer-15uofq7", "data-border": true, "data-framer-name": "User Image", layoutDependency, layoutId: "KPNXS_L9z", style: { "--border-bottom-width": "3px", "--border-color": "rgb(245, 245, 245)", "--border-left-width": "3px", "--border-right-width": "3px", "--border-style": "solid", "--border-top-width": "3px", borderBottomLeftRadius: 11, borderBottomRightRadius: 11, borderTopLeftRadius: 11, borderTopRightRadius: 11 } }), /* @__PURE__ */ _jsx(Image, { background: { alt: "", fit: "fill", sizes: "40px", ...toResponsiveImage(IZ55npdxw) }, className: "framer-6tjf31", "data-border": true, "data-framer-name": "User Image", layoutDependency, layoutId: "QdhKQKa0p", style: { "--border-bottom-width": "3px", "--border-color": "rgb(245, 245, 245)", "--border-left-width": "3px", "--border-right-width": "3px", "--border-style": "solid", "--border-top-width": "3px", borderBottomLeftRadius: 11, borderBottomRightRadius: 11, borderTopLeftRadius: 11, borderTopRightRadius: 11 } }), /* @__PURE__ */ _jsx(Image, { background: { alt: "", fit: "fill", sizes: "40px", ...toResponsiveImage(pWb8XMXN5) }, className: "framer-18e85sx", "data-border": true, "data-framer-name": "User Image", layoutDependency, layoutId: "trHfb_SYq", style: { "--border-bottom-width": "3px", "--border-color": "rgb(245, 245, 245)", "--border-left-width": "3px", "--border-right-width": "3px", "--border-style": "solid", "--border-top-width": "3px", borderBottomLeftRadius: 11, borderBottomRightRadius: 11, borderTopLeftRadius: 11, borderTopRightRadius: 11 } }), /* @__PURE__ */ _jsx(Image, { background: { alt: "", fit: "fill", sizes: "40px", ...toResponsiveImage(Ags4r5t7K) }, className: "framer-lwaefd", "data-border": true, "data-framer-name": "User Image", layoutDependency, layoutId: "wQrjU8Seu", style: { "--border-bottom-width": "3px", "--border-color": "rgb(245, 245, 245)", "--border-left-width": "3px", "--border-right-width": "3px", "--border-style": "solid", "--border-top-width": "3px", borderBottomLeftRadius: 11, borderBottomRightRadius: 11, borderTopLeftRadius: 11, borderTopRightRadius: 11 } }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-1xp3t2u", "data-border": true, "data-framer-name": "Rating Count", layoutDependency, layoutId: "M98LNYUX3", style: { "--border-bottom-width": "3px", "--border-color": "rgb(245, 245, 245)", "--border-left-width": "3px", "--border-right-width": "3px", "--border-style": "solid", "--border-top-width": "3px", backgroundColor: "rgb(9, 9, 9)", borderBottomLeftRadius: 11, borderBottomRightRadius: 11, borderTopLeftRadius: 11, borderTopRightRadius: 11 }, children: /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "11px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "130%", "--framer-text-color": "var(--extracted-r6o4lv, rgb(255, 255, 255))" }, children: "56+" }) }), className: "framer-sfh4ia", "data-framer-name": "Rating Count", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "ZqLAPR4F6", style: { "--extracted-r6o4lv": "rgb(255, 255, 255)", "--framer-paragraph-spacing": "0px" }, text: UHxkMn1dd, verticalAlignment: "top", withExternalLayout: true }) })] }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-1j135v4", "data-framer-name": "Star Rating Info", layoutDependency, layoutId: "lg3PTM8Va", children: [/* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 12, width: "56px", children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-gc0t88-container", layoutDependency, layoutId: "xLyXc9YrW-container", nodeId: "xLyXc9YrW", rendersWithMotion: true, scopeId: "uTEGFhvJJ", children: /* @__PURE__ */ _jsx(stdin_default, { height: "100%", id: "xLyXc9YrW", layoutId: "xLyXc9YrW", style: { height: "100%", width: "100%" }, width: "100%" }) }) }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-uu8mpa", layoutDependency, layoutId: "bug85PU8o", children: [visible && /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItTWVkaXVt", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "12px", "--framer-font-weight": "500", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "130%", "--framer-text-color": "var(--extracted-r6o4lv, rgb(9, 9, 9))" }, children: "Trusted by" }) }), className: "framer-1vgpd6d", "data-framer-name": "Rating Text", fonts: ["Inter-Medium"], layoutDependency, layoutId: "ZqH_jbflO", style: { "--extracted-r6o4lv": "rgb(9, 9, 9)", "--framer-paragraph-spacing": "0px" }, text: I1q2j8ExU, verticalAlignment: "top", withExternalLayout: true }), visible1 && /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItTWVkaXVt", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "12px", "--framer-font-weight": "500", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "130%", "--framer-text-color": "var(--extracted-r6o4lv, rgba(21, 21, 21, 0.6))" }, children: "clients worldwide" }) }), className: "framer-sr0y3t", "data-framer-name": "Rating Text", fonts: ["Inter-Medium"], layoutDependency, layoutId: "t4wpKkwVz", style: { "--extracted-r6o4lv": "rgba(21, 21, 21, 0.6)", "--framer-paragraph-spacing": "0px" }, text: gCMYsiPtV, verticalAlignment: "top", withExternalLayout: true })] })] })] }) }) }) });
});
var css = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-yGNK9.framer-xne83e, .framer-yGNK9 .framer-xne83e { display: block; }", ".framer-yGNK9.framer-1t33i6z { align-content: center; align-items: center; display: flex; flex-direction: row; flex-wrap: wrap; gap: 12px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: 300px; }", ".framer-yGNK9 .framer-1mqkkmi { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: flex-start; min-height: 40px; min-width: 136px; overflow: visible; padding: 0px; position: relative; width: min-content; }", ".framer-yGNK9 .framer-15uofq7 { aspect-ratio: 1 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 40px); left: 0px; position: absolute; top: 0px; width: 40px; z-index: 1; }", ".framer-yGNK9 .framer-6tjf31 { aspect-ratio: 1 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 40px); left: 24px; position: absolute; top: 0px; width: 40px; z-index: 1; }", ".framer-yGNK9 .framer-18e85sx { aspect-ratio: 1 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 40px); left: 48px; position: absolute; top: 0px; width: 40px; z-index: 1; }", ".framer-yGNK9 .framer-lwaefd { aspect-ratio: 1 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 40px); left: 72px; position: absolute; top: 0px; width: 40px; z-index: 1; }", ".framer-yGNK9 .framer-1xp3t2u { align-content: center; align-items: center; aspect-ratio: 1 / 1; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: var(--framer-aspect-ratio-supported, 40px); justify-content: center; left: 96px; overflow: visible; padding: 13px 8px 13px 8px; position: absolute; top: 0px; width: 40px; z-index: 1; }", ".framer-yGNK9 .framer-sfh4ia, .framer-yGNK9 .framer-1vgpd6d, .framer-yGNK9 .framer-sr0y3t { flex: none; height: auto; position: relative; white-space: pre; width: auto; }", ".framer-yGNK9 .framer-1j135v4 { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: wrap; gap: 4px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: 151px; }", ".framer-yGNK9 .framer-gc0t88-container { flex: none; height: 12px; position: relative; width: 56px; }", ".framer-yGNK9 .framer-uu8mpa { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 2px; height: min-content; justify-content: center; overflow: visible; padding: 0px; position: relative; width: min-content; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-yGNK9.framer-1t33i6z, .framer-yGNK9 .framer-1mqkkmi, .framer-yGNK9 .framer-1xp3t2u, .framer-yGNK9 .framer-1j135v4, .framer-yGNK9 .framer-uu8mpa { gap: 0px; } .framer-yGNK9.framer-1t33i6z > * { margin: 0px; margin-left: calc(12px / 2); margin-right: calc(12px / 2); } .framer-yGNK9.framer-1t33i6z > :first-child, .framer-yGNK9 .framer-1mqkkmi > :first-child, .framer-yGNK9 .framer-uu8mpa > :first-child { margin-left: 0px; } .framer-yGNK9.framer-1t33i6z > :last-child, .framer-yGNK9 .framer-1mqkkmi > :last-child, .framer-yGNK9 .framer-uu8mpa > :last-child { margin-right: 0px; } .framer-yGNK9 .framer-1mqkkmi > * { margin: 0px; margin-left: calc(-16px / 2); margin-right: calc(-16px / 2); } .framer-yGNK9 .framer-1xp3t2u > * { margin: 0px; margin-bottom: calc(10px / 2); margin-top: calc(10px / 2); } .framer-yGNK9 .framer-1xp3t2u > :first-child, .framer-yGNK9 .framer-1j135v4 > :first-child { margin-top: 0px; } .framer-yGNK9 .framer-1xp3t2u > :last-child, .framer-yGNK9 .framer-1j135v4 > :last-child { margin-bottom: 0px; } .framer-yGNK9 .framer-1j135v4 > * { margin: 0px; margin-bottom: calc(4px / 2); margin-top: calc(4px / 2); } .framer-yGNK9 .framer-uu8mpa > * { margin: 0px; margin-left: calc(2px / 2); margin-right: calc(2px / 2); } }", '.framer-yGNK9[data-border="true"]::after, .framer-yGNK9 [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none; }'];
var FrameruTEGFhvJJ = withCSS(Component, css, "framer-yGNK9");
var stdin_default2 = FrameruTEGFhvJJ;
FrameruTEGFhvJJ.displayName = "Rating";
FrameruTEGFhvJJ.defaultProps = { height: 51, width: 300 };
addPropertyControls(FrameruTEGFhvJJ, { UHxkMn1dd: { defaultValue: "56+", displayTextArea: false, title: "Amount", type: ControlType.String }, Dsqc3tVs9: { __defaultAssetReference: "data:framer/asset-reference,7XElicIcn53vdnwyFHTpct98.jpg?originalFilename=User+Image.jpg&preferredSize=auto", __vekterDefault: { alt: "", assetReference: "data:framer/asset-reference,7XElicIcn53vdnwyFHTpct98.jpg?originalFilename=User+Image.jpg&preferredSize=auto" }, title: "Image 1", type: ControlType.ResponsiveImage }, IZ55npdxw: { __defaultAssetReference: "data:framer/asset-reference,D53nCbgrC45WamdByYxomUf9c.jpg?originalFilename=User+Image.jpg&preferredSize=auto", __vekterDefault: { alt: "", assetReference: "data:framer/asset-reference,D53nCbgrC45WamdByYxomUf9c.jpg?originalFilename=User+Image.jpg&preferredSize=auto" }, title: "Image 2", type: ControlType.ResponsiveImage }, pWb8XMXN5: { __defaultAssetReference: "data:framer/asset-reference,fqOOPJWEd96G4368QW9n1dcVU.jpg?originalFilename=User+Image.jpg&preferredSize=auto", __vekterDefault: { alt: "", assetReference: "data:framer/asset-reference,fqOOPJWEd96G4368QW9n1dcVU.jpg?originalFilename=User+Image.jpg&preferredSize=auto" }, title: "Image 3", type: ControlType.ResponsiveImage }, Ags4r5t7K: { __defaultAssetReference: "data:framer/asset-reference,lVMA2BWo8D0yz8GINpzGpDx4.jpg?originalFilename=User+Image.jpg&preferredSize=auto", __vekterDefault: { alt: "", assetReference: "data:framer/asset-reference,lVMA2BWo8D0yz8GINpzGpDx4.jpg?originalFilename=User+Image.jpg&preferredSize=auto" }, title: "Image 4", type: ControlType.ResponsiveImage }, I1q2j8ExU: { defaultValue: "Trusted by", displayTextArea: false, title: "Text 1", type: ControlType.String }, gCMYsiPtV: { defaultValue: "clients worldwide", displayTextArea: false, title: "Text 2", type: ControlType.String } });
addFonts(FrameruTEGFhvJJ, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/hyOgCu0Xnghbimh0pE8QTvtt2AU.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/NeGmSOXrPBfEFIy5YZeHq17LEDA.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/oYaAX5himiTPYuN8vLWnqBbfD2s.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/lEJLP4R0yuCaMCjSXYHtJw72M.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/cRJyLNuTJR5jbyKzGi33wU9cqIQ.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/1ZFS7N918ojhhd0nQWdj3jz4w.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/A0Wcc7NgXMjUuFdquHDrIZpzZw0.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5A3Ce6C9YYmCjpQx9M4inSaKU.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/Qx95Xyt0Ka3SGhinnbXIGpEIyP4.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/6mJuEAguuIuMog10gGvH5d3cl8.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/xYYWaj7wCU5zSQH0eXvSaS19wo.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/otTaNuNpVK4RbdlT7zDDdKvQBA.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/d3tHnaQIAeqiE5hGcRw4mmgWYU.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/DolVirEGb34pEXEp8t8FQBSK4.woff2", weight: "500" }] }, ...RatingStarsFonts], { supportsExplicitInterCodegen: true });

// virtual:rating
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {};
stdin_default2.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default2,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default2, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default2);
export {
  ComponentWithRoot as default
};
