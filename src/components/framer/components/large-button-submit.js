// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";

// virtual:large-button-submit
import { Fragment as Fragment2 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/jwmEWI6XYLAo8iFW1jdT/yq8KXx0rmwm7kswAk0oY/ZsPpcvZL7.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, RichText, useComponentViewport, useLocaleInfo, useVariantState, withCSS, withFX } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var MotionDivWithFX = withFX(motion.div);
var enabledGestures = { wYDONdaYp: { hover: true } };
var cycleOrder = ["wYDONdaYp", "klvioFlTK", "eaKPgDO7P", "dq7iWPLH9", "wDK8O1Ezz"];
var serializationHash = "framer-GdT3P";
var variantClassNames = { dq7iWPLH9: "framer-v-p8053s", eaKPgDO7P: "framer-v-2gmwtk", klvioFlTK: "framer-v-1rmx67o", wDK8O1Ezz: "framer-v-1h3xpm4", wYDONdaYp: "framer-v-4lucl9" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { bounce: 0.35, delay: 0, duration: 0.39, type: "spring" };
var transformTemplate1 = (_, t) => `translateX(-50%) ${t}`;
var transition2 = { delay: 0, duration: 1, ease: [0, 0, 1, 1], type: "tween" };
var animation = { opacity: 1, rotate: 360, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, x: 0, y: 0 };
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { Default: "wYDONdaYp", Disabled: "eaKPgDO7P", Error: "wDK8O1Ezz", Loading: "klvioFlTK", Success: "dq7iWPLH9" };
var getProps = ({ bG, color, height, hoverBG, hoverColor, id, title, width, ...props }) => {
  return { ...props, GNYtni0Of: color ?? props.GNYtni0Of ?? "rgb(255, 255, 255)", MoodCIitJ: bG ?? props.MoodCIitJ ?? "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", UZBAPRl6f: hoverBG ?? props.UZBAPRl6f ?? "rgb(0, 0, 0)", variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "wYDONdaYp", X9Xju9FBn: title ?? props.X9Xju9FBn ?? "Submit", xKHL3eyCS: hoverColor ?? props.xKHL3eyCS ?? "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className, layoutId, variant, X9Xju9FBn, GNYtni0Of, MoodCIitJ, xKHL3eyCS, UZBAPRl6f, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "wYDONdaYp", enabledGestures, ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  const isDisplayed = () => {
    if (baseVariant === "klvioFlTK") return false;
    return true;
  };
  const isDisplayed1 = () => {
    if (baseVariant === "klvioFlTK") return true;
    return false;
  };
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsxs(motion.button, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-4lucl9", className, classNames), "data-framer-name": "Default", "data-reset": "button", layoutDependency, layoutId: "wYDONdaYp", ref: refBinding, style: { backgroundColor: MoodCIitJ, borderBottomLeftRadius: 50, borderBottomRightRadius: 50, borderTopLeftRadius: 50, borderTopRightRadius: 50, opacity: 1, ...style }, variants: { "wYDONdaYp-hover": { backgroundColor: UZBAPRl6f, opacity: 1 }, dq7iWPLH9: { opacity: 1 }, eaKPgDO7P: { opacity: 0.5 }, wDK8O1Ezz: { backgroundColor: "rgba(255, 34, 68, 0.15)", opacity: 1 } }, ...addPropertyOverrides({ "wYDONdaYp-hover": { "data-framer-name": void 0 }, dq7iWPLH9: { "data-framer-name": "Success" }, eaKPgDO7P: { "data-framer-name": "Disabled" }, klvioFlTK: { "data-framer-name": "Loading" }, wDK8O1Ezz: { "data-framer-name": "Error" } }, baseVariant, gestureVariant), children: [isDisplayed() && /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "18px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-text-color": "var(--extracted-r6o4lv, var(--variable-reference-GNYtni0Of-ZsPpcvZL7))" }, children: "Submit" }) }), className: "framer-o38np2", "data-framer-name": "Submit 1", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "C10wEhuIa", style: { "--extracted-r6o4lv": "var(--variable-reference-GNYtni0Of-ZsPpcvZL7)", "--framer-link-text-color": "rgb(0, 153, 255)", "--framer-link-text-decoration": "underline", "--variable-reference-GNYtni0Of-ZsPpcvZL7": GNYtni0Of, "--variable-reference-xKHL3eyCS-ZsPpcvZL7": xKHL3eyCS, opacity: 0 }, text: X9Xju9FBn, transformTemplate: transformTemplate1, variants: { "wYDONdaYp-hover": { "--extracted-r6o4lv": "var(--variable-reference-xKHL3eyCS-ZsPpcvZL7)", "--variable-reference-xKHL3eyCS-ZsPpcvZL7": xKHL3eyCS, opacity: 1 }, dq7iWPLH9: { "--extracted-r6o4lv": "rgb(255, 255, 255)" }, wDK8O1Ezz: { "--extracted-r6o4lv": "rgb(255, 34, 68)" } }, verticalAlignment: "top", withExternalLayout: true, ...addPropertyOverrides({ "wYDONdaYp-hover": { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "18px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-text-color": "var(--extracted-r6o4lv, var(--variable-reference-xKHL3eyCS-ZsPpcvZL7))" }, children: "Submit" }) }), transformTemplate: void 0 }, dq7iWPLH9: { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "18px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-text-color": "var(--extracted-r6o4lv, rgb(255, 255, 255))" }, children: "Thank you!" }) }), text: void 0 }, wDK8O1Ezz: { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "18px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-text-color": "var(--extracted-r6o4lv, rgb(255, 34, 68))" }, children: "Something went wrong" }) }), text: void 0 } }, baseVariant, gestureVariant) }), isDisplayed() && /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "18px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-text-color": "var(--extracted-r6o4lv, var(--variable-reference-GNYtni0Of-ZsPpcvZL7))" }, children: "Submit" }) }), className: "framer-1zgpu1", "data-framer-name": "Submit 2", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "g4yB2dsE8", style: { "--extracted-r6o4lv": "var(--variable-reference-GNYtni0Of-ZsPpcvZL7)", "--framer-link-text-color": "rgb(0, 153, 255)", "--framer-link-text-decoration": "underline", "--variable-reference-GNYtni0Of-ZsPpcvZL7": GNYtni0Of, "--variable-reference-xKHL3eyCS-ZsPpcvZL7": xKHL3eyCS, opacity: 1 }, text: X9Xju9FBn, variants: { "wYDONdaYp-hover": { "--extracted-r6o4lv": "var(--variable-reference-xKHL3eyCS-ZsPpcvZL7)", "--variable-reference-xKHL3eyCS-ZsPpcvZL7": xKHL3eyCS, opacity: 0 }, dq7iWPLH9: { "--extracted-r6o4lv": "rgb(255, 255, 255)" }, wDK8O1Ezz: { "--extracted-r6o4lv": "rgb(255, 34, 68)" } }, verticalAlignment: "top", withExternalLayout: true, ...addPropertyOverrides({ "wYDONdaYp-hover": { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "18px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-text-color": "var(--extracted-r6o4lv, var(--variable-reference-xKHL3eyCS-ZsPpcvZL7))" }, children: "Submit" }) }), transformTemplate: transformTemplate1 }, dq7iWPLH9: { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "18px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-text-color": "var(--extracted-r6o4lv, rgb(255, 255, 255))" }, children: "Thank you!" }) }), text: void 0 }, wDK8O1Ezz: { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "18px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-text-color": "var(--extracted-r6o4lv, rgb(255, 34, 68))" }, children: "Something went wrong" }) }), text: void 0 } }, baseVariant, gestureVariant) }), isDisplayed1() && /* @__PURE__ */ _jsx(motion.div, { className: "framer-6agcka", "data-framer-name": "Spinner", layoutDependency, layoutId: "F5gWyYcTT", style: { mask: "url('https://framerusercontent.com/images/pGiXYozQ3mE4cilNOItfe2L2fUA.svg') alpha no-repeat center / cover add", WebkitMask: "url('https://framerusercontent.com/images/pGiXYozQ3mE4cilNOItfe2L2fUA.svg') alpha no-repeat center / cover add" }, children: /* @__PURE__ */ _jsx(MotionDivWithFX, { __framer__loop: animation, __framer__loopEffectEnabled: true, __framer__loopRepeatDelay: 0, __framer__loopRepeatType: "loop", __framer__loopTransition: transition2, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-18aqab3", "data-framer-name": "Conic", layoutDependency, layoutId: "Aaebayspd", style: { background: "conic-gradient(from 180deg at 50% 50%, rgb(68, 204, 255) 0deg, rgb(68, 204, 255) 360deg)", backgroundColor: "rgb(68, 204, 255)", mask: "none", WebkitMask: "none" }, variants: { klvioFlTK: { background: "conic-gradient(from 0deg at 50% 50%, rgba(255, 255, 255, 0) 7.208614864864882deg, rgb(255, 255, 255) 342deg)", backgroundColor: "rgba(0, 0, 0, 0)", mask: "url('https://framerusercontent.com/images/pGiXYozQ3mE4cilNOItfe2L2fUA.svg') alpha no-repeat center / cover add", WebkitMask: "url('https://framerusercontent.com/images/pGiXYozQ3mE4cilNOItfe2L2fUA.svg') alpha no-repeat center / cover add" } }, children: /* @__PURE__ */ _jsx(motion.div, { className: "framer-1r4sjfr", "data-framer-name": "Rounding", layoutDependency, layoutId: "vvMLhy71E", style: { backgroundColor: "rgb(255, 255, 255)", borderBottomLeftRadius: 1, borderBottomRightRadius: 1, borderTopLeftRadius: 1, borderTopRightRadius: 1 }, transformTemplate: transformTemplate1 }) }) })] }) }) }) });
});
var css = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-GdT3P.framer-my2n2e, .framer-GdT3P .framer-my2n2e { display: block; }", ".framer-GdT3P.framer-4lucl9 { align-content: center; align-items: center; cursor: pointer; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 0px; height: 58px; justify-content: center; overflow: hidden; padding: 18px; position: relative; width: 240px; will-change: var(--framer-will-change-override, transform); }", ".framer-GdT3P .framer-o38np2 { -webkit-user-select: none; flex: none; height: auto; left: 50%; position: absolute; top: -22px; user-select: none; white-space: pre; width: auto; z-index: 1; }", ".framer-GdT3P .framer-1zgpu1 { -webkit-user-select: none; flex: none; height: auto; position: relative; user-select: none; white-space: pre; width: auto; }", ".framer-GdT3P .framer-6agcka { aspect-ratio: 1 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 20px); overflow: hidden; position: relative; width: 20px; }", ".framer-GdT3P .framer-18aqab3 { bottom: 0px; flex: none; left: 0px; overflow: visible; position: absolute; right: 0px; top: 0px; }", ".framer-GdT3P .framer-1r4sjfr { aspect-ratio: 1 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 2px); left: 50%; overflow: visible; position: absolute; top: 0px; width: 2px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-GdT3P.framer-4lucl9 { gap: 0px; } .framer-GdT3P.framer-4lucl9 > * { margin: 0px; margin-left: calc(0px / 2); margin-right: calc(0px / 2); } .framer-GdT3P.framer-4lucl9 > :first-child { margin-left: 0px; } .framer-GdT3P.framer-4lucl9 > :last-child { margin-right: 0px; } }", ".framer-GdT3P.framer-v-1rmx67o.framer-4lucl9, .framer-GdT3P.framer-v-2gmwtk.framer-4lucl9, .framer-GdT3P.framer-v-p8053s.framer-4lucl9, .framer-GdT3P.framer-v-1h3xpm4.framer-4lucl9 { cursor: unset; }", ".framer-GdT3P.framer-v-1rmx67o .framer-18aqab3 { overflow: hidden; }", ".framer-GdT3P.framer-v-4lucl9.hover .framer-o38np2 { left: unset; position: relative; top: unset; }", ".framer-GdT3P.framer-v-4lucl9.hover .framer-1zgpu1 { left: 50%; position: absolute; top: 59px; z-index: 1; }"];
var FramerZsPpcvZL7 = withCSS(Component, css, "framer-GdT3P");
var stdin_default = FramerZsPpcvZL7;
FramerZsPpcvZL7.displayName = "Large button submit";
FramerZsPpcvZL7.defaultProps = { height: 58, width: 240 };
addPropertyControls(FramerZsPpcvZL7, { variant: { options: ["wYDONdaYp", "klvioFlTK", "eaKPgDO7P", "dq7iWPLH9", "wDK8O1Ezz"], optionTitles: ["Default", "Loading", "Disabled", "Success", "Error"], title: "Variant", type: ControlType.Enum }, X9Xju9FBn: { defaultValue: "Submit", displayTextArea: false, title: "Title", type: ControlType.String }, GNYtni0Of: { defaultValue: "rgb(255, 255, 255)", title: "Color", type: ControlType.Color }, MoodCIitJ: { defaultValue: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", title: "BG", type: ControlType.Color }, xKHL3eyCS: { defaultValue: 'var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)) /* {"name":"White"} */', title: "Hover color", type: ControlType.Color }, UZBAPRl6f: { defaultValue: "rgb(0, 0, 0)", title: "Hover BG", type: ControlType.Color } });
addFonts(FramerZsPpcvZL7, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/hyOgCu0Xnghbimh0pE8QTvtt2AU.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/NeGmSOXrPBfEFIy5YZeHq17LEDA.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/oYaAX5himiTPYuN8vLWnqBbfD2s.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/lEJLP4R0yuCaMCjSXYHtJw72M.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/cRJyLNuTJR5jbyKzGi33wU9cqIQ.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/1ZFS7N918ojhhd0nQWdj3jz4w.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/A0Wcc7NgXMjUuFdquHDrIZpzZw0.woff2", weight: "600" }] }], { supportsExplicitInterCodegen: true });

// virtual:large-button-submit
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {
  "base": "wYDONdaYp"
};
stdin_default.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default);
export {
  ComponentWithRoot as default
};
