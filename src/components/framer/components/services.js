// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";
import {
  stdin_default as stdin_default4
} from "./chunks/chunk-ZT4GUZK4.js";
import {
  className,
  css,
  fonts
} from "./chunks/chunk-F5ZOKVUG.js";
import {
  stdin_default as stdin_default2
} from "./chunks/chunk-32JJPPLR.js";
import "./chunks/chunk-G73PZF75.js";
import "./chunks/chunk-MO4ELLBD.js";
import {
  stdin_default
} from "./chunks/chunk-CHS3IKS5.js";
import "./chunks/chunk-MKEJQGYO.js";
import {
  stdin_default as stdin_default3
} from "./chunks/chunk-X65FQ74X.js";
import "./chunks/chunk-NFATBK6V.js";
import "./chunks/chunk-45SLURIC.js";
import "./chunks/chunk-YNN2WKUD.js";
import "./chunks/chunk-SIWCYXHC.js";
import "./chunks/chunk-VY5WWL2S.js";
import "./chunks/chunk-3LMAB7ZO.js";
import "./chunks/chunk-YWUWNR35.js";

// virtual:services
import { Fragment as Fragment2 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/hURpBmt4DoL1ekI04Bxb/9v8HRjwLCA1gvmbMoIE2/CiB8L1GbI.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, ComponentViewportProvider, cx, getFonts, getFontsFromSharedStyle, ResolveLinks, RichText, SmartComponentScopedContainer, useComponentViewport, useLocaleInfo, useRouter, useVariantState, withCSS, withFX } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var IntroFonts = getFonts(stdin_default);
var RichTextWithFX = withFX(RichText);
var TableFonts = getFonts(stdin_default3);
var LargeButtonFonts = getFonts(stdin_default2);
var GradientBackgroundFonts = getFonts(stdin_default4);
var serializationHash = "framer-yFFd9";
var variantClassNames = { F8MDhUWNo: "framer-v-8d5aox" };
var transition1 = { bounce: 0.2, delay: 0, duration: 0.4, type: "spring" };
var animation = { opacity: 0, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, x: 0, y: 0 };
var transition2 = { bounce: 0.1, delay: 0.2, duration: 1.2, type: "spring" };
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var getProps = ({ height, id, width, ...props }) => {
  return { ...props };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className: className2, layoutId, variant, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ defaultVariant: "F8MDhUWNo", ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [className];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  const router = useRouter();
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsxs(motion.section, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-8d5aox", className2, classNames), "data-framer-name": "Variant 1", layoutDependency, layoutId: "F8MDhUWNo", ref: refBinding, style: { backgroundColor: "var(--token-eea70a16-506d-4b3b-87b7-e85e653a6e7c, rgb(245, 245, 245))", ...style }, children: [/* @__PURE__ */ _jsxs(motion.div, { className: "framer-o7lid4", "data-framer-name": "Container", layoutDependency, layoutId: "RC_t3iBCu", children: [/* @__PURE__ */ _jsxs(motion.div, { className: "framer-104r01y", "data-framer-name": "Top", layoutDependency, layoutId: "tbbK7Bxv8", children: [/* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 22, width: `max((min(max(${componentViewport?.width || "100vw"} - 72px, 1px), 1520px) - 12px) / 4, 50px)`, y: (componentViewport?.y || 0) + (200 + ((componentViewport?.height || 1290) - 400 - 867.48) / 2) + 0 + 0 + 0 + 0, children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-66gr9q-container", layoutDependency, layoutId: "PzR23hkVz-container", nodeId: "PzR23hkVz", rendersWithMotion: true, scopeId: "CiB8L1GbI", children: /* @__PURE__ */ _jsx(stdin_default, { height: "100%", id: "PzR23hkVz", k3Z3ztoi4: "What we do", layoutId: "PzR23hkVz", RaZgbjWXH: "rgb(255, 255, 255)", TmIm48vq7: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", variant: "VaDzezBN8", width: "100%", ws4vDxEZM: "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))" }) }) }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-1xt25lo", "data-framer-name": "Heading", layoutDependency, layoutId: "nPAKIwZlF", children: [/* @__PURE__ */ _jsx(RichTextWithFX, { __framer__animate: { transition: transition2 }, __framer__animateOnce: true, __framer__enter: animation, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0, __fromCanvasComponent: true, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.h2, { className: "framer-styles-preset-1yvd34u", "data-styles-preset": "GKtOymhXV", style: { "--framer-text-color": "var(--extracted-1of0zx5, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Services." }) }), className: "framer-1ampza", fonts: ["Inter"], layoutDependency, layoutId: "aouiHhqUu", style: { "--extracted-1of0zx5": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", "--framer-paragraph-spacing": "0px" }, verticalAlignment: "top", withExternalLayout: true }), /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "37px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.06em", "--framer-text-color": "var(--extracted-r6o4lv, rgb(255, 255, 255))" }, children: "(4)" }) }), className: "framer-65m0y4", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "fMpwN1Zhc", style: { "--extracted-r6o4lv": "rgb(255, 255, 255)", "--framer-paragraph-spacing": "0px", opacity: 0.4 }, verticalAlignment: "top", withExternalLayout: true })] })] }), /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 517, width: `min(max(${componentViewport?.width || "100vw"} - 72px, 1px), 1520px)`, y: (componentViewport?.y || 0) + (200 + ((componentViewport?.height || 1290) - 400 - 867.48) / 2) + 0 + 212.48, children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-1hh6mhy-container", layoutDependency, layoutId: "St9IOFxCU-container", nodeId: "St9IOFxCU", rendersWithMotion: true, scopeId: "CiB8L1GbI", children: /* @__PURE__ */ _jsx(stdin_default3, { height: "100%", id: "St9IOFxCU", layoutId: "St9IOFxCU", style: { width: "100%" }, variant: "ooQ9Z1hJi", width: "100%" }) }) }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-1lbrd8x", "data-framer-name": "Bottom", layoutDependency, layoutId: "sX3lU50ZD", children: [/* @__PURE__ */ _jsx(motion.div, { className: "framer-1ykazkz", "data-framer-name": "Filler", layoutDependency, layoutId: "GHACI_yup" }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-o105nz", "data-framer-name": "Container", layoutDependency, layoutId: "qDu52fU7L", children: /* @__PURE__ */ _jsx(ResolveLinks, { links: [{ href: { webPageId: "hAlvrhFMj" }, implicitPathVariables: void 0 }], children: (resolvedLinks) => /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 58, y: (componentViewport?.y || 0) + (200 + ((componentViewport?.height || 1290) - 400 - 867.48) / 2) + 0 + 809.48 + 0 + 0 + 0, children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-r9wof4-container", layoutDependency, layoutId: "R5n5ZdzBH-container", nodeId: "R5n5ZdzBH", rendersWithMotion: true, scopeId: "CiB8L1GbI", children: /* @__PURE__ */ _jsx(stdin_default2, { height: "100%", id: "R5n5ZdzBH", iyuXB1N8q: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", layoutId: "R5n5ZdzBH", R8iwJ2h7U: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", SnWMJ5xn7: "rgb(235, 235, 235)", tulsc_W5A: "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", variant: "VMnT_oIqC", width: "100%", X9Xju9FBn: "Get started", XooFhyn6y: resolvedLinks[0] }) }) }) }) })] })] }), /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: (componentViewport?.height || 1290) - 0, width: `calc(${componentViewport?.width || "100vw"} - 12px)`, y: (componentViewport?.y || 0) + 0, children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-kmvmgk-container", layoutDependency, layoutId: "lhAEFJAl6-container", nodeId: "lhAEFJAl6", rendersWithMotion: true, scopeId: "CiB8L1GbI", children: /* @__PURE__ */ _jsx(stdin_default4, { EfstzZdWy: 0.5, fgfZL9eoW: "Upload", height: "100%", id: "lhAEFJAl6", layoutId: "lhAEFJAl6", NXkb7129J: false, style: { height: "100%", width: "100%" }, variant: "DQjSdvNWb", width: "100%" }) }) })] }) }) }) });
});
var css2 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-yFFd9.framer-19ulqwx, .framer-yFFd9 .framer-19ulqwx { display: block; }", ".framer-yFFd9.framer-8d5aox { align-content: center; align-items: center; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: center; overflow: hidden; padding: 200px 36px 200px 36px; position: relative; width: 1200px; }", ".framer-yFFd9 .framer-o7lid4 { align-content: center; align-items: center; display: flex; flex: 1 0 0px; flex-direction: column; flex-wrap: nowrap; gap: 80px; height: min-content; justify-content: center; max-width: 1520px; overflow: hidden; padding: 0px; position: relative; width: 1px; z-index: 1; }", ".framer-yFFd9 .framer-104r01y { display: grid; flex: none; gap: 4px; grid-auto-rows: minmax(0, 1fr); grid-template-columns: repeat(4, minmax(50px, 1fr)); grid-template-rows: repeat(1, minmax(0, 1fr)); height: min-content; justify-content: center; overflow: visible; padding: 0px; position: relative; width: 100%; z-index: 1; }", ".framer-yFFd9 .framer-66gr9q-container { align-self: start; flex: none; height: auto; justify-self: start; position: relative; width: 100%; }", ".framer-yFFd9 .framer-1xt25lo { align-content: flex-start; align-items: flex-start; align-self: start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 22px; grid-column: span 3; height: min-content; justify-content: center; justify-self: start; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-yFFd9 .framer-1ampza { flex: none; height: auto; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }", ".framer-yFFd9 .framer-65m0y4 { flex: none; height: auto; left: 573px; position: absolute; top: 2px; white-space: pre; width: auto; z-index: 1; }", ".framer-yFFd9 .framer-1hh6mhy-container { flex: none; height: auto; position: relative; width: 100%; }", ".framer-yFFd9 .framer-1lbrd8x { display: grid; flex: none; gap: 4px; grid-auto-rows: minmax(0, 1fr); grid-template-columns: repeat(4, minmax(50px, 1fr)); grid-template-rows: repeat(1, minmax(0, 1fr)); height: min-content; justify-content: center; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-yFFd9 .framer-1ykazkz { align-self: start; flex: none; height: 100%; justify-self: start; overflow: hidden; position: relative; width: 100%; }", ".framer-yFFd9 .framer-o105nz { align-content: flex-start; align-items: flex-start; align-self: start; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 4px; grid-column: span 3; height: min-content; justify-content: flex-start; justify-self: start; overflow: hidden; padding: 0px; position: relative; width: 100%; }", ".framer-yFFd9 .framer-r9wof4-container { flex: none; height: auto; position: relative; width: auto; }", ".framer-yFFd9 .framer-kmvmgk-container { bottom: 0px; flex: none; left: 6px; position: absolute; right: 6px; top: 0px; z-index: 0; }", ...css];
var FramerCiB8L1GbI = withCSS(Component, css2, "framer-yFFd9");
var stdin_default5 = FramerCiB8L1GbI;
FramerCiB8L1GbI.displayName = "Services";
FramerCiB8L1GbI.defaultProps = { height: 1290, width: 1200 };
addFonts(FramerCiB8L1GbI, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5vvr9Vy74if2I6bQbJvbw7SY1pQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/EOr0mi4hNtlgWNn9if640EZzXCo.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/Y9k9QrlZAqio88Klkmbd8VoMQc.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/OYrD2tBIBPvoJXiIHnLoOXnY9M.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/JeYwfuaPfZHQhEG8U5gtPDZ7WQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/vQyevYAyHtARFwPqUzQGpnDs.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/b6Y37FthZeALduNqHicBT6FutY.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/hyOgCu0Xnghbimh0pE8QTvtt2AU.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/NeGmSOXrPBfEFIy5YZeHq17LEDA.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/oYaAX5himiTPYuN8vLWnqBbfD2s.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/lEJLP4R0yuCaMCjSXYHtJw72M.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/cRJyLNuTJR5jbyKzGi33wU9cqIQ.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/1ZFS7N918ojhhd0nQWdj3jz4w.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/A0Wcc7NgXMjUuFdquHDrIZpzZw0.woff2", weight: "600" }] }, ...IntroFonts, ...TableFonts, ...LargeButtonFonts, ...GradientBackgroundFonts, ...getFontsFromSharedStyle(fonts)], { supportsExplicitInterCodegen: true });

// virtual:services
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {};
stdin_default5.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default5,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default5, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default5);
export {
  ComponentWithRoot as default
};
