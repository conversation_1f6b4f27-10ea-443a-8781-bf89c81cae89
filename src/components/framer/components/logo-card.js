// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";

// virtual:logo-card
import { Fragment as Fragment2 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/yRDItg6CGqm47W2hBzp5/IzT1L6BlcymqiRv450Eg/h_AuDmjef.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, getLoadingLazyAtYPosition, Image, RichText, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
var cycleOrder = ["syyj8WlGu", "QkN8ZtXfV", "uxdlC_ZUF"];
var serializationHash = "framer-BN5AO";
var variantClassNames = { QkN8ZtXfV: "framer-v-taipr6", syyj8WlGu: "framer-v-1e28j5d", uxdlC_ZUF: "framer-v-u3hjxn" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { bounce: 0.2, delay: 0, duration: 0.4, type: "spring" };
var toResponsiveImage = (value) => {
  if (typeof value === "object" && value !== null && typeof value.src === "string") {
    return value;
  }
  return typeof value === "string" ? { src: value } : void 0;
};
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { Desktop: "syyj8WlGu", Phone: "uxdlC_ZUF", Tablet: "QkN8ZtXfV" };
var getProps = ({ height, id, image, text, width, ...props }) => {
  return { ...props, qsyQalKks: image ?? props.qsyQalKks ?? { alt: "", src: "https://framerusercontent.com/images/2e9rGrOkACVfd78cX0SzqLLw.svg" }, variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "syyj8WlGu", ykMn11QcS: text ?? props.ykMn11QcS ?? "/2025" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const { activeLocale, setLocale } = useLocaleInfo();
  const { style, className, layoutId, variant, qsyQalKks, ykMn11QcS, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "syyj8WlGu", variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  const ref1 = React.useRef(null);
  const defaultLayoutId = React.useId();
  const componentViewport = useComponentViewport();
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsxs(motion.div, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-1e28j5d", className, classNames), "data-framer-name": "Desktop", layoutDependency, layoutId: "syyj8WlGu", ref: ref ?? ref1, style: { backgroundColor: "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", borderBottomLeftRadius: 14, borderBottomRightRadius: 14, borderTopLeftRadius: 14, borderTopRightRadius: 14, ...style }, ...addPropertyOverrides({ QkN8ZtXfV: { "data-framer-name": "Tablet" }, uxdlC_ZUF: { "data-framer-name": "Phone" } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsx(Image, { background: { alt: "", fit: "fit", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + (0 + ((componentViewport?.height || 370) - 0 - 54) / 2)), pixelHeight: 42, pixelWidth: 104, sizes: "120px", ...toResponsiveImage(qsyQalKks), ...{ positionX: "center", positionY: "center" } }, className: "framer-1wkm36l", layoutDependency, layoutId: "HtMUtbGPp", ...addPropertyOverrides({ QkN8ZtXfV: { background: { alt: "", fit: "fit", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + (0 + ((componentViewport?.height || 370) - 0 - 45) / 2)), pixelHeight: 42, pixelWidth: 104, sizes: "90px", ...toResponsiveImage(qsyQalKks), ...{ positionX: "center", positionY: "center" } } }, uxdlC_ZUF: { background: { alt: "", fit: "fit", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + (0 + ((componentViewport?.height || 370) - 0 - 40) / 2)), pixelHeight: 42, pixelWidth: 104, sizes: "86px", ...toResponsiveImage(qsyQalKks), ...{ positionX: "center", positionY: "center" } } } }, baseVariant, gestureVariant) }), /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItTWVkaXVt", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "12px", "--framer-font-weight": "500", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "130%", "--framer-text-alignment": "right", "--framer-text-color": "var(--extracted-r6o4lv, rgb(9, 9, 9))" }, children: "/2025" }) }), className: "framer-s0i6xp", "data-framer-name": "Date", fonts: ["Inter-Medium"], layoutDependency, layoutId: "LjXFVNFid", style: { "--extracted-r6o4lv": "rgb(9, 9, 9)", "--framer-paragraph-spacing": "0px", opacity: 0.6 }, text: ykMn11QcS, verticalAlignment: "top", withExternalLayout: true, ...addPropertyOverrides({ QkN8ZtXfV: { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItTWVkaXVt", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "11px", "--framer-font-weight": "500", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "130%", "--framer-text-alignment": "right", "--framer-text-color": "var(--extracted-r6o4lv, rgb(9, 9, 9))" }, children: "/2025" }) }) }, uxdlC_ZUF: { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItTWVkaXVt", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "10px", "--framer-font-weight": "500", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "130%", "--framer-text-alignment": "right", "--framer-text-color": "var(--extracted-r6o4lv, rgb(9, 9, 9))" }, children: "/2025" }) }) } }, baseVariant, gestureVariant) })] }) }) }) });
});
var css = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-BN5AO.framer-1nipkoe, .framer-BN5AO .framer-1nipkoe { display: block; }", ".framer-BN5AO.framer-1e28j5d { align-content: center; align-items: center; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: 370px; justify-content: center; overflow: hidden; padding: 0px; position: relative; width: 370px; will-change: var(--framer-will-change-override, transform); }", ".framer-BN5AO .framer-1wkm36l { aspect-ratio: 2.2222222222222223 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 54px); position: relative; width: 120px; }", ".framer-BN5AO .framer-s0i6xp { bottom: 16px; flex: none; height: auto; position: absolute; right: 16px; white-space: pre; width: auto; z-index: 1; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-BN5AO.framer-1e28j5d { gap: 0px; } .framer-BN5AO.framer-1e28j5d > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } .framer-BN5AO.framer-1e28j5d > :first-child { margin-left: 0px; } .framer-BN5AO.framer-1e28j5d > :last-child { margin-right: 0px; } }", ".framer-BN5AO.framer-v-taipr6.framer-1e28j5d, .framer-BN5AO.framer-v-u3hjxn.framer-1e28j5d { aspect-ratio: 1 / 1; height: var(--framer-aspect-ratio-supported, 370px); }", ".framer-BN5AO.framer-v-taipr6 .framer-1wkm36l { height: var(--framer-aspect-ratio-supported, 45px); width: 90px; }", ".framer-BN5AO.framer-v-u3hjxn .framer-1wkm36l { height: var(--framer-aspect-ratio-supported, 40px); width: 86px; }", ".framer-BN5AO.framer-v-u3hjxn .framer-s0i6xp { bottom: 14px; right: 14px; }"];
var Framerh_AuDmjef = withCSS(Component, css, "framer-BN5AO");
var stdin_default = Framerh_AuDmjef;
Framerh_AuDmjef.displayName = "Logo card";
Framerh_AuDmjef.defaultProps = { height: 370, width: 370 };
addPropertyControls(Framerh_AuDmjef, { variant: { options: ["syyj8WlGu", "QkN8ZtXfV", "uxdlC_ZUF"], optionTitles: ["Desktop", "Tablet", "Phone"], title: "Variant", type: ControlType.Enum }, qsyQalKks: { __defaultAssetReference: "data:framer/asset-reference,2e9rGrOkACVfd78cX0SzqLLw.svg?originalFilename=Logo-2.svg&preferredSize=auto", __vekterDefault: { alt: "", assetReference: "data:framer/asset-reference,2e9rGrOkACVfd78cX0SzqLLw.svg?originalFilename=Logo-2.svg&preferredSize=auto" }, title: "Image", type: ControlType.ResponsiveImage }, ykMn11QcS: { defaultValue: "/2025", displayTextArea: false, title: "Text", type: ControlType.String } });
addFonts(Framerh_AuDmjef, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5A3Ce6C9YYmCjpQx9M4inSaKU.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/Qx95Xyt0Ka3SGhinnbXIGpEIyP4.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/6mJuEAguuIuMog10gGvH5d3cl8.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/xYYWaj7wCU5zSQH0eXvSaS19wo.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/otTaNuNpVK4RbdlT7zDDdKvQBA.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/d3tHnaQIAeqiE5hGcRw4mmgWYU.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/DolVirEGb34pEXEp8t8FQBSK4.woff2", weight: "500" }] }], { supportsExplicitInterCodegen: true });

// virtual:logo-card
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {
  "base": "uxdlC_ZUF",
  "md": "QkN8ZtXfV",
  "xl": "syyj8WlGu"
};
stdin_default.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default);
export {
  ComponentWithRoot as default
};
