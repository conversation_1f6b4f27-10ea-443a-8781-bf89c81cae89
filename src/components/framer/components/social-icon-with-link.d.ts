/* This file was generated by Unframer, do not edit manually */
import type * as React from "react"

import type { UnframerBreakpoint } from "unframer"

type Locale = string
export interface Props {
    children?: React.ReactNode
    locale?: Locale
    style?: React.CSSProperties
    className?: string
    id?: string
    width?: any
    height?: any
    layoutId?: string
    "icon"?: {src: string, srcSet?: string, alt?: string}
    "iconOpacity"?: number
    "link"?: string
    "bGColor"?: string
    "padding"?: undefined
}

const SocialIconWithLinkFramerComponent = (props: Props) => any

type VariantsMap = Partial<Record<UnframerBreakpoint, Props['variant']>> & { base: Props['variant'] }

SocialIconWithLinkFramerComponent.Responsive = (props: Omit<Props, 'variant'> & {variants?: VariantsMap}) => any

export default SocialIconWithLinkFramerComponent

