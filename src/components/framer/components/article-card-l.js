// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";
import {
  className as className3,
  css as css3,
  fonts as fonts3
} from "./chunks/chunk-6MRKUUEA.js";
import {
  className as className2,
  css as css2,
  fonts as fonts2
} from "./chunks/chunk-SATCLWQE.js";
import {
  className,
  css,
  fonts
} from "./chunks/chunk-VY5WWL2S.js";

// virtual:article-card-l
import { Fragment as Fragment2 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/wb4wdricYk6hX5QoFCY0/EzLNKPFRUFRrupUhGwbG/fOV3xrRss.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, getFontsFromSharedStyle, getLoadingLazyAtYPosition, Image, Link, RichText, useComponentViewport, useLocaleCode, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var enabledGestures = { vufSgNgRJ: { hover: true } };
var cycleOrder = ["vufSgNgRJ", "Rzb4V8aYg"];
var serializationHash = "framer-NquKo";
var variantClassNames = { Rzb4V8aYg: "framer-v-1mjaou6", vufSgNgRJ: "framer-v-11vuojt" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { delay: 0, duration: 0.45, ease: [0.82, 0.11, 0.37, 0.82], type: "tween" };
var toDateString = (value, options = {}, activeLocale) => {
  if (typeof value !== "string") return "";
  const date = new Date(value);
  if (isNaN(date.getTime())) return "";
  const display = options.display ? options.display : "date";
  const dateOptions = { dateStyle: display !== "time" ? options.dateStyle : void 0, timeStyle: display === "date" ? void 0 : "short", timeZone: "UTC" };
  const fallbackLocale = "en-US";
  const locale = options.locale || activeLocale || fallbackLocale;
  try {
    return date.toLocaleString(locale, dateOptions);
  } catch {
    return date.toLocaleString(fallbackLocale, dateOptions);
  }
};
var toResponsiveImage = (value) => {
  if (typeof value === "object" && value !== null && typeof value.src === "string") {
    return value;
  }
  return typeof value === "string" ? { src: value } : void 0;
};
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { Desktop: "vufSgNgRJ", Phone: "Rzb4V8aYg" };
var getProps = ({ date, height, id, image, intro, link, title, width, ...props }) => {
  return { ...props, dBc4OjPnD: link ?? props.dBc4OjPnD, dQlWupaeL: title ?? props.dQlWupaeL ?? "How a well-designed website can transform your business", G6vHa7s1g: intro ?? props.G6vHa7s1g ?? "Discover the latest design trends shaping the digital world and how they impact business.", TXyIa9qqJ: date ?? props.TXyIa9qqJ ?? "2025-02-02T00:00:00.000Z", variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "vufSgNgRJ", XMEncQWXR: image ?? props.XMEncQWXR };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className: className4, layoutId, variant, TXyIa9qqJ, dQlWupaeL, G6vHa7s1g, XMEncQWXR, dBc4OjPnD, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "vufSgNgRJ", enabledGestures, ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [className, className3, className2];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  const activeLocaleCode = useLocaleCode();
  const textContent = toDateString(TXyIa9qqJ, { dateStyle: "medium", locale: "" }, activeLocaleCode);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsx(Link, { href: dBc4OjPnD, motionChild: true, nodeId: "vufSgNgRJ", scopeId: "fOV3xrRss", children: /* @__PURE__ */ _jsxs(motion.a, { ...restProps, ...gestureHandlers, className: `${cx(scopingClassNames, "framer-11vuojt", className4, classNames)} framer-gey2ys`, "data-framer-name": "Desktop", layoutDependency, layoutId: "vufSgNgRJ", ref: refBinding, style: { backgroundColor: "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", borderBottomLeftRadius: 18, borderBottomRightRadius: 18, borderTopLeftRadius: 18, borderTopRightRadius: 18, ...style }, ...addPropertyOverrides({ "vufSgNgRJ-hover": { "data-framer-name": void 0 }, Rzb4V8aYg: { "data-framer-name": "Phone" } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsxs(motion.div, { className: "framer-18zds6h", "data-framer-name": "Bottom", layoutDependency, layoutId: "zJmK0MRns", style: { background: "linear-gradient(180deg, rgba(10, 10, 10, 0) 0%, rgba(10, 10, 10, 0.95) 100%)", borderBottomLeftRadius: 12, borderBottomRightRadius: 12, borderTopLeftRadius: 12, borderTopRightRadius: 12 }, children: [/* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-2s58fc", "data-styles-preset": "svYtzYwMA", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Content" }) }), className: "framer-9ljkyf", fonts: ["Inter"], layoutDependency, layoutId: "emXU6Tsxi", style: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", "--framer-paragraph-spacing": "0px" }, text: textContent, verticalAlignment: "top", withExternalLayout: true }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-1ukh2tn", "data-framer-name": "Text", layoutDependency, layoutId: "d81hCIo7e", children: [/* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-hik9eh", "data-styles-preset": "zgy6bak25", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "How a well-designed website can transform your business" }) }), className: "framer-pti5bc", fonts: ["Inter"], layoutDependency, layoutId: "wWoAdZKrM", style: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))" }, text: dQlWupaeL, verticalAlignment: "top", withExternalLayout: true }), /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-1mf8d9g", "data-styles-preset": "ypR5VEWEl", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Discover the latest design trends shaping the digital world and how they impact business." }) }), className: "framer-1xb5vtk", fonts: ["Inter"], layoutDependency, layoutId: "yD24WRI2p", style: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", "--framer-paragraph-spacing": "0px", opacity: 0.6 }, text: G6vHa7s1g, verticalAlignment: "top", withExternalLayout: true })] })] }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-q8s7fm", "data-framer-name": "Image container", layoutDependency, layoutId: "P_sL9SkB5", children: /* @__PURE__ */ _jsx(Image, { background: { alt: "", fit: "fill", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0 + 0), sizes: componentViewport?.width || "100vw", ...toResponsiveImage(XMEncQWXR) }, className: "framer-f0oy6c", layoutDependency, layoutId: "pQr9gez3A", style: { filter: "none", scale: 1, WebkitFilter: "none" }, variants: { "vufSgNgRJ-hover": { filter: "blur(5px)", scale: 1.1, WebkitFilter: "blur(5px)" } } }) })] }) }) }) }) });
});
var css4 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-NquKo.framer-gey2ys, .framer-NquKo .framer-gey2ys { display: block; }", ".framer-NquKo.framer-11vuojt { align-content: center; align-items: center; cursor: pointer; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: 520px; justify-content: flex-end; overflow: hidden; padding: 0px; position: relative; text-decoration: none; width: 562px; will-change: var(--framer-will-change-override, transform); }", ".framer-NquKo .framer-18zds6h { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 20px; height: min-content; justify-content: flex-start; overflow: visible; padding: 40px; position: relative; width: 100%; z-index: 1; }", ".framer-NquKo .framer-9ljkyf { flex: none; height: auto; position: relative; white-space: pre; width: auto; }", ".framer-NquKo .framer-1ukh2tn { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 20px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-NquKo .framer-pti5bc, .framer-NquKo .framer-1xb5vtk { --framer-text-wrap-override: balance; flex: none; height: auto; max-width: 400px; position: relative; width: 100%; }", ".framer-NquKo .framer-q8s7fm { align-content: center; align-items: center; bottom: 0px; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 4px; justify-content: center; left: 0px; overflow: hidden; padding: 0px; position: absolute; right: 0px; top: 0px; z-index: 0; }", ".framer-NquKo .framer-f0oy6c { bottom: 0px; flex: none; left: 0px; position: absolute; right: 0px; top: 0px; z-index: 0; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-NquKo.framer-11vuojt, .framer-NquKo .framer-18zds6h, .framer-NquKo .framer-1ukh2tn, .framer-NquKo .framer-q8s7fm { gap: 0px; } .framer-NquKo.framer-11vuojt > * { margin: 0px; margin-bottom: calc(10px / 2); margin-top: calc(10px / 2); } .framer-NquKo.framer-11vuojt > :first-child, .framer-NquKo .framer-18zds6h > :first-child, .framer-NquKo .framer-1ukh2tn > :first-child { margin-top: 0px; } .framer-NquKo.framer-11vuojt > :last-child, .framer-NquKo .framer-18zds6h > :last-child, .framer-NquKo .framer-1ukh2tn > :last-child { margin-bottom: 0px; } .framer-NquKo .framer-18zds6h > *, .framer-NquKo .framer-1ukh2tn > * { margin: 0px; margin-bottom: calc(20px / 2); margin-top: calc(20px / 2); } .framer-NquKo .framer-q8s7fm > * { margin: 0px; margin-left: calc(4px / 2); margin-right: calc(4px / 2); } .framer-NquKo .framer-q8s7fm > :first-child { margin-left: 0px; } .framer-NquKo .framer-q8s7fm > :last-child { margin-right: 0px; } }", ".framer-NquKo.framer-v-1mjaou6.framer-11vuojt { cursor: unset; }", ".framer-NquKo.framer-v-1mjaou6 .framer-18zds6h { padding: 30px; }", ...css, ...css3, ...css2];
var FramerfOV3xrRss = withCSS(Component, css4, "framer-NquKo");
var stdin_default = FramerfOV3xrRss;
FramerfOV3xrRss.displayName = "Article card L";
FramerfOV3xrRss.defaultProps = { height: 520, width: 562 };
addPropertyControls(FramerfOV3xrRss, { variant: { options: ["vufSgNgRJ", "Rzb4V8aYg"], optionTitles: ["Desktop", "Phone"], title: "Variant", type: ControlType.Enum }, TXyIa9qqJ: { defaultValue: "2025-02-02T00:00:00.000Z", title: "Date", type: ControlType.Date }, dQlWupaeL: { defaultValue: "How a well-designed website can transform your business", title: "Title", type: ControlType.String }, G6vHa7s1g: { defaultValue: "Discover the latest design trends shaping the digital world and how they impact business.", displayTextArea: true, title: "Intro", type: ControlType.String }, XMEncQWXR: { title: "Image", type: ControlType.ResponsiveImage }, dBc4OjPnD: { title: "Link", type: ControlType.Link } });
addFonts(FramerfOV3xrRss, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5vvr9Vy74if2I6bQbJvbw7SY1pQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/EOr0mi4hNtlgWNn9if640EZzXCo.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/Y9k9QrlZAqio88Klkmbd8VoMQc.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/OYrD2tBIBPvoJXiIHnLoOXnY9M.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/JeYwfuaPfZHQhEG8U5gtPDZ7WQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/vQyevYAyHtARFwPqUzQGpnDs.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/b6Y37FthZeALduNqHicBT6FutY.woff2", weight: "400" }] }, ...getFontsFromSharedStyle(fonts), ...getFontsFromSharedStyle(fonts3), ...getFontsFromSharedStyle(fonts2)], { supportsExplicitInterCodegen: true });

// virtual:article-card-l
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {
  "base": "Rzb4V8aYg",
  "xl": "vufSgNgRJ"
};
stdin_default.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default);
export {
  ComponentWithRoot as default
};
