// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";

// virtual:rating-stars
import { Fragment as Fragment2 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/t1iJuWnZpK9UlGn9LTZR/MmGYJmvrUwcTK5x20ctC/d5lvgh6Gd.js
import { jsx as _jsx } from "react/jsx-runtime";
import { addFonts, cx, SVG, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var serializationHash = "framer-oFNEt";
var variantClassNames = { sOcfW8Afc: "framer-v-1ku8kgp" };
var transition1 = { bounce: 0.2, delay: 0, duration: 0.4, type: "spring" };
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var getProps = ({ height, id, width, ...props }) => {
  return { ...props };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className, layoutId, variant, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ defaultVariant: "sOcfW8Afc", ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsx(motion.div, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-1ku8kgp", className, classNames), "data-framer-name": "Variant 1", layoutDependency, layoutId: "sOcfW8Afc", ref: refBinding, style: { ...style }, children: /* @__PURE__ */ _jsx(SVG, { className: "framer-1pptfs4", "data-framer-name": "Star Rating", layout: "position", layoutDependency, layoutId: "teusMbag_", opacity: 1, svg: '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 56 12"><path d="M 5.087 2.049 C 5.439 1.259 6.561 1.259 6.913 2.049 L 7.423 3.192 C 7.568 3.518 7.876 3.742 8.231 3.779 L 9.475 3.91 C 10.336 4.001 10.683 5.068 10.04 5.648 L 9.11 6.485 C 8.845 6.724 8.728 7.086 8.802 7.436 L 9.061 8.66 C 9.241 9.506 8.333 10.166 7.583 9.733 L 6.5 9.108 C 6.19 8.93 5.81 8.93 5.5 9.108 L 4.417 9.733 C 3.667 10.166 2.759 9.506 2.939 8.66 L 3.198 7.436 C 3.272 7.086 3.155 6.724 2.89 6.485 L 1.96 5.648 C 1.317 5.068 1.664 4.001 2.525 3.91 L 3.769 3.779 C 4.124 3.742 4.432 3.518 4.577 3.192 Z" fill="rgb(251,152,38)"></path><path d="M 16.087 2.049 C 16.439 1.259 17.561 1.259 17.913 2.049 L 18.423 3.192 C 18.568 3.518 18.876 3.742 19.231 3.779 L 20.475 3.91 C 21.336 4.001 21.683 5.068 21.04 5.648 L 20.11 6.485 C 19.845 6.724 19.728 7.086 19.802 7.436 L 20.061 8.66 C 20.241 9.506 19.333 10.166 18.583 9.733 L 17.5 9.108 C 17.19 8.93 16.81 8.93 16.5 9.108 L 15.417 9.733 C 14.667 10.166 13.759 9.506 13.939 8.66 L 14.198 7.436 C 14.272 7.086 14.155 6.724 13.89 6.485 L 12.96 5.648 C 12.317 5.068 12.664 4.001 13.525 3.91 L 14.769 3.779 C 15.124 3.742 15.432 3.518 15.577 3.192 Z" fill="rgb(251,152,38)"></path><path d="M 27.087 2.049 C 27.439 1.259 28.561 1.259 28.913 2.049 L 29.423 3.192 C 29.568 3.518 29.876 3.742 30.231 3.779 L 31.475 3.91 C 32.336 4.001 32.683 5.068 32.04 5.648 L 31.11 6.485 C 30.845 6.724 30.728 7.086 30.802 7.436 L 31.061 8.66 C 31.241 9.506 30.333 10.166 29.583 9.733 L 28.5 9.108 C 28.19 8.93 27.81 8.93 27.5 9.108 L 26.417 9.733 C 25.667 10.166 24.759 9.506 24.939 8.66 L 25.198 7.436 C 25.272 7.086 25.155 6.724 24.89 6.485 L 23.96 5.648 C 23.317 5.068 23.664 4.001 24.525 3.91 L 25.769 3.779 C 26.124 3.742 26.432 3.518 26.577 3.192 Z" fill="rgb(251,152,38)"></path><path d="M 38.087 2.049 C 38.439 1.259 39.561 1.259 39.913 2.049 L 40.423 3.192 C 40.568 3.518 40.876 3.742 41.231 3.779 L 42.475 3.91 C 43.336 4.001 43.683 5.068 43.04 5.648 L 42.11 6.485 C 41.845 6.724 41.728 7.086 41.802 7.436 L 42.061 8.66 C 42.241 9.506 41.333 10.166 40.583 9.733 L 39.5 9.108 C 39.19 8.93 38.81 8.93 38.5 9.108 L 37.417 9.733 C 36.667 10.166 35.759 9.506 35.939 8.66 L 36.198 7.436 C 36.272 7.086 36.155 6.724 35.89 6.485 L 34.96 5.648 C 34.317 5.068 34.664 4.001 35.525 3.91 L 36.769 3.779 C 37.124 3.742 37.432 3.518 37.577 3.192 Z" fill="rgb(251,152,38)"></path><path d="M 49.087 2.049 C 49.439 1.259 50.561 1.259 50.913 2.049 L 51.423 3.192 C 51.568 3.518 51.876 3.742 52.231 3.779 L 53.475 3.91 C 54.336 4.001 54.683 5.068 54.04 5.648 L 53.11 6.485 C 52.845 6.724 52.728 7.086 52.802 7.436 L 53.061 8.66 C 53.241 9.506 52.333 10.166 51.583 9.733 L 50.5 9.108 C 50.19 8.93 49.81 8.93 49.5 9.108 L 48.417 9.733 C 47.667 10.166 46.759 9.506 46.939 8.66 L 47.198 7.436 C 47.272 7.086 47.155 6.724 46.89 6.485 L 45.96 5.648 C 45.317 5.068 45.664 4.001 46.525 3.91 L 47.769 3.779 C 48.124 3.742 48.432 3.518 48.577 3.192 Z" fill="rgb(251,152,38)"></path></svg>', svgContentId: 10693373749, withExternalLayout: true }) }) }) }) });
});
var css = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-oFNEt.framer-1vkcmat, .framer-oFNEt .framer-1vkcmat { display: block; }", ".framer-oFNEt.framer-1ku8kgp { height: 12px; overflow: hidden; position: relative; width: 56px; }", ".framer-oFNEt .framer-1pptfs4 { flex: none; height: 12px; left: calc(50.00000000000002% - 56px / 2); position: absolute; top: calc(50.00000000000002% - 12px / 2); width: 56px; }"];
var Framerd5lvgh6Gd = withCSS(Component, css, "framer-oFNEt");
var stdin_default = Framerd5lvgh6Gd;
Framerd5lvgh6Gd.displayName = "Rating stars";
Framerd5lvgh6Gd.defaultProps = { height: 12, width: 56 };
addFonts(Framerd5lvgh6Gd, [{ explicitInter: true, fonts: [] }], { supportsExplicitInterCodegen: true });

// virtual:rating-stars
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {};
stdin_default.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default);
export {
  ComponentWithRoot as default
};
