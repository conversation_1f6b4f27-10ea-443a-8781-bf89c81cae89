// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";

// virtual:animated-line
import { Fragment as Fragment2 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/ggiAzj5S78TrxIvWWoMw/8KwDBIYbpseXDz20Cmq5/uAsrSsx9N.js
import { jsx as _jsx } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var cycleOrder = ["KTHclA_ym", "W8r8zwETw"];
var serializationHash = "framer-Sd69O";
var variantClassNames = { KTHclA_ym: "framer-v-1usovu", W8r8zwETw: "framer-v-1gi0f1v" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { delay: 0, duration: 1, ease: [0.85, -0.02, 0.31, 0.96], type: "tween" };
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { End: "W8r8zwETw", Start: "KTHclA_ym" };
var getProps = ({ color, height, id, opacity, width, ...props }) => {
  return { ...props, eV0W9xNCm: opacity ?? props.eV0W9xNCm ?? 0.12, L7khgg0dA: color ?? props.L7khgg0dA ?? "rgb(0, 0, 0)", variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "KTHclA_ym" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className, layoutId, variant, L7khgg0dA, eV0W9xNCm, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "KTHclA_ym", ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsx(motion.div, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-1usovu", className, classNames), "data-framer-name": "Start", layoutDependency, layoutId: "KTHclA_ym", ref: refBinding, style: { ...style }, ...addPropertyOverrides({ W8r8zwETw: { "data-framer-name": "End" } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx(motion.div, { className: "framer-14cnlj3", layoutDependency, layoutId: "V6ORrYAOD", style: { backgroundColor: L7khgg0dA, opacity: 1 }, variants: { W8r8zwETw: { opacity: eV0W9xNCm } } }) }) }) }) });
});
var css = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-Sd69O.framer-1x809v9, .framer-Sd69O .framer-1x809v9 { display: block; }", ".framer-Sd69O.framer-1usovu { height: 1px; overflow: visible; position: relative; width: 200px; }", ".framer-Sd69O .framer-14cnlj3 { flex: none; height: 1px; left: 0px; overflow: hidden; position: absolute; top: 0px; width: 1px; }", ".framer-Sd69O.framer-v-1gi0f1v .framer-14cnlj3 { width: 100%; }"];
var FrameruAsrSsx9N = withCSS(Component, css, "framer-Sd69O");
var stdin_default = FrameruAsrSsx9N;
FrameruAsrSsx9N.displayName = "Animated line";
FrameruAsrSsx9N.defaultProps = { height: 1, width: 200 };
addPropertyControls(FrameruAsrSsx9N, { variant: { options: ["KTHclA_ym", "W8r8zwETw"], optionTitles: ["Start", "End"], title: "Variant", type: ControlType.Enum }, L7khgg0dA: { defaultValue: "rgb(0, 0, 0)", title: "Color", type: ControlType.Color }, eV0W9xNCm: { defaultValue: 0.12, max: 1, min: 0, step: 0.01, title: "Opacity", type: ControlType.Number } });
addFonts(FrameruAsrSsx9N, [{ explicitInter: true, fonts: [] }], { supportsExplicitInterCodegen: true });

// virtual:animated-line
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {
  "base": "KTHclA_ym",
  "xl": "W8r8zwETw"
};
stdin_default.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default);
export {
  ComponentWithRoot as default
};
