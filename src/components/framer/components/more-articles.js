// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";
import {
  className,
  css,
  fonts
} from "./chunks/chunk-SIWCYXHC.js";
import {
  className as className2,
  css as css2,
  fonts as fonts2
} from "./chunks/chunk-VY5WWL2S.js";

// virtual:more-articles
import { Fragment as Fragment2 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/s05IbLhj72TZhduOAuNj/weAk3ePS4spt3ofDeb8O/Uqu9MXhEM.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, getFontsFromSharedStyle, getLoadingLazyAtYPosition, Image, Link, RichText, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var enabledGestures = { jCunBFOfe: { hover: true }, nMB5UvAyL: { hover: true } };
var cycleOrder = ["jCunBFOfe", "nMB5UvAyL", "ufdMzEXzI", "biKIUuwSe"];
var serializationHash = "framer-OfAkg";
var variantClassNames = { biKIUuwSe: "framer-v-o4h3t1", jCunBFOfe: "framer-v-1qtwle1", nMB5UvAyL: "framer-v-17ifwsy", ufdMzEXzI: "framer-v-93909c" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { delay: 0, duration: 0.45, ease: [0.82, 0.11, 0.37, 0.82], type: "tween" };
var toResponsiveImage = (value) => {
  if (typeof value === "object" && value !== null && typeof value.src === "string") {
    return value;
  }
  return typeof value === "string" ? { src: value } : void 0;
};
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { "Next Desktop": "nMB5UvAyL", "Next Phone": "ufdMzEXzI", "Previous Desktop": "jCunBFOfe", "Previous Phone": "biKIUuwSe" };
var getProps = ({ height, id, image, link, title, width, ...props }) => {
  return { ...props, HEjf_43Qf: image ?? props.HEjf_43Qf, lCEynwwe4: link ?? props.lCEynwwe4, NpDL3Onrz: title ?? props.NpDL3Onrz ?? "How a well-designed website can transform your business", variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "jCunBFOfe" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className: className3, layoutId, variant, lCEynwwe4, HEjf_43Qf, NpDL3Onrz, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "jCunBFOfe", enabledGestures, ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [className2, className];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsx(Link, { href: lCEynwwe4, motionChild: true, nodeId: "jCunBFOfe", openInNewTab: false, scopeId: "Uqu9MXhEM", children: /* @__PURE__ */ _jsxs(motion.a, { ...restProps, ...gestureHandlers, className: `${cx(scopingClassNames, "framer-1qtwle1", className3, classNames)} framer-1ivnhzg`, "data-framer-name": "Previous Desktop", layoutDependency, layoutId: "jCunBFOfe", ref: refBinding, style: { backgroundColor: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", borderBottomLeftRadius: 14, borderBottomRightRadius: 14, borderTopLeftRadius: 14, borderTopRightRadius: 14, ...style }, ...addPropertyOverrides({ "jCunBFOfe-hover": { "data-framer-name": void 0 }, "nMB5UvAyL-hover": { "data-framer-name": void 0 }, biKIUuwSe: { "data-framer-name": "Previous Phone" }, nMB5UvAyL: { "data-framer-name": "Next Desktop" }, ufdMzEXzI: { "data-framer-name": "Next Phone" } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsx(Image, { background: { alt: "", fit: "fill", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 0), sizes: componentViewport?.width || "100vw", ...toResponsiveImage(HEjf_43Qf) }, className: "framer-1g5uafu", layoutDependency, layoutId: "nP1JAf8Zu", style: { filter: "none", opacity: 0.6, scale: 1, WebkitFilter: "none" }, variants: { "jCunBFOfe-hover": { filter: "blur(5px)", scale: 1.1, WebkitFilter: "blur(5px)" }, "nMB5UvAyL-hover": { filter: "blur(5px)", scale: 1.1, WebkitFilter: "blur(5px)" } } }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-q0qaps", "data-framer-name": "Content", layoutDependency, layoutId: "tsjb7jdcV", children: [/* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-2s58fc", "data-styles-preset": "svYtzYwMA", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Previous" }) }), className: "framer-1fluduv", fonts: ["Inter"], layoutDependency, layoutId: "aHiPvTOLI", style: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", "--framer-paragraph-spacing": "0px", opacity: 0.7 }, verticalAlignment: "top", withExternalLayout: true, ...addPropertyOverrides({ nMB5UvAyL: { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-2s58fc", "data-styles-preset": "svYtzYwMA", style: { "--framer-text-alignment": "right", "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Next" }) }) }, ufdMzEXzI: { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-2s58fc", "data-styles-preset": "svYtzYwMA", style: { "--framer-text-alignment": "left", "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Next" }) }) } }, baseVariant, gestureVariant) }), /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-1oueo73", "data-styles-preset": "HLpRTFhim", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "How a well-designed website can transform your business" }) }), className: "framer-tss070", fonts: ["Inter"], layoutDependency, layoutId: "nru6sDvBK", style: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))" }, text: NpDL3Onrz, verticalAlignment: "top", withExternalLayout: true, ...addPropertyOverrides({ nMB5UvAyL: { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-1oueo73", "data-styles-preset": "HLpRTFhim", style: { "--framer-text-alignment": "right", "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "How a well-designed website can transform your business" }) }) }, ufdMzEXzI: { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-1oueo73", "data-styles-preset": "HLpRTFhim", style: { "--framer-text-alignment": "left", "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "How a well-designed website can transform your business" }) }) } }, baseVariant, gestureVariant) })] })] }) }) }) }) });
});
var css3 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-OfAkg.framer-1ivnhzg, .framer-OfAkg .framer-1ivnhzg { display: block; }", ".framer-OfAkg.framer-1qtwle1 { align-content: center; align-items: center; cursor: pointer; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 4px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 50px 40px 50px 40px; position: relative; text-decoration: none; width: 361px; will-change: var(--framer-will-change-override, transform); }", ".framer-OfAkg .framer-1g5uafu { bottom: 0px; flex: none; left: 0px; position: absolute; right: 0px; top: 0px; z-index: 1; }", ".framer-OfAkg .framer-q0qaps { align-content: flex-start; align-items: flex-start; display: flex; flex: 1 0 0px; flex-direction: column; flex-wrap: nowrap; gap: 14px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: 1px; z-index: 1; }", ".framer-OfAkg .framer-1fluduv { flex: none; height: auto; position: relative; white-space: pre; width: auto; }", ".framer-OfAkg .framer-tss070 { --framer-text-wrap-override: balance; flex: none; height: auto; max-width: 400px; position: relative; width: 100%; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-OfAkg.framer-1qtwle1, .framer-OfAkg .framer-q0qaps { gap: 0px; } .framer-OfAkg.framer-1qtwle1 > * { margin: 0px; margin-left: calc(4px / 2); margin-right: calc(4px / 2); } .framer-OfAkg.framer-1qtwle1 > :first-child { margin-left: 0px; } .framer-OfAkg.framer-1qtwle1 > :last-child { margin-right: 0px; } .framer-OfAkg .framer-q0qaps > * { margin: 0px; margin-bottom: calc(14px / 2); margin-top: calc(14px / 2); } .framer-OfAkg .framer-q0qaps > :first-child { margin-top: 0px; } .framer-OfAkg .framer-q0qaps > :last-child { margin-bottom: 0px; } }", ".framer-OfAkg.framer-v-17ifwsy .framer-q0qaps { align-content: flex-end; align-items: flex-end; }", ".framer-OfAkg.framer-v-93909c.framer-1qtwle1, .framer-OfAkg.framer-v-o4h3t1.framer-1qtwle1 { cursor: unset; padding: 26px; }", ".framer-OfAkg.framer-v-93909c .framer-q0qaps, .framer-OfAkg.framer-v-o4h3t1 .framer-q0qaps { gap: 10px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-OfAkg.framer-v-93909c .framer-q0qaps { gap: 0px; } .framer-OfAkg.framer-v-93909c .framer-q0qaps > * { margin: 0px; margin-bottom: calc(10px / 2); margin-top: calc(10px / 2); } .framer-OfAkg.framer-v-93909c .framer-q0qaps > :first-child { margin-top: 0px; } .framer-OfAkg.framer-v-93909c .framer-q0qaps > :last-child { margin-bottom: 0px; } }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-OfAkg.framer-v-o4h3t1 .framer-q0qaps { gap: 0px; } .framer-OfAkg.framer-v-o4h3t1 .framer-q0qaps > * { margin: 0px; margin-bottom: calc(10px / 2); margin-top: calc(10px / 2); } .framer-OfAkg.framer-v-o4h3t1 .framer-q0qaps > :first-child { margin-top: 0px; } .framer-OfAkg.framer-v-o4h3t1 .framer-q0qaps > :last-child { margin-bottom: 0px; } }", ...css2, ...css];
var FramerUqu9MXhEM = withCSS(Component, css3, "framer-OfAkg");
var stdin_default = FramerUqu9MXhEM;
FramerUqu9MXhEM.displayName = "More articles";
FramerUqu9MXhEM.defaultProps = { height: 209, width: 361 };
addPropertyControls(FramerUqu9MXhEM, { variant: { options: ["jCunBFOfe", "nMB5UvAyL", "ufdMzEXzI", "biKIUuwSe"], optionTitles: ["Previous Desktop", "Next Desktop", "Next Phone", "Previous Phone"], title: "Variant", type: ControlType.Enum }, lCEynwwe4: { title: "Link", type: ControlType.Link }, HEjf_43Qf: { title: "Image", type: ControlType.ResponsiveImage }, NpDL3Onrz: { defaultValue: "How a well-designed website can transform your business", title: "Title", type: ControlType.String } });
addFonts(FramerUqu9MXhEM, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5vvr9Vy74if2I6bQbJvbw7SY1pQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/EOr0mi4hNtlgWNn9if640EZzXCo.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/Y9k9QrlZAqio88Klkmbd8VoMQc.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/OYrD2tBIBPvoJXiIHnLoOXnY9M.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/JeYwfuaPfZHQhEG8U5gtPDZ7WQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/vQyevYAyHtARFwPqUzQGpnDs.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/b6Y37FthZeALduNqHicBT6FutY.woff2", weight: "400" }] }, ...getFontsFromSharedStyle(fonts2), ...getFontsFromSharedStyle(fonts)], { supportsExplicitInterCodegen: true });

// virtual:more-articles
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {
  "base": "biKIUuwSe",
  "xl": "jCunBFOfe"
};
stdin_default.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default);
export {
  ComponentWithRoot as default
};
