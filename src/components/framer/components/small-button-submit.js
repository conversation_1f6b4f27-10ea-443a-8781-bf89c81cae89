// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";

// virtual:small-button-submit
import { Fragment as Fragment2 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/3oGNt9rERhKQntdKF0qc/4dTqq4IM1tz2qInyxUiv/P34Gbmd7R.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, RichText, useComponentViewport, useLocaleInfo, useVariantState, withCSS, withFX } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var MotionDivWithFX = withFX(motion.div);
var enabledGestures = { NqvE1QO0C: { hover: true } };
var cycleOrder = ["NqvE1QO0C", "fPC2XkpQE", "PkBAWkJ0a", "bfP_Q8e5t", "oitdR1kQL", "TIP2pCzRk"];
var serializationHash = "framer-iwDdg";
var variantClassNames = { bfP_Q8e5t: "framer-v-1ggjet5", fPC2XkpQE: "framer-v-1mza8qt", NqvE1QO0C: "framer-v-1bcofgr", oitdR1kQL: "framer-v-90gt74", PkBAWkJ0a: "framer-v-vpsdjm", TIP2pCzRk: "framer-v-1en0xkx" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { bounce: 0.35, delay: 0, duration: 0.39, type: "spring" };
var transition2 = { delay: 0, duration: 0.6, ease: [0, 0, 1, 1], type: "tween" };
var animation = { opacity: 1, rotate: 360, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, x: 0, y: 0 };
var transformTemplate1 = (_, t) => `translateX(-50%) ${t}`;
var transformTemplate2 = (_, t) => `translateY(-50%) ${t}`;
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { Desktop: "NqvE1QO0C", Disabled: "bfP_Q8e5t", Error: "TIP2pCzRk", Loading: "PkBAWkJ0a", Phone: "fPC2XkpQE", Success: "oitdR1kQL" };
var getProps = ({ height, id, loadingMessage, successMessage, title, width, ...props }) => {
  return { ...props, J1bovNTGx: title ?? props.J1bovNTGx ?? "Text", L89rSxcsv: successMessage ?? props.L89rSxcsv ?? "Subscribed!", s6_EzKg7X: loadingMessage ?? props.s6_EzKg7X ?? "Sending...", variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "NqvE1QO0C" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className, layoutId, variant, J1bovNTGx, L89rSxcsv, s6_EzKg7X, UIZYiGKCr, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "NqvE1QO0C", enabledGestures, ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  const isDisplayed = () => {
    if (baseVariant === "PkBAWkJ0a") return true;
    return false;
  };
  const isDisplayed1 = () => {
    if (["PkBAWkJ0a", "oitdR1kQL"].includes(baseVariant)) return false;
    return true;
  };
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsxs(motion.button, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-1bcofgr", className, classNames), "data-framer-name": "Desktop", "data-reset": "button", layoutDependency, layoutId: "NqvE1QO0C", ref: refBinding, style: { backgroundColor: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", borderBottomLeftRadius: 50, borderBottomRightRadius: 50, borderTopLeftRadius: 50, borderTopRightRadius: 50, ...style }, variants: { "NqvE1QO0C-hover": { backgroundColor: "rgb(0, 0, 0)" }, TIP2pCzRk: { backgroundColor: "rgba(255, 33, 66, 0.2)" } }, ...addPropertyOverrides({ "NqvE1QO0C-hover": { "data-framer-name": void 0 }, bfP_Q8e5t: { "data-framer-name": "Disabled" }, fPC2XkpQE: { "data-framer-name": "Phone" }, oitdR1kQL: { "data-framer-name": "Success" }, PkBAWkJ0a: { "data-framer-name": "Loading" }, TIP2pCzRk: { "data-framer-name": "Error" } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "12px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "100%", "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Text" }) }), className: "framer-1954osx", "data-framer-name": "Text 1", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "MzaBWatJ1", style: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", "--framer-paragraph-spacing": "0px", opacity: 0 }, text: J1bovNTGx, variants: { "NqvE1QO0C-hover": { opacity: 1 } }, verticalAlignment: "top", withExternalLayout: true, ...addPropertyOverrides({ PkBAWkJ0a: { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "12px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "100%", "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Sending..." }) }), text: void 0 } }, baseVariant, gestureVariant) }), /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "12px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "100%", "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Text" }) }), className: "framer-1s1iawp", "data-framer-name": "Text 2", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "cNooVg_GT", style: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", "--framer-paragraph-spacing": "0px", opacity: 1 }, text: J1bovNTGx, variants: { "NqvE1QO0C-hover": { opacity: 0 }, bfP_Q8e5t: { opacity: 0.3 }, oitdR1kQL: { opacity: 1 }, TIP2pCzRk: { "--extracted-r6o4lv": "rgb(255, 34, 68)", opacity: 1 } }, verticalAlignment: "top", withExternalLayout: true, ...addPropertyOverrides({ oitdR1kQL: { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "12px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "100%", "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Thank you!" }) }), text: L89rSxcsv }, PkBAWkJ0a: { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "12px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "100%", "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Sending..." }) }), text: s6_EzKg7X }, TIP2pCzRk: { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "12px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "100%", "--framer-text-color": "var(--extracted-r6o4lv, rgb(255, 34, 68))" }, children: "Something went wrong" }) }), text: void 0 } }, baseVariant, gestureVariant) }), isDisplayed() && /* @__PURE__ */ _jsx(MotionDivWithFX, { __framer__loop: animation, __framer__loopEffectEnabled: true, __framer__loopRepeatDelay: 0, __framer__loopRepeatType: "loop", __framer__loopTransition: transition2, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-hw7rfr", "data-framer-name": "Conic", layoutDependency, layoutId: "GFHPsJJ46", style: { background: "conic-gradient(from 0deg at 50% 50%, rgba(255, 255, 255, 0) 7.208614864864882deg, rgb(255, 255, 255) 342deg)", mask: "url('https://framerusercontent.com/images/pGiXYozQ3mE4cilNOItfe2L2fUA.svg') alpha no-repeat center / cover add", WebkitMask: "url('https://framerusercontent.com/images/pGiXYozQ3mE4cilNOItfe2L2fUA.svg') alpha no-repeat center / cover add" }, children: /* @__PURE__ */ _jsx(motion.div, { className: "framer-13n8hk4", "data-framer-name": "Rounding", layoutDependency, layoutId: "eKW6IhEFd", style: { backgroundColor: "rgb(255, 255, 255)", borderBottomLeftRadius: 1, borderBottomRightRadius: 1, borderTopLeftRadius: 1, borderTopRightRadius: 1 }, transformTemplate: transformTemplate1 }) }), isDisplayed1() && /* @__PURE__ */ _jsx(motion.div, { className: "framer-1n90y1x", "data-framer-name": "Circle 1", layoutDependency, layoutId: "C816hV4VB", style: { backgroundColor: "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", borderBottomLeftRadius: 50, borderBottomRightRadius: 50, borderTopLeftRadius: 50, borderTopRightRadius: 50, opacity: 1 }, variants: { "NqvE1QO0C-hover": { backgroundColor: UIZYiGKCr, opacity: 0 }, bfP_Q8e5t: { opacity: 0.3 }, TIP2pCzRk: { backgroundColor: "rgb(255, 34, 68)", opacity: 1 } } }), isDisplayed1() && /* @__PURE__ */ _jsx(motion.div, { className: "framer-r4ytk6", "data-framer-name": "Circle 2", layoutDependency, layoutId: "YqCVbSbXR", style: { backgroundColor: "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", borderBottomLeftRadius: 50, borderBottomRightRadius: 50, borderTopLeftRadius: 50, borderTopRightRadius: 50, opacity: 1 }, transformTemplate: transformTemplate2, variants: { "NqvE1QO0C-hover": { opacity: 1 }, bfP_Q8e5t: { opacity: 0 }, TIP2pCzRk: { backgroundColor: "rgb(255, 35, 68)", opacity: 1 } } })] }) }) }) });
});
var css = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-iwDdg.framer-806yq6, .framer-iwDdg .framer-806yq6 { display: block; }", ".framer-iwDdg.framer-1bcofgr { align-content: center; align-items: center; cursor: pointer; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 34px; height: min-content; justify-content: center; overflow: hidden; padding: 9px 11px 9px 12px; position: relative; width: min-content; will-change: var(--framer-will-change-override, transform); }", ".framer-iwDdg .framer-1954osx { flex: none; height: auto; left: 12px; position: absolute; top: -11px; white-space: pre; width: auto; z-index: 1; }", ".framer-iwDdg .framer-1s1iawp { flex: none; height: auto; position: relative; white-space: pre; width: auto; z-index: 2; }", ".framer-iwDdg .framer-hw7rfr { aspect-ratio: 1 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 13px); overflow: hidden; position: relative; width: 12px; }", ".framer-iwDdg .framer-13n8hk4 { aspect-ratio: 1 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 2px); left: 50%; overflow: visible; position: absolute; top: 0px; width: 2px; }", ".framer-iwDdg .framer-1n90y1x { aspect-ratio: 1 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 8px); overflow: visible; position: relative; width: 8px; }", ".framer-iwDdg .framer-r4ytk6 { aspect-ratio: 1 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 8px); overflow: visible; position: absolute; right: 11px; top: 50%; width: 8px; z-index: 1; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-iwDdg.framer-1bcofgr { gap: 0px; } .framer-iwDdg.framer-1bcofgr > * { margin: 0px; margin-left: calc(34px / 2); margin-right: calc(34px / 2); } .framer-iwDdg.framer-1bcofgr > :first-child { margin-left: 0px; } .framer-iwDdg.framer-1bcofgr > :last-child { margin-right: 0px; } }", ".framer-iwDdg.framer-v-1mza8qt.framer-1bcofgr, .framer-iwDdg.framer-v-vpsdjm.framer-1bcofgr, .framer-iwDdg.framer-v-1ggjet5.framer-1bcofgr, .framer-iwDdg.framer-v-90gt74.framer-1bcofgr, .framer-iwDdg.framer-v-1en0xkx.framer-1bcofgr { cursor: unset; }", ".framer-iwDdg.framer-v-vpsdjm .framer-1954osx { left: unset; right: 0px; }", ".framer-iwDdg.framer-v-1bcofgr.hover .framer-1954osx { left: unset; position: relative; top: unset; }", ".framer-iwDdg.framer-v-1bcofgr.hover .framer-1s1iawp { bottom: -12px; left: 12px; position: absolute; z-index: 1; }", ".framer-iwDdg.framer-v-1bcofgr.hover .framer-1n90y1x { aspect-ratio: unset; height: 8px; }", ".framer-iwDdg.framer-v-1bcofgr.hover .framer-r4ytk6 { height: var(--framer-aspect-ratio-supported, 10px); right: 10px; width: 10px; }"];
var FramerP34Gbmd7R = withCSS(Component, css, "framer-iwDdg");
var stdin_default = FramerP34Gbmd7R;
FramerP34Gbmd7R.displayName = "Small button submit";
FramerP34Gbmd7R.defaultProps = { height: 30, width: 88 };
addPropertyControls(FramerP34Gbmd7R, { variant: { options: ["NqvE1QO0C", "fPC2XkpQE", "PkBAWkJ0a", "bfP_Q8e5t", "oitdR1kQL", "TIP2pCzRk"], optionTitles: ["Desktop", "Phone", "Loading", "Disabled", "Success", "Error"], title: "Variant", type: ControlType.Enum }, J1bovNTGx: { defaultValue: "Text", displayTextArea: true, title: "Title", type: ControlType.String }, L89rSxcsv: { defaultValue: "Subscribed!", displayTextArea: true, title: "Success message", type: ControlType.String }, s6_EzKg7X: { defaultValue: "Sending...", displayTextArea: true, title: "Loading message", type: ControlType.String } });
addFonts(FramerP34Gbmd7R, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/hyOgCu0Xnghbimh0pE8QTvtt2AU.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/NeGmSOXrPBfEFIy5YZeHq17LEDA.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/oYaAX5himiTPYuN8vLWnqBbfD2s.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/lEJLP4R0yuCaMCjSXYHtJw72M.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/cRJyLNuTJR5jbyKzGi33wU9cqIQ.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/1ZFS7N918ojhhd0nQWdj3jz4w.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/A0Wcc7NgXMjUuFdquHDrIZpzZw0.woff2", weight: "600" }] }], { supportsExplicitInterCodegen: true });

// virtual:small-button-submit
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {
  "base": "NqvE1QO0C"
};
stdin_default.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default);
export {
  ComponentWithRoot as default
};
