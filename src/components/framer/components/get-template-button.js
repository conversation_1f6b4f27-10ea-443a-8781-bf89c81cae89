// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";

// virtual:get-template-button
import { Fragment as Fragment2 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/A8glwj2nHygG7Ntk0hyl/B6kwjp0eqCTnq3NK2QmC/AgkVqOuxm.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, Link, RichText, SVG, useComponentViewport, useLocaleInfo, useVariantState, withCSS, withFX, withOptimizedAppearEffect } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var MotionAWithFXWithOptimizedAppearEffect = withOptimizedAppearEffect(withFX(motion.a));
var enabledGestures = { rmFyw7bgB: { hover: true } };
var cycleOrder = ["rmFyw7bgB", "nJJwcaHgO"];
var serializationHash = "framer-8vABr";
var variantClassNames = { nJJwcaHgO: "framer-v-1u4q7qq", rmFyw7bgB: "framer-v-1kqz90s" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { damping: 30, delay: 1.8, mass: 1, stiffness: 235, type: "spring" };
var animation = { opacity: 1, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, transition: transition1, x: 0, y: 0 };
var animation1 = { opacity: 1e-3, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, x: 0, y: 40 };
var transition2 = { bounce: 0.25, delay: 0, duration: 0.4, type: "spring" };
var transformTemplate1 = (_, t) => `translateY(-50%) ${t}`;
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { Desktop: "rmFyw7bgB", Phone: "nJJwcaHgO" };
var getProps = ({ height, id, link, price, title, width, ...props }) => {
  return { ...props, ALB7ZT71t: title ?? props.ALB7ZT71t ?? "Buy template", KS3B6CvPC: link ?? props.KS3B6CvPC, uNVvEr0e9: price ?? props.uNVvEr0e9 ?? "Unlock for $99", variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "rmFyw7bgB" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className, layoutId, variant, ALB7ZT71t, uNVvEr0e9, KS3B6CvPC, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "rmFyw7bgB", enabledGestures, ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition2, children: /* @__PURE__ */ _jsx(Link, { href: KS3B6CvPC, motionChild: true, nodeId: "rmFyw7bgB", scopeId: "AgkVqOuxm", children: /* @__PURE__ */ _jsxs(MotionAWithFXWithOptimizedAppearEffect, { ...restProps, ...gestureHandlers, __framer__presenceAnimate: animation, __framer__presenceInitial: animation1, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: `${cx(scopingClassNames, "framer-1kqz90s", className, classNames)} framer-1yg7o7j`, "data-framer-appear-id": "1kqz90s", "data-framer-name": "Desktop", layoutDependency, layoutId: "rmFyw7bgB", optimized: true, ref: refBinding, style: { backgroundColor: "rgb(0, 0, 0)", borderBottomLeftRadius: 9, borderBottomRightRadius: 9, borderTopLeftRadius: 9, borderTopRightRadius: 9, boxShadow: "0px 0.6021873017743928px 0.6021873017743928px -1.25px rgba(0, 0, 0, 0.25916), 0px 2.288533303243457px 2.288533303243457px -2.5px rgba(0, 0, 0, 0.22881), 0px 10px 10px -3.75px rgba(0, 0, 0, 0.09)", ...style }, variants: { "rmFyw7bgB-hover": { backgroundColor: "rgb(210, 255, 55)" } }, ...addPropertyOverrides({ "rmFyw7bgB-hover": { "data-framer-name": void 0 }, nJJwcaHgO: { "data-framer-name": "Phone" } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "14px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.05em", "--framer-line-height": "1em", "--framer-text-color": "var(--extracted-r6o4lv, rgb(255, 255, 255))" }, children: "Buy template" }) }), className: "framer-1yag2w4", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "OUU59L5fY", style: { "--extracted-r6o4lv": "rgb(255, 255, 255)", "--framer-link-text-color": "rgb(0, 153, 255)", "--framer-link-text-decoration": "underline" }, text: ALB7ZT71t, variants: { "rmFyw7bgB-hover": { "--extracted-r6o4lv": "rgb(34, 34, 34)" } }, verticalAlignment: "top", withExternalLayout: true, ...addPropertyOverrides({ "rmFyw7bgB-hover": { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "14px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.05em", "--framer-line-height": "1em", "--framer-text-color": "var(--extracted-r6o4lv, rgb(34, 34, 34))" }, children: "Buy template" }) }) } }, baseVariant, gestureVariant) }), /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "14px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.05em", "--framer-line-height": "1em", "--framer-text-color": "var(--extracted-r6o4lv, rgb(255, 255, 255))" }, children: "Unlock for $99" }) }), className: "framer-1lj9uqj", fonts: ["Inter-SemiBold"], layoutDependency, layoutId: "nTdh72zpA", style: { "--extracted-r6o4lv": "rgb(255, 255, 255)", "--framer-link-text-color": "rgb(0, 153, 255)", "--framer-link-text-decoration": "underline" }, text: uNVvEr0e9, variants: { "rmFyw7bgB-hover": { "--extracted-r6o4lv": "rgb(34, 34, 34)" } }, verticalAlignment: "top", withExternalLayout: true, ...addPropertyOverrides({ "rmFyw7bgB-hover": { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "14px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.05em", "--framer-line-height": "1em", "--framer-text-color": "var(--extracted-r6o4lv, rgb(34, 34, 34))" }, children: "Unlock for $99" }) }) } }, baseVariant, gestureVariant) }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-ug9blw", "data-framer-name": "Icon", layoutDependency, layoutId: "d5ATWqC6u", style: { backgroundColor: "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", borderBottomLeftRadius: 50, borderBottomRightRadius: 50, borderTopLeftRadius: 50, borderTopRightRadius: 50 }, transformTemplate: transformTemplate1, variants: { "rmFyw7bgB-hover": { backgroundColor: "rgb(210, 255, 55)" } }, ...addPropertyOverrides({ "rmFyw7bgB-hover": { transformTemplate: void 0 } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx(SVG, { className: "framer-1e1afs2", "data-framer-name": "SVG", fill: "rgba(0,0,0,1)", intrinsicHeight: 13, intrinsicWidth: 13, layoutDependency, layoutId: "IB3mdPkuA", svg: '<svg width="13" height="13" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">\n<path d="M6.43017 13C6.36586 13 6.30217 12.9874 6.24275 12.963C6.18334 12.9385 6.12935 12.9027 6.08387 12.8575C6.03839 12.8123 6.00232 12.7586 5.97771 12.6996C5.9531 12.6405 5.94044 12.5772 5.94045 12.5133V8.39429L2.48954 8.39305C2.40465 8.39303 2.32123 8.37109 2.24746 8.32937C2.17369 8.28764 2.1121 8.22758 2.06873 8.15506C2.02536 8.08255 2.00171 8.00008 2.00009 7.91573C1.99847 7.83139 2.01894 7.74808 2.0595 7.67398L6.11832 0.254155C6.17033 0.159114 6.25272 0.0840055 6.35248 0.0406966C6.45224 -0.00261237 6.56367 -0.0116522 6.66917 0.0150055C6.77468 0.0416631 6.86824 0.102498 6.93507 0.187899C7.0019 0.273299 7.03819 0.378396 7.03821 0.486582V4.61345H10.5104C10.5953 4.61346 10.6789 4.63545 10.7527 4.67726C10.8266 4.71908 10.8882 4.77927 10.9315 4.85193C10.9749 4.92459 10.9984 5.00721 10.9999 5.09166C11.0014 5.17612 10.9807 5.2595 10.94 5.33361L6.8597 12.7469C6.81753 12.8235 6.7554 12.8874 6.67983 12.9319C6.60426 12.9764 6.51803 13 6.43017 13Z" fill="#222222"/>\n</svg>\n', withExternalLayout: true }) })] }) }) }) }) });
});
var css = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-8vABr.framer-1yg7o7j, .framer-8vABr .framer-1yg7o7j { display: block; }", ".framer-8vABr.framer-1kqz90s { align-content: center; align-items: center; cursor: pointer; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 13px; height: 36px; justify-content: flex-start; overflow: hidden; padding: 8px 8px 8px 13px; position: relative; text-decoration: none; width: 142px; will-change: var(--framer-will-change-override, transform); }", ".framer-8vABr .framer-1yag2w4 { flex: none; height: auto; position: relative; white-space: pre; width: auto; z-index: 2; }", ".framer-8vABr .framer-1lj9uqj { flex: none; height: auto; left: 12px; position: absolute; top: -16px; white-space: pre; width: auto; z-index: 2; }", ".framer-8vABr .framer-ug9blw { align-content: center; align-items: center; aspect-ratio: 1 / 1; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: var(--framer-aspect-ratio-supported, 17px); justify-content: center; left: 114px; overflow: visible; padding: 0px; position: absolute; top: 50%; width: 17px; z-index: 1; }", ".framer-8vABr .framer-1e1afs2 { flex: none; height: 11px; position: relative; width: 11px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-8vABr.framer-1kqz90s, .framer-8vABr .framer-ug9blw { gap: 0px; } .framer-8vABr.framer-1kqz90s > * { margin: 0px; margin-left: calc(13px / 2); margin-right: calc(13px / 2); } .framer-8vABr.framer-1kqz90s > :first-child, .framer-8vABr .framer-ug9blw > :first-child { margin-left: 0px; } .framer-8vABr.framer-1kqz90s > :last-child, .framer-8vABr .framer-ug9blw > :last-child { margin-right: 0px; } .framer-8vABr .framer-ug9blw > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } }", ".framer-8vABr.framer-v-1u4q7qq.framer-1kqz90s { cursor: unset; }", ".framer-8vABr.framer-v-1kqz90s.hover .framer-1yag2w4 { left: 12px; position: absolute; top: 39px; }", ".framer-8vABr.framer-v-1kqz90s.hover .framer-1lj9uqj { left: unset; position: relative; top: unset; }", ".framer-8vABr.framer-v-1kqz90s.hover .framer-ug9blw { display: block; gap: unset; height: var(--framer-aspect-ratio-supported, 279px); left: -16px; padding: unset; top: -121px; width: 279px; }", ".framer-8vABr.framer-v-1kqz90s.hover .framer-1e1afs2 { height: 16px; left: calc(49.46236559139787% - 16px / 2); position: absolute; top: calc(49.82078853046597% - 16px / 2); width: 16px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-8vABr.framer-v-1kqz90s.hover .framer-ug9blw { gap: 0px; } .framer-8vABr.framer-v-1kqz90s.hover .framer-ug9blw > *, .framer-8vABr.framer-v-1kqz90s.hover .framer-ug9blw > :first-child, .framer-8vABr.framer-v-1kqz90s.hover .framer-ug9blw > :last-child { margin: 0px; } }"];
var FramerAgkVqOuxm = withCSS(Component, css, "framer-8vABr");
var stdin_default = FramerAgkVqOuxm;
FramerAgkVqOuxm.displayName = "Get template button";
FramerAgkVqOuxm.defaultProps = { height: 36, width: 142 };
addPropertyControls(FramerAgkVqOuxm, { variant: { options: ["rmFyw7bgB", "nJJwcaHgO"], optionTitles: ["Desktop", "Phone"], title: "Variant", type: ControlType.Enum }, ALB7ZT71t: { defaultValue: "Buy template", displayTextArea: false, title: "Title", type: ControlType.String }, uNVvEr0e9: { defaultValue: "Unlock for $99", displayTextArea: false, title: "Price", type: ControlType.String }, KS3B6CvPC: { title: "Link", type: ControlType.Link } });
addFonts(FramerAgkVqOuxm, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/hyOgCu0Xnghbimh0pE8QTvtt2AU.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/NeGmSOXrPBfEFIy5YZeHq17LEDA.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/oYaAX5himiTPYuN8vLWnqBbfD2s.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/lEJLP4R0yuCaMCjSXYHtJw72M.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/cRJyLNuTJR5jbyKzGi33wU9cqIQ.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/1ZFS7N918ojhhd0nQWdj3jz4w.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/A0Wcc7NgXMjUuFdquHDrIZpzZw0.woff2", weight: "600" }] }], { supportsExplicitInterCodegen: true });

// virtual:get-template-button
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {};
stdin_default.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default);
export {
  ComponentWithRoot as default
};
