// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";
import {
  className as className2,
  css as css2,
  fonts as fonts2
} from "./chunks/chunk-B6WKPYK7.js";
import {
  Grain
} from "./chunks/chunk-MO4ELLBD.js";
import "./chunks/chunk-MKEJQGYO.js";
import {
  className,
  css,
  fonts
} from "./chunks/chunk-YWUWNR35.js";

// virtual:video
import { Fragment as Fragment3 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/qjgWXMzvxaNU7gU0HfTs/67qbZo3yAQzhksxoJAxc/c3jVHY4cP.js
import { jsx as _jsx2, jsxs as _jsxs2 } from "react/jsx-runtime";
import { addFonts as addFonts2, addPropertyControls as addPropertyControls2, ComponentViewportProvider, ControlType as ControlType2, cx as cx2, getFonts, getLoadingLazyAtYPosition, Image, SmartComponentScopedContainer, useActiveVariantCallback, useComponentViewport as useComponentViewport2, useLocaleInfo as useLocaleInfo2, useVariantState as useVariantState2, withCSS as withCSS2, withFX } from "unframer";
import { LayoutGroup as LayoutGroup2, motion as motion2, MotionConfigContext as MotionConfigContext2 } from "unframer";
import * as React2 from "react";
import { useRef as useRef2 } from "react";

// /:https://framerusercontent.com/modules/Ik27nyqmPbQ4RLC0V1Hw/09nrParnXLeO68YEmFcO/Nr9N73Jiy.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, getFontsFromSharedStyle, RichText, SVG, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var cycleOrder = ["peF_wHPfx", "fvZkfyHcS", "YAFSMiRUP"];
var serializationHash = "framer-lUytp";
var variantClassNames = { fvZkfyHcS: "framer-v-119szyo", peF_wHPfx: "framer-v-sovyad", YAFSMiRUP: "framer-v-1gf3ra7" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { delay: 0, duration: 0.45, ease: [0.82, 0.11, 0.37, 0.82], type: "tween" };
var transformTemplate1 = (_, t) => `translateY(-50%) ${t}`;
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { Desktop: "peF_wHPfx", Hover: "fvZkfyHcS", Phone: "YAFSMiRUP" };
var getProps = ({ height, id, subtitle, title, width, ...props }) => {
  return { ...props, h2Q8Sv9Jq: subtitle ?? props.h2Q8Sv9Jq ?? "(2016-25\xA9)", q7VJkczE3: title ?? props.q7VJkczE3 ?? "Watch showreel", variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "peF_wHPfx" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className: className3, layoutId, variant, q7VJkczE3, h2Q8Sv9Jq, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "peF_wHPfx", ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [className2, className];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsxs(motion.div, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-sovyad", className3, classNames), "data-framer-name": "Desktop", layoutDependency, layoutId: "peF_wHPfx", ref: refBinding, style: { ...style }, ...addPropertyOverrides({ fvZkfyHcS: { "data-framer-name": "Hover" }, YAFSMiRUP: { "data-framer-name": "Phone" } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsx(motion.div, { className: "framer-q3klrh", "data-framer-name": "Icon", layoutDependency, layoutId: "REhaQb_R4", style: { scale: 1 }, variants: { fvZkfyHcS: { scale: 1.2 }, YAFSMiRUP: { scale: 1 } }, children: /* @__PURE__ */ _jsx(SVG, { className: "framer-1xmpe27", "data-framer-name": "SVG", fill: "rgba(0,0,0,1)", intrinsicHeight: 92, intrinsicWidth: 92, layoutDependency, layoutId: "vWuHPXCTj", svg: '<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">\n<rect width="92" height="92" rx="46" fill="white"/>\n<path d="M55.5 45.134C56.1667 45.5189 56.1667 46.4811 55.5 46.866L41.25 55.0933C40.5833 55.4782 39.75 54.997 39.75 54.2272L39.75 37.7728C39.75 37.003 40.5833 36.5218 41.25 36.9067L55.5 45.134Z" fill="#0A0A0A"/>\n</svg>\n', withExternalLayout: true }) }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-13b5nrz", "data-framer-name": "Video Info Container", layoutDependency, layoutId: "zH5aIVuYT", style: { opacity: 1 }, variants: { fvZkfyHcS: { opacity: 0 }, YAFSMiRUP: { opacity: 1 } }, ...addPropertyOverrides({ fvZkfyHcS: { transformTemplate: transformTemplate1 } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-xgn84q", "data-styles-preset": "LyKOtaXoC", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Watch showreel" }) }), className: "framer-4qhudt", fonts: ["Inter"], layoutDependency, layoutId: "UjC6cBmyD", style: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", "--framer-paragraph-spacing": "0px" }, text: q7VJkczE3, verticalAlignment: "top", withExternalLayout: true, ...addPropertyOverrides({ YAFSMiRUP: { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-xgn84q", "data-styles-preset": "LyKOtaXoC", style: { "--framer-text-alignment": "center", "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "Watch showreel" }) }) } }, baseVariant, gestureVariant) }), /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-1n1wh7h", "data-styles-preset": "gd6AWaps9", style: { "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "(2016-25\xA9)" }) }), className: "framer-1jwabl3", fonts: ["Inter"], layoutDependency, layoutId: "tNUn6QRvY", style: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", "--framer-paragraph-spacing": "0px", opacity: 0.6 }, text: h2Q8Sv9Jq, verticalAlignment: "top", withExternalLayout: true, ...addPropertyOverrides({ YAFSMiRUP: { children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-1n1wh7h", "data-styles-preset": "gd6AWaps9", style: { "--framer-text-alignment": "center", "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "(2016-25\xA9)" }) }) } }, baseVariant, gestureVariant) })] })] }) }) }) });
});
var css3 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-lUytp.framer-gvhwtl, .framer-lUytp .framer-gvhwtl { display: block; }", ".framer-lUytp.framer-sovyad { align-content: center; align-items: center; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 26px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: min-content; }", ".framer-lUytp .framer-q3klrh { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 4px; height: min-content; justify-content: center; overflow: visible; padding: 0px; position: relative; width: min-content; }", ".framer-lUytp .framer-1xmpe27 { flex: none; height: 92px; position: relative; width: 92px; }", ".framer-lUytp .framer-13b5nrz { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 3px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: 217px; }", ".framer-lUytp .framer-4qhudt, .framer-lUytp .framer-1jwabl3 { flex: none; height: auto; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-lUytp.framer-sovyad, .framer-lUytp .framer-q3klrh, .framer-lUytp .framer-13b5nrz { gap: 0px; } .framer-lUytp.framer-sovyad > * { margin: 0px; margin-left: calc(26px / 2); margin-right: calc(26px / 2); } .framer-lUytp.framer-sovyad > :first-child, .framer-lUytp .framer-q3klrh > :first-child { margin-left: 0px; } .framer-lUytp.framer-sovyad > :last-child, .framer-lUytp .framer-q3klrh > :last-child { margin-right: 0px; } .framer-lUytp .framer-q3klrh > * { margin: 0px; margin-left: calc(4px / 2); margin-right: calc(4px / 2); } .framer-lUytp .framer-13b5nrz > * { margin: 0px; margin-bottom: calc(3px / 2); margin-top: calc(3px / 2); } .framer-lUytp .framer-13b5nrz > :first-child { margin-top: 0px; } .framer-lUytp .framer-13b5nrz > :last-child { margin-bottom: 0px; } }", ".framer-lUytp.framer-v-119szyo .framer-13b5nrz { left: 55px; position: absolute; top: 48%; z-index: 1; }", ".framer-lUytp.framer-v-1gf3ra7.framer-sovyad { flex-direction: column; gap: 16px; }", ".framer-lUytp.framer-v-1gf3ra7 .framer-1xmpe27 { aspect-ratio: 1 / 1; height: var(--framer-aspect-ratio-supported, 60px); width: 60px; }", ".framer-lUytp.framer-v-1gf3ra7 .framer-13b5nrz { align-content: center; align-items: center; gap: 2px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-lUytp.framer-v-1gf3ra7.framer-sovyad, .framer-lUytp.framer-v-1gf3ra7 .framer-13b5nrz { gap: 0px; } .framer-lUytp.framer-v-1gf3ra7.framer-sovyad > * { margin: 0px; margin-bottom: calc(16px / 2); margin-top: calc(16px / 2); } .framer-lUytp.framer-v-1gf3ra7.framer-sovyad > :first-child, .framer-lUytp.framer-v-1gf3ra7 .framer-13b5nrz > :first-child { margin-top: 0px; } .framer-lUytp.framer-v-1gf3ra7.framer-sovyad > :last-child, .framer-lUytp.framer-v-1gf3ra7 .framer-13b5nrz > :last-child { margin-bottom: 0px; } .framer-lUytp.framer-v-1gf3ra7 .framer-13b5nrz > * { margin: 0px; margin-bottom: calc(2px / 2); margin-top: calc(2px / 2); } }", ...css2, ...css];
var FramerNr9N73Jiy = withCSS(Component, css3, "framer-lUytp");
var stdin_default = FramerNr9N73Jiy;
FramerNr9N73Jiy.displayName = "Play";
FramerNr9N73Jiy.defaultProps = { height: 92, width: 335 };
addPropertyControls(FramerNr9N73Jiy, { variant: { options: ["peF_wHPfx", "fvZkfyHcS", "YAFSMiRUP"], optionTitles: ["Desktop", "Hover", "Phone"], title: "Variant", type: ControlType.Enum }, q7VJkczE3: { defaultValue: "Watch showreel", displayTextArea: false, title: "Title", type: ControlType.String }, h2Q8Sv9Jq: { defaultValue: "(2016-25\xA9)", displayTextArea: false, title: "Subtitle", type: ControlType.String } });
addFonts(FramerNr9N73Jiy, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5vvr9Vy74if2I6bQbJvbw7SY1pQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/EOr0mi4hNtlgWNn9if640EZzXCo.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/Y9k9QrlZAqio88Klkmbd8VoMQc.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/OYrD2tBIBPvoJXiIHnLoOXnY9M.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/JeYwfuaPfZHQhEG8U5gtPDZ7WQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/vQyevYAyHtARFwPqUzQGpnDs.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/b6Y37FthZeALduNqHicBT6FutY.woff2", weight: "400" }] }, ...getFontsFromSharedStyle(fonts2), ...getFontsFromSharedStyle(fonts)], { supportsExplicitInterCodegen: true });

// /:https://framerusercontent.com/modules/qjgWXMzvxaNU7gU0HfTs/67qbZo3yAQzhksxoJAxc/c3jVHY4cP.js
var PlayFonts = getFonts(stdin_default);
var ImageWithFX = withFX(Image);
var GrainFonts = getFonts(Grain);
var enabledGestures = { LYp1X5Hpl: { hover: true } };
var cycleOrder2 = ["LYp1X5Hpl", "E3n80lD95"];
var serializationHash2 = "framer-aAPQZ";
var variantClassNames2 = { E3n80lD95: "framer-v-13ebya5", LYp1X5Hpl: "framer-v-1n90iiz" };
function addPropertyOverrides2(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition12 = { delay: 0, duration: 0.45, ease: [0.82, 0.11, 0.37, 0.82], type: "tween" };
var transformTemplate12 = (_, t) => `translate(-50%, -50%) ${t}`;
var toResponsiveImage = (value) => {
  if (typeof value === "object" && value !== null && typeof value.src === "string") {
    return value;
  }
  return typeof value === "string" ? { src: value } : void 0;
};
var Transition2 = ({ value, children }) => {
  const config = React2.useContext(MotionConfigContext2);
  const transition = value ?? config.transition;
  const contextValue = React2.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx2(MotionConfigContext2.Provider, { value: contextValue, children });
};
var Variants2 = motion2.create(React2.Fragment);
var humanReadableVariantMap2 = { Desktop: "LYp1X5Hpl", Phone: "E3n80lD95" };
var getProps2 = ({ click, height, id, image, smallText, text, width, ...props }) => {
  return { ...props, M7GdAI5Rj: click ?? props.M7GdAI5Rj, oA1DUcxGC: text ?? props.oA1DUcxGC ?? "Text", uUm_1jxjT: image ?? props.uUm_1jxjT ?? { alt: "", pixelHeight: 1350, pixelWidth: 2280, src: "https://framerusercontent.com/images/cWKPopujkJqclchyOL1bYOiZDs.jpg?scale-down-to=2048", srcSet: "https://framerusercontent.com/images/cWKPopujkJqclchyOL1bYOiZDs.jpg?scale-down-to=512 512w,https://framerusercontent.com/images/cWKPopujkJqclchyOL1bYOiZDs.jpg?scale-down-to=1024 1024w,https://framerusercontent.com/images/cWKPopujkJqclchyOL1bYOiZDs.jpg?scale-down-to=2048 2048w,https://framerusercontent.com/images/cWKPopujkJqclchyOL1bYOiZDs.jpg 2280w" }, variant: humanReadableVariantMap2[props.variant] ?? props.variant ?? "LYp1X5Hpl", ZOyXnGfCw: smallText ?? props.ZOyXnGfCw ?? "Small text" };
};
var createLayoutDependency2 = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component2 = /* @__PURE__ */ React2.forwardRef(function(props, ref) {
  const fallbackRef = useRef2(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React2.useId();
  const { activeLocale, setLocale } = useLocaleInfo2();
  const componentViewport = useComponentViewport2();
  const { style, className: className3, layoutId, variant, M7GdAI5Rj, oA1DUcxGC, ZOyXnGfCw, uUm_1jxjT, ...restProps } = getProps2(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState2({ cycleOrder: cycleOrder2, defaultVariant: "LYp1X5Hpl", enabledGestures, ref: refBinding, variant, variantClassNames: variantClassNames2 });
  const layoutDependency = createLayoutDependency2(props, variants);
  const { activeVariantCallback, delay } = useActiveVariantCallback(baseVariant);
  const onTap1doq78u = activeVariantCallback(async (...args) => {
    setGestureState({ isPressed: false });
    if (M7GdAI5Rj) {
      const res = await M7GdAI5Rj(...args);
      if (res === false) return false;
    }
  });
  const sharedStyleClassNames = [];
  const scopingClassNames = cx2(serializationHash2, ...sharedStyleClassNames);
  return /* @__PURE__ */ _jsx2(LayoutGroup2, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx2(Variants2, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx2(Transition2, { value: transition12, children: /* @__PURE__ */ _jsxs2(motion2.div, { ...restProps, ...gestureHandlers, className: cx2(scopingClassNames, "framer-1n90iiz", className3, classNames), "data-framer-name": "Desktop", "data-highlight": true, layoutDependency, layoutId: "LYp1X5Hpl", onTap: onTap1doq78u, ref: refBinding, style: { borderBottomLeftRadius: 25, borderBottomRightRadius: 25, borderTopLeftRadius: 25, borderTopRightRadius: 25, ...style }, variants: { E3n80lD95: { borderBottomLeftRadius: 18, borderBottomRightRadius: 18, borderTopLeftRadius: 18, borderTopRightRadius: 18 } }, ...addPropertyOverrides2({ "LYp1X5Hpl-hover": { "data-framer-name": void 0 }, E3n80lD95: { "data-framer-name": "Phone" } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsx2(ComponentViewportProvider, { height: 92, y: (componentViewport?.y || 0) + ((componentViewport?.height || 700) * 0.5000000000000002 - 46), ...addPropertyOverrides2({ E3n80lD95: { y: (componentViewport?.y || 0) + ((componentViewport?.height || 260) * 0.5021645021645024 - 46) } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx2(SmartComponentScopedContainer, { className: "framer-zzk5z2-container", layoutDependency, layoutId: "kc36L9Tt5-container", nodeId: "kc36L9Tt5", rendersWithMotion: true, scopeId: "c3jVHY4cP", transformTemplate: transformTemplate12, children: /* @__PURE__ */ _jsx2(stdin_default, { h2Q8Sv9Jq: ZOyXnGfCw, height: "100%", id: "kc36L9Tt5", layoutId: "kc36L9Tt5", q7VJkczE3: oA1DUcxGC, variant: "peF_wHPfx", width: "100%", ...addPropertyOverrides2({ "LYp1X5Hpl-hover": { variant: "fvZkfyHcS" }, E3n80lD95: { variant: "YAFSMiRUP" } }, baseVariant, gestureVariant) }) }) }), /* @__PURE__ */ _jsx2(ImageWithFX, { __framer__styleTransformEffectEnabled: true, __framer__transformTargets: [{ target: { opacity: 1, rotate: 0, rotateX: 0, rotateY: 0, scale: 1.2, skewX: 0, skewY: 0, x: 0, y: -220 } }, { target: { opacity: 1, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, x: 0, y: 0 } }], __framer__transformTrigger: "onInView", __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, background: { alt: "", fit: "fill", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + (0 + ((componentViewport?.height || 700) - 0 - 700) / 2)), pixelHeight: 1350, pixelWidth: 2280, sizes: `max(${componentViewport?.width || "100vw"}, 1px)`, ...toResponsiveImage(uUm_1jxjT) }, className: "framer-1gwxckt", "data-framer-name": "Image", layoutDependency, layoutId: "m8YAQX5zh", style: { filter: "none", scale: 1, transformPerspective: 1200, WebkitFilter: "none" }, variants: { "LYp1X5Hpl-hover": { filter: "blur(6px)", scale: 1.1, WebkitFilter: "blur(6px)" }, E3n80lD95: { transformPerspective: void 0 } }, ...addPropertyOverrides2({ E3n80lD95: { __framer__styleTransformEffectEnabled: void 0, background: { alt: "", fit: "fill", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + (0 + ((componentViewport?.height || 260) - 0 - 260) / 2)), pixelHeight: 1350, pixelWidth: 2280, sizes: `max(${componentViewport?.width || "100vw"}, 1px)`, ...toResponsiveImage(uUm_1jxjT) } } }, baseVariant, gestureVariant) }), /* @__PURE__ */ _jsx2(ComponentViewportProvider, { children: /* @__PURE__ */ _jsx2(SmartComponentScopedContainer, { className: "framer-jkchvq-container", isAuthoredByUser: true, isModuleExternal: true, layoutDependency, layoutId: "DSGL5BWbL-container", nodeId: "DSGL5BWbL", rendersWithMotion: true, scopeId: "c3jVHY4cP", children: /* @__PURE__ */ _jsx2(Grain, { height: "100%", id: "DSGL5BWbL", layoutId: "DSGL5BWbL", opacity: 0.05, style: { height: "100%", width: "100%" }, width: "100%" }) }) })] }) }) }) });
});
var css4 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-aAPQZ.framer-1orr5py, .framer-aAPQZ .framer-1orr5py { display: block; }", ".framer-aAPQZ.framer-1n90iiz { align-content: center; align-items: center; cursor: pointer; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 4px; height: min-content; justify-content: center; overflow: hidden; padding: 0px; position: relative; width: 1128px; will-change: var(--framer-will-change-override, transform); }", ".framer-aAPQZ .framer-zzk5z2-container { flex: none; height: auto; left: 50%; position: absolute; top: 50%; width: auto; z-index: 1; }", ".framer-aAPQZ .framer-1gwxckt { align-content: center; align-items: center; aspect-ratio: 1.6114285714285714 / 1; display: flex; flex: 1 0 0px; flex-direction: column; flex-wrap: nowrap; gap: 4px; height: var(--framer-aspect-ratio-supported, 700px); justify-content: center; overflow: hidden; padding: 0px; position: relative; width: 1px; }", ".framer-aAPQZ .framer-jkchvq-container { -webkit-user-select: none; bottom: 0px; flex: none; left: 0px; pointer-events: none; position: absolute; right: 0px; top: 0px; user-select: none; z-index: 0; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-aAPQZ.framer-1n90iiz, .framer-aAPQZ .framer-1gwxckt { gap: 0px; } .framer-aAPQZ.framer-1n90iiz > * { margin: 0px; margin-left: calc(4px / 2); margin-right: calc(4px / 2); } .framer-aAPQZ.framer-1n90iiz > :first-child { margin-left: 0px; } .framer-aAPQZ.framer-1n90iiz > :last-child { margin-right: 0px; } .framer-aAPQZ .framer-1gwxckt > * { margin: 0px; margin-bottom: calc(4px / 2); margin-top: calc(4px / 2); } .framer-aAPQZ .framer-1gwxckt > :first-child { margin-top: 0px; } .framer-aAPQZ .framer-1gwxckt > :last-child { margin-bottom: 0px; } }", ".framer-aAPQZ.framer-v-13ebya5.framer-1n90iiz { width: 390px; }", ".framer-aAPQZ.framer-v-13ebya5 .framer-zzk5z2-container { top: 50%; }", ".framer-aAPQZ.framer-v-13ebya5 .framer-1gwxckt { aspect-ratio: 1.5 / 1; height: var(--framer-aspect-ratio-supported, 260px); }"];
var Framerc3jVHY4cP = withCSS2(Component2, css4, "framer-aAPQZ");
var stdin_default2 = Framerc3jVHY4cP;
Framerc3jVHY4cP.displayName = "Video";
Framerc3jVHY4cP.defaultProps = { height: 700, width: 1128 };
addPropertyControls2(Framerc3jVHY4cP, { variant: { options: ["LYp1X5Hpl", "E3n80lD95"], optionTitles: ["Desktop", "Phone"], title: "Variant", type: ControlType2.Enum }, M7GdAI5Rj: { title: "Click", type: ControlType2.EventHandler }, oA1DUcxGC: { defaultValue: "Text", displayTextArea: true, title: "Text", type: ControlType2.String }, ZOyXnGfCw: { defaultValue: "Small text", displayTextArea: true, title: "Small text", type: ControlType2.String }, uUm_1jxjT: { __defaultAssetReference: "data:framer/asset-reference,cWKPopujkJqclchyOL1bYOiZDs.jpg?originalFilename=image-2.jpg&preferredSize=auto", __vekterDefault: { alt: "", assetReference: "data:framer/asset-reference,cWKPopujkJqclchyOL1bYOiZDs.jpg?originalFilename=image-2.jpg&preferredSize=auto" }, title: "Image", type: ControlType2.ResponsiveImage } });
addFonts2(Framerc3jVHY4cP, [{ explicitInter: true, fonts: [] }, ...PlayFonts, ...GrainFonts], { supportsExplicitInterCodegen: true });

// virtual:video
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {
  "base": "E3n80lD95",
  "xl": "LYp1X5Hpl"
};
stdin_default2.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default2,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default2, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default2);
export {
  ComponentWithRoot as default
};
