/* This file was generated by Unframer, do not edit manually */
import type * as React from "react"

import type { UnframerBreakpoint } from "unframer"

type Locale = string
export interface Props {
    children?: React.ReactNode
    locale?: Locale
    style?: React.CSSProperties
    className?: string
    id?: string
    width?: any
    height?: any
    layoutId?: string
    "variant"?: 'Desktop' | 'Phone'
    "image"?: {src: string, srcSet?: string, alt?: string}
    "title"?: string
    "year"?: string
}

const PhotoItemFramerComponent = (props: Props) => any

type VariantsMap = Partial<Record<UnframerBreakpoint, Props['variant']>> & { base: Props['variant'] }

PhotoItemFramerComponent.Responsive = (props: Omit<Props, 'variant'> & {variants?: VariantsMap}) => any

export default PhotoItemFramerComponent

