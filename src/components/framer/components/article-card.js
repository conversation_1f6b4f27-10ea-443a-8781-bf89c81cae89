// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";
import {
  className,
  css,
  fonts
} from "./chunks/chunk-SATCLWQE.js";
import {
  stdin_default
} from "./chunks/chunk-3LMAB7ZO.js";

// virtual:article-card
import { Fragment as Fragment2 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/AoMRsMghfrPsFoStNO5j/P8G2i4Dy8PwIpLcRzeNX/ZwY2WTzsN.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ComponentViewportProvider, ControlType, cx, getFonts, getFontsFromSharedStyle, getLoadingLazyAtYPosition, Image, Link, RichText, SmartComponentScopedContainer, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var PlusIconSmallFonts = getFonts(stdin_default);
var enabledGestures = { xeyYUsuRj: { hover: true } };
var cycleOrder = ["xeyYUsuRj", "bmVn3ad3f", "DtPLW4ZIv"];
var serializationHash = "framer-yVnXQ";
var variantClassNames = { bmVn3ad3f: "framer-v-p7tjmc", DtPLW4ZIv: "framer-v-1ltupdo", xeyYUsuRj: "framer-v-1dub8au" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { delay: 0, duration: 0.45, ease: [0.82, 0.11, 0.37, 0.82], type: "tween" };
var toResponsiveImage = (value) => {
  if (typeof value === "object" && value !== null && typeof value.src === "string") {
    return value;
  }
  return typeof value === "string" ? { src: value } : void 0;
};
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { Desktop: "xeyYUsuRj", Phone: "DtPLW4ZIv", Tablet: "bmVn3ad3f" };
var getProps = ({ date, height, id, image, intro, link, title, width, ...props }) => {
  return { ...props, F50RVideU: image ?? props.F50RVideU, K12ik372n: intro ?? props.K12ik372n ?? "Intro", KJlEifJo5: date ?? props.KJlEifJo5 ?? "Feb 2, 2025", nNNBkmlUp: title ?? props.nNNBkmlUp ?? "Title", qZiPiDj_d: link ?? props.qZiPiDj_d, variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "xeyYUsuRj" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className: className2, layoutId, variant, qZiPiDj_d, F50RVideU, KJlEifJo5, nNNBkmlUp, K12ik372n, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "xeyYUsuRj", enabledGestures, ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [className];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsx(Link, { href: qZiPiDj_d, motionChild: true, nodeId: "xeyYUsuRj", scopeId: "ZwY2WTzsN", children: /* @__PURE__ */ _jsxs(motion.a, { ...restProps, ...gestureHandlers, className: `${cx(scopingClassNames, "framer-1dub8au", className2, classNames)} framer-1eh8g9`, "data-framer-name": "Desktop", layoutDependency, layoutId: "xeyYUsuRj", ref: refBinding, style: { backgroundColor: "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", borderBottomLeftRadius: 18, borderBottomRightRadius: 18, borderTopLeftRadius: 18, borderTopRightRadius: 18, ...style }, ...addPropertyOverrides({ "xeyYUsuRj-hover": { "data-framer-name": void 0 }, bmVn3ad3f: { "data-framer-name": "Tablet" }, DtPLW4ZIv: { "data-framer-name": "Phone" } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsxs(motion.div, { className: "framer-1cdnoa9", "data-framer-name": "Top", layoutDependency, layoutId: "AWIJecaxU", children: [/* @__PURE__ */ _jsx(Image, { background: { alt: "", fit: "fill", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 10 + (0 + 0 + ((componentViewport?.height || 550) - 20 - (Math.max(0, ((componentViewport?.height || 550) - 20 - 325.6) / 1) * 1 + 325.6)) / 1 * 0) + 0), sizes: "110px", ...toResponsiveImage(F50RVideU) }, className: "framer-1pncdfo", layoutDependency, layoutId: "s6zjV0pEM", style: { borderBottomLeftRadius: 12, borderBottomRightRadius: 12, borderTopLeftRadius: 12, borderTopRightRadius: 12 }, ...addPropertyOverrides({ "xeyYUsuRj-hover": { background: { alt: "", fit: "fill", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 10 + (0 + 0 + ((componentViewport?.height || 550) - 20 - (Math.max(0, ((componentViewport?.height || 550) - 20 - 312.6) / 1) * 1 + 312.6)) / 1 * 0) + 0), sizes: "170px", ...toResponsiveImage(F50RVideU) } }, bmVn3ad3f: { background: { alt: "", fit: "fill", loading: getLoadingLazyAtYPosition((componentViewport?.y || 0) + 10 + (0 + 0 + ((componentViewport?.height || 550) - 20 - (Math.max(0, ((componentViewport?.height || 550) - 20 - 313.6) / 1) * 1 + 313.6)) / 1 * 0) + 0), sizes: "110px", ...toResponsiveImage(F50RVideU) } } }, baseVariant, gestureVariant) }), /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 18, y: (componentViewport?.y || 0) + 10 + (0 + 0 + ((componentViewport?.height || 550) - 20 - (Math.max(0, ((componentViewport?.height || 550) - 20 - 325.6) / 1) * 1 + 325.6)) / 1 * 0) + 10, ...addPropertyOverrides({ "xeyYUsuRj-hover": { y: (componentViewport?.y || 0) + 10 + (0 + 0 + ((componentViewport?.height || 550) - 20 - (Math.max(0, ((componentViewport?.height || 550) - 20 - 312.6) / 1) * 1 + 312.6)) / 1 * 0) + 10 }, bmVn3ad3f: { y: (componentViewport?.y || 0) + 10 + (0 + 0 + ((componentViewport?.height || 550) - 20 - (Math.max(0, ((componentViewport?.height || 550) - 20 - 313.6) / 1) * 1 + 313.6)) / 1 * 0) + 10 } }, baseVariant, gestureVariant), children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-1ri13cr-container", layoutDependency, layoutId: "t4cMJ7yMg-container", nodeId: "t4cMJ7yMg", rendersWithMotion: true, scopeId: "ZwY2WTzsN", style: { rotate: 0 }, variants: { "xeyYUsuRj-hover": { rotate: 90 } }, children: /* @__PURE__ */ _jsx(stdin_default, { CgCxwDz_B: true, CquvwTJCF: "rgb(255, 255, 255)", height: "100%", id: "t4cMJ7yMg", L_MXIE6eA: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", layoutId: "t4cMJ7yMg", width: "100%" }) }) })] }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-8ukse3", "data-framer-name": "Bottom", layoutDependency, layoutId: "Py3XHE20T", children: [/* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItTWVkaXVt", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "12px", "--framer-font-weight": "500", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "130%", "--framer-text-color": "var(--extracted-r6o4lv, rgb(9, 9, 9))" }, children: "Feb 2, 2025" }) }), className: "framer-x32njr", fonts: ["Inter-Medium"], layoutDependency, layoutId: "d6gX87ozh", style: { "--extracted-r6o4lv": "rgb(9, 9, 9)", "--framer-paragraph-spacing": "0px", opacity: 0.6 }, text: KJlEifJo5, verticalAlignment: "top", withExternalLayout: true }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-778232", "data-framer-name": "Text", layoutDependency, layoutId: "MwPk2gyPk", children: [/* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItTWVkaXVt", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "20px", "--framer-font-weight": "500", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "1.3em", "--framer-text-color": "var(--extracted-r6o4lv, var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10)))" }, children: "5 website trends to watch in 2025" }) }), className: "framer-1293ubj", "data-framer-name": "Title", fonts: ["Inter-Medium"], layoutDependency, layoutId: "OptUVJ7q5", style: { "--extracted-r6o4lv": "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))" }, text: nNNBkmlUp, verticalAlignment: "top", withExternalLayout: true }), /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-1mf8d9g", "data-styles-preset": "ypR5VEWEl", children: "Content" }) }), className: "framer-1mek970", fonts: ["Inter"], layoutDependency, layoutId: "mk46abD9W", style: { "--framer-paragraph-spacing": "0px", opacity: 0.6 }, text: K12ik372n, verticalAlignment: "top", withExternalLayout: true })] })] })] }) }) }) }) });
});
var css2 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-yVnXQ.framer-1eh8g9, .framer-yVnXQ .framer-1eh8g9 { display: block; }", ".framer-yVnXQ.framer-1dub8au { align-content: center; align-items: center; cursor: pointer; display: flex; flex-direction: column; flex-wrap: nowrap; height: 550px; justify-content: space-between; padding: 10px; position: relative; text-decoration: none; width: 377px; }", ".framer-yVnXQ .framer-1cdnoa9 { align-content: flex-start; align-items: flex-start; display: flex; flex: 1 0 0px; flex-direction: row; flex-wrap: nowrap; height: 1px; justify-content: space-between; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-yVnXQ .framer-1pncdfo { aspect-ratio: 1 / 1; flex: none; height: 110px; position: relative; width: var(--framer-aspect-ratio-supported, 110px); }", ".framer-yVnXQ .framer-1ri13cr-container { flex: none; height: auto; position: absolute; right: 10px; top: 10px; width: auto; z-index: 1; }", ".framer-yVnXQ .framer-8ukse3 { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 25px; height: min-content; justify-content: flex-start; overflow: visible; padding: 20px; position: relative; width: 100%; }", ".framer-yVnXQ .framer-x32njr { flex: none; height: auto; position: relative; white-space: pre; width: auto; }", ".framer-yVnXQ .framer-778232 { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-yVnXQ .framer-1293ubj { --framer-text-wrap: balance; flex: none; height: auto; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }", ".framer-yVnXQ .framer-1mek970 { --framer-text-wrap-override: balance; flex: none; height: auto; position: relative; width: 100%; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-yVnXQ .framer-8ukse3, .framer-yVnXQ .framer-778232 { gap: 0px; } .framer-yVnXQ .framer-8ukse3 > * { margin: 0px; margin-bottom: calc(25px / 2); margin-top: calc(25px / 2); } .framer-yVnXQ .framer-8ukse3 > :first-child, .framer-yVnXQ .framer-778232 > :first-child { margin-top: 0px; } .framer-yVnXQ .framer-8ukse3 > :last-child, .framer-yVnXQ .framer-778232 > :last-child { margin-bottom: 0px; } .framer-yVnXQ .framer-778232 > * { margin: 0px; margin-bottom: calc(10px / 2); margin-top: calc(10px / 2); } }", ".framer-yVnXQ.framer-v-p7tjmc.framer-1dub8au, .framer-yVnXQ.framer-v-1ltupdo.framer-1dub8au { cursor: unset; }", ".framer-yVnXQ.framer-v-p7tjmc .framer-8ukse3 { padding: 14px; }", ".framer-yVnXQ.framer-v-1dub8au.hover .framer-1pncdfo { height: 170px; width: var(--framer-aspect-ratio-supported, 170px); }", ".framer-yVnXQ.framer-v-1dub8au.hover .framer-8ukse3 { gap: 16px; }", ".framer-yVnXQ.framer-v-1dub8au.hover .framer-778232 { gap: 6px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-yVnXQ.framer-v-1dub8au.hover .framer-8ukse3, .framer-yVnXQ.framer-v-1dub8au.hover .framer-778232 { gap: 0px; } .framer-yVnXQ.framer-v-1dub8au.hover .framer-8ukse3 > * { margin: 0px; margin-bottom: calc(16px / 2); margin-top: calc(16px / 2); } .framer-yVnXQ.framer-v-1dub8au.hover .framer-8ukse3 > :first-child, .framer-yVnXQ.framer-v-1dub8au.hover .framer-778232 > :first-child { margin-top: 0px; } .framer-yVnXQ.framer-v-1dub8au.hover .framer-8ukse3 > :last-child, .framer-yVnXQ.framer-v-1dub8au.hover .framer-778232 > :last-child { margin-bottom: 0px; } .framer-yVnXQ.framer-v-1dub8au.hover .framer-778232 > * { margin: 0px; margin-bottom: calc(6px / 2); margin-top: calc(6px / 2); } }", ...css];
var FramerZwY2WTzsN = withCSS(Component, css2, "framer-yVnXQ");
var stdin_default2 = FramerZwY2WTzsN;
FramerZwY2WTzsN.displayName = "Article card";
FramerZwY2WTzsN.defaultProps = { height: 550, width: 377 };
addPropertyControls(FramerZwY2WTzsN, { variant: { options: ["xeyYUsuRj", "bmVn3ad3f", "DtPLW4ZIv"], optionTitles: ["Desktop", "Tablet", "Phone"], title: "Variant", type: ControlType.Enum }, qZiPiDj_d: { title: "Link", type: ControlType.Link }, F50RVideU: { title: "Image", type: ControlType.ResponsiveImage }, KJlEifJo5: { defaultValue: "Feb 2, 2025", displayTextArea: false, title: "Date", type: ControlType.String }, nNNBkmlUp: { defaultValue: "Title", displayTextArea: false, title: "Title", type: ControlType.String }, K12ik372n: { defaultValue: "Intro", title: "Intro", type: ControlType.String } });
addFonts(FramerZwY2WTzsN, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5A3Ce6C9YYmCjpQx9M4inSaKU.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/Qx95Xyt0Ka3SGhinnbXIGpEIyP4.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/6mJuEAguuIuMog10gGvH5d3cl8.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/xYYWaj7wCU5zSQH0eXvSaS19wo.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/otTaNuNpVK4RbdlT7zDDdKvQBA.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/d3tHnaQIAeqiE5hGcRw4mmgWYU.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/DolVirEGb34pEXEp8t8FQBSK4.woff2", weight: "500" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5vvr9Vy74if2I6bQbJvbw7SY1pQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/EOr0mi4hNtlgWNn9if640EZzXCo.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/Y9k9QrlZAqio88Klkmbd8VoMQc.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/OYrD2tBIBPvoJXiIHnLoOXnY9M.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/JeYwfuaPfZHQhEG8U5gtPDZ7WQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/vQyevYAyHtARFwPqUzQGpnDs.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/b6Y37FthZeALduNqHicBT6FutY.woff2", weight: "400" }] }, ...PlusIconSmallFonts, ...getFontsFromSharedStyle(fonts)], { supportsExplicitInterCodegen: true });

// virtual:article-card
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {
  "base": "DtPLW4ZIv",
  "md": "bmVn3ad3f",
  "xl": "xeyYUsuRj"
};
stdin_default2.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default2,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default2, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default2);
export {
  ComponentWithRoot as default
};
