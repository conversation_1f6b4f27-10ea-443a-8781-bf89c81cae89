// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";
import {
  stdin_default as stdin_default3
} from "./chunks/chunk-BAOIGPCO.js";
import {
  className,
  css,
  fonts
} from "./chunks/chunk-IWIJOZKQ.js";
import "./chunks/chunk-CVJIPDTS.js";
import {
  stdin_default
} from "./chunks/chunk-CHS3IKS5.js";
import "./chunks/chunk-3LMAB7ZO.js";
import {
  stdin_default as stdin_default2
} from "./chunks/chunk-77A4MMPH.js";
import "./chunks/chunk-PWMJ5SHX.js";
import "./chunks/chunk-YWUWNR35.js";

// virtual:advantages
import { Fragment as Fragment2 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/Evvpdb11LprA8hF4wwQ2/YUjEKblsUHoIv1sTKJxL/XMbv3HI5C.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, ComponentViewportProvider, cx, getFonts, getFontsFromSharedStyle, ResolveLinks, RichText, SmartComponentScopedContainer, useComponentViewport, useLocaleInfo, useRouter, useVariantState, withCSS, withFX } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";

// /:https://framerusercontent.com/modules/p1bBWLZwVrvHXuWlH4xx/n9OidViwumXepK7AzYdF/nCOD2Sdie.js
import { fontStore } from "unframer";
fontStore.loadFonts(["Inter-SemiBold", "Inter-Bold", "Inter-BoldItalic", "Inter-SemiBoldItalic"]);
var fonts2 = [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/hyOgCu0Xnghbimh0pE8QTvtt2AU.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/NeGmSOXrPBfEFIy5YZeHq17LEDA.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/oYaAX5himiTPYuN8vLWnqBbfD2s.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/lEJLP4R0yuCaMCjSXYHtJw72M.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/cRJyLNuTJR5jbyKzGi33wU9cqIQ.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/1ZFS7N918ojhhd0nQWdj3jz4w.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/A0Wcc7NgXMjUuFdquHDrIZpzZw0.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/DpPBYI0sL4fYLgAkX8KXOPVt7c.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/4RAEQdEOrcnDkhHiiCbJOw92Lk.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/1K3W8DizY3v4emK8Mb08YHxTbs.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/tUSCtfYVM1I1IchuyCwz9gDdQ.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/VgYFWiwsAC5OYxAycRXXvhze58.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/DXD0Q7LSl7HEvDzucnyLnGBHM.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/GIryZETIX4IFypco5pYZONKhJIo.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/H89BbHkbHDzlxZzxi8uPzTsp90.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/u6gJwDuwB143kpNK1T1MDKDWkMc.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/43sJ6MfOPh1LCJt46OvyDuSbA6o.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/wccHG0r4gBDAIRhfHiOlq6oEkqw.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/WZ367JPwf9bRW6LdTHN8rXgSjw.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/QxmhnWTzLtyjIiZcfaLIJ8EFBXU.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/2A4Xx7CngadFGlVV4xrO06OBHY.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/vxBnBhH8768IFAXAb4Qf6wQHKs.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/zSsEuoJdh8mcFVk976C05ZfQr8.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/b8ezwLrN7h2AUoPEENcsTMVJ0.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/mvNEIBLyHbscgHtwfsByjXUz3XY.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/6FI2EneKzM3qBy5foOZXey7coCA.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/qrVgiXNd6RuQjETYQiVQ9nqCk.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "italic", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/NHHeAKJVP0ZWHk5YZnQQChIsBM.woff2", weight: "600" }] }];
var css2 = ['.framer-iz7vR .framer-styles-preset-4vuy4n:not(.rich-text-wrapper), .framer-iz7vR .framer-styles-preset-4vuy4n.rich-text-wrapper h2 { --framer-font-family: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-open-type-features: normal; --framer-font-size: 60px; --framer-font-style: normal; --framer-font-style-bold: normal; --framer-font-style-bold-italic: italic; --framer-font-style-italic: italic; --framer-font-variation-axes: normal; --framer-font-weight: 600; --framer-font-weight-bold: 700; --framer-font-weight-bold-italic: 700; --framer-font-weight-italic: 600; --framer-letter-spacing: -0.06em; --framer-line-height: 110%; --framer-paragraph-spacing: 40px; --framer-text-alignment: start; --framer-text-color: #090909; --framer-text-decoration: none; --framer-text-stroke-color: initial; --framer-text-stroke-width: initial; --framer-text-transform: none; }', '@media (max-width: 1199px) and (min-width: 810px) { .framer-iz7vR .framer-styles-preset-4vuy4n:not(.rich-text-wrapper), .framer-iz7vR .framer-styles-preset-4vuy4n.rich-text-wrapper h2 { --framer-font-family: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-open-type-features: normal; --framer-font-size: 50px; --framer-font-style: normal; --framer-font-style-bold: normal; --framer-font-style-bold-italic: italic; --framer-font-style-italic: italic; --framer-font-variation-axes: normal; --framer-font-weight: 600; --framer-font-weight-bold: 700; --framer-font-weight-bold-italic: 700; --framer-font-weight-italic: 600; --framer-letter-spacing: -0.06em; --framer-line-height: 110%; --framer-paragraph-spacing: 40px; --framer-text-alignment: start; --framer-text-color: #090909; --framer-text-decoration: none; --framer-text-stroke-color: initial; --framer-text-stroke-width: initial; --framer-text-transform: none; } }', '@media (max-width: 809px) and (min-width: 0px) { .framer-iz7vR .framer-styles-preset-4vuy4n:not(.rich-text-wrapper), .framer-iz7vR .framer-styles-preset-4vuy4n.rich-text-wrapper h2 { --framer-font-family: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-bold-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-family-italic: "Inter", "Inter Placeholder", sans-serif; --framer-font-open-type-features: normal; --framer-font-size: 32px; --framer-font-style: normal; --framer-font-style-bold: normal; --framer-font-style-bold-italic: italic; --framer-font-style-italic: italic; --framer-font-variation-axes: normal; --framer-font-weight: 600; --framer-font-weight-bold: 700; --framer-font-weight-bold-italic: 700; --framer-font-weight-italic: 600; --framer-letter-spacing: -0.06em; --framer-line-height: 110%; --framer-paragraph-spacing: 40px; --framer-text-alignment: start; --framer-text-color: #090909; --framer-text-decoration: none; --framer-text-stroke-color: initial; --framer-text-stroke-width: initial; --framer-text-transform: none; } }'];
var className2 = "framer-iz7vR";

// /:https://framerusercontent.com/modules/Evvpdb11LprA8hF4wwQ2/YUjEKblsUHoIv1sTKJxL/XMbv3HI5C.js
var IntroFonts = getFonts(stdin_default);
var BannerFonts = getFonts(stdin_default3);
var SmartComponentScopedContainerWithFX = withFX(SmartComponentScopedContainer);
var CardFonts = getFonts(stdin_default2);
var serializationHash = "framer-uJw09";
var variantClassNames = { T1We8L0U5: "framer-v-3enyfc" };
var transition1 = { bounce: 0.2, delay: 0, duration: 0.4, type: "spring" };
var animation = { opacity: 1e-3, rotate: 0, scale: 1, skewX: 0, skewY: 0, x: 0, y: 20 };
var transition2 = { delay: 0.02, duration: 0.9, ease: [0.44, 0, 0.13, 0.96], type: "tween" };
var textEffect = { effect: animation, repeat: false, startDelay: 0, threshold: 0, tokenization: "word", transition: transition2, trigger: "onInView", type: "appear" };
var animation1 = { opacity: 0, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, x: 0, y: 0 };
var transition3 = { bounce: 0.1, delay: 0.1, duration: 1.2, type: "spring" };
var transition4 = { bounce: 0.1, delay: 0.3, duration: 1.2, type: "spring" };
var transition5 = { bounce: 0.1, delay: 0.6, duration: 1.2, type: "spring" };
var addImageAlt = (image, alt) => {
  if (!image || typeof image !== "object") {
    return;
  }
  return { ...image, alt };
};
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var getProps = ({ height, id, width, ...props }) => {
  return { ...props };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className: className3, layoutId, variant, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ defaultVariant: "T1We8L0U5", ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [className2, className];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  const router = useRouter();
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsx(motion.section, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-3enyfc", className3, classNames), "data-framer-name": "Variant 1", layoutDependency, layoutId: "T1We8L0U5", ref: refBinding, style: { ...style }, children: /* @__PURE__ */ _jsxs(motion.div, { className: "framer-1xayq43", "data-framer-name": "Container", layoutDependency, layoutId: "xDOg16BG_", children: [/* @__PURE__ */ _jsxs(motion.div, { className: "framer-15lgm9m", "data-framer-name": "Top", layoutDependency, layoutId: "WvD8O_aGU", children: [/* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 22, width: `max((min(${componentViewport?.width || "100vw"} - 72px, 1520px) - 12px) / 4, 50px)`, y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 200) - 0 - 807) / 2 + 0 + 0) + 0 + 0 + 0 + 0, children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-1q8kseh-container", layoutDependency, layoutId: "jOGPNpumU-container", nodeId: "jOGPNpumU", rendersWithMotion: true, scopeId: "XMbv3HI5C", children: /* @__PURE__ */ _jsx(stdin_default, { height: "100%", id: "jOGPNpumU", k3Z3ztoi4: "Why choose us", layoutId: "jOGPNpumU", RaZgbjWXH: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", TmIm48vq7: "rgb(255, 255, 255)", variant: "VaDzezBN8", width: "100%", ws4vDxEZM: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))" }) }) }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-lftblh", "data-framer-name": "Heading", layoutDependency, layoutId: "KrKAe4C_v", children: /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsxs(motion.h2, { className: "framer-styles-preset-4vuy4n", "data-styles-preset": "nCOD2Sdie", children: ["Proven results for every project, ", /* @__PURE__ */ _jsx(motion.span, { style: { "--framer-text-color": "var(--extracted-1qn201a, rgba(10, 10, 10, 0.6))" }, children: "with a focus on design and functionality." })] }) }), className: "framer-zegauz", "data-framer-name": "Section Description", effect: textEffect, fonts: ["Inter"], layoutDependency, layoutId: "ebq5DspQ6", style: { "--extracted-1qn201a": "rgba(10, 10, 10, 0.6)", "--framer-paragraph-spacing": "0px" }, verticalAlignment: "top", withExternalLayout: true }) })] }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-ftqq3z", "data-framer-name": "Content", layoutDependency, layoutId: "GLvcr2zCn", children: [/* @__PURE__ */ _jsx(ResolveLinks, { links: [{ href: { webPageId: "hAlvrhFMj" }, implicitPathVariables: void 0 }], children: (resolvedLinks) => /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 621, width: `max((min(${componentViewport?.width || "100vw"} - 72px, 1520px) - 12px) / 4, 50px)`, y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 200) - 0 - 807) / 2 + 0 + 0) + 0 + 186 + 0 + 0, children: /* @__PURE__ */ _jsx(SmartComponentScopedContainerWithFX, { __framer__animate: { transition: transition3 }, __framer__animateOnce: true, __framer__enter: animation1, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-88qoxx-container", layoutDependency, layoutId: "Gzss9vWXY-container", nodeId: "Gzss9vWXY", rendersWithMotion: true, scopeId: "XMbv3HI5C", children: /* @__PURE__ */ _jsx(stdin_default3, { height: "100%", hw6v_4Fbo: "Your digital journey begins with a conversation. Let's talk today.", id: "Gzss9vWXY", layoutId: "Gzss9vWXY", style: { height: "100%", width: "100%" }, u9qzj8Ajl: resolvedLinks[0], variant: "IjVHBd9Ch", width: "100%", XL_ELPYj5: "Let's talk" }) }) }) }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-kvz4tx", "data-framer-name": "Filler", layoutDependency, layoutId: "dVo6mm29h" }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-k82g9i", "data-framer-name": "Items", layoutDependency, layoutId: "zXkKShQjg", children: [/* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsxs(motion.p, { className: "framer-styles-preset-1oueo73", "data-styles-preset": "HLpRTFhim", style: { "--framer-text-color": "var(--extracted-r6o4lv, rgba(10, 10, 10, 0.6))" }, children: [/* @__PURE__ */ _jsx(motion.span, { style: { "--framer-text-color": "var(--extracted-1w3ko1f, var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10)))" }, children: "                  No fluff, just results." }), " Thoughtful design and tools that make your work easier. We focus on smart design and useful features, project after project."] }) }), className: "framer-1xbqwt4", "data-framer-name": "Collaboration Text", fonts: ["Inter"], layoutDependency, layoutId: "Grb13ygDt", style: { "--extracted-1w3ko1f": "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", "--extracted-r6o4lv": "rgba(10, 10, 10, 0.6)", "--framer-paragraph-spacing": "0px" }, verticalAlignment: "top", withExternalLayout: true }), /* @__PURE__ */ _jsxs(motion.div, { className: "framer-3k1kkv", "data-framer-name": "Cards", layoutDependency, layoutId: "SLvPspDPy", children: [/* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 0, width: `max((max((min(${componentViewport?.width || "100vw"} - 72px, 1520px) - 12px) / 4, 50px) * 2 - 0px) / 2, 1px)`, y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 200) - 0 - 807) / 2 + 0 + 0) + 0 + 186 + 0 + 0 + 0 + 212 + 0, children: /* @__PURE__ */ _jsx(SmartComponentScopedContainerWithFX, { __framer__animate: { transition: transition4 }, __framer__animateOnce: true, __framer__enter: animation1, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-sp8ce4-container", layoutDependency, layoutId: "Otw8_qkgf-container", nodeId: "Otw8_qkgf", rendersWithMotion: true, scopeId: "XMbv3HI5C", children: /* @__PURE__ */ _jsx(stdin_default2, { CEihHXtRL: 100, eiMtdGMEG: 50, height: "100%", HVRXp_Sg7: false, id: "Otw8_qkgf", JE5J2kZ5f: 25, layoutId: "Otw8_qkgf", noOZ4mEf6: "We\u2019ve delivered 50+ projects that help companies generate real results.", RE0VYGptP: "01", style: { height: "100%", width: "100%" }, variant: "oHqpGkbFc", width: "100%", XxQcXuo02: "Successful projects completed", ZSqaLyjGx: "+" }) }) }), /* @__PURE__ */ _jsx(ComponentViewportProvider, { height: 0, width: `max((max((min(${componentViewport?.width || "100vw"} - 72px, 1520px) - 12px) / 4, 50px) * 2 - 0px) / 2, 1px)`, y: (componentViewport?.y || 0) + 0 + (((componentViewport?.height || 200) - 0 - 807) / 2 + 0 + 0) + 0 + 186 + 0 + 0 + 0 + 212 + 0, children: /* @__PURE__ */ _jsx(SmartComponentScopedContainerWithFX, { __framer__animate: { transition: transition5 }, __framer__animateOnce: true, __framer__enter: animation1, __framer__styleAppearEffectEnabled: true, __framer__threshold: 0, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, className: "framer-h1veau-container", layoutDependency, layoutId: "sW23RFGiP-container", nodeId: "sW23RFGiP", rendersWithMotion: true, scopeId: "XMbv3HI5C", children: /* @__PURE__ */ _jsx(stdin_default2, { B2goKeVx1: addImageAlt({ pixelHeight: 20, pixelWidth: 77, src: "https://framerusercontent.com/images/CtaV2dn3ujpK8zv0Py3i9IJArPQ.svg" }, ""), CEihHXtRL: 100, eiMtdGMEG: 95, height: "100%", HVRXp_Sg7: true, id: "sW23RFGiP", JE5J2kZ5f: 16, layoutId: "sW23RFGiP", noOZ4mEf6: "", oi6jPflMU: addImageAlt({ pixelHeight: 22, pixelWidth: 86, src: "https://framerusercontent.com/images/m9cv2Bx2sImOjy4Q3x1Fk5d5WGM.svg" }, ""), QsA9HyaU6: addImageAlt({ pixelHeight: 34, pixelWidth: 65, src: "https://framerusercontent.com/images/wk98ext8C9l414fS0PK6BvjTA.svg" }, ""), RE0VYGptP: "02", style: { height: "100%", width: "100%" }, variant: "oHqpGkbFc", width: "100%", XxQcXuo02: "Customer\nsatisfaction rate", ZSqaLyjGx: "%" }) }) })] })] })] })] }) }) }) }) });
});
var css3 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-uJw09.framer-1kubzs8, .framer-uJw09 .framer-1kubzs8 { display: block; }", ".framer-uJw09.framer-3enyfc { align-content: center; align-items: center; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 70px; height: min-content; justify-content: center; overflow: visible; padding: 0px 36px 0px 36px; position: relative; width: 1200px; }", ".framer-uJw09 .framer-1xayq43 { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 120px; height: min-content; justify-content: center; max-width: 1520px; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-uJw09 .framer-15lgm9m, .framer-uJw09 .framer-ftqq3z { display: grid; flex: none; gap: 4px; grid-auto-rows: minmax(0, 1fr); grid-template-columns: repeat(4, minmax(50px, 1fr)); grid-template-rows: repeat(1, minmax(0, 1fr)); height: min-content; justify-content: center; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-uJw09 .framer-1q8kseh-container { align-self: start; flex: none; height: auto; justify-self: start; position: relative; width: 100%; }", ".framer-uJw09 .framer-lftblh { align-content: flex-start; align-items: flex-start; align-self: start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 22px; grid-column: span 3; height: min-content; justify-content: center; justify-self: start; overflow: visible; padding: 0px; position: relative; width: 100%; }", ".framer-uJw09 .framer-zegauz { flex: none; height: auto; max-width: 830px; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }", ".framer-uJw09 .framer-88qoxx-container { align-self: start; flex: none; height: 100%; justify-self: start; position: relative; width: 100%; }", ".framer-uJw09 .framer-kvz4tx { align-self: start; flex: none; height: 100%; justify-self: start; overflow: visible; position: relative; width: 100%; }", ".framer-uJw09 .framer-k82g9i { align-content: flex-start; align-items: flex-start; align-self: start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 80px; grid-column: span 2; height: min-content; justify-content: center; justify-self: start; overflow: hidden; padding: 0px; position: relative; width: 100%; }", ".framer-uJw09 .framer-1xbqwt4 { flex: none; height: auto; max-width: 550px; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }", ".framer-uJw09 .framer-3k1kkv { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 4px; height: min-content; justify-content: center; overflow: hidden; padding: 0px; position: relative; width: 100%; }", ".framer-uJw09 .framer-sp8ce4-container, .framer-uJw09 .framer-h1veau-container { flex: 1 0 0px; height: 100%; position: relative; width: 1px; }", ...css2, ...css];
var FramerXMbv3HI5C = withCSS(Component, css3, "framer-uJw09");
var stdin_default4 = FramerXMbv3HI5C;
FramerXMbv3HI5C.displayName = "Advantages";
FramerXMbv3HI5C.defaultProps = { height: 200, width: 1200 };
addFonts(FramerXMbv3HI5C, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5vvr9Vy74if2I6bQbJvbw7SY1pQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/EOr0mi4hNtlgWNn9if640EZzXCo.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/Y9k9QrlZAqio88Klkmbd8VoMQc.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/OYrD2tBIBPvoJXiIHnLoOXnY9M.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/JeYwfuaPfZHQhEG8U5gtPDZ7WQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/vQyevYAyHtARFwPqUzQGpnDs.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/b6Y37FthZeALduNqHicBT6FutY.woff2", weight: "400" }] }, ...IntroFonts, ...BannerFonts, ...CardFonts, ...getFontsFromSharedStyle(fonts2), ...getFontsFromSharedStyle(fonts)], { supportsExplicitInterCodegen: true });

// virtual:advantages
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {};
stdin_default4.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default4,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default4, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default4);
export {
  ComponentWithRoot as default
};
