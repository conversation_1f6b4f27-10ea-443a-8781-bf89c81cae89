// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";
import {
  Counter
} from "./chunks/chunk-PWMJ5SHX.js";
import {
  className,
  css,
  fonts
} from "./chunks/chunk-YWUWNR35.js";

// virtual:number-item
import { Fragment as Fragment2 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/cRPBb24hNrEYtkpZDWdW/i0M2yCuTPdw4utNeEQfN/QDgEnH9Gf.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ComponentViewportProvider, ControlType, cx, getFonts, getFontsFromSharedStyle, getPropertyControls, RichT<PERSON>t, SmartComponentScopedContainer, useComponentViewport, useLocaleInfo, useVariantState, withCSS } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var CounterFonts = getFonts(Counter);
var CounterControls = getPropertyControls(Counter);
var cycleOrder = ["uRjP6pc4r", "TU3WyJ6FD", "tcSKurt3w"];
var serializationHash = "framer-IVCHB";
var variantClassNames = { tcSKurt3w: "framer-v-1n85m93", TU3WyJ6FD: "framer-v-1erhzjb", uRjP6pc4r: "framer-v-1v8aapq" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { bounce: 0.2, delay: 0, duration: 0.4, type: "spring" };
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { Desktop: "uRjP6pc4r", Phone: "tcSKurt3w", Tablet: "TU3WyJ6FD" };
var getProps = ({ height, id, incrementType, number, speedMs, suffix, text, width, ...props }) => {
  return { ...props, AoCPZmL82: speedMs ?? props.AoCPZmL82 ?? 60, Bd3SgYY7x: number ?? props.Bd3SgYY7x ?? 17, oQLXoYWFl: text ?? props.oQLXoYWFl ?? "Text", variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "uRjP6pc4r", vZIxjO3nU: incrementType ?? props.vZIxjO3nU ?? "integer", zfTPlTmPF: suffix ?? props.zfTPlTmPF ?? "+" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className: className2, layoutId, variant, Bd3SgYY7x, vZIxjO3nU, zfTPlTmPF, AoCPZmL82, oQLXoYWFl, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "uRjP6pc4r", ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const sharedStyleClassNames = [className];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsxs(motion.div, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-1v8aapq", className2, classNames), "data-framer-name": "Desktop", layoutDependency, layoutId: "uRjP6pc4r", ref: refBinding, style: { ...style }, ...addPropertyOverrides({ tcSKurt3w: { "data-framer-name": "Phone" }, TU3WyJ6FD: { "data-framer-name": "Tablet" } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsx(ComponentViewportProvider, { children: /* @__PURE__ */ _jsx(SmartComponentScopedContainer, { className: "framer-rnq1xt-container", isAuthoredByUser: true, layoutDependency, layoutId: "SCstb8RtQ-container", nodeId: "SCstb8RtQ", rendersWithMotion: true, scopeId: "QDgEnH9Gf", children: /* @__PURE__ */ _jsx(Counter, { decimalSeparatorType: "none", end: Bd3SgYY7x, gapSize: 0, height: "100%", id: "SCstb8RtQ", incrementType: vZIxjO3nU, layoutId: "SCstb8RtQ", loop: false, prefixColor: "rgb(255, 255, 255)", prefixFont: { fontFamily: '"Inter", "Inter Placeholder", sans-serif', fontStyle: "normal", fontWeight: 700 }, prefixText: "", restartOnViewport: false, selectedFont: { fontFamily: '"Inter", "Inter Placeholder", sans-serif', fontStyle: "normal", fontWeight: 600 }, speed: AoCPZmL82, start: 0, startOnViewport: true, suffixColor: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", suffixFont: {}, suffixText: zfTPlTmPF, textColor: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))", textSize: 85, width: "100%", ...addPropertyOverrides({ tcSKurt3w: { textSize: 40 }, TU3WyJ6FD: { textSize: 58 } }, baseVariant, gestureVariant) }) }) }), /* @__PURE__ */ _jsx(RichText, { __fromCanvasComponent: true, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { className: "framer-styles-preset-1n1wh7h", "data-styles-preset": "gd6AWaps9", children: "Text" }) }), className: "framer-j604sy", "data-framer-name": "Description", fonts: ["Inter"], layoutDependency, layoutId: "qgOKyG9EP", style: { "--framer-paragraph-spacing": "0px", opacity: 0.6 }, text: oQLXoYWFl, verticalAlignment: "top", withExternalLayout: true })] }) }) }) });
});
var css2 = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-IVCHB.framer-3em11l, .framer-IVCHB .framer-3em11l { display: block; }", ".framer-IVCHB.framer-1v8aapq { align-content: flex-start; align-items: flex-start; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 18px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 0px; position: relative; width: 400px; }", ".framer-IVCHB .framer-rnq1xt-container { flex: none; height: auto; position: relative; width: auto; }", ".framer-IVCHB .framer-j604sy { --framer-text-wrap-override: balance; flex: none; height: auto; max-width: 140px; position: relative; width: 100%; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-IVCHB.framer-1v8aapq { gap: 0px; } .framer-IVCHB.framer-1v8aapq > * { margin: 0px; margin-bottom: calc(18px / 2); margin-top: calc(18px / 2); } .framer-IVCHB.framer-1v8aapq > :first-child { margin-top: 0px; } .framer-IVCHB.framer-1v8aapq > :last-child { margin-bottom: 0px; } }", ".framer-IVCHB.framer-v-1erhzjb.framer-1v8aapq, .framer-IVCHB.framer-v-1n85m93.framer-1v8aapq { gap: 8px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-IVCHB.framer-v-1erhzjb.framer-1v8aapq { gap: 0px; } .framer-IVCHB.framer-v-1erhzjb.framer-1v8aapq > * { margin: 0px; margin-bottom: calc(8px / 2); margin-top: calc(8px / 2); } .framer-IVCHB.framer-v-1erhzjb.framer-1v8aapq > :first-child { margin-top: 0px; } .framer-IVCHB.framer-v-1erhzjb.framer-1v8aapq > :last-child { margin-bottom: 0px; } }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-IVCHB.framer-v-1n85m93.framer-1v8aapq { gap: 0px; } .framer-IVCHB.framer-v-1n85m93.framer-1v8aapq > * { margin: 0px; margin-bottom: calc(8px / 2); margin-top: calc(8px / 2); } .framer-IVCHB.framer-v-1n85m93.framer-1v8aapq > :first-child { margin-top: 0px; } .framer-IVCHB.framer-v-1n85m93.framer-1v8aapq > :last-child { margin-bottom: 0px; } }", ...css];
var FramerQDgEnH9Gf = withCSS(Component, css2, "framer-IVCHB");
var stdin_default = FramerQDgEnH9Gf;
FramerQDgEnH9Gf.displayName = "Number item";
FramerQDgEnH9Gf.defaultProps = { height: 143, width: 400 };
addPropertyControls(FramerQDgEnH9Gf, { variant: { options: ["uRjP6pc4r", "TU3WyJ6FD", "tcSKurt3w"], optionTitles: ["Desktop", "Tablet", "Phone"], title: "Variant", type: ControlType.Enum }, Bd3SgYY7x: { defaultValue: 17, displayStepper: true, title: "Number", type: ControlType.Number }, vZIxjO3nU: CounterControls?.["incrementType"] && { ...CounterControls["incrementType"], defaultValue: "integer", description: void 0, hidden: void 0, title: "Increment Type" }, zfTPlTmPF: { defaultValue: "+", title: "Suffix", type: ControlType.String }, AoCPZmL82: { defaultValue: 60, max: 2e3, min: 0, step: 10, title: "Speed Ms", type: ControlType.Number }, oQLXoYWFl: { defaultValue: "Text", displayTextArea: true, title: "Text", type: ControlType.String } });
addFonts(FramerQDgEnH9Gf, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/hyOgCu0Xnghbimh0pE8QTvtt2AU.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/NeGmSOXrPBfEFIy5YZeHq17LEDA.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/oYaAX5himiTPYuN8vLWnqBbfD2s.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/lEJLP4R0yuCaMCjSXYHtJw72M.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/cRJyLNuTJR5jbyKzGi33wU9cqIQ.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/1ZFS7N918ojhhd0nQWdj3jz4w.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/A0Wcc7NgXMjUuFdquHDrIZpzZw0.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/DpPBYI0sL4fYLgAkX8KXOPVt7c.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/4RAEQdEOrcnDkhHiiCbJOw92Lk.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/1K3W8DizY3v4emK8Mb08YHxTbs.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/tUSCtfYVM1I1IchuyCwz9gDdQ.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/VgYFWiwsAC5OYxAycRXXvhze58.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/DXD0Q7LSl7HEvDzucnyLnGBHM.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/GIryZETIX4IFypco5pYZONKhJIo.woff2", weight: "700" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/5vvr9Vy74if2I6bQbJvbw7SY1pQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/EOr0mi4hNtlgWNn9if640EZzXCo.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/Y9k9QrlZAqio88Klkmbd8VoMQc.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/OYrD2tBIBPvoJXiIHnLoOXnY9M.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/JeYwfuaPfZHQhEG8U5gtPDZ7WQ.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/vQyevYAyHtARFwPqUzQGpnDs.woff2", weight: "400" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/b6Y37FthZeALduNqHicBT6FutY.woff2", weight: "400" }] }, ...CounterFonts, ...getFontsFromSharedStyle(fonts)], { supportsExplicitInterCodegen: true });

// virtual:number-item
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {
  "base": "tcSKurt3w",
  "md": "TU3WyJ6FD",
  "xl": "uRjP6pc4r"
};
stdin_default.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default);
export {
  ComponentWithRoot as default
};
