// @ts-nocheck
/* eslint-disable */
/* This file was generated by Unframer for Framer project 3ccb73edeeb799d0 "fabrica (copy)", do not edit manually */
"use client";

// virtual:pre-loader
import { Fragment as Fragment2 } from "react";
import { ContextProviders } from "unframer";

// /:https://framerusercontent.com/modules/hIy76KeQ1GGfbMKr8D9B/yvtQunteujxxyITievV0/wmZEqp0yx.js
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, RichText, useActiveVariantCallback, useComponentViewport, useLocaleInfo, useOnVariantChange, useVariantState, withCSS, withFX, withOptimizedAppearEffect } from "unframer";
import { LayoutGroup, motion, MotionConfigContext } from "unframer";
import * as React from "react";
import { useRef } from "react";
var RichTextWithFXWithOptimizedAppearEffect = withOptimizedAppearEffect(withFX(RichText));
var cycleOrder = ["w4tillrgy", "h0OxnBkBr", "LOgwjOwRC"];
var serializationHash = "framer-CnPlE";
var variantClassNames = { h0OxnBkBr: "framer-v-1lb8flo", LOgwjOwRC: "framer-v-1v8s4w6", w4tillrgy: "framer-v-1xyp36d" };
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants?.forEach((variant) => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
var transition1 = { delay: 0, duration: 0.9, ease: [0.96, -0.02, 0.38, 1.01], type: "tween" };
var transformTemplate1 = (_, t) => `translate(-50%, -50%) ${t}`;
var transition2 = { delay: 0, duration: 2.1, ease: [0.56, 0.22, 0.05, 0.99], type: "tween" };
var animation = { opacity: 1, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, transition: transition2, x: 0, y: 0 };
var animation1 = { opacity: 1e-3, rotate: 0, rotateX: 0, rotateY: 0, scale: 1, skewX: 0, skewY: 0, x: 0, y: 640 };
var animation2 = { filter: "blur(5px)", opacity: 1e-3, rotate: 0, scale: 1, skewX: 0, skewY: 0, x: 0, y: 270 };
var transition3 = { delay: 0.09, duration: 0.4, ease: [0.96, -0.02, 0.38, 1.01], type: "tween" };
var textEffect = { effect: animation2, startDelay: 0.3, tokenization: "character", transition: transition3, trigger: "onMount", type: "appear" };
var Transition = ({ value, children }) => {
  const config = React.useContext(MotionConfigContext);
  const transition = value ?? config.transition;
  const contextValue = React.useMemo(() => ({ ...config, transition }), [JSON.stringify(transition)]);
  return /* @__PURE__ */ _jsx(MotionConfigContext.Provider, { value: contextValue, children });
};
var Variants = motion.create(React.Fragment);
var humanReadableVariantMap = { "1": "w4tillrgy", "2": "h0OxnBkBr", "3": "LOgwjOwRC" };
var getProps = ({ height, id, width, ...props }) => {
  return { ...props, variant: humanReadableVariantMap[props.variant] ?? props.variant ?? "w4tillrgy" };
};
var createLayoutDependency = (props, variants) => {
  if (props.layoutDependency) return variants.join("-") + props.layoutDependency;
  return variants.join("-");
};
var Component = /* @__PURE__ */ React.forwardRef(function(props, ref) {
  const fallbackRef = useRef(null);
  const refBinding = ref ?? fallbackRef;
  const defaultLayoutId = React.useId();
  const { activeLocale, setLocale } = useLocaleInfo();
  const componentViewport = useComponentViewport();
  const { style, className, layoutId, variant, ...restProps } = getProps(props);
  const { baseVariant, classNames, clearLoadingGesture, gestureHandlers, gestureVariant, isLoading, setGestureState, setVariant, variants } = useVariantState({ cycleOrder, defaultVariant: "w4tillrgy", ref: refBinding, variant, variantClassNames });
  const layoutDependency = createLayoutDependency(props, variants);
  const { activeVariantCallback, delay } = useActiveVariantCallback(baseVariant);
  const onAppear18b601 = activeVariantCallback(async (...args) => {
    await delay(() => setVariant("h0OxnBkBr"), 1500);
  });
  const onAppear1fojsc0 = activeVariantCallback(async (...args) => {
    setVariant("LOgwjOwRC");
  });
  useOnVariantChange(baseVariant, { default: onAppear18b601, h0OxnBkBr: onAppear1fojsc0, LOgwjOwRC: void 0 });
  const sharedStyleClassNames = [];
  const scopingClassNames = cx(serializationHash, ...sharedStyleClassNames);
  return /* @__PURE__ */ _jsx(LayoutGroup, { id: layoutId ?? defaultLayoutId, children: /* @__PURE__ */ _jsx(Variants, { animate: variants, initial: false, children: /* @__PURE__ */ _jsx(Transition, { value: transition1, children: /* @__PURE__ */ _jsxs(motion.div, { ...restProps, ...gestureHandlers, className: cx(scopingClassNames, "framer-1xyp36d", className, classNames), "data-framer-name": "1", "data-highlight": true, layoutDependency, layoutId: "w4tillrgy", ref: refBinding, style: { ...style }, ...addPropertyOverrides({ h0OxnBkBr: { "data-framer-name": "2" }, LOgwjOwRC: { "data-framer-name": "3", "data-highlight": void 0 } }, baseVariant, gestureVariant), children: [/* @__PURE__ */ _jsx(RichTextWithFXWithOptimizedAppearEffect, { __fromCanvasComponent: true, __perspectiveFX: false, __smartComponentFX: true, __targetOpacity: 1, animate: animation, children: /* @__PURE__ */ _jsx(React.Fragment, { children: /* @__PURE__ */ _jsx(motion.p, { style: { "--font-selector": "SW50ZXItU2VtaUJvbGQ=", "--framer-font-family": '"Inter", "Inter Placeholder", sans-serif', "--framer-font-size": "38px", "--framer-font-weight": "600", "--framer-letter-spacing": "-0.04em", "--framer-line-height": "110%", "--framer-text-color": "var(--extracted-r6o4lv, var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255)))" }, children: "fabrica\xAE " }) }), className: "framer-1b4e3p4", "data-framer-appear-id": "1b4e3p4", effect: textEffect, fonts: ["Inter-SemiBold"], initial: animation1, layoutDependency, layoutId: "TWXXQNDuc", optimized: true, style: { "--extracted-r6o4lv": "var(--token-90ab9b9d-c64e-4230-9e06-707b75634f37, rgb(255, 255, 255))", "--framer-paragraph-spacing": "0px" }, transformTemplate: transformTemplate1, verticalAlignment: "top", withExternalLayout: true, ...addPropertyOverrides({ h0OxnBkBr: { animate: void 0, effect: void 0, initial: void 0, optimized: void 0 }, LOgwjOwRC: { animate: void 0, effect: void 0, initial: void 0, optimized: void 0 } }, baseVariant, gestureVariant) }), /* @__PURE__ */ _jsx(motion.div, { className: "framer-1g084ze", "data-framer-name": "BG", layoutDependency, layoutId: "M54D6Sqhc", style: { backgroundColor: "var(--token-88d5059b-bc5d-4e0a-ad79-b21e9a2c4948, rgb(10, 10, 10))" } })] }) }) }) });
});
var css = ["@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-CnPlE.framer-1yfq4w7, .framer-CnPlE .framer-1yfq4w7 { display: block; }", ".framer-CnPlE.framer-1xyp36d { align-content: center; align-items: center; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 0px; height: 939px; justify-content: center; overflow: hidden; padding: 0px; position: relative; width: 1200px; }", ".framer-CnPlE .framer-1b4e3p4 { flex: none; height: auto; left: 50%; position: absolute; top: 50%; white-space: pre; width: auto; z-index: 1; }", ".framer-CnPlE .framer-1g084ze { flex: 1 0 0px; height: 1px; overflow: visible; position: relative; width: 100%; z-index: 0; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-CnPlE.framer-1xyp36d { gap: 0px; } .framer-CnPlE.framer-1xyp36d > * { margin: 0px; margin-bottom: calc(0px / 2); margin-top: calc(0px / 2); } .framer-CnPlE.framer-1xyp36d > :first-child { margin-top: 0px; } .framer-CnPlE.framer-1xyp36d > :last-child { margin-bottom: 0px; } }", ".framer-CnPlE.framer-v-1v8s4w6 .framer-1b4e3p4 { top: -3%; z-index: 5; }", ".framer-CnPlE.framer-v-1v8s4w6 .framer-1g084ze { flex: none; height: 2px; left: 0px; position: absolute; right: 0px; top: -2px; width: unset; }"];
var FramerwmZEqp0yx = withCSS(Component, css, "framer-CnPlE");
var stdin_default = FramerwmZEqp0yx;
FramerwmZEqp0yx.displayName = "PreLoader";
FramerwmZEqp0yx.defaultProps = { height: 939, width: 1200 };
addPropertyControls(FramerwmZEqp0yx, { variant: { options: ["w4tillrgy", "h0OxnBkBr", "LOgwjOwRC"], optionTitles: ["1", "2", "3"], title: "Variant", type: ControlType.Enum } });
addFonts(FramerwmZEqp0yx, [{ explicitInter: true, fonts: [{ family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F", url: "https://framerusercontent.com/assets/hyOgCu0Xnghbimh0pE8QTvtt2AU.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116", url: "https://framerusercontent.com/assets/NeGmSOXrPBfEFIy5YZeHq17LEDA.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+1F00-1FFF", url: "https://framerusercontent.com/assets/oYaAX5himiTPYuN8vLWnqBbfD2s.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0370-03FF", url: "https://framerusercontent.com/assets/lEJLP4R0yuCaMCjSXYHtJw72M.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF", url: "https://framerusercontent.com/assets/cRJyLNuTJR5jbyKzGi33wU9cqIQ.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD", url: "https://framerusercontent.com/assets/1ZFS7N918ojhhd0nQWdj3jz4w.woff2", weight: "600" }, { family: "Inter", source: "framer", style: "normal", unicodeRange: "U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB", url: "https://framerusercontent.com/assets/A0Wcc7NgXMjUuFdquHDrIZpzZw0.woff2", weight: "600" }] }], { supportsExplicitInterCodegen: true });

// virtual:pre-loader
import { WithFramerBreakpoints } from "unframer";
import { jsx } from "react/jsx-runtime";
var locales = [];
var defaultResponsiveVariants = {};
stdin_default.Responsive = ({ locale, ...rest }) => {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: { "CSWMauv_K": { "path": "/projects/:slug" }, "CmPsU4Vle": { "path": "/legal/:slug" }, "EHD2n_rsP": { "path": "/projects" }, "KWF5sRdmJ": { "path": "/blog/:slug" }, "ZLJzEk6Fj": { "path": "/studio" }, "augiA20Il": { "path": "/" }, "hAlvrhFMj": { "path": "/contact" }, "p3zwtzFek": { "path": "/blog" }, "tR95VGg86": { "path": "/404" } },
      children: /* @__PURE__ */ jsx(
        WithFramerBreakpoints,
        {
          Component: stdin_default,
          variants: defaultResponsiveVariants,
          ...rest
        }
      ),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
};
function ComponentWithRoot({ locale, ...rest }) {
  return /* @__PURE__ */ jsx(
    ContextProviders,
    {
      routes: {
        "CSWMauv_K": {
          "path": "/projects/:slug"
        },
        "CmPsU4Vle": {
          "path": "/legal/:slug"
        },
        "EHD2n_rsP": {
          "path": "/projects"
        },
        "KWF5sRdmJ": {
          "path": "/blog/:slug"
        },
        "ZLJzEk6Fj": {
          "path": "/studio"
        },
        "augiA20Il": {
          "path": "/"
        },
        "hAlvrhFMj": {
          "path": "/contact"
        },
        "p3zwtzFek": {
          "path": "/blog"
        },
        "tR95VGg86": {
          "path": "/404"
        }
      },
      children: /* @__PURE__ */ jsx(stdin_default, { ...rest }),
      framerSiteId: "3ccb73edeeb799d0fbba38c74bc93847cbdc1d5953ee225f528934e69c9de9f1",
      locale,
      locales
    }
  );
}
Object.assign(ComponentWithRoot, stdin_default);
export {
  ComponentWithRoot as default
};
