import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";

// Define auth-related mutations and queries

export const signUp = mutation({
  args: {
    email: v.string(),
    password: v.string(),
    name: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Check if user exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first();
    
    if (existingUser) {
      throw new Error("User already exists");
    }
    
    // Create user
    const userId = await ctx.db.insert("users", {
      email: args.email,
      name: args.name,
    });
    
    // Initialize user stats
    await ctx.db.insert("stats", {
      type: "userActivity",
      value: 1,
      timestamp: Date.now(),
    });
    
    return { userId };
  },
});

export const signIn = mutation({
  args: {
    email: v.string(),
    password: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first();
    
    if (!user) {
      throw new Error("Invalid credentials");
    }
    
    return { userId: user._id };
  },
});

export const signOut = mutation({
  args: {},
  handler: async (ctx) => {
    return { success: true };
  },
});

export const session = query({
  args: {},
  handler: async (ctx) => {
    // Simple session check - would need actual session management
    return { user: null };
  },
});

export const verifyEmail = mutation({
  args: { token: v.string() },
  handler: async (ctx, args) => {
    return { success: true };
  },
});

export const forgotPassword = mutation({
  args: { email: v.string() },
  handler: async (ctx, args) => {
    return { success: true };
  },
});

export const resetPassword = mutation({
  args: { 
    token: v.string(),
    password: v.string(),
  },
  handler: async (ctx, args) => {
    return { success: true };
  },
});

// User management functions
export const createUser = mutation({
  args: {
    email: v.string(),
    name: v.optional(v.string()),
    image: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await ctx.db.insert("users", {
      email: args.email,
      name: args.name,
      image: args.image,
    });
    return { userId };
  },
});

export const deleteUser = mutation({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.userId);
    return { success: true };
  },
});

export const updateUser = mutation({
  args: {
    userId: v.id("users"),
    email: v.optional(v.string()),
    name: v.optional(v.string()),
    image: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { userId, ...updates } = args;
    await ctx.db.patch(userId, updates);
    return { success: true };
  },
});

export const createSession = mutation({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    // Simple session creation - would need actual implementation
    return { sessionId: "mock-session" };
  },
});

export const isAuthenticated = query({
  args: {},
  handler: async (ctx) => {
    // Simple auth check - would need actual implementation
    return false;
  },
});

// Export grouped functions for compatibility
export const authFunctions = {
  signUp,
  signIn,
  signOut,
  session,
  verifyEmail,
  forgotPassword,
  resetPassword,
  createUser,
  deleteUser,
  updateUser,
  createSession,
};

export const publicAuthFunctions = {
  signIn,
  signUp,
  session,
  isAuthenticated,
};

// For Better Auth component compatibility
export const betterAuthComponent = {
  httpHandler: async (request: Request) => {
    // Mock handler for HTTP routes
    return new Response(JSON.stringify({ success: true }), {
      headers: { "Content-Type": "application/json" },
    });
  },
};