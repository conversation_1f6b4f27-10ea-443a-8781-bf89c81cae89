import {
  BetterAuth,
  type AuthFunctions,
} from "@convex-dev/better-auth";
import { api, components, internal } from "./_generated/api";
import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import type { Id, DataModel } from "./_generated/dataModel";

// Typesafe way to pass Convex functions defined in this file
const authFunctions: AuthFunctions = internal.auth;

// Initialize the component
export const betterAuthComponent = new BetterAuth(
  components.betterAuth,
  {
    authFunctions,
  }
);

// These are required named exports for Better Auth
export const {
  createUser,
  updateUser,
  deleteUser,
  createSession,
} =
  betterAuthComponent.createAuthFunctions<DataModel>({
    // Must create a user and return the user id
    onCreateUser: async (ctx, user) => {
      // Create user in our application's users table
      const userId = await ctx.db.insert("users", {
        email: user.email,
        name: user.name,
        image: user.image,
        emailVerified: user.emailVerified,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        role: "user", // Default role
        isActive: true,
        lastLoginAt: Date.now(),
        preferences: {
          theme: "dark",
          notifications: true,
          timezone: "UTC",
        },
      });

      return userId;
    },

    // Update user when Better Auth updates user metadata
    onUpdateUser: async (ctx, user) => {
      await ctx.db.patch(user.userId as Id<"users">, {
        email: user.email,
        name: user.name,
        image: user.image,
        emailVerified: user.emailVerified,
        updatedAt: Date.now(),
        lastLoginAt: Date.now(),
      });

      // Log user update activity
      await ctx.db.insert("recentActivities", {
        action: `User profile updated: ${user.email}`,
        type: "user",
        time: "Just now",
        timestamp: Date.now(),
        userId: user.userId,
        severity: "info",
      });
    },

    // Delete the user when they are deleted from Better Auth
    onDeleteUser: async (ctx, userId) => {
      const user = await ctx.db.get(userId as Id<"users">);

      if (user) {
        // Log user deletion activity
        await ctx.db.insert("recentActivities", {
          action: `User account deleted: ${user.email}`,
          type: "system",
          time: "Just now",
          timestamp: Date.now(),
          severity: "warning",
        });
      }

      // Delete the user from our application's users table
      await ctx.db.delete(userId as Id<"users">);
    },
  });

// Example function for getting the current user
export const getCurrentUser = query({
  args: {},
  handler: async (ctx) => {
    // Get user data from Better Auth - email, name, image, etc.
    const userMetadata = await betterAuthComponent.getAuthUser(ctx);

    if (!userMetadata) {
      return null;
    }

    // Get user data from your application's database
    const user = await ctx.db.get(userMetadata.userId as Id<"users">);

    if (!user) {
      return null;
    }

    return {
      ...user,
      ...userMetadata,
    };
  },
});

// Get user by ID
export const getUserById = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId as Id<"users">);
    return user;
  },
});

// Update user preferences
export const updateUserPreferences = mutation({
  args: {
    preferences: v.object({
      theme: v.optional(v.union(v.literal("light"), v.literal("dark"), v.literal("system"))),
      notifications: v.optional(v.boolean()),
      timezone: v.optional(v.string()),
    }),
  },
  handler: async (ctx, args) => {
    const userMetadata = await betterAuthComponent.getAuthUser(ctx);

    if (!userMetadata) {
      throw new Error("User not authenticated");
    }

    await ctx.db.patch(userMetadata.userId as Id<"users">, {
      preferences: args.preferences,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

// Get user session info
export const getSession = query({
  args: {},
  handler: async (ctx) => {
    const userMetadata = await betterAuthComponent.getAuthUser(ctx);

    if (!userMetadata) {
      return null;
    }

    return {
      userId: userMetadata.userId,
      email: userMetadata.email,
      name: userMetadata.name,
      image: userMetadata.image,
      emailVerified: userMetadata.emailVerified,
    };
  },
});