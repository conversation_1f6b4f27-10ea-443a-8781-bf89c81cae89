import { query, mutation, internalMutation } from "./_generated/server";
import { v } from "convex/values";

// Helper functions
function randomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1) + min);
}

function randomFloat(min: number, max: number): number {
  return parseFloat((Math.random() * (max - min) + min).toFixed(2));
}

// Query to get agent metrics
export const getAgentMetrics = query({
  args: {},
  handler: async (ctx) => {
    // Get recent agent calls
    const recentCalls = await ctx.db
      .query("agentCalls")
      .withIndex("by_timestamp")
      .order("desc")
      .take(50);

    // Calculate summary metrics
    const totalRequests = recentCalls.length > 0 ? recentCalls.length * randomInt(200, 300) : randomInt(10000, 15000);
    const successCalls = recentCalls.filter(call => call.status === "success");
    const successRate = recentCalls.length > 0 
      ? (successCalls.length / recentCalls.length) * 100 
      : randomFloat(95, 99.9);
    const failureRate = 100 - successRate;
    
    // Calculate average latency
    const averageLatency = recentCalls.length > 0
      ? recentCalls.reduce((sum, call) => sum + call.duration, 0) / recentCalls.length
      : randomFloat(350, 750);
    
    // Calculate total tokens
    const totalTokensConsumed = recentCalls.length > 0
      ? recentCalls.reduce((sum, call) => sum + call.totalTokens, 0) * randomInt(100, 200)
      : randomInt(5000000, 15000000);

    // Get slow calls (duration > 5000ms)
    const slowCalls = recentCalls
      .filter(call => call.duration > 5000)
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 5);

    // Get error calls
    const errorCalls = recentCalls
      .filter(call => call.status === "error")
      .slice(0, 5);

    // Generate time series data (last 24 hours)
    const now = Date.now();
    const requestsTimeSeries = [];
    const latencyTimeSeries = [];
    const successRateTimeSeries = [];

    for (let i = 24; i >= 0; i--) {
      const timestamp = new Date(now - i * 60 * 60 * 1000).toISOString();
      requestsTimeSeries.push({ timestamp, value: randomFloat(50, 200) });
      latencyTimeSeries.push({ timestamp, value: randomFloat(300, 900) });
      successRateTimeSeries.push({ timestamp, value: randomFloat(94, 100) });
    }

    // Generate agent type distribution
    const agentTypes = ["Code Generator", "Documentation Assistant", "Test Generator", "Ontology Mapper", "Schema Analyzer"];
    const agentTypeDistribution = agentTypes.map(name => ({
      name,
      value: randomInt(5, 40),
    }));

    return {
      summary: {
        totalRequests,
        successRate,
        failureRate,
        averageLatency,
        totalTokensConsumed,
      },
      timeSeries: {
        requests: requestsTimeSeries,
        latency: latencyTimeSeries,
        successRate: successRateTimeSeries,
      },
      agentTypeDistribution,
      recentCalls: recentCalls.slice(0, 10),
      slowCalls,
      errorCalls,
    };
  },
});

// Mutation to log an agent call
export const logAgentCall = mutation({
  args: {
    agentType: v.string(),
    projectName: v.string(),
    status: v.union(v.literal("success"), v.literal("error"), v.literal("timeout"), v.literal("cancelled")),
    username: v.string(),
    duration: v.number(),
    promptTokens: v.number(),
    completionTokens: v.number(),
  },
  handler: async (ctx, args) => {
    const call = await ctx.db.insert("agentCalls", {
      agentType: args.agentType,
      projectName: args.projectName,
      status: args.status,
      username: args.username,
      timestamp: Date.now(),
      duration: args.duration,
      promptTokens: args.promptTokens,
      completionTokens: args.completionTokens,
      totalTokens: args.promptTokens + args.completionTokens,
    });

    return call;
  },
});

// Internal mutation to generate demo agent calls
export const generateDemoAgentCalls = internalMutation({
  args: {},
  handler: async (ctx) => {
    const agentTypes = ["Code Generator", "Documentation Assistant", "Test Generator", "Ontology Mapper", "Schema Analyzer"];
    const projectNames = ["Enterprise CRM", "Healthcare Platform", "Financial Dashboard", "E-commerce Portal", "Government System"];
    const statuses = ["success", "success", "success", "success", "error", "timeout", "cancelled"] as const;
    const usernames = ["Sarah Chen", "Michael Rodriguez", "Lisa Johnson", "David Park", "Alex Mercer"];

    // Generate 20 demo calls
    for (let i = 0; i < 20; i++) {
      const duration = randomInt(100, 10000);
      const promptTokens = randomInt(10, 500);
      const completionTokens = randomInt(50, 2000);

      await ctx.db.insert("agentCalls", {
        agentType: agentTypes[randomInt(0, agentTypes.length - 1)],
        projectName: projectNames[randomInt(0, projectNames.length - 1)],
        status: statuses[randomInt(0, statuses.length - 1)],
        username: usernames[randomInt(0, usernames.length - 1)],
        timestamp: Date.now() - randomInt(0, 24 * 60 * 60 * 1000), // Within last 24 hours
        duration,
        promptTokens,
        completionTokens,
        totalTokens: promptTokens + completionTokens,
      });
    }

    return { message: "Demo agent calls generated successfully" };
  },
});