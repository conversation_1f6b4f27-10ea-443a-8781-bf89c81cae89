/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as agentMetrics from "../agentMetrics.js";
import type * as auth from "../auth.js";
import type * as betterAuth from "../betterAuth.js";
import type * as http from "../http.js";
import type * as initDemo from "../initDemo.js";
import type * as stats from "../stats.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  agentMetrics: typeof agentMetrics;
  auth: typeof auth;
  betterAuth: typeof betterAuth;
  http: typeof http;
  initDemo: typeof initDemo;
  stats: typeof stats;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
