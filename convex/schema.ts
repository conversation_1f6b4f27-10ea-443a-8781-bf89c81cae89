import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

// Define the database schema based on the existing API data
export default defineSchema({
  // Users table for authentication
  users: defineTable({
    email: v.string(),
    name: v.optional(v.string()),
    image: v.optional(v.string()),
    // Better Auth compatibility fields
    emailVerified: v.optional(v.boolean()),
    createdAt: v.optional(v.number()),
    updatedAt: v.optional(v.number()),
  })
    .index("by_email", ["email"]),

  // Sessions table for Better Auth
  sessions: defineTable({
    userId: v.string(),
    token: v.string(),
    expiresAt: v.number(),
    createdAt: v.number(),
  })
    .index("by_token", ["token"])
    .index("by_userId", ["userId"]),

  // Stats for dashboard metrics
  stats: defineTable({
    type: v.string(), // "activeUsers", "ontologyEntities", etc.
    value: v.number(),
    timestamp: v.number(),
  })
    .index("by_type", ["type"])
    .index("by_timestamp", ["timestamp"]),

  // Project activities for the globe visualization
  projectActivities: defineTable({
    location: v.object({
      lat: v.number(),
      lng: v.number(),
    }),
    type: v.string(), // "development", "testing", "deployment", "planning"
    action: v.string(),
    project: v.string(),
    importance: v.number(),
    timestamp: v.number(),
  })
    .index("by_timestamp", ["timestamp"])
    .index("by_type", ["type"]),

  // Recent activities feed
  recentActivities: defineTable({
    action: v.string(),
    type: v.string(),
    time: v.string(),
    timestamp: v.number(),
  })
    .index("by_timestamp", ["timestamp"]),

  // Project performance metrics
  projectPerformance: defineTable({
    name: v.string(),
    velocity: v.number(),
    qualityScore: v.number(),
    budget: v.string(),
    budgetStatus: v.string(), // "stable", "positive", "negative"
    timeline: v.string(),
    timelineStatus: v.string(), // "stable", "positive", "negative"
    stakeholderSatisfaction: v.number(),
  })
    .index("by_name", ["name"]),

  // Agent activities
  agentActivities: defineTable({
    agent: v.string(),
    action: v.string(),
    project: v.string(),
    timestamp: v.number(),
    status: v.string(), // "completed", "in-progress", "failed"
  })
    .index("by_timestamp", ["timestamp"])
    .index("by_status", ["status"]),

  // Agent metrics data
  agentMetrics: defineTable({
    metricType: v.string(), // "summary", "distribution", "timeseries"
    data: v.any(), // Flexible data structure for different metric types
    timestamp: v.number(),
  })
    .index("by_type_timestamp", ["metricType", "timestamp"]),

  // Agent calls log
  agentCalls: defineTable({
    agentType: v.string(),
    projectName: v.string(),
    status: v.string(),
    username: v.string(),
    timestamp: v.number(),
    duration: v.number(),
    promptTokens: v.number(),
    completionTokens: v.number(),
    totalTokens: v.number(),
  })
    .index("by_timestamp", ["timestamp"])
    .index("by_status", ["status"])
    .index("by_duration", ["duration"]),
});