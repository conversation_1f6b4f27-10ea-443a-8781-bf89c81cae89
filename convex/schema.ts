import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

// Define the database schema optimized for production
export default defineSchema({
  // Users table for authentication with enhanced validation
  users: defineTable({
    email: v.string(), // Required, unique identifier
    name: v.optional(v.string()),
    image: v.optional(v.string()),
    // Better Auth compatibility fields
    emailVerified: v.optional(v.boolean()),
    createdAt: v.optional(v.number()),
    updatedAt: v.optional(v.number()),
    // Additional user metadata
    role: v.optional(v.union(v.literal("admin"), v.literal("user"), v.literal("viewer"))),
    isActive: v.optional(v.boolean()),
    lastLoginAt: v.optional(v.number()),
    preferences: v.optional(v.object({
      theme: v.optional(v.union(v.literal("light"), v.literal("dark"), v.literal("system"))),
      notifications: v.optional(v.boolean()),
      timezone: v.optional(v.string()),
    })),
  })
    .index("by_email", ["email"])
    .index("by_role", ["role"])
    .index("by_active", ["isActive"])
    .index("by_last_login", ["lastLoginAt"]),

  // Sessions table for Better Auth with enhanced security
  sessions: defineTable({
    userId: v.string(), // Reference to users table
    token: v.string(), // Unique session token
    expiresAt: v.number(), // Unix timestamp
    createdAt: v.number(), // Unix timestamp
    ipAddress: v.optional(v.string()), // For security tracking
    userAgent: v.optional(v.string()), // For device tracking
    isActive: v.optional(v.boolean()), // For session management
  })
    .index("by_token", ["token"])
    .index("by_userId", ["userId"])
    .index("by_expires", ["expiresAt"])
    .index("by_active_user", ["isActive", "userId"]),

  // Stats for dashboard metrics with validation
  stats: defineTable({
    type: v.union(
      v.literal("activeUsers"),
      v.literal("ontologyEntities"),
      v.literal("totalProjects"),
      v.literal("completedTasks"),
      v.literal("systemLoad"),
      v.literal("apiCalls"),
      v.literal("errorRate")
    ),
    value: v.number(),
    timestamp: v.number(),
    metadata: v.optional(v.object({
      source: v.optional(v.string()),
      unit: v.optional(v.string()),
      tags: v.optional(v.array(v.string())),
    })),
  })
    .index("by_type", ["type"])
    .index("by_timestamp", ["timestamp"])
    .index("by_type_timestamp", ["type", "timestamp"]),

  // Project activities for the globe visualization with validation
  projectActivities: defineTable({
    location: v.object({
      lat: v.number(), // Latitude: -90 to 90
      lng: v.number(), // Longitude: -180 to 180
      city: v.optional(v.string()),
      country: v.optional(v.string()),
    }),
    type: v.union(
      v.literal("development"),
      v.literal("testing"),
      v.literal("deployment"),
      v.literal("planning"),
      v.literal("review"),
      v.literal("maintenance")
    ),
    action: v.string(),
    project: v.string(),
    importance: v.number(), // 1-10 scale
    timestamp: v.number(),
    userId: v.optional(v.string()), // Reference to users table
    duration: v.optional(v.number()), // Duration in minutes
    status: v.optional(v.union(
      v.literal("completed"),
      v.literal("in-progress"),
      v.literal("failed"),
      v.literal("cancelled")
    )),
  })
    .index("by_timestamp", ["timestamp"])
    .index("by_type", ["type"])
    .index("by_project", ["project"])
    .index("by_user", ["userId"])
    .index("by_importance", ["importance"])
    .index("by_type_timestamp", ["type", "timestamp"]),

  // Recent activities feed with enhanced structure
  recentActivities: defineTable({
    action: v.string(),
    type: v.union(
      v.literal("user"),
      v.literal("system"),
      v.literal("agent"),
      v.literal("project"),
      v.literal("deployment")
    ),
    time: v.string(), // Human-readable time
    timestamp: v.number(), // Unix timestamp for sorting
    userId: v.optional(v.string()), // Reference to users table
    projectId: v.optional(v.string()), // Reference to project
    severity: v.optional(v.union(
      v.literal("info"),
      v.literal("warning"),
      v.literal("error"),
      v.literal("success")
    )),
    metadata: v.optional(v.object({
      source: v.optional(v.string()),
      details: v.optional(v.string()),
    })),
  })
    .index("by_timestamp", ["timestamp"])
    .index("by_type", ["type"])
    .index("by_user", ["userId"])
    .index("by_severity", ["severity"])
    .index("by_type_timestamp", ["type", "timestamp"]),

  // Project performance metrics with validation
  projectPerformance: defineTable({
    name: v.string(),
    velocity: v.number(), // Story points per sprint
    qualityScore: v.number(), // 0-100 scale
    budget: v.string(), // Budget amount with currency
    budgetStatus: v.union(
      v.literal("stable"),
      v.literal("positive"),
      v.literal("negative"),
      v.literal("critical")
    ),
    timeline: v.string(), // Timeline description
    timelineStatus: v.union(
      v.literal("stable"),
      v.literal("positive"),
      v.literal("negative"),
      v.literal("critical")
    ),
    stakeholderSatisfaction: v.number(), // 0-100 scale
    lastUpdated: v.number(), // Unix timestamp
    projectId: v.optional(v.string()), // Unique project identifier
    teamSize: v.optional(v.number()),
    completionPercentage: v.optional(v.number()), // 0-100
  })
    .index("by_name", ["name"])
    .index("by_project_id", ["projectId"])
    .index("by_last_updated", ["lastUpdated"])
    .index("by_budget_status", ["budgetStatus"])
    .index("by_timeline_status", ["timelineStatus"]),

  // Agent activities with enhanced tracking
  agentActivities: defineTable({
    agent: v.string(), // Agent identifier
    action: v.string(), // Action description
    project: v.string(), // Project name
    timestamp: v.number(), // Unix timestamp
    status: v.union(
      v.literal("completed"),
      v.literal("in-progress"),
      v.literal("failed"),
      v.literal("cancelled"),
      v.literal("pending")
    ),
    duration: v.optional(v.number()), // Duration in milliseconds
    userId: v.optional(v.string()), // User who initiated
    errorMessage: v.optional(v.string()), // Error details if failed
    metadata: v.optional(v.object({
      version: v.optional(v.string()),
      environment: v.optional(v.string()),
      tags: v.optional(v.array(v.string())),
    })),
  })
    .index("by_timestamp", ["timestamp"])
    .index("by_status", ["status"])
    .index("by_agent", ["agent"])
    .index("by_project", ["project"])
    .index("by_user", ["userId"])
    .index("by_status_timestamp", ["status", "timestamp"]),

  // Agent metrics data with structured types
  agentMetrics: defineTable({
    metricType: v.union(
      v.literal("summary"),
      v.literal("distribution"),
      v.literal("timeseries"),
      v.literal("performance"),
      v.literal("usage")
    ),
    data: v.any(), // Flexible data structure for different metric types
    timestamp: v.number(),
    agentId: v.optional(v.string()), // Agent identifier
    projectId: v.optional(v.string()), // Project identifier
    version: v.optional(v.string()), // Metric schema version
  })
    .index("by_type_timestamp", ["metricType", "timestamp"])
    .index("by_agent", ["agentId"])
    .index("by_project", ["projectId"]),

  // Agent calls log with enhanced tracking
  agentCalls: defineTable({
    agentType: v.string(),
    projectName: v.string(),
    status: v.union(
      v.literal("success"),
      v.literal("error"),
      v.literal("timeout"),
      v.literal("cancelled")
    ),
    username: v.string(),
    timestamp: v.number(),
    duration: v.number(), // Duration in milliseconds
    promptTokens: v.number(),
    completionTokens: v.number(),
    totalTokens: v.number(),
    // Additional tracking fields
    model: v.optional(v.string()), // AI model used
    cost: v.optional(v.number()), // Cost in USD
    errorCode: v.optional(v.string()), // Error code if failed
    errorMessage: v.optional(v.string()), // Error message if failed
    requestId: v.optional(v.string()), // Unique request identifier
  })
    .index("by_timestamp", ["timestamp"])
    .index("by_status", ["status"])
    .index("by_duration", ["duration"])
    .index("by_username", ["username"])
    .index("by_agent_type", ["agentType"])
    .index("by_project", ["projectName"])
    .index("by_status_timestamp", ["status", "timestamp"]),
});