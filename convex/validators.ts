/**
 * Data validation utilities for Convex schema
 * These validators ensure data integrity and consistency
 */

import { v } from "convex/values";

// Common validation patterns
export const validators = {
  // Email validation
  email: v.string(),
  
  // Timestamp validation (Unix timestamp)
  timestamp: v.number(),
  
  // Latitude validation (-90 to 90)
  latitude: v.number(),
  
  // Longitude validation (-180 to 180)
  longitude: v.number(),
  
  // Percentage validation (0 to 100)
  percentage: v.number(),
  
  // Score validation (0 to 100)
  score: v.number(),
  
  // Importance level (1 to 10)
  importance: v.number(),
  
  // Duration in milliseconds
  duration: v.number(),
  
  // Token count (non-negative)
  tokenCount: v.number(),
  
  // Cost in USD (non-negative)
  cost: v.number(),
};

// User role validation
export const userRoles = v.union(
  v.literal("admin"),
  v.literal("user"),
  v.literal("viewer")
);

// Activity types
export const activityTypes = v.union(
  v.literal("development"),
  v.literal("testing"),
  v.literal("deployment"),
  v.literal("planning"),
  v.literal("review"),
  v.literal("maintenance")
);

// Status types
export const statusTypes = v.union(
  v.literal("completed"),
  v.literal("in-progress"),
  v.literal("failed"),
  v.literal("cancelled"),
  v.literal("pending")
);

// Severity levels
export const severityLevels = v.union(
  v.literal("info"),
  v.literal("warning"),
  v.literal("error"),
  v.literal("success")
);

// Budget/Timeline status
export const budgetTimelineStatus = v.union(
  v.literal("stable"),
  v.literal("positive"),
  v.literal("negative"),
  v.literal("critical")
);

// Agent call status
export const agentCallStatus = v.union(
  v.literal("success"),
  v.literal("error"),
  v.literal("timeout"),
  v.literal("cancelled")
);

// Metric types
export const metricTypes = v.union(
  v.literal("summary"),
  v.literal("distribution"),
  v.literal("timeseries"),
  v.literal("performance"),
  v.literal("usage")
);

// Stats types
export const statsTypes = v.union(
  v.literal("activeUsers"),
  v.literal("ontologyEntities"),
  v.literal("totalProjects"),
  v.literal("completedTasks"),
  v.literal("systemLoad"),
  v.literal("apiCalls"),
  v.literal("errorRate")
);

// Recent activity types
export const recentActivityTypes = v.union(
  v.literal("user"),
  v.literal("system"),
  v.literal("agent"),
  v.literal("project"),
  v.literal("deployment")
);

// Theme options
export const themeOptions = v.union(
  v.literal("light"),
  v.literal("dark"),
  v.literal("system")
);

// Location object with validation
export const locationValidator = v.object({
  lat: validators.latitude,
  lng: validators.longitude,
  city: v.optional(v.string()),
  country: v.optional(v.string()),
});

// User preferences object
export const userPreferencesValidator = v.object({
  theme: v.optional(themeOptions),
  notifications: v.optional(v.boolean()),
  timezone: v.optional(v.string()),
});

// Metadata object for activities
export const activityMetadataValidator = v.object({
  source: v.optional(v.string()),
  details: v.optional(v.string()),
});

// Metadata object for stats
export const statsMetadataValidator = v.object({
  source: v.optional(v.string()),
  unit: v.optional(v.string()),
  tags: v.optional(v.array(v.string())),
});

// Metadata object for agent activities
export const agentActivityMetadataValidator = v.object({
  version: v.optional(v.string()),
  environment: v.optional(v.string()),
  tags: v.optional(v.array(v.string())),
});

// Validation helper functions (for use in mutations)
export const validationHelpers = {
  /**
   * Validate email format
   */
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  /**
   * Validate latitude range
   */
  isValidLatitude: (lat: number): boolean => {
    return lat >= -90 && lat <= 90;
  },

  /**
   * Validate longitude range
   */
  isValidLongitude: (lng: number): boolean => {
    return lng >= -180 && lng <= 180;
  },

  /**
   * Validate percentage range
   */
  isValidPercentage: (value: number): boolean => {
    return value >= 0 && value <= 100;
  },

  /**
   * Validate importance level
   */
  isValidImportance: (value: number): boolean => {
    return value >= 1 && value <= 10;
  },

  /**
   * Validate timestamp (not in future, reasonable past)
   */
  isValidTimestamp: (timestamp: number): boolean => {
    const now = Date.now();
    const oneYearAgo = now - (365 * 24 * 60 * 60 * 1000);
    return timestamp <= now && timestamp >= oneYearAgo;
  },

  /**
   * Validate duration (positive number, reasonable max)
   */
  isValidDuration: (duration: number): boolean => {
    const maxDuration = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
    return duration >= 0 && duration <= maxDuration;
  },

  /**
   * Validate token count (non-negative, reasonable max)
   */
  isValidTokenCount: (tokens: number): boolean => {
    const maxTokens = 1000000; // 1M tokens max
    return tokens >= 0 && tokens <= maxTokens;
  },

  /**
   * Validate cost (non-negative, reasonable max)
   */
  isValidCost: (cost: number): boolean => {
    const maxCost = 10000; // $10,000 max per call
    return cost >= 0 && cost <= maxCost;
  },

  /**
   * Sanitize string input
   */
  sanitizeString: (input: string): string => {
    return input.trim().slice(0, 1000); // Trim and limit length
  },

  /**
   * Validate project name format
   */
  isValidProjectName: (name: string): boolean => {
    const projectNameRegex = /^[a-zA-Z0-9\s\-_]{1,100}$/;
    return projectNameRegex.test(name);
  },

  /**
   * Validate username format
   */
  isValidUsername: (username: string): boolean => {
    const usernameRegex = /^[a-zA-Z0-9_]{3,30}$/;
    return usernameRegex.test(username);
  },
};

// Error messages for validation failures
export const validationErrors = {
  INVALID_EMAIL: "Invalid email format",
  INVALID_LATITUDE: "Latitude must be between -90 and 90",
  INVALID_LONGITUDE: "Longitude must be between -180 and 180",
  INVALID_PERCENTAGE: "Percentage must be between 0 and 100",
  INVALID_IMPORTANCE: "Importance must be between 1 and 10",
  INVALID_TIMESTAMP: "Invalid timestamp",
  INVALID_DURATION: "Duration must be positive and reasonable",
  INVALID_TOKEN_COUNT: "Token count must be non-negative and reasonable",
  INVALID_COST: "Cost must be non-negative and reasonable",
  INVALID_PROJECT_NAME: "Project name contains invalid characters",
  INVALID_USERNAME: "Username must be 3-30 characters, alphanumeric and underscore only",
  STRING_TOO_LONG: "String exceeds maximum length",
  REQUIRED_FIELD_MISSING: "Required field is missing",
};
