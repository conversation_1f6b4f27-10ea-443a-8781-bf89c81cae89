import { internalMutation } from "./_generated/server";
import { internal } from "./_generated/api";

// Run this mutation to initialize demo data
export const setupDemoData = internalMutation({
  args: {},
  handler: async (ctx) => {
    // Initialize demo data for all collections
    const results = await Promise.all([
      ctx.runMutation(internal.stats.initializeDemoData),
      ctx.runMutation(internal.agentMetrics.generateDemoAgentCalls),
    ]);

    // Generate some initial project activities
    for (let i = 0; i < 50; i++) {
      await ctx.runMutation(internal.stats.generateProjectActivity);
    }

    return { success: true, message: "Demo data initialized" };
  },
});