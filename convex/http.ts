import { httpRouter } from "convex/server";
import { httpAction } from "./_generated/server";

const http = httpRouter();

// Create HTTP action handlers for auth routes
const authHandler = httpAction(async (ctx, request) => {
  // Mock handler for HTTP routes
  return new Response(JSON.stringify({ success: true }), {
    headers: { "Content-Type": "application/json" },
  });
});

// Mount Better Auth HTTP routes
http.route({
  path: "/auth/*",
  method: "GET",
  handler: authHandler,
});

http.route({
  path: "/auth/*",
  method: "POST",
  handler: authHandler,
});

export default http;