{"name": "unified-alias-damn", "version": "0.1.0", "private": true, "scripts": {"dev": "npm-run-all --parallel 'dev:*'", "dev:next": "next dev -H 0.0.0.0 --turbopack", "dev:convex": "convex dev", "build": "next build && convex deploy", "build:prod": "NODE_ENV=production next build && convex deploy --prod", "start": "next start", "deploy:prod": "npm run build:prod && vercel --prod", "deploy:netlify": "npm run build:prod && netlify deploy --prod", "convex:prod": "convex deploy --prod", "convex:init-prod": "convex run initDemo:setupDemoData --prod", "lint": "bunx biome lint --write && bunx tsc --noEmit", "format": "bunx biome format --write", "type-check": "bunx tsc --noEmit", "test": "echo \"Tests will be added in testing phase\" && exit 0"}, "dependencies": {"@convex-dev/better-auth": "^0.7.11", "@copilotkit/react-core": "^1.8.12", "@copilotkit/react-ui": "^1.8.12", "@motionone/dom": "^10.18.0", "@motionone/easing": "^10.18.0", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "@react-three/drei": "^10.0.7", "@react-three/fiber": "^9.1.2", "@types/three": "^0.176.0", "better-auth": "^1.3.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.25.4", "framer-motion": "^12.12.1", "lucide-react": "^0.475.0", "next": "^15.2.0", "next-themes": "^0.4.6", "react": "^18.3.1", "react-dom": "^18.3.1", "styled-components": "^6.1.18", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "three": "^0.176.0", "unframer": "^2.25.0"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "eslint": "^9", "eslint-config-next": "15.1.7", "npm-run-all": "^4.1.5", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}