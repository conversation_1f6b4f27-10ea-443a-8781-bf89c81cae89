# Production Setup Guide

This guide walks you through setting up the production environment for the ALIAS HQ application with Convex backend.

## Prerequisites

- Convex account with production deployment access
- Hosting platform account (Vercel/Netlify)
- Domain name (optional but recommended)
- OAuth provider credentials (GitHub, Google, etc.)

## Step 1: Create Production Convex Deployment

1. **Login to Convex Dashboard**
   ```bash
   npx convex login
   ```

2. **Create Production Deployment**
   ```bash
   npx convex deploy --prod
   ```
   
   This will create a production deployment and provide you with:
   - Production Convex URL
   - Production deployment name

3. **Configure Production Environment Variables**
   
   Update `.env.production` with your actual production values:
   ```bash
   NEXT_PUBLIC_CONVEX_URL=https://your-actual-production.convex.cloud
   CONVEX_DEPLOYMENT=prod:your-actual-deployment-name
   ```

## Step 2: Generate Secure Secrets

1. **Generate Better Auth Secret**
   ```bash
   openssl rand -base64 32
   ```
   
   Add to `.env.production`:
   ```bash
   BETTER_AUTH_SECRET=your-generated-secret
   ```

2. **Set Production App URL**
   ```bash
   NEXT_PUBLIC_APP_URL=https://your-domain.com
   ```

## Step 3: Configure OAuth Providers

### GitHub OAuth Setup

1. Go to GitHub Settings > Developer settings > OAuth Apps
2. Create a new OAuth App with:
   - Application name: "ALIAS HQ Production"
   - Homepage URL: `https://your-domain.com`
   - Authorization callback URL: `https://your-domain.com/api/auth/callback/github`
3. Copy Client ID and Client Secret to `.env.production`

### Google OAuth Setup (Optional)

1. Go to Google Cloud Console > APIs & Services > Credentials
2. Create OAuth 2.0 Client ID with:
   - Application type: Web application
   - Authorized redirect URIs: `https://your-domain.com/api/auth/callback/google`
3. Copy Client ID and Client Secret to `.env.production`

## Step 4: Deploy to Production

### Option A: Vercel Deployment

1. **Install Vercel CLI**
   ```bash
   npm i -g vercel
   ```

2. **Deploy to Vercel**
   ```bash
   vercel --prod
   ```

3. **Configure Environment Variables in Vercel Dashboard**
   - Go to your project settings
   - Add all variables from `.env.production`

### Option B: Netlify Deployment

1. **Install Netlify CLI**
   ```bash
   npm i -g netlify-cli
   ```

2. **Deploy to Netlify**
   ```bash
   netlify deploy --prod
   ```

3. **Configure Environment Variables in Netlify Dashboard**
   - Go to Site settings > Environment variables
   - Add all variables from `.env.production`

## Step 5: Initialize Production Data

After deployment, initialize demo data in production:

```bash
# Set production environment
export CONVEX_DEPLOYMENT=prod:your-deployment-name

# Initialize demo data
npx convex run initDemo:setupDemoData
```

## Step 6: Verify Production Deployment

1. **Check Application Health**
   - Visit your production URL
   - Test authentication flows
   - Verify data loading from Convex

2. **Monitor Convex Dashboard**
   - Check function execution logs
   - Monitor database operations
   - Verify real-time subscriptions

## Security Checklist

- [ ] Secure secrets generated and configured
- [ ] OAuth providers configured with production URLs
- [ ] Environment variables secured in hosting platform
- [ ] HTTPS enabled with valid SSL certificate
- [ ] CSP headers configured (next step in security hardening)
- [ ] Rate limiting configured (next step in security hardening)

## Next Steps

After completing production setup:

1. **Database Schema Optimization** - Review and optimize schema for production
2. **Security Hardening** - Implement CORS, rate limiting, and security headers
3. **Performance Optimization** - Optimize queries and implement caching
4. **Monitoring Setup** - Configure error tracking and performance monitoring

## Troubleshooting

### Common Issues

1. **Convex Connection Errors**
   - Verify `NEXT_PUBLIC_CONVEX_URL` is correct
   - Check Convex deployment status in dashboard

2. **Authentication Issues**
   - Verify OAuth callback URLs match exactly
   - Check `BETTER_AUTH_SECRET` is set correctly
   - Ensure `NEXT_PUBLIC_APP_URL` matches your domain

3. **Build Failures**
   - Check all required environment variables are set
   - Verify TypeScript compilation passes locally
   - Review build logs for specific errors

## Support

- Convex Documentation: https://docs.convex.dev
- Better Auth Documentation: https://better-auth.com
- Project Issues: Create an issue in the repository
